// Simple test for the status function
function determineIVFAssessmentStatus(currentStep, hasError = false) {
  if (hasError) {
    return 'failed';
  }
  
  if (currentStep >= 3) {
    return 'completed';
  }
  
  return 'pending';
}

// Test cases
console.log('Step 1:', determineIVFAssessmentStatus(1)); // should be 'pending'
console.log('Step 2:', determineIVFAssessmentStatus(2)); // should be 'pending'
console.log('Step 3:', determineIVFAssessmentStatus(3)); // should be 'completed'
console.log('Step 4:', determineIVFAssessmentStatus(4)); // should be 'completed'
console.log('Error case:', determineIVFAssessmentStatus(3, true)); // should be 'failed' 