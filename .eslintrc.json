{"extends": ["next/core-web-vitals", "next/typescript", "plugin:storybook/recommended"], "ignorePatterns": ["src/generated/**/*", "src/generated/prisma/**/*", "src/generated/prisma/runtime/**/*"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "rules": {"no-unused-vars": "off", "@typescript-eslint/no-unused-vars": "warn", "no-useless-escape": "error", "no-irregular-whitespace": ["error", {"skipStrings": false, "skipComments": false, "skipRegExps": false, "skipTemplates": false}], "no-misleading-character-class": "error", "quotes": "off", "react/no-unescaped-entities": ["error", {"forbid": [">", "}"]}]}}