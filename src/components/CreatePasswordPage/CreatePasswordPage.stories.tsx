import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import CreatePasswordPage from "./CreatePasswordPage";
import { AuthProvider } from "@/contexts/AuthContext";
import { ToastProvider } from "@/contexts/ToastContext";

const meta: Meta<typeof CreatePasswordPage> = {
  title: "Pages/CreatePasswordPage",
  component: CreatePasswordPage,
  parameters: {
    layout: "fullscreen",
  },
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <ToastProvider duration={4000} maxToasts={5}>
        <AuthProvider>
          <Story />
        </AuthProvider>
      </ToastProvider>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const MobileView: Story = {
  args: {},
  parameters: {
    viewport: {
      defaultViewport: "mobile1",
    },
  },
};

export const TabletView: Story = {
  args: {},
  parameters: {
    viewport: {
      defaultViewport: "tablet",
    },
  },
};

export const WithValidPassword: Story = {
  args: {},
  play: async ({ canvasElement }) => {
    const canvas = canvasElement;
    const newPasswordInput = canvas.querySelector(
      "#new-password"
    ) as HTMLInputElement;
    const confirmPasswordInput = canvas.querySelector(
      "#confirm-password"
    ) as HTMLInputElement;

    if (newPasswordInput && confirmPasswordInput) {
      // Simulate entering a valid password
      newPasswordInput.value = "Password123!";
      newPasswordInput.dispatchEvent(new Event("change", { bubbles: true }));

      confirmPasswordInput.value = "Password123!";
      confirmPasswordInput.dispatchEvent(
        new Event("change", { bubbles: true })
      );
    }
  },
};

export const WithMismatchedPasswords: Story = {
  args: {},
  play: async ({ canvasElement }) => {
    const canvas = canvasElement;
    const newPasswordInput = canvas.querySelector(
      "#new-password"
    ) as HTMLInputElement;
    const confirmPasswordInput = canvas.querySelector(
      "#confirm-password"
    ) as HTMLInputElement;

    if (newPasswordInput && confirmPasswordInput) {
      // Simulate entering mismatched passwords
      newPasswordInput.value = "Password123!";
      newPasswordInput.dispatchEvent(new Event("change", { bubbles: true }));

      confirmPasswordInput.value = "DifferentPass!";
      confirmPasswordInput.dispatchEvent(
        new Event("change", { bubbles: true })
      );
    }
  },
};
