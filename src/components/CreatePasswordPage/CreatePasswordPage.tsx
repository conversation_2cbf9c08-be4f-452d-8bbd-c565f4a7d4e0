import React, { useState, useEffect } from "react";
import Header, { HeaderState } from "../shared/Header/Header";
import Footer from "../shared/Footer/Footer";
import Button, { ButtonType } from "../shared/Button/Button";
import PasswordInput from "../shared/PasswordInput/PasswordInput";
import PageHeader from "../shared/PageHeader/PageHeader";

// Check icon component
const CheckIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
    <circle cx="12" cy="12" r="10" fill="#10B981" />
    <path
      d="M9 12l2 2 4-4"
      stroke="white"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

// Unchecked circle icon component
const UncheckedIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
    <circle cx="12" cy="12" r="10" stroke="#D1D5DB" strokeWidth="2" />
  </svg>
);

interface PasswordRequirement {
  id: string;
  text: string;
  isValid: boolean;
}

// Define requirements structure as constant to avoid dependency issues
const INITIAL_REQUIREMENTS = [
  { id: "length", text: "At least 8 character", isValid: false },
  { id: "lowercase", text: "1 Lowercase letter", isValid: false },
  { id: "uppercase", text: "1 Upper case letter", isValid: false },
  { id: "symbol", text: "1 A symbol (@&%$)", isValid: false },
];

const CreatePasswordPage = () => {
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [requirements, setRequirements] = useState<PasswordRequirement[]>(INITIAL_REQUIREMENTS);

  // Validate password requirements
  useEffect(() => {
    const newRequirements = INITIAL_REQUIREMENTS.map((req) => {
      switch (req.id) {
        case "length":
          return { ...req, isValid: newPassword.length >= 8 };
        case "lowercase":
          return { ...req, isValid: /[a-z]/.test(newPassword) };
        case "uppercase":
          return { ...req, isValid: /[A-Z]/.test(newPassword) };
        case "symbol":
          return { ...req, isValid: /[@&%$#!*]/.test(newPassword) };
        default:
          return req;
      }
    });
    setRequirements(newRequirements);
  }, [newPassword]);

  const handleNewPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewPassword(e.target.value);
  };

  const handleConfirmPasswordChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setConfirmPassword(e.target.value);
  };

  const handleContinueToVerifyEmail = () => {
    if (isFormValid()) {
      console.log("Continue to Verify Email clicked");
    }
  };

  const handleLoginClick = () => {
    console.log("Navigate to login");
  };

  const isFormValid = () => {
    const allRequirementsMet = requirements.every((req) => req.isValid);
    const passwordsMatch =
      newPassword === confirmPassword && confirmPassword.length > 0;
    return allRequirementsMet && passwordsMatch;
  };

  const getPasswordStrength = () => {
    const validCount = requirements.filter((req) => req.isValid).length;
    const percentage = (validCount / requirements.length) * 100;

    let color = "#EF4444"; // Red for weak
    if (percentage >= 75) {
      color = "#10B981"; // Green for strong
    } else if (percentage >= 50) {
      color = "#F59E0B"; // Yellow for medium
    }

    return { percentage, color };
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header state={HeaderState.HELP} />
      <main className="flex-1 flex justify-center items-center py-4 px-4 md:py-6 md:px-[9.375rem]">
        <div className="max-w-[40.125rem] w-full">
          <div className="bg-white rounded-lg">
            <div className="max-w-md mx-auto space-y-6 text-center">
              {/* Title */}
              <PageHeader title="Create Password" />

              {/* Description */}
              <div className="mb-12">
                <p className="text-[var(--grey-6)] text-base leading-relaxed">
                  Choose your password to begin, this will allow you to login
                  again into your IVF account
                </p>
              </div>

              {/* Password Fields */}
              <div className="space-y-4 text-left">
                {/* New Password */}
                <div>
                  <label
                    htmlFor="new-password"
                    className="block text-[var(--grey-6)] text-base font-medium mb-2"
                  >
                    New Password
                  </label>
                  <PasswordInput
                    id="new-password"
                    value={newPassword}
                    onChange={handleNewPasswordChange}
                    placeholder="Enter new password"
                    minLength={8}
                  />
                </div>

                {/* Confirm Password */}
                <div>
                  <label
                    htmlFor="confirm-password"
                    className="block text-[var(--grey-6)] text-base font-medium mb-2"
                  >
                    Confirm Password
                  </label>
                  <PasswordInput
                    id="confirm-password"
                    value={confirmPassword}
                    onChange={handleConfirmPasswordChange}
                    placeholder="Confirm password"
                    minLength={8}
                  />
                </div>
              </div>

              {/* Password Requirements */}
              <div className="text-left space-y-4">
                <div>
                  <p className="text-[var(--grey-6)] text-sm">
                    Must have at least 8 character
                  </p>
                  <div className="w-full h-1 bg-gray-200 rounded-full mt-2">
                    <div
                      className="h-full rounded-full transition-all duration-300 ease-in-out"
                      style={{
                        width: `${getPasswordStrength().percentage}%`,
                        backgroundColor: getPasswordStrength().color,
                      }}
                    ></div>
                  </div>
                </div>

                <div>
                  <p className="text-[var(--grey-6)] text-sm mb-3">
                    To make your password Stronger:
                  </p>
                  <div className="grid grid-cols-2 gap-x-8 gap-y-3">
                    {requirements.map((requirement) => (
                      <div
                        key={requirement.id}
                        className="flex items-center gap-3"
                      >
                        {requirement.isValid ? (
                          <CheckIcon />
                        ) : (
                          <UncheckedIcon />
                        )}
                        <span
                          className={`text-sm ${
                            requirement.isValid
                              ? "text-[var(--grey-7)]"
                              : "text-[var(--grey-5)]"
                          }`}
                        >
                          {requirement.text}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Continue Button */}
              <div className="pt-6">
                <Button
                  type={ButtonType.PRIMARY}
                  text="Continue to Verify Email"
                  onClick={handleContinueToVerifyEmail}
                  disabled={!isFormValid()}
                  className="w-full"
                />
              </div>

              {/* Login Link */}
              <div className="pt-6">
                <p className="text-[var(--grey-6)] text-center text-base mb-2">
                  or
                </p>
                <p className="text-[var(--grey-6)] text-center text-base">
                  Already have an account?{" "}
                  <button
                    type="button"
                    onClick={handleLoginClick}
                    className="text-[var(--red-6)] hover:text-[var(--red-7)] underline font-medium transition-colors duration-200"
                  >
                    Login
                  </button>
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default CreatePasswordPage;
