/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";

// Mock shared components
jest.mock("../shared/Header/Header", () => {
  const MockHeader = () => <div data-testid="header">Header</div>;
  MockHeader.displayName = "MockHeader";
  return {
    __esModule: true,
    default: MockHeader,
    HeaderState: { HELP: "HELP" },
  };
});

jest.mock("../shared/Footer/Footer", () => {
  const MockFooter = () => <div data-testid="footer">Footer</div>;
  MockFooter.displayName = "MockFooter";
  return MockFooter;
});

jest.mock("../shared/Button/Button", () => {
  interface ButtonProps {
    text: string;
    onClick: () => void;
    disabled?: boolean;
    type?: string;
    className?: string;
  }
  const MockButton = ({ text, onClick, disabled }: ButtonProps) => (
    <button onClick={onClick} disabled={disabled} data-testid="continue-button">
      {text}
    </button>
  );
  MockButton.displayName = "MockButton";
  return {
    __esModule: true,
    default: MockButton,
    ButtonType: { PRIMARY: "primary" },
  };
});

jest.mock("../shared/PasswordInput/PasswordInput", () => {
  interface PasswordInputProps {
    id: string;
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    placeholder: string;
    minLength?: number;
  }
  const MockPasswordInput = ({
    id,
    value,
    onChange,
    placeholder,
  }: PasswordInputProps) => (
    <input
      id={id}
      type="password"
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      data-testid={id}
    />
  );
  MockPasswordInput.displayName = "MockPasswordInput";
  return MockPasswordInput;
});

jest.mock("../shared/PageHeader/PageHeader", () => {
  interface PageHeaderProps {
    title: string;
  }
  const MockPageHeader = ({ title }: PageHeaderProps) => (
    <div data-testid="page-header">{title}</div>
  );
  MockPageHeader.displayName = "MockPageHeader";
  return MockPageHeader;
});

import CreatePasswordPage from "./CreatePasswordPage";

describe("CreatePasswordPage", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Component Rendering", () => {
    it("renders all main components", () => {
      render(<CreatePasswordPage />);

      expect(screen.getByTestId("header")).toBeInTheDocument();
      expect(screen.getByTestId("footer")).toBeInTheDocument();
      expect(screen.getByTestId("page-header")).toBeInTheDocument();
      expect(screen.getByTestId("new-password")).toBeInTheDocument();
      expect(screen.getByTestId("confirm-password")).toBeInTheDocument();
      expect(screen.getByTestId("continue-button")).toBeInTheDocument();
    });

    it("renders page title correctly", () => {
      render(<CreatePasswordPage />);

      expect(screen.getByText("Create Password")).toBeInTheDocument();
    });

    it("renders description text", () => {
      render(<CreatePasswordPage />);

      expect(
        screen.getByText(
          /Choose your password to begin, this will allow you to login again into your IVF account/
        )
      ).toBeInTheDocument();
    });

    it("renders password field labels", () => {
      render(<CreatePasswordPage />);

      expect(screen.getByText("New Password")).toBeInTheDocument();
      expect(screen.getByText("Confirm Password")).toBeInTheDocument();
    });

    it("renders password requirements section", () => {
      render(<CreatePasswordPage />);

      expect(
        screen.getByText("Must have at least 8 character")
      ).toBeInTheDocument();
      expect(
        screen.getByText("To make your password Stronger:")
      ).toBeInTheDocument();
    });

    it("renders all password requirements", () => {
      render(<CreatePasswordPage />);

      expect(screen.getByText("At least 8 character")).toBeInTheDocument();
      expect(screen.getByText("1 Lowercase letter")).toBeInTheDocument();
      expect(screen.getByText("1 Upper case letter")).toBeInTheDocument();
      expect(screen.getByText("1 A symbol (@&%$)")).toBeInTheDocument();
    });

    it("renders login link", () => {
      render(<CreatePasswordPage />);

      expect(screen.getByText("Already have an account?")).toBeInTheDocument();
      expect(screen.getByText("Login")).toBeInTheDocument();
    });
  });

  describe("Password Input Handling", () => {
    it("updates new password value when typing", () => {
      render(<CreatePasswordPage />);

      const newPasswordInput = screen.getByTestId("new-password");
      fireEvent.change(newPasswordInput, {
        target: { value: "MyPassword123@" },
      });

      expect(newPasswordInput).toHaveValue("MyPassword123@");
    });

    it("updates confirm password value when typing", () => {
      render(<CreatePasswordPage />);

      const confirmPasswordInput = screen.getByTestId("confirm-password");
      fireEvent.change(confirmPasswordInput, {
        target: { value: "MyPassword123@" },
      });

      expect(confirmPasswordInput).toHaveValue("MyPassword123@");
    });
  });

  describe("Password Requirements Validation", () => {
    it("validates length requirement", () => {
      render(<CreatePasswordPage />);
      const newPasswordInput = screen.getByTestId("new-password");

      // Password less than 8 characters
      fireEvent.change(newPasswordInput, { target: { value: "Test12@" } });
      let lengthRequirement = screen.getByText("At least 8 character");
      expect(lengthRequirement).toHaveClass("text-[var(--grey-5)]");

      // Password 8 or more characters
      fireEvent.change(newPasswordInput, { target: { value: "Test123@" } });
      lengthRequirement = screen.getByText("At least 8 character");
      expect(lengthRequirement).toHaveClass("text-[var(--grey-7)]");
    });

    it("validates lowercase requirement", () => {
      render(<CreatePasswordPage />);
      const newPasswordInput = screen.getByTestId("new-password");

      // Password without lowercase
      fireEvent.change(newPasswordInput, { target: { value: "PASSWORD123@" } });
      let lowercaseRequirement = screen.getByText("1 Lowercase letter");
      expect(lowercaseRequirement).toHaveClass("text-[var(--grey-5)]");

      // Password with lowercase
      fireEvent.change(newPasswordInput, { target: { value: "Password123@" } });
      lowercaseRequirement = screen.getByText("1 Lowercase letter");
      expect(lowercaseRequirement).toHaveClass("text-[var(--grey-7)]");
    });

    it("validates uppercase requirement", () => {
      render(<CreatePasswordPage />);
      const newPasswordInput = screen.getByTestId("new-password");

      // Password without uppercase
      fireEvent.change(newPasswordInput, { target: { value: "password123@" } });
      let uppercaseRequirement = screen.getByText("1 Upper case letter");
      expect(uppercaseRequirement).toHaveClass("text-[var(--grey-5)]");

      // Password with uppercase
      fireEvent.change(newPasswordInput, { target: { value: "Password123@" } });
      uppercaseRequirement = screen.getByText("1 Upper case letter");
      expect(uppercaseRequirement).toHaveClass("text-[var(--grey-7)]");
    });

    it("validates symbol requirement", () => {
      render(<CreatePasswordPage />);
      const newPasswordInput = screen.getByTestId("new-password");

      // Password without symbol
      fireEvent.change(newPasswordInput, { target: { value: "Password123" } });
      let symbolRequirement = screen.getByText("1 A symbol (@&%$)");
      expect(symbolRequirement).toHaveClass("text-[var(--grey-5)]");

      // Password with symbol
      fireEvent.change(newPasswordInput, { target: { value: "Password123@" } });
      symbolRequirement = screen.getByText("1 A symbol (@&%$)");
      expect(symbolRequirement).toHaveClass("text-[var(--grey-7)]");
    });
  });

  describe("Form Validation", () => {
    it("disables continue button when form is invalid", () => {
      render(<CreatePasswordPage />);

      const continueButton = screen.getByTestId("continue-button");
      expect(continueButton).toBeDisabled();
    });

    it("keeps continue button disabled when passwords don't match", () => {
      render(<CreatePasswordPage />);

      const newPasswordInput = screen.getByTestId("new-password");
      const confirmPasswordInput = screen.getByTestId("confirm-password");
      const continueButton = screen.getByTestId("continue-button");

      fireEvent.change(newPasswordInput, { target: { value: "Password123@" } });
      fireEvent.change(confirmPasswordInput, {
        target: { value: "DifferentPassword123@" },
      });

      expect(continueButton).toBeDisabled();
    });

    it("enables continue button when form is valid", () => {
      render(<CreatePasswordPage />);

      const newPasswordInput = screen.getByTestId("new-password");
      const confirmPasswordInput = screen.getByTestId("confirm-password");
      const continueButton = screen.getByTestId("continue-button");

      fireEvent.change(newPasswordInput, { target: { value: "Password123@" } });
      fireEvent.change(confirmPasswordInput, {
        target: { value: "Password123@" },
      });

      expect(continueButton).not.toBeDisabled();
    });

    it("validates all requirements are met for form to be valid", () => {
      render(<CreatePasswordPage />);

      const newPasswordInput = screen.getByTestId("new-password");
      const confirmPasswordInput = screen.getByTestId("confirm-password");
      const continueButton = screen.getByTestId("continue-button");

      // Only length requirement met
      fireEvent.change(newPasswordInput, { target: { value: "12345678" } });
      fireEvent.change(confirmPasswordInput, { target: { value: "12345678" } });
      expect(continueButton).toBeDisabled();

      // All requirements met
      fireEvent.change(newPasswordInput, { target: { value: "Password123@" } });
      fireEvent.change(confirmPasswordInput, {
        target: { value: "Password123@" },
      });
      expect(continueButton).not.toBeDisabled();
    });
  });

  describe("Password Strength Indicator", () => {
    it("displays password strength progress bar", () => {
      render(<CreatePasswordPage />);

      const progressBar = document.querySelector(".h-full.rounded-full");
      expect(progressBar).toBeInTheDocument();
    });

    it("updates progress bar based on requirements met", () => {
      render(<CreatePasswordPage />);

      const newPasswordInput = screen.getByTestId("new-password");

      // No requirements met (0%)
      fireEvent.change(newPasswordInput, { target: { value: "123" } });
      const progressBar = document.querySelector(
        ".h-full.rounded-full"
      ) as HTMLElement;
      expect(progressBar.style.width).toBe("0%");

      // All requirements met (100%)
      fireEvent.change(newPasswordInput, { target: { value: "Password123@" } });
      expect(progressBar.style.width).toBe("100%");
    });
  });

  describe("Button Interactions", () => {
    it("handles continue button click when form is valid", () => {
      const consoleSpy = jest.spyOn(console, "log").mockImplementation();
      render(<CreatePasswordPage />);

      const newPasswordInput = screen.getByTestId("new-password");
      const confirmPasswordInput = screen.getByTestId("confirm-password");
      const continueButton = screen.getByTestId("continue-button");

      fireEvent.change(newPasswordInput, { target: { value: "Password123@" } });
      fireEvent.change(confirmPasswordInput, {
        target: { value: "Password123@" },
      });
      fireEvent.click(continueButton);

      expect(consoleSpy).toHaveBeenCalledWith(
        "Continue to Verify Email clicked"
      );
      consoleSpy.mockRestore();
    });

    it("handles login link click", () => {
      const consoleSpy = jest.spyOn(console, "log").mockImplementation();
      render(<CreatePasswordPage />);

      const loginLink = screen.getByText("Login");
      fireEvent.click(loginLink);

      expect(consoleSpy).toHaveBeenCalledWith("Navigate to login");
      consoleSpy.mockRestore();
    });
  });

  describe("Edge Cases", () => {
    it("handles empty password inputs", () => {
      render(<CreatePasswordPage />);

      const newPasswordInput = screen.getByTestId("new-password");
      const confirmPasswordInput = screen.getByTestId("confirm-password");

      expect(newPasswordInput).toHaveValue("");
      expect(confirmPasswordInput).toHaveValue("");
      expect(screen.getByTestId("continue-button")).toBeDisabled();
    });

    it("handles special symbols in password validation", () => {
      render(<CreatePasswordPage />);

      const newPasswordInput = screen.getByTestId("new-password");

      // Test different symbols
      const symbolsToTest = ["@", "&", "%", "$", "#", "!", "*"];

      symbolsToTest.forEach((symbol) => {
        fireEvent.change(newPasswordInput, {
          target: { value: `Password123${symbol}` },
        });
        const symbolRequirement = screen.getByText("1 A symbol (@&%$)");
        expect(symbolRequirement).toHaveClass("text-[var(--grey-7)]");
      });
    });

    it("resets requirements when password is cleared", () => {
      render(<CreatePasswordPage />);

      const newPasswordInput = screen.getByTestId("new-password");

      // Set valid password
      fireEvent.change(newPasswordInput, { target: { value: "Password123@" } });

      // Clear password
      fireEvent.change(newPasswordInput, { target: { value: "" } });

      // All requirements should be invalid
      expect(screen.getByText("At least 8 character")).toHaveClass(
        "text-[var(--grey-5)]"
      );
      expect(screen.getByText("1 Lowercase letter")).toHaveClass(
        "text-[var(--grey-5)]"
      );
      expect(screen.getByText("1 Upper case letter")).toHaveClass(
        "text-[var(--grey-5)]"
      );
      expect(screen.getByText("1 A symbol (@&%$)")).toHaveClass(
        "text-[var(--grey-5)]"
      );
    });

    it("handles partial requirement fulfillment", () => {
      render(<CreatePasswordPage />);

      const newPasswordInput = screen.getByTestId("new-password");

      // Only length and lowercase
      fireEvent.change(newPasswordInput, { target: { value: "password" } });

      expect(screen.getByText("At least 8 character")).toHaveClass(
        "text-[var(--grey-7)]"
      );
      expect(screen.getByText("1 Lowercase letter")).toHaveClass(
        "text-[var(--grey-7)]"
      );
      expect(screen.getByText("1 Upper case letter")).toHaveClass(
        "text-[var(--grey-5)]"
      );
      expect(screen.getByText("1 A symbol (@&%$)")).toHaveClass(
        "text-[var(--grey-5)]"
      );
    });
  });
});
