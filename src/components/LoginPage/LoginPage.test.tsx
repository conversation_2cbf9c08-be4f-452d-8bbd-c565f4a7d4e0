/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";

// Mock Next.js Image component
jest.mock("next/image", () => {
  const MockImage = ({ src, alt, className, width, height }: any) => (
    <Image
      src={src}
      alt={alt}
      className={className}
      width={width}
      height={height}
      data-testid="mock-image"
    />
  );
  MockImage.displayName = "MockImage";
  return MockImage;
});

// Mock sub-components
jest.mock("../shared/Header/Header", () => {
  interface HeaderProps {
    state: string;
    onClick?: () => void;
  }
  const MockHeader = ({ onClick }: HeaderProps) => (
    <div data-testid="header" onClick={onClick}>
      Header
    </div>
  );
  MockHeader.displayName = "MockHeader";
  return {
    __esModule: true,
    default: MockHeader,
    HeaderState: { HELP: "HELP" },
  };
});

jest.mock("../shared/Footer/Footer", () => {
  const MockFooter = () => <div data-testid="footer">Footer</div>;
  MockFooter.displayName = "MockFooter";
  return {
    __esModule: true,
    default: MockFooter,
  };
});

jest.mock("../shared/Button/Button", () => {
  interface ButtonProps {
    type: string;
    text: string;
    onClick?: () => void;
  }
  const MockButton = ({ text, onClick }: ButtonProps) => (
    <button
      onClick={onClick}
      data-testid={`button-${text.toLowerCase().replace(/\s+/g, "-")}`}
    >
      {text}
    </button>
  );
  MockButton.displayName = "MockButton";
  return {
    __esModule: true,
    default: MockButton,
    ButtonType: { PRIMARY: "primary", SECONDARY: "secondary" },
  };
});

jest.mock("../shared/Input/Input", () => {
  interface InputProps {
    type: string;
    label: string;
    value: string;
    onChange: (value: string) => void;
    placeholder: string;
  }
  const MockInput = ({ label, value, onChange, placeholder }: InputProps) => (
    <div data-testid="input-wrapper">
      <label>{label}</label>
      <input
        data-testid={`input-${label.toLowerCase().replace(/\s+/g, "-")}`}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
      />
    </div>
  );
  MockInput.displayName = "MockInput";
  return {
    __esModule: true,
    default: MockInput,
  };
});

jest.mock("../shared/PasswordInput", () => {
  interface PasswordInputProps {
    id: string;
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    placeholder: string;
  }
  const MockPasswordInput = ({
    id,
    value,
    onChange,
    placeholder,
  }: PasswordInputProps) => (
    <input
      id={id}
      type="password"
      data-testid="password-input"
      value={value}
      onChange={onChange}
      placeholder={placeholder}
    />
  );
  MockPasswordInput.displayName = "MockPasswordInput";
  return {
    __esModule: true,
    default: MockPasswordInput,
  };
});

// Mock lucide-react
jest.mock("lucide-react", () => ({
  Mail: ({ size }: { size: number }) => (
    <div data-testid="mail-icon" style={{ width: size, height: size }}>
      Mail
    </div>
  ),
}));

import LoginPage from "./LoginPage";
import Image from "next/image";

describe("LoginPage", () => {
  const defaultProps = {
    onLogin: jest.fn(),
    onLoginWithOTP: jest.fn(),
    onContinueWithGoogle: jest.fn(),
    onContinueWithEmail: jest.fn(),
    onForgotPassword: jest.fn(),
    onSignUp: jest.fn(),
    onGetHelp: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Component Rendering", () => {
    it("renders all main components", () => {
      render(<LoginPage {...defaultProps} />);

      expect(screen.getByTestId("header")).toBeInTheDocument();
      expect(screen.getByTestId("footer")).toBeInTheDocument();
      expect(screen.getByText("Welcome Back")).toBeInTheDocument();
      expect(screen.getByTestId("mock-image")).toBeInTheDocument();
    });

    it("renders the welcome message", () => {
      render(<LoginPage {...defaultProps} />);

      expect(screen.getByText("Welcome Back")).toBeInTheDocument();
      expect(
        screen.getByText(/Enter your credentials to log in and access your/)
      ).toBeInTheDocument();
      expect(
        screen.getByText(/personalized IVF dashboard./)
      ).toBeInTheDocument();
    });

    it("renders form elements", () => {
      render(<LoginPage {...defaultProps} />);

      expect(screen.getByTestId("input-email-or-number")).toBeInTheDocument();
      expect(screen.getByTestId("password-input")).toBeInTheDocument();
      expect(screen.getByRole("checkbox")).toBeInTheDocument();
      expect(screen.getByText("Remember me")).toBeInTheDocument();
      expect(screen.getByText("Forgot Password?")).toBeInTheDocument();
    });

    it("renders login buttons", () => {
      render(<LoginPage {...defaultProps} />);

      expect(screen.getByTestId("button-login")).toBeInTheDocument();
      expect(screen.getByTestId("button-login-with-otp")).toBeInTheDocument();
    });

    it("renders social login buttons", () => {
      render(<LoginPage {...defaultProps} />);

      expect(screen.getByText("Continue with Google")).toBeInTheDocument();
      expect(screen.getByText("Continue with Email")).toBeInTheDocument();
      expect(screen.getByTestId("mail-icon")).toBeInTheDocument();
    });

    it("renders sign up text", () => {
      render(<LoginPage {...defaultProps} />);

      expect(screen.getByText("Don't have an account?")).toBeInTheDocument();
    });
  });

  describe("Form Interactions", () => {
    it("updates email when input changes", () => {
      render(<LoginPage {...defaultProps} />);

      const emailInput = screen.getByTestId("input-email-or-number");
      fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });

      expect(emailInput).toHaveValue("<EMAIL>");
    });

    it("updates password when input changes", () => {
      render(<LoginPage {...defaultProps} />);

      const passwordInput = screen.getByTestId("password-input");
      fireEvent.change(passwordInput, { target: { value: "newpassword123" } });

      expect(passwordInput).toHaveValue("newpassword123");
    });

    it("toggles remember me checkbox", () => {
      render(<LoginPage {...defaultProps} />);

      const checkbox = screen.getByRole("checkbox");
      expect(checkbox).toBeChecked(); // Initially checked

      fireEvent.click(checkbox);
      expect(checkbox).not.toBeChecked();

      fireEvent.click(checkbox);
      expect(checkbox).toBeChecked();
    });

    it("has default email and password values", () => {
      render(<LoginPage {...defaultProps} />);

      const emailInput = screen.getByTestId("input-email-or-number");
      const passwordInput = screen.getByTestId("password-input");

      expect(emailInput).toHaveValue("<EMAIL>");
      expect(passwordInput).toHaveValue("password123");
    });
  });

  describe("Form Submission", () => {
    it("calls onLogin when login button is clicked", () => {
      render(<LoginPage {...defaultProps} />);

      const loginButton = screen.getByTestId("button-login");
      fireEvent.click(loginButton);

      expect(defaultProps.onLogin).toHaveBeenCalledWith(
        "<EMAIL>",
        "password123"
      );
    });

    it("calls onLogin when form is submitted", () => {
      render(<LoginPage {...defaultProps} />);

      const form = screen.getByTestId("input-email-or-number").closest("form");
      fireEvent.submit(form!);

      expect(defaultProps.onLogin).toHaveBeenCalledWith(
        "<EMAIL>",
        "password123"
      );
    });

    it("calls onLogin with updated values", () => {
      render(<LoginPage {...defaultProps} />);

      const emailInput = screen.getByTestId("input-email-or-number");
      const passwordInput = screen.getByTestId("password-input");

      fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
      fireEvent.change(passwordInput, { target: { value: "newpass123" } });

      const loginButton = screen.getByTestId("button-login");
      fireEvent.click(loginButton);

      expect(defaultProps.onLogin).toHaveBeenCalledWith(
        "<EMAIL>",
        "newpass123"
      );
    });

    it("prevents default form submission", () => {
      render(<LoginPage {...defaultProps} />);

      const form = screen.getByTestId("input-email-or-number").closest("form");
      const submitEvent = new Event("submit", {
        bubbles: true,
        cancelable: true,
      });
      const preventDefaultSpy = jest.spyOn(submitEvent, "preventDefault");

      form!.dispatchEvent(submitEvent);

      expect(preventDefaultSpy).toHaveBeenCalled();
    });
  });

  describe("Button Interactions", () => {
    it("calls onLoginWithOTP when OTP button is clicked", () => {
      render(<LoginPage {...defaultProps} />);

      const otpButton = screen.getByTestId("button-login-with-otp");
      fireEvent.click(otpButton);

      expect(defaultProps.onLoginWithOTP).toHaveBeenCalledTimes(1);
    });

    it("calls onForgotPassword when forgot password link is clicked", () => {
      render(<LoginPage {...defaultProps} />);

      const forgotPasswordLink = screen.getByText("Forgot Password?");
      fireEvent.click(forgotPasswordLink);

      expect(defaultProps.onForgotPassword).toHaveBeenCalledTimes(1);
    });

    it("calls onContinueWithGoogle when Google button is clicked", () => {
      render(<LoginPage {...defaultProps} />);

      const googleButton = screen.getByText("Continue with Google");
      fireEvent.click(googleButton);

      expect(defaultProps.onContinueWithGoogle).toHaveBeenCalledTimes(1);
    });

    it("calls onContinueWithEmail when Email button is clicked", () => {
      render(<LoginPage {...defaultProps} />);

      const emailButton = screen.getByText("Continue with Email");
      fireEvent.click(emailButton);

      expect(defaultProps.onContinueWithEmail).toHaveBeenCalledTimes(1);
    });

    it("calls onGetHelp when header is clicked", () => {
      render(<LoginPage {...defaultProps} />);

      const header = screen.getByTestId("header");
      fireEvent.click(header);

      expect(defaultProps.onGetHelp).toHaveBeenCalledTimes(1);
    });
  });

  describe("Edge Cases", () => {
    it("handles missing callback props gracefully", () => {
      const propsWithoutCallbacks = {};

      expect(() => {
        render(<LoginPage {...propsWithoutCallbacks} />);
      }).not.toThrow();
    });

    it("handles undefined onLogin callback", () => {
      const propsWithoutLogin = { ...defaultProps, onLogin: undefined };

      render(<LoginPage {...propsWithoutLogin} />);

      const loginButton = screen.getByTestId("button-login");
      expect(() => {
        fireEvent.click(loginButton);
      }).not.toThrow();
    });

    it("handles undefined onLoginWithOTP callback", () => {
      const propsWithoutOTP = { ...defaultProps, onLoginWithOTP: undefined };

      render(<LoginPage {...propsWithoutOTP} />);

      const otpButton = screen.getByTestId("button-login-with-otp");
      expect(() => {
        fireEvent.click(otpButton);
      }).not.toThrow();
    });

    it("handles undefined onForgotPassword callback", () => {
      const propsWithoutForgot = {
        ...defaultProps,
        onForgotPassword: undefined,
      };

      render(<LoginPage {...propsWithoutForgot} />);

      const forgotLink = screen.getByText("Forgot Password?");
      expect(() => {
        fireEvent.click(forgotLink);
      }).not.toThrow();
    });

    it("renders Google SVG icon correctly", () => {
      render(<LoginPage {...defaultProps} />);

      const googleButton = screen.getByText("Continue with Google");
      const svgIcon = googleButton.querySelector("svg");

      expect(svgIcon).toBeInTheDocument();
      expect(svgIcon).toHaveClass("w-5", "h-5");
    });
  });

  describe("Styling and Classes", () => {
    it("applies correct classes to remember me checkbox", () => {
      render(<LoginPage {...defaultProps} />);

      const checkbox = screen.getByRole("checkbox");
      expect(checkbox).toHaveClass("w-4", "h-4");
    });

    it("applies hover effects to social buttons", () => {
      render(<LoginPage {...defaultProps} />);

      const googleButton = screen.getByText("Continue with Google");
      expect(googleButton).toHaveClass("hover:bg-gray-50");

      const emailButton = screen.getByText("Continue with Email");
      expect(emailButton).toHaveClass("hover:bg-gray-50");
    });

    it("applies correct styling to forgot password link", () => {
      render(<LoginPage {...defaultProps} />);

      const forgotLink = screen.getByText("Forgot Password?");
      expect(forgotLink).toHaveClass("underline", "text-[var(--red-6)]");
    });
  });

  describe("Accessibility", () => {
    it("has proper labels for form inputs", () => {
      render(<LoginPage {...defaultProps} />);

      expect(screen.getByText("Email or Number")).toBeInTheDocument();
      expect(screen.getByText("Password")).toBeInTheDocument();
      expect(screen.getByText("Remember me")).toBeInTheDocument();
    });

    it("has proper button roles", () => {
      render(<LoginPage {...defaultProps} />);

      const buttons = screen.getAllByRole("button");
      expect(buttons).toHaveLength(5); // Forgot Password, Login, OTP, Google, Email
    });

    it("has proper checkbox role", () => {
      render(<LoginPage {...defaultProps} />);

      const checkbox = screen.getByRole("checkbox");
      expect(checkbox).toBeInTheDocument();
      expect(checkbox).toHaveAttribute("id", "remember-me");
    });

    it("associates labels with inputs correctly", () => {
      render(<LoginPage {...defaultProps} />);

      const passwordInput = screen.getByTestId("password-input");
      expect(passwordInput).toHaveAttribute("id", "password");

      const passwordLabel = screen.getByText("Password");
      expect(passwordLabel).toHaveAttribute("for", "password");
    });
  });

  describe("State Management", () => {
    it("maintains independent state for email and password", () => {
      render(<LoginPage {...defaultProps} />);

      const emailInput = screen.getByTestId("input-email-or-number");
      const passwordInput = screen.getByTestId("password-input");

      fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
      fireEvent.change(passwordInput, { target: { value: "pass123" } });

      expect(emailInput).toHaveValue("<EMAIL>");
      expect(passwordInput).toHaveValue("pass123");

      fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });

      expect(emailInput).toHaveValue("<EMAIL>");
      expect(passwordInput).toHaveValue("pass123"); // Should remain unchanged
    });

    it("maintains remember me state independently", () => {
      render(<LoginPage {...defaultProps} />);

      const checkbox = screen.getByRole("checkbox");
      const emailInput = screen.getByTestId("input-email-or-number");

      fireEvent.click(checkbox);
      fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });

      expect(checkbox).not.toBeChecked();
      expect(emailInput).toHaveValue("<EMAIL>");
    });
  });
});
