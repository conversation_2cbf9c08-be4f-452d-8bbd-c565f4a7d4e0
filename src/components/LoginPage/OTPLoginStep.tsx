import React, { useState } from "react";
import OTPInput from "../shared/OTPInput/OTPInput";
import Button, { ButtonType } from "../shared/Button/Button";
import { useCountdown } from "@/hooks/useCountdown";
import { createClient } from "@/utils/supabase/client";
import { useToast } from "@/contexts/ToastContext";
import PageHeader from "../shared/PageHeader";

interface OTPLoginStepProps {
  email: string;
  onBack: () => void;
  onVerifyOTP: (email: string, otp: string) => Promise<void>;
  onLoginClick: () => void;
}

const OTPLoginStep: React.FC<OTPLoginStepProps> = ({
  email,
  onVerifyOTP,
  onLoginClick,
}) => {
  const [otp, setOtp] = useState("");
  const [isResending, setIsResending] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [remainingAttempts, setRemainingAttempts] = useState<
    number | undefined
  >(undefined);
  const countdown = useCountdown(120); // 2 minutes countdown
  const toast = useToast();
  const supabase = createClient();

  const handleOTPChange = (otpValue: string) => {
    setOtp(otpValue);
  };

  const handleOTPComplete = async (otpValue: string) => {
    setOtp(otpValue);
    // Auto-verify when OTP is complete (6 digits entered)
    if (otpValue.length === 6) {
      setIsLoading(true);
      setErrorMessage(null);
      try {
        await onVerifyOTP(email, otpValue);
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
      } catch (error: any) {
        console.error("OTP verification error:", error);

        // Handle different error types and extract remaining attempts if available
        let errorMsg = "Failed to verify OTP. Please try again.";
        let attempts = undefined;

        if (error?.message) {
          errorMsg = error.message;
        }

        // Check if error contains remaining attempts info
        if (error?.remainingAttempts !== undefined) {
          attempts = error.remainingAttempts;
        } else if (error?.message?.includes("attempt")) {
          // Try to extract attempts from error message
          const attemptMatch = error.message.match(/(\d+)\s+attempt/i);
          if (attemptMatch) {
            attempts = parseInt(attemptMatch[1]);
          }
        }

        setErrorMessage(errorMsg);
        setRemainingAttempts(attempts);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleVerifyOTP = async () => {
    if (otp.length !== 6) return;

    setIsLoading(true);
    setErrorMessage(null);
    try {
      await onVerifyOTP(email, otp);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      console.error("OTP verification error:", error);

      // Handle different error types and extract remaining attempts if available
      let errorMsg = "Failed to verify OTP. Please try again.";
      let attempts = undefined;

      if (error?.message) {
        errorMsg = error.message;
      }

      // Check if error contains remaining attempts info
      if (error?.remainingAttempts !== undefined) {
        attempts = error.remainingAttempts;
      } else if (error?.message?.includes("attempt")) {
        // Try to extract attempts from error message
        const attemptMatch = error.message.match(/(\d+)\s+attempt/i);
        if (attemptMatch) {
          attempts = parseInt(attemptMatch[1]);
        }
      }

      setErrorMessage(errorMsg);
      setRemainingAttempts(attempts);
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendCode = async () => {
    setIsResending(true);
    try {
      const { error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          shouldCreateUser: false,
        },
      });

      if (error) {
        setErrorMessage(error.message);
      } else {
        toast.success("Verification code resent successfully!");
        setShowSuccessMessage(true);
        setTimeout(() => setShowSuccessMessage(false), 5000);
        // Reset countdown when code is resent successfully
        countdown.reset(120);
        // Clear OTP input
        setOtp("");
      }
    } catch (error) {
      console.error("Resend OTP error:", error);
      setErrorMessage("Failed to resend verification code. Please try again.");
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="w-[20.063rem] md:w-[26.5rem] flex flex-col justify-center gap-8">
      {/* Title */}
      <div className="text-center flex flex-col">
        <PageHeader title="Enter Verification Code" />
        <p className="text-[var(--grey-6)] text-base font-medium">
          We have sent a 6-digit code to
          <br />
          <span className="font-semibold">{email}</span>
        </p>
      </div>

      {/* OTP Input and Verify Button */}
      <div className="space-y-6">
        <OTPInput
          length={6}
          onChange={handleOTPChange}
          onComplete={handleOTPComplete}
          disabled={isLoading}
          error={!!errorMessage}
          autoFocus
        />

        {/* Verify Button */}
        <div className="pt-2">
          <Button
            type={ButtonType.PRIMARY}
            text={isLoading ? "Verifying..." : "Verify Code"}
            onClick={handleVerifyOTP}
            disabled={isLoading || otp.length !== 6}
          />
        </div>

        {/* Success Message */}
        {showSuccessMessage && (
          <div className="text-[var(--green-6)] text-sm text-center bg-green-50 border border-green-200 rounded-sm p-3">
            Verification code sent successfully!
          </div>
        )}

        {/* Error Message */}
        {errorMessage && (
          <div className="text-[var(--red-6)] text-sm text-center bg-red-50 border border-red-200 rounded-sm p-3">
            {errorMessage}
            {remainingAttempts !== undefined && remainingAttempts > 0 && (
              <div className="mt-1">
                {remainingAttempts} attempt
                {remainingAttempts !== 1 ? "s" : ""} remaining
              </div>
            )}
          </div>
        )}

        {/* Resend Code Link with Countdown Timer */}
        <div className="flex items-center justify-center gap-1">
          <button
            type="button"
            onClick={handleResendCode}
            disabled={isResending || !countdown.isExpired}
            className="cursor-pointer text-[var(--grey-5)] text-base hover:text-[var(--grey-6)] transition-colors duration-200 underline disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isResending ? "Resending..." : "Resend Code"}
          </button>

          {/* Countdown Timer Display */}
          {!countdown.isExpired && (
            <p className="text-[var(--grey-5)] text-base">
              in{" "}
              <span className="text-[var(--violet-11)] text-base font-bold">
                ({countdown.formattedTime})
              </span>
            </p>
          )}
        </div>

        <div className="flex items-center mt-8 mb-2">
          <div className="flex-grow h-px bg-gray-200" />
          <span className="mx-3 text-gray-400 text-base font-medium">OR</span>
          <div className="flex-grow h-px bg-gray-200" />
        </div>
        <p className="text-[var(--grey-6)] text-base font-medium text-center pt-4">
          Continue with Password ?{" "}
          <button
            type="button"
            onClick={onLoginClick}
            className="cursor-pointer text-[var(--red-6)] hover:text-[var(--red-7)] underline"
          >
            Login
          </button>
        </p>
      </div>
    </div>
  );
};

export default OTPLoginStep;
