import React, { useState } from "react";
import Button, { ButtonType } from "../shared/Button/Button";
import Input from "../shared/Input/Input";
import { createClient } from "@/utils/supabase/client";
import { useToast } from "@/contexts/ToastContext";
import { isValidEmail } from "@/lib/utils/inputValidations";
import PageHeader from "../shared/PageHeader";

interface OTPEmailStepProps {
  onBack: () => void;
  onEmailSent: (email: string) => void;
  onLoginClick: () => void;
}

const OTPEmailStep: React.FC<OTPEmailStepProps> = ({
  onEmailSent,
  onLoginClick,
}) => {
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const toast = useToast();
  const supabase = createClient();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email.trim()) {
      setErrorMessage("Email is required");
      return;
    }

    if (!isValidEmail(email)) {
      setErrorMessage("Please enter a valid email address");
      return;
    }

    setIsLoading(true);
    setErrorMessage(null);

    try {
      const { error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          shouldCreateUser: false,
        },
      });

      if (error) {
        setErrorMessage(error.message);
      } else {
        toast.success("Verification code sent to your email!");
        onEmailSent(email);
      }
    } catch (error) {
      console.error("OTP sending error:", error);
      setErrorMessage("Failed to send verification code. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <main className="flex-1 flex items-center justify-center px-6 py-16">
      <div className="w-full flex justify-center">
        <div className="w-[20.063rem] md:w-[26.5rem] flex flex-col justify-center gap-12">
          {/* Title */}
          <div className="text-center flex flex-col">
            <PageHeader title="Login with OTP" />

            <p className="text-[var(--grey-6)] text-base font-medium">
              Enter your email address to receive a
              <br />
              verification code for secure login.
            </p>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit}>
            <div className="space-y-6">
              {/* Email Field */}
              <div className="flex flex-col gap-2">
                <Input
                  id="email"
                  type="email"
                  label="Email Address"
                  value={email}
                  onChange={setEmail}
                  placeholder="Enter your email address"
                  maxLength={50}
                  required
                />
              </div>

              {/* Error Message */}
              {errorMessage && (
                <div className="text-[var(--red-6)] text-sm text-center bg-red-50 border border-red-200 rounded-sm p-3">
                  {errorMessage}
                </div>
              )}

              {/* Login with OTP Button */}
              <div className="pt-2">
                <Button
                  type={ButtonType.PRIMARY}
                  text={isLoading ? "Sending..." : "Login with OTP"}
                  onClick={() => {
                    if (email.trim()) {
                      handleSubmit({
                        preventDefault: () => {},
                      } as React.FormEvent);
                    }
                  }}
                  disabled={isLoading || !email.trim() || !isValidEmail(email)}
                />
              </div>
              <div className="flex items-center mt-8 mb-2">
                <div className="flex-grow h-px bg-gray-200" />
                <span className="mx-3 text-gray-400 text-base font-medium">
                  OR
                </span>
                <div className="flex-grow h-px bg-gray-200" />
              </div>
              <p className="text-[var(--grey-6)] text-base font-medium text-center pt-4">
                Continue with Password ?{" "}
                <button
                  type="button"
                  onClick={onLoginClick}
                  className="cursor-pointer text-[var(--red-6)] hover:text-[var(--red-7)] underline"
                >
                  Login
                </button>
              </p>
            </div>
          </form>
        </div>
      </div>
    </main>
  );
};

export default OTPEmailStep;
