import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import CircularProgress from "./CircularProgress";

const meta: Meta<typeof CircularProgress> = {
  title: "Components/Pages/CircularProgress",
  component: CircularProgress,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "An animated semi-circular progress indicator that displays fertility assessment scores with visual feedback.",
      },
    },
  },
  argTypes: {
    percentage: {
      control: { type: "range", min: 0, max: 100, step: 1 },
      description: "Progress percentage (0-100)",
    },
    score: {
      control: "text",
      description: "Score text to display in the center",
    },
    band: {
      control: "text",
      description: "Score band/category name",
    },
    bandStyles: {
      control: "object",
      description: "Styling configuration for the band",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Default story
export const Default: Story = {
  args: {
    percentage: 75,
    score: "7.5",
    band: "High",
    bandStyles: {
      dotColor: "#4ADE80",
      bgColor: "#DCFCE7",
      textColor: "#166534",
    },
  },
};

// Low score
export const LowScore: Story = {
  args: {
    percentage: 25,
    score: "2.5",
    band: "Low",
    bandStyles: {
      dotColor: "#EF4444",
      bgColor: "#FEE2E2",
      textColor: "#991B1B",
    },
  },
};

// Medium score
export const MediumScore: Story = {
  args: {
    percentage: 50,
    score: "5.0",
    band: "Medium",
    bandStyles: {
      dotColor: "#F59E0B",
      bgColor: "#FEF3C7",
      textColor: "#92400E",
    },
  },
};

// High score
export const HighScore: Story = {
  args: {
    percentage: 90,
    score: "9.0",
    band: "High",
    bandStyles: {
      dotColor: "#10B981",
      bgColor: "#D1FAE5",
      textColor: "#047857",
    },
  },
};

// Perfect score
export const PerfectScore: Story = {
  args: {
    percentage: 100,
    score: "10",
    band: "Excellent",
    bandStyles: {
      dotColor: "#8B5CF6",
      bgColor: "#EDE9FE",
      textColor: "#5B21B6",
    },
  },
};

// Zero score
export const ZeroScore: Story = {
  args: {
    percentage: 0,
    score: "0.0",
    band: "Very Low",
    bandStyles: {
      dotColor: "#DC2626",
      bgColor: "#FEE2E2",
      textColor: "#991B1B",
    },
  },
};

// Decimal score
export const DecimalScore: Story = {
  args: {
    percentage: 67,
    score: "6.7",
    band: "Good",
    bandStyles: {
      dotColor: "#059669",
      bgColor: "#ECFDF5",
      textColor: "#065F46",
    },
  },
};

// Custom styling
export const CustomStyling: Story = {
  args: {
    percentage: 80,
    score: "8.0",
    band: "Excellent",
    bandStyles: {
      dotColor: "#7C3AED",
      bgColor: "#F3E8FF",
      textColor: "#581C87",
    },
  },
  parameters: {
    docs: {
      description: {
        story: "CircularProgress with custom purple theme styling.",
      },
    },
  },
};

// Animation demo (resets on re-render)
export const AnimationDemo: Story = {
  args: {
    percentage: 85,
    score: "8.5",
    band: "High",
    bandStyles: {
      dotColor: "#10B981",
      bgColor: "#D1FAE5",
      textColor: "#047857",
    },
  },
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates the animation effect. The segments fill up progressively when the component mounts.",
      },
    },
  },
};

// Interactive playground
export const Interactive: Story = {
  args: {
    percentage: 60,
    score: "6.0",
    band: "Medium",
    bandStyles: {
      dotColor: "#F59E0B",
      bgColor: "#FEF3C7",
      textColor: "#92400E",
    },
  },
  parameters: {
    docs: {
      description: {
        story:
          "Interactive version where you can adjust all properties using the controls below.",
      },
    },
  },
};

// Edge cases
export const EdgeCaseVeryLowPercentage: Story = {
  args: {
    percentage: 1,
    score: "0.1",
    band: "Critical",
    bandStyles: {
      dotColor: "#DC2626",
      bgColor: "#FEE2E2",
      textColor: "#991B1B",
    },
  },
  parameters: {
    docs: {
      description: {
        story: "Edge case with very low percentage (1%).",
      },
    },
  },
};

export const EdgeCaseNearPerfect: Story = {
  args: {
    percentage: 99,
    score: "9.9",
    band: "Nearly Perfect",
    bandStyles: {
      dotColor: "#8B5CF6",
      bgColor: "#EDE9FE",
      textColor: "#5B21B6",
    },
  },
  parameters: {
    docs: {
      description: {
        story: "Edge case with near-perfect percentage (99%).",
      },
    },
  },
};

// Accessibility focused
export const AccessibilityFocus: Story = {
  args: {
    percentage: 70,
    score: "7.0",
    band: "Good",
    bandStyles: {
      dotColor: "#059669",
      bgColor: "#ECFDF5",
      textColor: "#065F46",
    },
  },
  parameters: {
    docs: {
      description: {
        story:
          "Focus on accessibility with proper contrast ratios and semantic structure.",
      },
    },
  },
};
