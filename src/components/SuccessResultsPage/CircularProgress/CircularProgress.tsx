import React, { useEffect, useState } from "react";

// Circular Progress Component
interface CircularProgressProps {
  percentage: number;
  score: string;
  band: string;
  bandStyles: {
    dotColor: string;
    bgColor: string;
    textColor: string;
  };
  className?: string;
}

const CircularProgress: React.FC<CircularProgressProps> = ({
  percentage,
  score,
  band,
  bandStyles,
}) => {
  const containerSize = 285.5;
  const radius = 130;
  const totalSegments = 17;
  const filledSegments = Math.round((percentage / 100) * totalSegments);

  // Animation state
  const [animatedFilledSegments, setAnimatedFilledSegments] = useState(0);

  useEffect(() => {
    setAnimatedFilledSegments(0);
    if (filledSegments === 0) return;
    let current = 0;
    const interval = setInterval(() => {
      current++;
      setAnimatedFilledSegments(current);
      if (current >= filledSegments) {
        clearInterval(interval);
      }
    }, 80); // Adjust speed as needed
    return () => clearInterval(interval);
  }, [filledSegments, percentage]);

  return (
    <div className="flex flex-col items-center mb-8">
      <div
        className="relative flex items-center justify-center"
        style={{ width: containerSize, height: containerSize / 2 + 60 }}
      >
        {/* Render segments in semi-circle */}
        {Array.from({ length: totalSegments }, (_, i) => {
          // Semi-circle from -π to 0 (180° to 0°)
          const angle = Math.PI + (i / (totalSegments - 1)) * Math.PI;
          const isFilled = i < animatedFilledSegments;
          const segmentColor = isFilled
            ? "var(--success-green-3)"
            : "var(--violet-1)";

          const x = Math.cos(angle) * radius;
          const y = Math.sin(angle) * radius;

          return (
            <div
              key={i}
              className="absolute"
              style={{
                width: "16px",
                height: "38.5px",
                backgroundColor: segmentColor,
                borderRadius: "10px",
                transform: `translate(-50%, -50%) translate(${x}px, ${y + 30}px) rotate(${angle + Math.PI / 2}rad)`,
                left: "50%",
                top: "50%",
                transition: isFilled ? "background-color 0.2s" : undefined,
              }}
            />
          );
        })}

        {/* Center content */}
        <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2">
          <span className="text-4xl font-bold text-[var(--grey-7)]">
            {score}
          </span>
        </div>

        {/* Labels */}
        <div className="absolute left-0 bottom-8">
          <span className="text-sm text-[var(--grey-5)]">Low</span>
        </div>
        <div className="absolute right-0 bottom-8">
          <span className="text-sm text-[var(--grey-5)]">High</span>
        </div>

        {/* Band indicator positioned above the end point of filled segments */}
        {animatedFilledSegments > 0 &&
          (() => {
            // Calculate position of the last filled segment
            const lastFilledIndex = animatedFilledSegments - 1;
            const angle = Math.PI + (lastFilledIndex / totalSegments) * Math.PI;
            const indicatorX = Math.cos(angle) * (radius + 35); // Slightly further out than segments
            const indicatorY = Math.sin(angle) * (radius + 35) - 15; // Above the segment

            return (
              <div
                className="absolute border-1 border-[var(--grey-2)] px-2 py-1.5 rounded-[6px] bg-white shadow-[#A2A4B640] shadow-sm"
                style={{
                  left: "50%",
                  top: "50%",
                  transform: `translate(-50%, -50%) translate(${indicatorX}px, ${indicatorY + 30}px)`,
                }}
              >
                <span className="text-sm text-[var(--grey-6)] font-medium flex items-center gap-1">
                  <div
                    className="w-2 h-2 rounded-full"
                    style={{ backgroundColor: bandStyles.dotColor }}
                  ></div>
                  {band}
                </span>
              </div>
            );
          })()}
      </div>
    </div>
  );
};

export default CircularProgress;
