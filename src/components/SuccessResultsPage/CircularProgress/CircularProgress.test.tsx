import React from "react";
import { render, screen, waitFor, act } from "@testing-library/react";
import CircularProgress from "./CircularProgress";

// Mock timers for animation testing
jest.useFakeTimers();

describe("CircularProgress", () => {
  const defaultProps = {
    percentage: 50,
    score: "5.0",
    band: "Medium",
    bandStyles: {
      dotColor: "#F59E0B",
      bgColor: "#FEF3C7",
      textColor: "#92400E",
    },
  };

  beforeEach(() => {
    jest.clearAllTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  describe("Rendering", () => {
    it("renders with correct props", () => {
      render(<CircularProgress {...defaultProps} />);

      expect(screen.getByText("5.0")).toBeInTheDocument();
      expect(screen.getByText("Low")).toBeInTheDocument();
      expect(screen.getByText("High")).toBeInTheDocument();
    });

    it("renders band indicator after animation", async () => {
      render(<CircularProgress {...defaultProps} />);

      // Initially no band indicator
      expect(screen.queryByText("Medium")).not.toBeInTheDocument();

      // Let animation complete
      act(() => {
        jest.advanceTimersByTime(2000);
      });

      // Band indicator should appear
      await waitFor(() => {
        expect(screen.getByText("Medium")).toBeInTheDocument();
      });
    });

    it("renders score in center", () => {
      render(<CircularProgress {...defaultProps} />);

      const scoreElement = screen.getByText("5.0");
      expect(scoreElement).toHaveClass("text-4xl", "font-bold");
    });

    it("renders low and high labels", () => {
      render(<CircularProgress {...defaultProps} />);

      expect(screen.getByText("Low")).toBeInTheDocument();
      expect(screen.getByText("High")).toBeInTheDocument();
    });
  });

  describe("Animation", () => {
    it("animates segments on mount", async () => {
      render(<CircularProgress {...defaultProps} />);

      // Initially no segments should be filled
      const segments = document.querySelectorAll(
        '[style*="background-color: var(--success-green-3)"]'
      );
      expect(segments).toHaveLength(0);

      // Advance timers to trigger animation
      act(() => {
        jest.advanceTimersByTime(1000);
      });

      // After animation, some segments should be filled
      await waitFor(() => {
        const filledSegments = document.querySelectorAll(
          '[style*="background-color: var(--success-green-3)"]'
        );
        expect(filledSegments.length).toBeGreaterThan(0);
      });
    });

    it("animates when percentage changes", async () => {
      const { rerender } = render(<CircularProgress {...defaultProps} />);

      // Let initial animation complete
      act(() => {
        jest.advanceTimersByTime(2000);
      });

      // Change percentage
      rerender(<CircularProgress {...defaultProps} percentage={80} />);

      // Should trigger new animation
      await waitFor(() => {
        const filledSegments = document.querySelectorAll(
          '[style*="background-color: var(--success-green-3)"]'
        );
        expect(filledSegments.length).toBeGreaterThan(0);
      });
    });
  });

  describe("Props", () => {
    it("displays correct score", () => {
      render(<CircularProgress {...defaultProps} score="8.5" />);
      expect(screen.getByText("8.5")).toBeInTheDocument();
    });

    it("displays correct band after animation", async () => {
      render(<CircularProgress {...defaultProps} band="High" />);

      act(() => {
        jest.advanceTimersByTime(2000);
      });

      await waitFor(() => {
        const bandIndicator = screen.getByText("High", {
          selector: 'span[class*="text-[var(--grey-6)]"]',
        });
        expect(bandIndicator).toBeInTheDocument();
      });
    });

    it("applies custom band styles", async () => {
      const customStyles = {
        dotColor: "#FF0000",
        bgColor: "#FFF",
        textColor: "#000",
      };

      render(<CircularProgress {...defaultProps} bandStyles={customStyles} />);

      // Let animation complete
      act(() => {
        jest.advanceTimersByTime(2000);
      });

      // Check if the dot has the custom color
      await waitFor(() => {
        const dot = document.querySelector(
          'div[style*="background-color: rgb(255, 0, 0)"]'
        );
        expect(dot).toBeInTheDocument();
      });
    });

    it("handles zero percentage", () => {
      render(<CircularProgress {...defaultProps} percentage={0} score="0" />);

      // Should still render the component
      expect(screen.getByText("0")).toBeInTheDocument();
      expect(screen.getByText("Low")).toBeInTheDocument();
      expect(screen.getByText("High")).toBeInTheDocument();
    });

    it("handles 100 percentage", () => {
      render(
        <CircularProgress {...defaultProps} percentage={100} score="100" />
      );

      // Should render all segments
      expect(screen.getByText("100")).toBeInTheDocument();
    });
  });

  describe("Edge Cases", () => {
    it("handles negative percentage", () => {
      render(
        <CircularProgress {...defaultProps} percentage={-10} score="-10" />
      );

      // Should handle gracefully
      expect(screen.getByText("-10")).toBeInTheDocument();
    });

    it("handles percentage over 100", () => {
      render(
        <CircularProgress {...defaultProps} percentage={150} score="150" />
      );

      // Should cap at 100%
      expect(screen.getByText("150")).toBeInTheDocument();
    });

    it("handles empty band text", () => {
      render(<CircularProgress {...defaultProps} band="" />);

      // Should render without band indicator
      expect(screen.queryByText("Medium")).not.toBeInTheDocument();
    });
  });

  describe("Styling", () => {
    it("has correct container dimensions", () => {
      const { container } = render(<CircularProgress {...defaultProps} />);

      const mainContainer = container.firstChild as HTMLElement;
      expect(mainContainer).toHaveClass(
        "flex",
        "flex-col",
        "items-center",
        "mb-8"
      );
    });

    it("has correct segment styling", () => {
      render(<CircularProgress {...defaultProps} />);

      // Let animation complete
      act(() => {
        jest.advanceTimersByTime(2000);
      });

      const segments = document.querySelectorAll(
        '[style*="border-radius: 10px"]'
      );
      expect(segments.length).toBeGreaterThan(0);
    });

    it("band indicator has correct styling", async () => {
      render(<CircularProgress {...defaultProps} />);

      act(() => {
        jest.advanceTimersByTime(2000);
      });

      await waitFor(() => {
        const bandContainer = screen.getByText("Medium").closest("div");
        expect(bandContainer).toHaveClass("bg-white", "shadow-sm");
      });
    });
  });

  describe("Accessibility", () => {
    it("has proper text content for screen readers", () => {
      render(<CircularProgress {...defaultProps} />);

      expect(screen.getByText("Low")).toBeInTheDocument();
      expect(screen.getByText("High")).toBeInTheDocument();
    });

    it("has proper contrast with band indicator", async () => {
      render(<CircularProgress {...defaultProps} />);

      act(() => {
        jest.advanceTimersByTime(2000);
      });

      await waitFor(() => {
        const bandText = screen.getByText("Medium");
        expect(bandText).toHaveClass("text-[var(--grey-6)]");
      });
    });
  });

  describe("Performance", () => {
    it("cleans up animation timers", () => {
      const { unmount } = render(<CircularProgress {...defaultProps} />);

      // Start animation
      act(() => {
        jest.advanceTimersByTime(500);
      });

      // Unmount should clean up timers
      unmount();

      // No timers should be running
      expect(jest.getTimerCount()).toBe(0);
    });
  });
});
