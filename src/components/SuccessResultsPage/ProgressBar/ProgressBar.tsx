import React, { useEffect, useState } from "react";

// Progress Bar Component for breakdown
interface ProgressBarProps {
  label: string;
  value: number;
  maxValue: number;
  color: string;
  animate?: boolean;
  showInPercentage?: boolean;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  label,
  value,
  maxValue,
  color,
  animate = false,
  showInPercentage = false,
}) => {
  const [animatedValue, setAnimatedValue] = useState(animate ? 0 : value);

  useEffect(() => {
    if (!animate) {
      setAnimatedValue(value);
      return;
    }
    setAnimatedValue(0);
    if (value === 0) return;
    let current = 0;
    const step = value > 0 ? Math.max(1, Math.round(value / 30)) : 1;
    const interval = setInterval(() => {
      current += step;
      if (current >= value) {
        current = value;
        clearInterval(interval);
      }
      setAnimatedValue(current);
    }, 100); // Adjust speed as needed
    return () => clearInterval(interval);
  }, [value, animate]);

  const percentage = (animatedValue / maxValue) * 100;

  return (
    <div className="text-center">
      <h3 className="text-[var(--grey-6)] text-base font-medium mb-3 text-left md:text-center">
        {label}
      </h3>
      <div className="mb-3 text-left md:text-center">
        <span className="text-3xl font-bold text-[var(--grey-7)]">
          {showInPercentage
            ? `${Math.round((animatedValue / maxValue) * 100)}%`
            : animatedValue}
        </span>
        {!showInPercentage && (
          <span className="text-[var(--grey-5)] text-base">/{maxValue}</span>
        )}
      </div>
      <div className="w-full bg-[var(--grey-2)] h-2 rounded">
        <div
          className="h-2 rounded transition-all duration-300"
          style={{
            width: `${percentage}%`,
            backgroundColor: color,
          }}
        />
      </div>
    </div>
  );
};

export default ProgressBar;
