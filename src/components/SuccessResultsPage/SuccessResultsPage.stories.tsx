import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import SuccessResultsPage from "./SuccessResultsPage";
import { AuthProvider } from "@/contexts/AuthContext";
import { QueryProvider } from "@/components/providers/QueryProvider";
import { ToastProvider } from "@/contexts/ToastContext";

// Mock the hooks used by the component

const meta: Meta<typeof SuccessResultsPage> = {
  title: "Pages/SuccessResultsPage",
  component: SuccessResultsPage,
  decorators: [
    (Story) => (
      <QueryProvider>
        <ToastProvider duration={4000} maxToasts={5}>
          <AuthProvider>
            <Story />
          </AuthProvider>
        </ToastProvider>
      </QueryProvider>
    ),
  ],
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component:
          "The SuccessResultsPage displays fertility assessment results with a circular progress meter, breakdown highlights, and action buttons for next steps.",
      },
    },
    mockData: [
      {
        url: "/api/graphql",
        method: "POST",
        status: 200,
        response: {
          data: {
            getIVFScore: {
              percentage: 85.2,
              totalScore: 85.2,
              maxScore: 100,
              category: "High Fertility",
              factors: {
                biological: 8.5,
                lifestyle: 7.8,
                environmental: 9.1,
              },
            },
          },
        },
      },
    ],
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof SuccessResultsPage>;

export const HighFertilityScore: Story = {
  args: {
    onRetakeAssessment: () => console.log("Retake Assessment clicked"),
    onVisitIVFCenter: () => console.log("Visit IVF Center clicked"),
    onShareEmail: () => console.log("Share Email clicked"),
    onHome: () => console.log("Home clicked"),
    onBookConsultation: () => console.log("Book Consultation clicked"),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Shows the results page with a high fertility score (85.2%), demonstrating positive results and encouraging suggestions.",
      },
    },
  },
};

export const ModerateFertilityScore: Story = {
  args: {
    onRetakeAssessment: () => console.log("Retake Assessment clicked"),
    onVisitIVFCenter: () => console.log("Visit IVF Center clicked"),
    onShareEmail: () => console.log("Share Email clicked"),
    onHome: () => console.log("Home clicked"),
    onBookConsultation: () => console.log("Book Consultation clicked"),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Shows the results page with a moderate fertility score (58.3%), with appropriate suggestions for improvement.",
      },
    },
  },
};

export const LowFertilityScore: Story = {
  args: {
    onRetakeAssessment: () => console.log("Retake Assessment clicked"),
    onVisitIVFCenter: () => console.log("Visit IVF Center clicked"),
    onShareEmail: () => console.log("Share Email clicked"),
    onHome: () => console.log("Home clicked"),
    onBookConsultation: () => console.log("Book Consultation clicked"),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Shows the results page with a low fertility score (32.7%), with appropriate concern-based suggestions.",
      },
    },
  },
};

export const LoadingState: Story = {
  args: {
    onRetakeAssessment: () => console.log("Retake Assessment clicked"),
    onVisitIVFCenter: () => console.log("Visit IVF Center clicked"),
    onShareEmail: () => console.log("Share Email clicked"),
    onHome: () => console.log("Home clicked"),
    onBookConsultation: () => console.log("Book Consultation clicked"),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Shows the loading state while fertility results are being calculated.",
      },
    },
  },
};

export const ErrorState: Story = {
  args: {
    onRetakeAssessment: () => console.log("Retake Assessment clicked"),
    onVisitIVFCenter: () => console.log("Visit IVF Center clicked"),
    onShareEmail: () => console.log("Share Email clicked"),
    onHome: () => console.log("Home clicked"),
    onBookConsultation: () => console.log("Book Consultation clicked"),
  },
  parameters: {
    docs: {
      description: {
        story: "Shows the error state when fertility results cannot be loaded.",
      },
    },
  },
};

export const MobileView: Story = {
  args: {
    onRetakeAssessment: () => console.log("Retake Assessment clicked"),
    onVisitIVFCenter: () => console.log("Visit IVF Center clicked"),
    onShareEmail: () => console.log("Share Email clicked"),
    onHome: () => console.log("Home clicked"),
    onBookConsultation: () => console.log("Book Consultation clicked"),
  },
  parameters: {
    viewport: {
      defaultViewport: "mobile1",
    },
    docs: {
      description: {
        story:
          "Mobile responsive view of the results page, showing how the layout adapts to smaller screens.",
      },
    },
  },
};

export const Default: Story = HighFertilityScore;
