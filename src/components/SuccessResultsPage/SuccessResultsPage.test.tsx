/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";

// Mock all the dependencies
jest.mock("@/contexts/AuthContext", () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
  useAuth: jest.fn(),
}));

jest.mock("@/hooks/useScoreResults", () => ({
  useScoreResults: jest.fn(),
}));

jest.mock("@/components/providers/QueryProvider", () => ({
  QueryProvider: ({ children }: { children: React.ReactNode }) => (
    <div>{children}</div>
  ),
}));

jest.mock("../Shimmer/shimmer.css", () => ({}));

// Mock shared components
jest.mock("../shared/Header/Header", () => {
  const MockHeader = () => <div data-testid="header">Header</div>;
  MockHeader.displayName = "MockHeader";
  return {
    __esModule: true,
    default: MockHeader,
    HeaderState: { LOGIN: "LOGIN" },
  };
});

jest.mock("../shared/Footer/Footer", () => {
  const MockFooter = () => <div data-testid="footer">Footer</div>;
  MockFooter.displayName = "MockFooter";
  return MockFooter;
});

jest.mock("../shared/NeedHelp/NeedHelp", () => {
  const MockNeedHelp = () => <div data-testid="need-help">Need Help</div>;
  MockNeedHelp.displayName = "MockNeedHelp";
  return MockNeedHelp;
});

jest.mock("../shared/StepHeader/StepHeader", () => {
  interface StepHeaderProps {
    currentStep: number;
    totalSteps: number;
    title: string;
  }
  const MockStepHeader = ({
    currentStep,
    totalSteps,
    title,
  }: StepHeaderProps) => (
    <div data-testid="step-header">
      Step {currentStep} of {totalSteps}: {title}
    </div>
  );
  MockStepHeader.displayName = "MockStepHeader";
  return MockStepHeader;
});

jest.mock("./CircularProgress/CircularProgress", () => {
  interface CircularProgressProps {
    percentage: number;
    score: string;
    band: string;
    bandStyles?: Record<string, string>;
  }
  const MockCircularProgress = ({
    percentage,
    score,
    band,
  }: CircularProgressProps) => (
    <div data-testid="circular-progress">
      <div data-testid="progress-percentage">{percentage}%</div>
      <div data-testid="progress-score">{score}</div>
      <div data-testid="progress-band">{band}</div>
    </div>
  );
  MockCircularProgress.displayName = "MockCircularProgress";
  return MockCircularProgress;
});

jest.mock("./ProgressBar/ProgressBar", () => {
  interface ProgressBarProps {
    label: string;
    value: number;
    maxValue: number;
    color?: string;
    animate?: boolean;
    showInPercentage?: boolean;
  }
  const MockProgressBar = ({ label, value, maxValue }: ProgressBarProps) => (
    <div data-testid={`progress-bar-${label.toLowerCase()}`}>
      {label}: {value}/{maxValue}
    </div>
  );
  MockProgressBar.displayName = "MockProgressBar";
  return MockProgressBar;
});

jest.mock("../shared/PageLoader", () => {
  interface PageLoaderProps {
    fillHeight?: boolean;
    enableText?: boolean;
    text?: string;
  }
  const MockPageLoader = ({ text }: PageLoaderProps) => (
    <div data-testid="page-loader">{text}</div>
  );
  MockPageLoader.displayName = "MockPageLoader";
  return MockPageLoader;
});

jest.mock("../shared/Button/Button", () => {
  interface ButtonProps {
    text?: string;
    onClick?: () => void;
    icon?: React.ReactNode;
    type?: string;
    size?: string;
    className?: string;
  }
  const MockButton = ({ text, onClick }: ButtonProps) => (
    <button
      onClick={onClick}
      data-testid={`button-${text?.replace(/\s+/g, "-").toLowerCase()}`}
    >
      {text}
    </button>
  );
  MockButton.displayName = "MockButton";
  return {
    __esModule: true,
    default: MockButton,
    ButtonType: { PRIMARY: "primary", SECONDARY: "secondary" },
  };
});

import SuccessResultsPage from "./SuccessResultsPage";
import { useAuth } from "@/contexts/AuthContext";
import { useScoreResults } from "@/hooks/useScoreResults";

// Type the mocked hooks
const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>;
const mockUseScoreResults = useScoreResults as jest.MockedFunction<
  typeof useScoreResults
>;

describe("SuccessResultsPage", () => {
  const mockProps = {
    onRetakeAssessment: jest.fn(),
    onVisitIVFCenter: jest.fn(),
    onShareEmail: jest.fn(),
    onHome: jest.fn(),
    onBookConsultation: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Loading State", () => {
    it("should show loading state when auth is loading", () => {
      mockUseAuth.mockReturnValue({ loading: true } as any);
      mockUseScoreResults.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: null,
        isError: false,
      } as any);

      render(<SuccessResultsPage {...mockProps} />);

      expect(document.querySelector(".shimmer-animated")).toBeInTheDocument();
    });

    it("should show loading state when score results are loading", () => {
      mockUseAuth.mockReturnValue({ loading: false } as any);
      mockUseScoreResults.mockReturnValue({
        data: undefined,
        isLoading: true,
        error: null,
        isError: false,
      } as any);

      render(<SuccessResultsPage {...mockProps} />);

      expect(document.querySelector(".shimmer-animated")).toBeInTheDocument();
    });
  });

  describe("Error State", () => {
    it("should show error state when there is an error", () => {
      mockUseAuth.mockReturnValue({ loading: false } as any);
      mockUseScoreResults.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: new Error("Failed to fetch results"),
        isError: true,
      } as any);

      render(<SuccessResultsPage {...mockProps} />);

      expect(screen.getByText("Error Loading Results")).toBeInTheDocument();
      expect(screen.getByText("Failed to fetch results")).toBeInTheDocument();
    });

    it("should call onHome when error button is clicked", () => {
      mockUseAuth.mockReturnValue({ loading: false } as any);
      mockUseScoreResults.mockReturnValue({
        data: undefined,
        isLoading: false,
        error: new Error("Test error"),
        isError: true,
      } as any);

      render(<SuccessResultsPage {...mockProps} />);

      fireEvent.click(screen.getByTestId("button-go-to-home"));
      expect(mockProps.onHome).toHaveBeenCalledTimes(1);
    });
  });

  describe("Success State", () => {
    const mockScoreData = {
      score: {
        percentage: 85.2,
        totalScore: 85.2,
        maxScore: 100,
        category: "High Fertility",
        factors: {
          biological: 8.5,
          lifestyle: 7.8,
          environmental: 9.1,
        },
      },
      cached: false,
      processingTime: 150,
    };

    beforeEach(() => {
      mockUseAuth.mockReturnValue({ loading: false } as any);
      mockUseScoreResults.mockReturnValue({
        data: mockScoreData,
        isLoading: false,
        error: null,
        isError: false,
      } as any);
    });

    it("should render main components", () => {
      render(<SuccessResultsPage {...mockProps} />);

      expect(screen.getByTestId("header")).toBeInTheDocument();
      expect(screen.getByTestId("footer")).toBeInTheDocument();
      expect(screen.getByTestId("step-header")).toBeInTheDocument();
      expect(screen.getByTestId("circular-progress")).toBeInTheDocument();
    });

    it("should display correct fertility score", () => {
      render(<SuccessResultsPage {...mockProps} />);

      expect(screen.getByTestId("progress-percentage")).toHaveTextContent(
        "85.2%"
      );
      expect(screen.getByTestId("progress-band")).toHaveTextContent(
        "High Fertility"
      );
      expect(screen.getByText("85.2 / 100")).toBeInTheDocument();
    });

    it("should display breakdown highlights", () => {
      render(<SuccessResultsPage {...mockProps} />);

      expect(screen.getByTestId("progress-bar-biological")).toHaveTextContent(
        "Biological: 850/50"
      );
      expect(screen.getByTestId("progress-bar-lifestyle")).toHaveTextContent(
        "Lifestyle: 780/25"
      );
      expect(
        screen.getByTestId("progress-bar-environmental")
      ).toHaveTextContent("Environmental: 910/25");
    });

    it("should show high fertility suggestion", () => {
      render(<SuccessResultsPage {...mockProps} />);

      expect(
        screen.getByText(
          /You.re doing well! A few lifestyle changes and timely consultation could increase your chances./
        )
      ).toBeInTheDocument();
    });

    it("should render action buttons", () => {
      render(<SuccessResultsPage {...mockProps} />);

      expect(
        screen.getByTestId("button-retake-assessment")
      ).toBeInTheDocument();
      expect(
        screen.getByTestId("button-visit-ivf-center-near-you")
      ).toBeInTheDocument();
      expect(
        screen.getByTestId("button-book-a-free-consultation")
      ).toBeInTheDocument();
    });

    it("should call handlers when buttons are clicked", () => {
      render(<SuccessResultsPage {...mockProps} />);

      fireEvent.click(screen.getByTestId("button-retake-assessment"));
      expect(mockProps.onRetakeAssessment).toHaveBeenCalledTimes(1);

      fireEvent.click(screen.getByTestId("button-visit-ivf-center-near-you"));
      expect(mockProps.onVisitIVFCenter).toHaveBeenCalledTimes(1);

      fireEvent.click(screen.getByTestId("button-book-a-free-consultation"));
      expect(mockProps.onBookConsultation).toHaveBeenCalledTimes(0);
    });
  });

  describe("Different Fertility Scores", () => {
    it("should show moderate fertility suggestion", () => {
      mockUseAuth.mockReturnValue({ loading: false } as any);
      mockUseScoreResults.mockReturnValue({
        data: {
          score: {
            percentage: 58.3,
            totalScore: 58.3,
            maxScore: 100,
            category: "Moderate Fertility",
            factors: { biological: 6, lifestyle: 6, environmental: 6 },
          },
        },
        isLoading: false,
        error: null,
        isError: false,
      } as any);

      render(<SuccessResultsPage {...mockProps} />);

      expect(
        screen.getByText(
          /Your fertility score is moderate. Some lifestyle changes and a consultation could help improve your chances./
        )
      ).toBeInTheDocument();
    });

    it("should show low fertility suggestion", () => {
      mockUseAuth.mockReturnValue({ loading: false } as any);
      mockUseScoreResults.mockReturnValue({
        data: {
          score: {
            percentage: 32.7,
            totalScore: 32.7,
            maxScore: 100,
            category: "Low Fertility",
            factors: { biological: 3, lifestyle: 3, environmental: 4 },
          },
        },
        isLoading: false,
        error: null,
        isError: false,
      } as any);

      render(<SuccessResultsPage {...mockProps} />);

      expect(
        screen.getByText(
          /Your fertility score indicates some concerns. We recommend consulting with a fertility specialist soon./
        )
      ).toBeInTheDocument();
    });
  });

  describe("Edge Cases", () => {
    it("should handle missing score data", () => {
      mockUseAuth.mockReturnValue({ loading: false } as any);
      mockUseScoreResults.mockReturnValue({
        data: { score: null },
        isLoading: false,
        error: null,
        isError: false,
      } as any);

      render(<SuccessResultsPage {...mockProps} />);

      expect(screen.getByTestId("progress-percentage")).toHaveTextContent("0%");
      expect(screen.getByTestId("progress-band")).toHaveTextContent(
        "Moderate Fertility"
      );
    });

    it("should work without prop handlers", () => {
      mockUseAuth.mockReturnValue({ loading: false } as any);
      mockUseScoreResults.mockReturnValue({
        data: {
          score: {
            percentage: 75,
            totalScore: 75,
            maxScore: 100,
            category: "Good Fertility",
            factors: { biological: 7, lifestyle: 7, environmental: 7 },
          },
        },
        isLoading: false,
        error: null,
        isError: false,
      } as any);

      expect(() => {
        render(<SuccessResultsPage />);
      }).not.toThrow();
    });

    it("should handle zero percentage correctly", () => {
      mockUseAuth.mockReturnValue({ loading: false } as any);
      mockUseScoreResults.mockReturnValue({
        data: {
          score: {
            percentage: 0,
            totalScore: 0,
            maxScore: 100,
            category: "Low Fertility",
            factors: { biological: 0, lifestyle: 0, environmental: 0 },
          },
        },
        isLoading: false,
        error: null,
        isError: false,
      } as any);

      render(<SuccessResultsPage {...mockProps} />);

      expect(screen.getByTestId("progress-percentage")).toHaveTextContent("0%");
      expect(screen.getByText("0.0 / 100")).toBeInTheDocument();
    });
  });

  describe("Console Logging", () => {
    it("should log cache information when data is cached", () => {
      const consoleSpy = jest.spyOn(console, "log").mockImplementation();

      mockUseAuth.mockReturnValue({ loading: false } as any);
      mockUseScoreResults.mockReturnValue({
        data: {
          score: {
            percentage: 75,
            totalScore: 75,
            maxScore: 100,
            category: "Good",
            factors: { biological: 7, lifestyle: 7, environmental: 7 },
          },
          cached: true,
          processingTime: 50,
        },
        isLoading: false,
        error: null,
        isError: false,
      } as any);

      render(<SuccessResultsPage {...mockProps} />);

      expect(consoleSpy).toHaveBeenCalledWith(
        "Score results served from cache"
      );

      consoleSpy.mockRestore();
    });

    it("should log processing time when data is not cached", () => {
      const consoleSpy = jest.spyOn(console, "log").mockImplementation();

      mockUseAuth.mockReturnValue({ loading: false } as any);
      mockUseScoreResults.mockReturnValue({
        data: {
          score: {
            percentage: 75,
            totalScore: 75,
            maxScore: 100,
            category: "Good",
            factors: { biological: 7, lifestyle: 7, environmental: 7 },
          },
          cached: false,
          processingTime: 250,
        },
        isLoading: false,
        error: null,
        isError: false,
      } as any);

      render(<SuccessResultsPage {...mockProps} />);

      expect(consoleSpy).toHaveBeenCalledWith(
        "Score results calculated in 250ms"
      );

      consoleSpy.mockRestore();
    });
  });
});
