import React from "react";
import { useRouter } from "next/navigation";
import Button, { ButtonType } from "../Button/Button";

export interface NoDataProps {
  title: string;
  description: string;
  buttonText?: string;
  buttonLink?: string;
  onButtonClick?: () => void;
  className?: string;
}

const NoData: React.FC<NoDataProps> = ({
  title,
  description,
  buttonText,
  buttonLink,
  onButtonClick,
  className = "",
}) => {
  const router = useRouter();

  const handleButtonClick = () => {
    if (onButtonClick) {
      onButtonClick();
    } else if (buttonLink) {
      router.push(buttonLink);
    }
  };

  return (
    <div className={`min-h-screen flex flex-col justify-center items-center py-4 md:py-6 ${className}`}>
      <div className="text-center max-w-md mx-auto px-4">
        {/* Icon placeholder - you can customize this */}
        <div className="mb-6">
          <div className="w-16 h-16 mx-auto bg-[var(--grey-3)] rounded-full flex items-center justify-center">
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              className="text-[var(--grey-6)]"
            >
              <circle cx="12" cy="12" r="10" />
              <path d="M8 12h8" />
              <path d="M12 8v8" />
            </svg>
          </div>
        </div>

        {/* Title */}
        <h2 className="text-[var(--grey-7)] text-xl font-bold mb-4">
          {title}
        </h2>

        {/* Description */}
        <p className="text-[var(--grey-6)] text-base mb-8 leading-relaxed">
          {description}
        </p>

        {/* Button */}
        {buttonText && (
          <div className="w-full max-w-xs mx-auto">
            <Button
              type={ButtonType.PRIMARY}
              text={buttonText}
              onClick={handleButtonClick}
              className="w-full"
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default NoData;
