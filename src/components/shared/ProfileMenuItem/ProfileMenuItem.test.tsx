import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import ProfileMenuItem from "./ProfileMenuItem";

const mockIcon = (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
    <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" />
  </svg>
);

describe("ProfileMenuItem", () => {
  it("renders with text and icon", () => {
    render(<ProfileMenuItem icon={mockIcon} text="Profile" />);

    expect(screen.getByText("Profile")).toBeInTheDocument();
    expect(screen.getByRole("button")).toBeInTheDocument();
  });

  it("calls onClick when clicked", () => {
    const mockOnClick = jest.fn();
    render(
      <ProfileMenuItem icon={mockIcon} text="Profile" onClick={mockOnClick} />
    );

    const button = screen.getByRole("button");
    fireEvent.click(button);

    expect(mockOnClick).toHaveBeenCalledTimes(1);
  });

  it("applies default variant styling", () => {
    render(<ProfileMenuItem icon={mockIcon} text="Profile" />);

    const button = screen.getByRole("button");
    expect(button).toHaveClass("hover:bg-[var(--grey-1)]");
    expect(button).toHaveClass("text-[var(--grey-7)]");
  });

  it("applies primary variant styling", () => {
    render(<ProfileMenuItem icon={mockIcon} text="Logout" variant="primary" />);

    const button = screen.getByRole("button");
    expect(button).toHaveClass("hover:bg-[var(--red-1)]");
    expect(button).toHaveClass("text-[var(--primary-pink)]");
  });

  it("applies custom className", () => {
    render(
      <ProfileMenuItem
        icon={mockIcon}
        text="Profile"
        className="custom-class"
      />
    );

    const button = screen.getByRole("button");
    expect(button).toHaveClass("custom-class");
  });

  it("handles undefined onClick gracefully", () => {
    render(<ProfileMenuItem icon={mockIcon} text="Profile" />);

    const button = screen.getByRole("button");

    // Should not throw error when onClick is undefined
    expect(() => fireEvent.click(button)).not.toThrow();
  });

  it("renders icon in correct container", () => {
    render(<ProfileMenuItem icon={mockIcon} text="Profile" />);

    const iconContainer = screen.getByRole("button").querySelector(".w-6.h-6");
    expect(iconContainer).toBeInTheDocument();
  });
});
