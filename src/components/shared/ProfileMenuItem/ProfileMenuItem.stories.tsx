import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import ProfileMenuItem from "./ProfileMenuItem";

// Mock icons for stories
const ProfileIcon = (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
    <path
      d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <circle
      cx="12"
      cy="7"
      r="4"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const SettingsIcon = (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
    <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="2" />
    <path
      d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"
      stroke="currentColor"
      strokeWidth="2"
    />
  </svg>
);

const LogoutIcon = (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
    <path
      d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <polyline
      points="16,17 21,12 16,7"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <line
      x1="21"
      y1="12"
      x2="9"
      y2="12"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
    />
  </svg>
);

const meta: Meta<typeof ProfileMenuItem> = {
  title: "Components/Shared/ProfileMenuItem",
  component: ProfileMenuItem,
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component:
          "A reusable component for individual profile menu items. Features icon, text, click handling, and variant styling for different item types.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    icon: {
      description: "React node containing the icon for the menu item",
    },
    text: {
      control: { type: "text" },
      description: "The text label for the menu item",
    },
    onClick: {
      action: "onClick",
      description: "Callback function when the menu item is clicked",
    },
    variant: {
      control: { type: "select" },
      options: ["default", "danger"],
      description:
        "Visual variant - default for normal items, danger for destructive actions",
    },
    className: {
      control: { type: "text" },
      description: "Additional CSS classes to apply",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Profile: Story = {
  args: {
    icon: ProfileIcon,
    text: "Profile",
    variant: "secondary",
  },
  decorators: [
    (Story) => (
      <div className="w-80 bg-white border border-gray-200 rounded-sm shadow-lg">
        <Story />
      </div>
    ),
  ],
};

export const Settings: Story = {
  args: {
    icon: SettingsIcon,
    text: "Settings",
    variant: "secondary",
  },
  decorators: [
    (Story) => (
      <div className="w-80 bg-white border border-gray-200 rounded-sm shadow-lg">
        <Story />
      </div>
    ),
  ],
};

export const Logout: Story = {
  args: {
    icon: LogoutIcon,
    text: "Logout",
    variant: "primary",
  },
  decorators: [
    (Story) => (
      <div className="w-80 bg-white border border-gray-200 rounded-sm shadow-lg">
        <Story />
      </div>
    ),
  ],
};

export const MenuItemsList: Story = {
  decorators: [
    () => (
      <div className="w-80 bg-white border border-gray-200 rounded-sm shadow-lg">
        <div className="py-2">
          <ProfileMenuItem icon={ProfileIcon} text="Profile" />
          <ProfileMenuItem icon={SettingsIcon} text="Settings" />
          <ProfileMenuItem icon={LogoutIcon} text="Logout" variant="primary" />
        </div>
      </div>
    ),
  ],
};
