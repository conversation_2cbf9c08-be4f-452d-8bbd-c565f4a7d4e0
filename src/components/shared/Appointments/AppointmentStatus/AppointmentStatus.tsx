import React from "react";

export enum AppointmentStatus {
  UPCOMING = "Upcoming",
  COMPLETED = "Completed",
  CANCELLED = "Cancelled",
}

interface AppointmentStatusProps {
  status: AppointmentStatus;
  className?: string;
}

const AppointmentStatusComponent: React.FC<AppointmentStatusProps> = ({
  status,
  className = "",
}) => {
  const getStatusStyles = (status: AppointmentStatus) => {
    switch (status) {
      case AppointmentStatus.UPCOMING:
        return {
          bgColor: "bg-[var(--info-blue-0)]",
          textColor: "text-[var(--info-blue-4)]",
        };
      case AppointmentStatus.COMPLETED:
        return {
          bgColor: "bg-[var(--success-green-0)]",
          textColor: "text-[var(--success-green-4)]",
        };
      case AppointmentStatus.CANCELLED:
        return {
          bgColor: "bg-[var(--error-red-0)]",
          textColor: "text-[var(--error-red-4)]",
        };
      default:
        return {
          bgColor: "bg-gray-50",
          textColor: "text-gray-600",
          borderColor: "border-gray-200",
        };
    }
  };

  const statusStyles = getStatusStyles(status);

  return (
    <span
      className={`px-2 py-1 rounded-[0.25rem] text-sm font-medium ${statusStyles.bgColor} ${statusStyles.textColor} ${className}`}
    >
      {status}
    </span>
  );
};

export default AppointmentStatusComponent;
