import React from "react";
import { render, screen } from "@testing-library/react";
import AppointmentStatusComponent, {
  AppointmentStatus,
} from "./AppointmentStatus";

describe("AppointmentStatus", () => {
  it("renders upcoming status correctly", () => {
    render(<AppointmentStatusComponent status={AppointmentStatus.UPCOMING} />);

    expect(screen.getByText("Upcoming")).toBeInTheDocument();
  });

  it("renders completed status correctly", () => {
    render(<AppointmentStatusComponent status={AppointmentStatus.COMPLETED} />);

    expect(screen.getByText("Completed")).toBeInTheDocument();
  });

  it("renders cancelled status correctly", () => {
    render(<AppointmentStatusComponent status={AppointmentStatus.CANCELLED} />);

    expect(screen.getByText("Cancelled")).toBeInTheDocument();
  });

  it("applies correct styles for upcoming status", () => {
    render(<AppointmentStatusComponent status={AppointmentStatus.UPCOMING} />);

    const statusElement = screen.getByText("Upcoming");
    expect(statusElement).toHaveClass(
      "bg-[var(--info-blue-0)]",
      "text-[var(--info-blue-4)]"
    );
  });

  it("applies correct styles for completed status", () => {
    render(<AppointmentStatusComponent status={AppointmentStatus.COMPLETED} />);

    const statusElement = screen.getByText("Completed");
    expect(statusElement).toHaveClass(
      "bg-[var(--success-green-0)]",
      "text-[var(--success-green-4)]"
    );
  });

  it("applies correct styles for cancelled status", () => {
    render(<AppointmentStatusComponent status={AppointmentStatus.CANCELLED} />);

    const statusElement = screen.getByText("Cancelled");
    expect(statusElement).toHaveClass(
      "bg-[var(--error-red-0)]",
      "text-[var(--error-red-4)]"
    );
  });

  it("applies default styles for unknown status", () => {
    render(
      <AppointmentStatusComponent status={"Unknown" as AppointmentStatus} />
    );

    const statusElement = screen.getByText("Unknown");
    expect(statusElement).toHaveClass("bg-gray-50", "text-gray-600");
  });

  it("renders with custom className", () => {
    render(
      <AppointmentStatusComponent
        status={AppointmentStatus.UPCOMING}
        className="custom-class"
      />
    );

    const statusElement = screen.getByText("Upcoming");
    expect(statusElement).toHaveClass("custom-class");
  });

  it("has correct base classes", () => {
    render(<AppointmentStatusComponent status={AppointmentStatus.UPCOMING} />);

    const statusElement = screen.getByText("Upcoming");
    expect(statusElement).toHaveClass(
      "px-2",
      "py-1",
      "rounded-[0.25rem]",
      "text-sm",
      "font-medium"
    );
  });

  it("renders as a span element", () => {
    render(<AppointmentStatusComponent status={AppointmentStatus.UPCOMING} />);

    const statusElement = screen.getByText("Upcoming");
    expect(statusElement.tagName).toBe("SPAN");
  });
});
