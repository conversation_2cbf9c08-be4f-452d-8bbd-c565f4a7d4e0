import { Meta, StoryObj } from "@storybook/nextjs";
import AppointmentStatusComponent, {
  AppointmentStatus,
} from "./AppointmentStatus";

const meta: Meta<typeof AppointmentStatusComponent> = {
  title: "Components/Appointments/AppointmentStatus",
  component: AppointmentStatusComponent,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
  argTypes: {
    status: {
      control: { type: "select" },
      options: Object.values(AppointmentStatus),
    },
  },
};

export default meta;

type Story = StoryObj<typeof AppointmentStatusComponent>;

export const Upcoming: Story = {
  args: {
    status: AppointmentStatus.UPCOMING,
  },
};

export const Completed: Story = {
  args: {
    status: AppointmentStatus.COMPLETED,
  },
};

export const Cancelled: Story = {
  args: {
    status: AppointmentStatus.CANCELLED,
  },
};

export const WithCustomClassName: Story = {
  args: {
    status: AppointmentStatus.UPCOMING,
    className: "custom-status-class",
  },
};
