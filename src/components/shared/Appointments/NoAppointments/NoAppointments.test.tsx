import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import NoAppointments from "./NoAppointments";

describe("NoAppointments", () => {
  it("renders the heading correctly", () => {
    render(<NoAppointments />);

    expect(screen.getByText("No Appointments Yet")).toBeInTheDocument();
  });

  it("renders the description text correctly", () => {
    render(<NoAppointments />);

    expect(
      screen.getByText("Looks like you haven't booked any appointments.")
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        "Start your IVF journey by scheduling your first consultation with a specialist."
      )
    ).toBeInTheDocument();
  });

  it("renders the schedule appointment button", () => {
    render(<NoAppointments />);

    expect(screen.getByText("Schedule Appointment")).toBeInTheDocument();
  });

  it("renders the FilesIcon", () => {
    render(<NoAppointments />);

    // Check that the icon container exists by looking for the mb-6 class
    const iconContainer = document.querySelector(".mb-6");
    expect(iconContainer).toBeInTheDocument();
  });

  it("has correct base classes", () => {
    render(<NoAppointments />);

    const container = screen.getByText("No Appointments Yet").closest("div");
    expect(container).toHaveClass(
      "flex",
      "flex-col",
      "items-center",
      "justify-center",
      "py-12",
      "px-4"
    );
  });

  it("calls onScheduleClick when schedule button is clicked", () => {
    const mockOnScheduleClick = jest.fn();
    render(<NoAppointments onScheduleClick={mockOnScheduleClick} />);

    const scheduleButton = screen.getByText("Schedule Appointment");
    fireEvent.click(scheduleButton);

    expect(mockOnScheduleClick).toHaveBeenCalledTimes(1);
  });

  it("renders with custom className", () => {
    render(<NoAppointments className="custom-class" />);

    const container = screen.getByText("No Appointments Yet").closest("div");
    expect(container).toHaveClass("custom-class");
  });

  it("renders without onScheduleClick handler", () => {
    render(<NoAppointments />);

    const scheduleButton = screen.getByText("Schedule Appointment");
    expect(scheduleButton).toBeInTheDocument();

    // Should not throw error when clicked without handler
    expect(() => fireEvent.click(scheduleButton)).not.toThrow();
  });

  it("has correct heading styling", () => {
    render(<NoAppointments />);

    const heading = screen.getByText("No Appointments Yet");
    expect(heading).toHaveClass(
      "text-[var(--grey-7)]",
      "font-bold",
      "text-2xl",
      "mb-4",
      "text-center"
    );
  });

  it("has correct description styling", () => {
    render(<NoAppointments />);

    const descriptionContainer = screen
      .getByText("Looks like you haven't booked any appointments.")
      .closest("div");
    expect(descriptionContainer).toHaveClass("text-center", "mb-8", "max-w-md");
  });

  it("renders description paragraphs with correct styling", () => {
    render(<NoAppointments />);

    const firstParagraph = screen.getByText(
      "Looks like you haven't booked any appointments."
    );
    const secondParagraph = screen.getByText(
      "Start your IVF journey by scheduling your first consultation with a specialist."
    );

    expect(firstParagraph).toHaveClass(
      "text-[var(--grey-6)]",
      "text-base",
      "mb-2"
    );
    expect(secondParagraph).toHaveClass("text-[var(--grey-6)]", "text-base");
  });

  it("renders as a complete empty state component", () => {
    render(<NoAppointments />);

    // Check all main elements are present
    expect(screen.getByText("No Appointments Yet")).toBeInTheDocument();
    expect(
      screen.getByText("Looks like you haven't booked any appointments.")
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        "Start your IVF journey by scheduling your first consultation with a specialist."
      )
    ).toBeInTheDocument();
    expect(screen.getByText("Schedule Appointment")).toBeInTheDocument();
  });
});
