import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import ClinicLocation from "./ClinicLocation";

describe("ClinicLocation Component", () => {
  const defaultProps = {
    address: "C-35, Sector 19, Ghaziabad, UP — 201001",
  };

  describe("Rendering", () => {
    it("renders without crashing", () => {
      render(<ClinicLocation {...defaultProps} />);
      expect(screen.getByText("Clinic Location")).toBeInTheDocument();
    });

    it("renders the address correctly", () => {
      render(<ClinicLocation {...defaultProps} />);
      expect(
        screen.getByText("C-35, Sector 19, Ghaziabad, UP — 201001")
      ).toBeInTheDocument();
    });

    it("renders the Get Direction button", () => {
      render(<ClinicLocation {...defaultProps} />);
      expect(
        screen.getByRole("button", { name: "Get Direction" })
      ).toBeInTheDocument();
    });

    it("renders with custom className", () => {
      const { container } = render(
        <ClinicLocation {...defaultProps} className="custom-class" />
      );

      expect(container.firstChild).toHaveClass("custom-class");
    });
  });

  describe("Props", () => {
    it("displays different address", () => {
      const newAddress = "123 Main Street, New York, NY — 10001";
      render(<ClinicLocation address={newAddress} />);

      expect(screen.getByText(newAddress)).toBeInTheDocument();
    });

    it("handles long address", () => {
      const longAddress =
        "123 Main Street, Building A, Floor 3, Suite 301, Downtown Business District, New York, NY — 10001";
      render(<ClinicLocation address={longAddress} />);

      expect(screen.getByText(longAddress)).toBeInTheDocument();
    });

    it("handles short address", () => {
      const shortAddress = "123 Main St, City — 12345";
      render(<ClinicLocation address={shortAddress} />);

      expect(screen.getByText(shortAddress)).toBeInTheDocument();
    });

    it("handles international address", () => {
      const internationalAddress =
        "42 Oxford Street, London, England, United Kingdom — SW1A 1AA";
      render(<ClinicLocation address={internationalAddress} />);

      expect(screen.getByText(internationalAddress)).toBeInTheDocument();
    });
  });

  describe("Button Interaction", () => {
    it("calls onGetDirection when button is clicked", () => {
      const mockOnGetDirection = jest.fn();
      render(
        <ClinicLocation {...defaultProps} onGetDirection={mockOnGetDirection} />
      );

      const button = screen.getByRole("button", { name: "Get Direction" });
      fireEvent.click(button);

      expect(mockOnGetDirection).toHaveBeenCalledTimes(1);
    });

    it("does not throw error when button is clicked without onGetDirection", () => {
      render(<ClinicLocation {...defaultProps} />);

      const button = screen.getByRole("button", { name: "Get Direction" });
      expect(() => fireEvent.click(button)).not.toThrow();
    });

    it("button is clickable", () => {
      render(<ClinicLocation {...defaultProps} />);

      const button = screen.getByRole("button", { name: "Get Direction" });
      expect(button).not.toBeDisabled();
    });
  });

  describe("Structure", () => {
    it("has correct heading structure", () => {
      render(<ClinicLocation {...defaultProps} />);

      const heading = screen.getByRole("heading", { level: 2 });
      expect(heading).toHaveTextContent("Clinic Location");
    });

    it("has correct CSS classes for styling", () => {
      const { container } = render(<ClinicLocation {...defaultProps} />);

      const card = container.firstChild as HTMLElement;
      expect(card).toHaveClass("bg-white", "rounded-[8px]", "border");
    });

    it("has proper button styling classes", () => {
      render(<ClinicLocation {...defaultProps} />);

      const button = screen.getByRole("button", { name: "Get Direction" });
      expect(button).toHaveClass("bg-[var(--pink-1)]", "text-[var(--pink-6)]");
    });
  });

  describe("Accessibility", () => {
    it("has proper heading hierarchy", () => {
      render(<ClinicLocation {...defaultProps} />);

      const heading = screen.getByRole("heading", { level: 2 });
      expect(heading).toBeInTheDocument();
    });

    it("button has proper accessibility attributes", () => {
      render(<ClinicLocation {...defaultProps} />);

      const button = screen.getByRole("button", { name: "Get Direction" });
      expect(button).toHaveAttribute("type", "button");
    });

    it("has proper text contrast with heading and address", () => {
      render(<ClinicLocation {...defaultProps} />);

      const heading = screen.getByText("Clinic Location");
      const address = screen.getByText(
        "C-35, Sector 19, Ghaziabad, UP — 201001"
      );

      expect(heading).toBeInTheDocument();
      expect(address).toBeInTheDocument();
    });
  });

  describe("Edge Cases", () => {
    it("handles empty address gracefully", () => {
      render(<ClinicLocation address="" />);

      expect(screen.getByText("Clinic Location")).toBeInTheDocument();
      expect(
        screen.getByRole("button", { name: "Get Direction" })
      ).toBeInTheDocument();
    });

    it("handles address with special characters", () => {
      const specialAddress =
        "C-35, Sector 19, Ghaziabad, UP — 201001 (Near Metro Station)";
      render(<ClinicLocation address={specialAddress} />);

      expect(screen.getByText(specialAddress)).toBeInTheDocument();
    });

    it("handles address with numbers and symbols", () => {
      const addressWithNumbers =
        "Building #42, Floor 3, Suite 301, 123-456 Main St, City — 12345";
      render(<ClinicLocation address={addressWithNumbers} />);

      expect(screen.getByText(addressWithNumbers)).toBeInTheDocument();
    });

    it("handles very long address", () => {
      const veryLongAddress =
        "123 Main Street, Building A, Floor 3, Suite 301, Downtown Business District, New York, NY — 10001, United States of America";
      render(<ClinicLocation address={veryLongAddress} />);

      expect(screen.getByText(veryLongAddress)).toBeInTheDocument();
    });
  });
});
