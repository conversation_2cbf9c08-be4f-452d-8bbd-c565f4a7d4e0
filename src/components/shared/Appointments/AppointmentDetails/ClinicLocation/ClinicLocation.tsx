import React from "react";
import Card from "@/components/shared/Card";
import Button, { ButtonType } from "@/components/shared/Button/Button";

interface ClinicLocationProps {
  address: string;
  onGetDirection?: () => void;
  className?: string;
}

const ClinicLocation: React.FC<ClinicLocationProps> = ({
  address,
  onGetDirection,
  className,
}) => {
  return (
    <Card
      className={`flex flex-col py-6 px-5 md:py-7.5 md:px-10 gap-6 md:gap-4 xl:gap-8 ${className}`}
    >
      {/* Header */}
      <h2 className="text-[var(--grey-7)] font-bold text-xl">
        Clinic Location
      </h2>

      {/* Address */}
      <p className="text-[var(--grey-6)] text-base">{address}</p>

      {/* Get Direction Button */}
      <Button
        type={ButtonType.PRIMARY}
        text="Get Direction"
        onClick={onGetDirection}
        className="!bg-[var(--red-1)] !text-[var(--primary-pink)] !hover:bg-[var(--red-2)] !border-0"
      />
    </Card>
  );
};

export default ClinicLocation;
