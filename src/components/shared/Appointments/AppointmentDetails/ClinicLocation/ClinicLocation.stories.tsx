import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import ClinicLocation from "./ClinicLocation";

const meta: Meta<typeof ClinicLocation> = {
  title: "Components/Appointments/ClinicLocation",
  component: ClinicLocation,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    address: {
      control: "text",
      description: "The clinic's address",
    },
    onGetDirection: {
      action: "getDirection",
      description: "Callback function when Get Direction button is clicked",
    },
    className: {
      control: "text",
      description: "Additional CSS classes",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    address: "C-35, Sector 19, Ghaziabad, UP — 201001",
  },
};

export const LongAddress: Story = {
  args: {
    address:
      "123 Main Street, Building A, Floor 3, Suite 301, Downtown Business District, New York, NY — 10001",
  },
};

export const ShortAddress: Story = {
  args: {
    address: "123 Main St, City — 12345",
  },
};

export const InternationalAddress: Story = {
  args: {
    address: "42 Oxford Street, London, England, United Kingdom — SW1A 1AA",
  },
};

export const WithCustomClass: Story = {
  args: {
    address: "C-35, Sector 19, Ghaziabad, UP — 201001",
    className: "shadow-lg",
  },
};

export const WithAction: Story = {
  args: {
    address: "C-35, Sector 19, Ghaziabad, UP — 201001",
    onGetDirection: () => {
      alert("Opening maps with directions to the clinic!");
    },
  },
};

export const EmptyAddress: Story = {
  args: {
    address: "",
  },
};
