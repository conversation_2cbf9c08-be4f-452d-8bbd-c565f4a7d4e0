import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import RemindersCard from "./RemindersCard";

describe("RemindersCard Component", () => {
  const defaultProps = {
    instructions:
      "Please arrive 10 minutes earlier for your appointment, bring your earlier reports and prescription.",
  };

  describe("Rendering", () => {
    it("renders without crashing", () => {
      render(<RemindersCard {...defaultProps} />);
      expect(screen.getByText("Reminders")).toBeInTheDocument();
    });

    it("renders the instructions correctly", () => {
      render(<RemindersCard {...defaultProps} />);
      expect(
        screen.getByText(
          "Please arrive 10 minutes earlier for your appointment, bring your earlier reports and prescription."
        )
      ).toBeInTheDocument();
    });

    it("renders both SMS and Email status indicators", () => {
      render(<RemindersCard {...defaultProps} />);
      expect(screen.getByText("SMS Sent")).toBeInTheDocument();
      expect(screen.getByText("Email Sent")).toBeInTheDocument();
    });

    it("renders with custom className", () => {
      const { container } = render(
        <RemindersCard {...defaultProps} className="custom-class" />
      );

      expect(container.firstChild).toHaveClass("custom-class");
    });
  });

  describe("Status Indicators", () => {
    it("shows green checkmarks when both reminders are sent", () => {
      render(
        <RemindersCard {...defaultProps} smsSent={true} emailSent={true} />
      );

      const checkmarks = document.querySelectorAll("svg");
      expect(checkmarks).toHaveLength(2);
    });

    it("shows only SMS checkmark when only SMS is sent", () => {
      render(
        <RemindersCard {...defaultProps} smsSent={true} emailSent={false} />
      );

      const checkmarks = document.querySelectorAll("svg");
      expect(checkmarks).toHaveLength(1);
    });

    it("shows only Email checkmark when only Email is sent", () => {
      render(
        <RemindersCard {...defaultProps} smsSent={false} emailSent={true} />
      );

      const checkmarks = document.querySelectorAll("svg");
      expect(checkmarks).toHaveLength(1);
    });

    it("shows no checkmarks when no reminders are sent", () => {
      render(
        <RemindersCard {...defaultProps} smsSent={false} emailSent={false} />
      );

      const checkmarks = document.querySelectorAll("svg");
      expect(checkmarks).toHaveLength(0);
    });

    it("has correct CSS classes for sent status", () => {
      render(
        <RemindersCard {...defaultProps} smsSent={true} emailSent={true} />
      );

      const statusIndicators = document.querySelectorAll(
        ".bg-\\[var\\(--success-green-4\\)\\]"
      );
      expect(statusIndicators).toHaveLength(2);
    });

    it("has correct CSS classes for unsent status", () => {
      render(
        <RemindersCard {...defaultProps} smsSent={false} emailSent={false} />
      );

      const statusIndicators = document.querySelectorAll(
        ".bg-\\[var\\(--grey-4\\)\\]"
      );
      expect(statusIndicators).toHaveLength(2);
    });
  });

  describe("Props", () => {
    it("displays different instructions", () => {
      const newInstructions = "Please bring your ID and insurance card.";
      render(<RemindersCard instructions={newInstructions} />);

      expect(screen.getByText(newInstructions)).toBeInTheDocument();
    });

    it("handles long instructions", () => {
      const longInstructions =
        "Please arrive 15 minutes earlier for your appointment. Bring all your previous medical reports, current prescriptions, and any relevant test results. Also, please bring a valid ID proof and insurance card if applicable. Wear comfortable clothing and avoid heavy meals 2 hours before the appointment.";
      render(<RemindersCard instructions={longInstructions} />);

      expect(screen.getByText(longInstructions)).toBeInTheDocument();
    });

    it("handles short instructions", () => {
      const shortInstructions = "Please arrive 10 minutes early.";
      render(<RemindersCard instructions={shortInstructions} />);

      expect(screen.getByText(shortInstructions)).toBeInTheDocument();
    });

    it("handles instructions with special characters", () => {
      const specialInstructions =
        "Please bring: 1) Previous reports, 2) Prescription, 3) ID proof. Note: Parking available on-site.";
      render(<RemindersCard instructions={specialInstructions} />);

      expect(screen.getByText(specialInstructions)).toBeInTheDocument();
    });
  });

  describe("Structure", () => {
    it("has correct heading structure", () => {
      render(<RemindersCard {...defaultProps} />);

      const heading = screen.getByRole("heading", { level: 2 });
      expect(heading).toHaveTextContent("Reminders");
    });

    it("has correct CSS classes for styling", () => {
      const { container } = render(<RemindersCard {...defaultProps} />);

      const card = container.firstChild as HTMLElement;
      expect(card).toHaveClass("bg-white", "rounded-[8px]", "border");
    });

    it("renders exactly 2 status indicators", () => {
      render(<RemindersCard {...defaultProps} />);

      const statusTexts = screen.getAllByText(/SMS Sent|Email Sent/);
      expect(statusTexts).toHaveLength(2);
    });

    it("has proper spacing between elements", () => {
      render(<RemindersCard {...defaultProps} />);

      const card = document.querySelector(".flex.flex-col.gap-6");
      expect(card).toBeInTheDocument();
    });
  });

  describe("Accessibility", () => {
    it("has proper heading hierarchy", () => {
      render(<RemindersCard {...defaultProps} />);

      const heading = screen.getByRole("heading", { level: 2 });
      expect(heading).toBeInTheDocument();
    });

    it("has proper text contrast with heading and instructions", () => {
      render(<RemindersCard {...defaultProps} />);

      const heading = screen.getByText("Reminders");
      const instructions = screen.getByText(
        "Please arrive 10 minutes earlier for your appointment, bring your earlier reports and prescription."
      );

      expect(heading).toBeInTheDocument();
      expect(instructions).toBeInTheDocument();
    });

    it("has proper SVG accessibility", () => {
      render(
        <RemindersCard {...defaultProps} smsSent={true} emailSent={true} />
      );

      const svgs = document.querySelectorAll("svg");
      svgs.forEach((svg) => {
        expect(svg).toHaveAttribute("width");
        expect(svg).toHaveAttribute("height");
        expect(svg).toHaveAttribute("viewBox");
      });
    });
  });

  describe("Edge Cases", () => {
    it("handles empty instructions gracefully", () => {
      render(<RemindersCard instructions="" />);

      expect(screen.getByText("Reminders")).toBeInTheDocument();
      expect(screen.getByText("SMS Sent")).toBeInTheDocument();
      expect(screen.getByText("Email Sent")).toBeInTheDocument();
    });

    it("handles instructions with numbers and symbols", () => {
      const instructionsWithNumbers =
        "Please bring items #1, #2, and #3. Call us at (555) 123-4567 if needed.";
      render(<RemindersCard instructions={instructionsWithNumbers} />);

      expect(screen.getByText(instructionsWithNumbers)).toBeInTheDocument();
    });

    it("handles very long instructions", () => {
      const veryLongInstructions =
        "Please arrive 15 minutes earlier for your appointment. Bring all your previous medical reports, current prescriptions, and any relevant test results. Also, please bring a valid ID proof and insurance card if applicable. Wear comfortable clothing and avoid heavy meals 2 hours before the appointment. Make sure to bring any recent blood work, X-rays, or other diagnostic tests that might be relevant to your current condition.";
      render(<RemindersCard instructions={veryLongInstructions} />);

      expect(screen.getByText(veryLongInstructions)).toBeInTheDocument();
    });
  });
});
