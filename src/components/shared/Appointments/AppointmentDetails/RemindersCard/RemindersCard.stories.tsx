import type { Meta, StoryObj } from "@storybook/nextjs";
import RemindersCard from "./RemindersCard";

const meta: Meta<typeof RemindersCard> = {
  title: "Components/Appointments/RemindersCard",
  component: RemindersCard,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    instructions: {
      control: "text",
      description: "The instruction text to display",
    },
    smsSent: {
      control: "boolean",
      description: "Whether SMS reminder was sent",
    },
    emailSent: {
      control: "boolean",
      description: "Whether Email reminder was sent",
    },
    className: {
      control: "text",
      description: "Additional CSS classes",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    instructions:
      "Please arrive 10 minutes earlier for your appointment, bring your earlier reports and prescription.",
    smsSent: true,
    emailSent: true,
  },
};

export const OnlySMS: Story = {
  args: {
    instructions:
      "Please arrive 10 minutes earlier for your appointment, bring your earlier reports and prescription.",
    smsSent: true,
    emailSent: false,
  },
};

export const OnlyEmail: Story = {
  args: {
    instructions:
      "Please arrive 10 minutes earlier for your appointment, bring your earlier reports and prescription.",
    smsSent: false,
    emailSent: true,
  },
};

export const NoRemindersSent: Story = {
  args: {
    instructions:
      "Please arrive 10 minutes earlier for your appointment, bring your earlier reports and prescription.",
    smsSent: false,
    emailSent: false,
  },
};

export const LongInstructions: Story = {
  args: {
    instructions:
      "Please arrive 15 minutes earlier for your appointment. Bring all your previous medical reports, current prescriptions, and any relevant test results. Also, please bring a valid ID proof and insurance card if applicable. Wear comfortable clothing and avoid heavy meals 2 hours before the appointment.",
    smsSent: true,
    emailSent: true,
  },
};

export const ShortInstructions: Story = {
  args: {
    instructions: "Please arrive 10 minutes early.",
    smsSent: true,
    emailSent: true,
  },
};

export const WithCustomClass: Story = {
  args: {
    instructions:
      "Please arrive 10 minutes earlier for your appointment, bring your earlier reports and prescription.",
    smsSent: true,
    emailSent: true,
    className: "shadow-lg",
  },
};

export const SpecialInstructions: Story = {
  args: {
    instructions:
      "Please bring: 1) Previous reports, 2) Prescription, 3) ID proof. Note: Parking available on-site.",
    smsSent: true,
    emailSent: false,
  },
};
