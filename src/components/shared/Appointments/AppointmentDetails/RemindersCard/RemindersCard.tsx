import React from "react";
import Card from "@/components/shared/Card";

interface RemindersCardProps {
  instructions: string;
  smsSent?: boolean;
  emailSent?: boolean;
  className?: string;
}

const RemindersCard: React.FC<RemindersCardProps> = ({
  instructions,
  smsSent = true,
  emailSent = true,
  className,
}) => {
  const reminders = [
    { type: "SMS", sent: smsSent },
    { type: "Email", sent: emailSent },
  ];

  return (
    <Card
      className={`flex flex-col py-6 px-5 md:py-7.5 md:px-10 gap-6 md:gap-4 xl:gap-8 ${className}`}
    >
      {/* Header */}
      <h2 className="text-[var(--grey-7)] font-bold text-xl">Reminders</h2>

      {/* Status Indicators */}
      <div className="flex gap-8">
        {reminders.map((reminder) => (
          <div key={reminder.type} className="flex items-center gap-3">
            <div
              className={`w-5 h-5 rounded-full flex items-center justify-center ${
                reminder.sent
                  ? "bg-[var(--success-green-3)]"
                  : "bg-[var(--grey-4)]"
              }`}
            >
              {reminder.sent && (
                <svg
                  width="12"
                  height="12"
                  viewBox="0 0 12 12"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10 3L4.5 8.5L2 6"
                    stroke="white"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              )}
            </div>
            <span className="text-[var(--grey-6)] text-base font-medium">
              {reminder.type} Sent
            </span>
          </div>
        ))}
      </div>

      {/* Instructions */}
      <p className="text-[var(--grey-6)] text-base line-height-[100%] font-medium">
        {instructions}
      </p>
    </Card>
  );
};

export default RemindersCard;
