import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import DoctorInfoCard from "./DoctorInfoCard";

const meta: Meta<typeof DoctorInfoCard> = {
  title: "Components/Appointments/DoctorInfoCard",
  component: DoctorInfoCard,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    doctorName: {
      control: "text",
      description: "The name of the doctor",
    },
    language: {
      control: "text",
      description: "Languages spoken by the doctor",
    },
    experience: {
      control: "text",
      description: "Years of experience",
    },
    className: {
      control: "text",
      description: "Additional CSS classes",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    doctorName: "Dr. <PERSON>",
    language: "Hindi, English",
    experience: "12+ Years",
  },
};

export const LongDoctorName: Story = {
  args: {
    doctorName: "Dr. <PERSON>",
    language: "English, Spanish, Portuguese",
    experience: "15+ Years",
  },
};

export const MultipleLanguages: Story = {
  args: {
    doctorName: "Dr. <PERSON>",
    language: "English, Mandarin, Cantonese, French",
    experience: "8+ Years",
  },
};

export const NewDoctor: Story = {
  args: {
    doctorName: "Dr. <PERSON> <PERSON>",
    language: "English",
    experience: "2+ Years",
  },
};

export const SeniorDoctor: Story = {
  args: {
    doctorName: "Dr. <PERSON> Williams",
    language: "English, German",
    experience: "25+ Years",
  },
};

export const WithCustomClass: Story = {
  args: {
    doctorName: "Dr. Emily Davis",
    language: "English, Spanish",
    experience: "10+ Years",
    className: "shadow-lg",
  },
};
