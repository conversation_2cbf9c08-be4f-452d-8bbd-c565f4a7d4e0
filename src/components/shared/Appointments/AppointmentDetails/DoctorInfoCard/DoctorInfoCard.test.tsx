import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import DoctorInfoCard from "./DoctorInfoCard";

describe("DoctorInfoCard Component", () => {
  const defaultProps = {
    doctorName: "<PERSON><PERSON> <PERSON>",
    language: "Hindi, English",
    experience: "12+ Years",
  };

  describe("Rendering", () => {
    it("renders without crashing", () => {
      render(<DoctorInfoCard {...defaultProps} />);
      expect(screen.getByText("Doctor Info")).toBeInTheDocument();
    });

    it("renders all doctor details correctly", () => {
      render(<DoctorInfoCard {...defaultProps} />);

      expect(screen.getByText("Doctor Name:")).toBeInTheDocument();
      expect(screen.getByText("Dr. Priya Sharma")).toBeInTheDocument();

      expect(screen.getByText("Language:")).toBeInTheDocument();
      expect(screen.getByText("Hindi, English")).toBeInTheDocument();

      expect(screen.getByText("Experience:")).toBeInTheDocument();
      expect(screen.getByText("12+ Years")).toBeInTheDocument();
    });

    it("renders with custom className", () => {
      const { container } = render(
        <DoctorInfoCard {...defaultProps} className="custom-class" />
      );

      expect(container.firstChild).toHaveClass("custom-class");
    });
  });

  describe("Props", () => {
    it("displays different doctor name", () => {
      render(
        <DoctorInfoCard
          doctorName="Dr. John Smith"
          language="English"
          experience="5+ Years"
        />
      );

      expect(screen.getByText("Dr. John Smith")).toBeInTheDocument();
    });

    it("displays multiple languages", () => {
      render(
        <DoctorInfoCard
          doctorName="Dr. Maria Garcia"
          language="English, Spanish, French"
          experience="15+ Years"
        />
      );

      expect(screen.getByText("English, Spanish, French")).toBeInTheDocument();
    });

    it("displays different experience levels", () => {
      render(
        <DoctorInfoCard
          doctorName="Dr. David Lee"
          language="English, Korean"
          experience="20+ Years"
        />
      );

      expect(screen.getByText("20+ Years")).toBeInTheDocument();
    });
  });

  describe("Structure", () => {
    it("has correct heading structure", () => {
      render(<DoctorInfoCard {...defaultProps} />);

      const heading = screen.getByRole("heading", { level: 2 });
      expect(heading).toHaveTextContent("Doctor Info");
    });

    it("renders exactly 3 detail items", () => {
      render(<DoctorInfoCard {...defaultProps} />);

      const labels = screen.getAllByText(/Doctor Name:|Language:|Experience:/);
      expect(labels).toHaveLength(3);
    });

    it("has correct CSS classes for styling", () => {
      const { container } = render(<DoctorInfoCard {...defaultProps} />);

      const card = container.firstChild as HTMLElement;
      expect(card).toHaveClass("bg-white", "rounded-[8px]", "border");
    });
  });

  describe("Accessibility", () => {
    it("has proper heading hierarchy", () => {
      render(<DoctorInfoCard {...defaultProps} />);

      const heading = screen.getByRole("heading", { level: 2 });
      expect(heading).toBeInTheDocument();
    });

    it("has proper text contrast with labels and values", () => {
      render(<DoctorInfoCard {...defaultProps} />);

      const labels = screen.getAllByText(/Doctor Name:|Language:|Experience:/);
      const values = screen.getAllByText(
        /Dr\. Priya Sharma|Hindi, English|12\+ Years/
      );

      expect(labels.length).toBeGreaterThan(0);
      expect(values.length).toBeGreaterThan(0);
    });
  });

  describe("Edge Cases", () => {
    it("handles empty strings gracefully", () => {
      render(<DoctorInfoCard doctorName="" language="" experience="" />);

      expect(screen.getByText("Doctor Name:")).toBeInTheDocument();
      expect(screen.getByText("Language:")).toBeInTheDocument();
      expect(screen.getByText("Experience:")).toBeInTheDocument();
    });

    it("handles very long doctor names", () => {
      const longName = "Dr. Alexander Rodriguez-Gonzalez de la Cruz y Martinez";
      render(
        <DoctorInfoCard
          doctorName={longName}
          language="English, Spanish"
          experience="10+ Years"
        />
      );

      expect(screen.getByText(longName)).toBeInTheDocument();
    });

    it("handles special characters in language field", () => {
      render(
        <DoctorInfoCard
          doctorName="Dr. Wei Zhang"
          language="中文, English, 日本語"
          experience="8+ Years"
        />
      );

      expect(screen.getByText("中文, English, 日本語")).toBeInTheDocument();
    });
  });
});
