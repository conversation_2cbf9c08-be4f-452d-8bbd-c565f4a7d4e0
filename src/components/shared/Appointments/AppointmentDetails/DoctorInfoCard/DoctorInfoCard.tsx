import React from "react";
import Card from "@/components/shared/Card";

interface DoctorInfoCardProps {
  doctorName: string;
  language: string;
  experience: string;
  className?: string;
}

const DoctorInfoCard: React.FC<DoctorInfoCardProps> = ({
  doctorName,
  language,
  experience,
  className,
}) => {
  const doctorDetails = [
    { label: "Doctor Name", value: doctorName },
    { label: "Language", value: language },
    { label: "Experience", value: experience },
  ];

  return (
    <Card
      className={`flex flex-col py-6 px-5 md:py-7.5 md:px-10 gap-6 md:gap-4 xl:gap-8 ${className}`}
    >
      {/* Header */}
      <h2 className="text-[var(--grey-7)] font-bold text-xl">Doctor Info</h2>

      {/* Doctor Details Grid */}
      <div className="flex flex-col w-full md:flex-row md:justify-between gap-5 md:gap-16">
        {doctorDetails.map((detail, index) => (
          <div key={index} className="flex flex-col gap-2">
            <span className="text-[var(--grey-6)] text-base font-medium">
              {detail.label}:
            </span>
            <p className="text-[var(--grey-7)] font-bold text-base">
              {detail.value}
            </p>
          </div>
        ))}
      </div>
    </Card>
  );
};

export default DoctorInfoCard;
