import React from "react";
import Card from "@/components/shared/Card";

interface PatientInfoCardProps {
  patientName: string;
  age: string;
  phone: string;
  email: string;
  className?: string;
}

const PatientInfoCard: React.FC<PatientInfoCardProps> = ({
  patientName,
  age,
  phone,
  email,
  className,
}) => {
  const patientDetails = [
    { label: "Patient Name", value: patientName },
    { label: "Age", value: age },
    { label: "Phone", value: phone },
    { label: "Email", value: email },
  ];

  return (
    <Card
      className={`flex flex-col py-6 px-5 md:py-7.5 md:px-10 gap-6 md:gap-4 xl:gap-8 ${className}`}
    >
      {/* Header */}
      <h2 className="text-[var(--grey-7)] font-bold text-xl">Patient Info</h2>

      {/* Patient Details Grid */}
      <div className="flex flex-col w-full md:flex-row md:justify-between gap-5 md:gap-6">
        {patientDetails.map((detail, index) => (
          <div key={index} className="flex flex-col gap-2">
            <span className="text-[var(--grey-6)] text-base font-medium">
              {detail.label}:
            </span>
            <p className="text-[var(--grey-7)] font-bold text-base">
              {detail.value}
            </p>
          </div>
        ))}
      </div>
    </Card>
  );
};

export default PatientInfoCard;
