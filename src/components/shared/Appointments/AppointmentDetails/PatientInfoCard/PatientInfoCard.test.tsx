import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import PatientInfoCard from "./PatientInfoCard";

describe("PatientInfoCard Component", () => {
  const defaultProps = {
    patientName: "<PERSON><PERSON> Patel",
    age: "30",
    phone: "90 1223 9880",
    email: "<EMAIL>",
  };

  describe("Rendering", () => {
    it("renders without crashing", () => {
      render(<PatientInfoCard {...defaultProps} />);
      expect(screen.getByText("Patient Info")).toBeInTheDocument();
    });

    it("renders all patient details correctly", () => {
      render(<PatientInfoCard {...defaultProps} />);

      expect(screen.getByText("Patient Name:")).toBeInTheDocument();
      expect(screen.getByText("Riya Patel")).toBeInTheDocument();

      expect(screen.getByText("Age:")).toBeInTheDocument();
      expect(screen.getByText("30")).toBeInTheDocument();

      expect(screen.getByText("Phone:")).toBeInTheDocument();
      expect(screen.getByText("90 1223 9880")).toBeInTheDocument();

      expect(screen.getByText("Email:")).toBeInTheDocument();
      expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
    });

    it("renders with custom className", () => {
      const { container } = render(
        <PatientInfoCard {...defaultProps} className="custom-class" />
      );

      expect(container.firstChild).toHaveClass("custom-class");
    });
  });

  describe("Props", () => {
    it("displays different patient name", () => {
      render(
        <PatientInfoCard
          patientName="John Smith"
          age="25"
          phone="555-0123"
          email="<EMAIL>"
        />
      );

      expect(screen.getByText("John Smith")).toBeInTheDocument();
    });

    it("displays different age", () => {
      render(
        <PatientInfoCard
          patientName="Maria Garcia"
          age="45"
          phone="555-0123"
          email="<EMAIL>"
        />
      );

      expect(screen.getByText("45")).toBeInTheDocument();
    });

    it("displays international phone number", () => {
      render(
        <PatientInfoCard
          patientName="Wei Zhang"
          age="28"
          phone="+86 138 0013 8000"
          email="<EMAIL>"
        />
      );

      expect(screen.getByText("+86 138 0013 8000")).toBeInTheDocument();
    });

    it("displays different email", () => {
      render(
        <PatientInfoCard
          patientName="David Lee"
          age="35"
          phone="555-0123"
          email="<EMAIL>"
        />
      );

      expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
    });
  });

  describe("Structure", () => {
    it("has correct heading structure", () => {
      render(<PatientInfoCard {...defaultProps} />);

      const heading = screen.getByRole("heading", { level: 2 });
      expect(heading).toHaveTextContent("Patient Info");
    });

    it("renders exactly 4 detail items", () => {
      render(<PatientInfoCard {...defaultProps} />);

      const labels = screen.getAllByText(/Patient Name:|Age:|Phone:|Email:/);
      expect(labels).toHaveLength(4);
    });

    it("has correct CSS classes for styling", () => {
      const { container } = render(<PatientInfoCard {...defaultProps} />);

      const card = container.firstChild as HTMLElement;
      expect(card).toHaveClass("bg-white", "rounded-[8px]", "border");
    });
  });

  describe("Accessibility", () => {
    it("has proper heading hierarchy", () => {
      render(<PatientInfoCard {...defaultProps} />);

      const heading = screen.getByRole("heading", { level: 2 });
      expect(heading).toBeInTheDocument();
    });

    it("has proper text contrast with labels and values", () => {
      render(<PatientInfoCard {...defaultProps} />);

      const labels = screen.getAllByText(/Patient Name:|Age:|Phone:|Email:/);
      const values = screen.getAllByText(
        /Riya Patel|30|90 1223 9880|riya@email\.com/
      );

      expect(labels.length).toBeGreaterThan(0);
      expect(values.length).toBeGreaterThan(0);
    });
  });

  describe("Edge Cases", () => {
    it("handles empty strings gracefully", () => {
      render(<PatientInfoCard patientName="" age="" phone="" email="" />);

      expect(screen.getByText("Patient Name:")).toBeInTheDocument();
      expect(screen.getByText("Age:")).toBeInTheDocument();
      expect(screen.getByText("Phone:")).toBeInTheDocument();
      expect(screen.getByText("Email:")).toBeInTheDocument();
    });

    it("handles very long patient names", () => {
      const longName = "Dr. Alexander Rodriguez-Gonzalez de la Cruz y Martinez";
      render(
        <PatientInfoCard
          patientName={longName}
          age="40"
          phone="555-0123"
          email="<EMAIL>"
        />
      );

      expect(screen.getByText(longName)).toBeInTheDocument();
    });

    it("handles special characters in email", () => {
      render(
        <PatientInfoCard
          patientName="Test User"
          age="25"
          phone="555-0123"
          email="<EMAIL>"
        />
      );

      expect(
        screen.getByText("<EMAIL>")
      ).toBeInTheDocument();
    });

    it("handles numeric age as string", () => {
      render(
        <PatientInfoCard
          patientName="Test User"
          age="0"
          phone="555-0123"
          email="<EMAIL>"
        />
      );

      expect(screen.getByText("0")).toBeInTheDocument();
    });
  });
});
