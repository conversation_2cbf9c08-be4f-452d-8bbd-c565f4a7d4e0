import AppointmentInfoCard from "./AppointmentInfoCard";
import { AppointmentStatus } from "../../AppointmentStatus/AppointmentStatus";
import { Meta, StoryObj } from "@storybook/nextjs";

const meta: Meta<typeof AppointmentInfoCard> = {
  title: "Components/Appointments/AppointmentInfoCard",
  component: AppointmentInfoCard,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
};

export default meta;

type Story = StoryObj<typeof AppointmentInfoCard>;

export const Upcoming: Story = {
  args: {
    appointmentId: "GJ-APT-20250711-001",
    status: AppointmentStatus.UPCOMING,
    doctorName: "Dr. <PERSON><PERSON>",
    specialization: "IVF Specialist, Gynecologist",
    clinic: "Gunjan IVF Clinic — Ghaziabad",
    consultationType: "In-Person",
    appointmentDate: "Thursday, 11 July 2025",
    appointmentTime: "10:00 AM to 10:30 AM",
    duration: "30 Min",
    fees: "₹1200",
    paymentStatus: "paid",
    bookingDate: "07 July 2025",
  },
};

export const Completed: Story = {
  args: {
    appointmentId: "GJ-APT-20250708-002",
    status: AppointmentStatus.COMPLETED,
    doctorName: "Dr. Aarti Mehra",
    specialization: "Fertility Consultant, Obstetrician",
    clinic: "Gunjan IVF Clinic — Meerut",
    consultationType: "In-Person",
    appointmentDate: "Monday, 08 July 2025",
    appointmentTime: "12:00 PM to 12:30 PM",
    duration: "30 Min",
    fees: "₹1500",
    paymentStatus: "paid",
    bookingDate: "05 July 2025",
  },
};

export const Cancelled: Story = {
  args: {
    appointmentId: "GJ-APT-20250612-003",
    status: AppointmentStatus.CANCELLED,
    doctorName: "Dr. Naresh Mehta",
    specialization: "IVF & IUI Expert",
    clinic: "Gunjan IVF Clinic — Jankpuri, Delhi",
    consultationType: "In-Person",
    appointmentDate: "Thursday, 12 June 2025",
    appointmentTime: "10:00 PM to 10:30 PM",
    duration: "30 Min",
    fees: "₹1800",
    paymentStatus: "unpaid",
    bookingDate: "10 June 2025",
  },
};
