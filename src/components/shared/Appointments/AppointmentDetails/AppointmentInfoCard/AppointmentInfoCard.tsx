import React from "react";
import { AppointmentStatus } from "../../AppointmentStatus/AppointmentStatus";
import Card from "@/components/shared/Card";

interface AppointmentInfoCardProps {
  appointmentId: string;
  status: AppointmentStatus;
  doctorName: string;
  specialization: string;
  clinic: string;
  consultationType: string;
  appointmentDate: string;
  appointmentTime: string;
  duration: string;
  fees: string;
  paymentStatus: "paid" | "unpaid";
  bookingDate: string;
  className?: string;
}

const AppointmentInfoCard: React.FC<AppointmentInfoCardProps> = ({
  appointmentId,
  status,
  doctorName,
  specialization,
  clinic,
  consultationType,
  appointmentDate,
  appointmentTime,
  duration,
  fees,
  paymentStatus,
  bookingDate,
  className,
}) => {
  const getStatusStyles = (status: AppointmentStatus) => {
    switch (status) {
      case AppointmentStatus.UPCOMING:
        return {
          bgColor: "bg-[var(--info-blue-0)]",
          textColor: "text-[var(--info-blue-4)]",
        };
      case AppointmentStatus.COMPLETED:
        return {
          bgColor: "bg-[var(--success-green-0)]",
          textColor: "text-[var(--success-green-4)]",
        };
      case AppointmentStatus.CANCELLED:
        return {
          bgColor: "bg-[var(--error-red-0)]",
          textColor: "text-[var(--error-red-4)]",
        };
      default:
        return {
          bgColor: "bg-[var(--grey-2)]",
          textColor: "text-[var(--grey-6)]",
        };
    }
  };

  const statusStyles = getStatusStyles(status);

  const appointmentDetails = [
    { label: "Doctor Name:", value: doctorName },
    { label: "Specialization:", value: specialization },
    { label: "Clinic:", value: clinic },
    { label: "Consultation Type:", value: consultationType },
    { label: "Appointment Date:", value: appointmentDate },
    { label: "Time:", value: appointmentTime },
    { label: "Appointment ID:", value: appointmentId },
    {
      label: "Status:",
      value: status.charAt(0).toUpperCase() + status.slice(1),
    },
    { label: "Duration:", value: duration },
    { label: "Fees:", value: fees },
    {
      label: "Payment Status:",
      value: paymentStatus,
      className: "capitalize",
    },
    { label: "Booking Date:", value: bookingDate },
  ];

  return (
    <Card
      className={`flex flex-col w-[362px] md:w-[681px] xl:w-full px-5 py-6 md:px-10 md:py-7.5 gap-6 ${className}`}
    >
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-[var(--grey-7)] font-bold text-xl">
          #{appointmentId}
        </h2>
        <span
          className={`px-3 py-1 rounded-full text-sm font-medium ${statusStyles.bgColor} ${statusStyles.textColor}`}
        >
          {status.charAt(0).toUpperCase() + status.slice(1)}
        </span>
      </div>

      {/* Appointment Details Grid */}
      <div className="flex flex-col w-full md:flex-row md:flex-wrap md:justify-between pr-7.5 md:py-5 gap-5 md:gap-7.5">
        {appointmentDetails.map((detail, index) => (
          <div key={index} className="md:min-w-[250px] flex flex-col gap-2">
            <span className="text-[var(--grey-6)] text-base font-medium">
              {detail.label}
            </span>
            <p
              className={`text-[var(--grey-7)] font-bold text-base ${detail.className || ""}`}
            >
              {detail.value}
            </p>
          </div>
        ))}
      </div>
    </Card>
  );
};

export default AppointmentInfoCard;
