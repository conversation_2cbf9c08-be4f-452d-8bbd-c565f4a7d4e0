import React from "react";
import { render, screen } from "@testing-library/react";
import AppointmentInfoCard from "./AppointmentInfoCard";
import { AppointmentStatus } from "../../AppointmentStatus/AppointmentStatus";

describe("AppointmentInfoCard", () => {
  const mockProps = {
    appointmentId: "GJ-APT-20250711-001",
    status: AppointmentStatus.UPCOMING,
    doctorName: "Dr. <PERSON><PERSON>",
    specialization: "IVF Specialist, Gynecologist",
    clinic: "Gunjan IVF Clinic — Ghaziabad",
    consultationType: "In-Person",
    appointmentDate: "Thursday, 11 July 2025",
    appointmentTime: "10:00 AM to 10:30 AM",
    duration: "30 Min",
    fees: "₹1200",
    paymentStatus: "paid" as const,
    bookingDate: "07 July 2025",
  };

  it("renders appointment ID in header", () => {
    render(<AppointmentInfoCard {...mockProps} />);

    expect(screen.getByText("#GJ-APT-20250711-001")).toBeInTheDocument();
  });

  it("renders status badge in header", () => {
    render(<AppointmentInfoCard {...mockProps} />);

    const statusBadges = screen.getAllByText("Upcoming");
    expect(statusBadges).toHaveLength(2); // One in header badge, one in details
  });

  it("renders doctor information correctly", () => {
    render(<AppointmentInfoCard {...mockProps} />);

    expect(screen.getByText("Dr. Priya Sharma")).toBeInTheDocument();
    expect(
      screen.getByText("IVF Specialist, Gynecologist")
    ).toBeInTheDocument();
  });

  it("renders clinic and consultation type", () => {
    render(<AppointmentInfoCard {...mockProps} />);

    expect(
      screen.getByText("Gunjan IVF Clinic — Ghaziabad")
    ).toBeInTheDocument();
    expect(screen.getByText("In-Person")).toBeInTheDocument();
  });

  it("renders appointment date and time", () => {
    render(<AppointmentInfoCard {...mockProps} />);

    expect(screen.getByText("Thursday, 11 July 2025")).toBeInTheDocument();
    expect(screen.getByText("10:00 AM to 10:30 AM")).toBeInTheDocument();
  });

  it("renders appointment ID in details", () => {
    render(<AppointmentInfoCard {...mockProps} />);

    expect(screen.getAllByText("GJ-APT-20250711-001")).toHaveLength(2); // One in header, one in details
  });

  it("renders duration and fees", () => {
    render(<AppointmentInfoCard {...mockProps} />);

    expect(screen.getByText("30 Min")).toBeInTheDocument();
    expect(screen.getByText("₹1200")).toBeInTheDocument();
  });

  it("renders payment status and booking date", () => {
    render(<AppointmentInfoCard {...mockProps} />);

    expect(screen.getByText("paid")).toBeInTheDocument(); // Lowercase as rendered
    expect(screen.getByText("07 July 2025")).toBeInTheDocument();
  });

  it("applies correct status styles for upcoming", () => {
    render(<AppointmentInfoCard {...mockProps} />);

    const statusBadges = screen.getAllByText("Upcoming");
    const headerBadge = statusBadges[0]; // First one is the header badge
    expect(headerBadge).toHaveClass(
      "bg-[var(--info-blue-0)]",
      "text-[var(--info-blue-4)]"
    );
  });

  it("applies correct status styles for completed", () => {
    render(
      <AppointmentInfoCard
        {...mockProps}
        status={AppointmentStatus.COMPLETED}
      />
    );

    const statusBadges = screen.getAllByText("Completed");
    const headerBadge = statusBadges[0]; // First one is the header badge
    expect(headerBadge).toHaveClass(
      "bg-[var(--success-green-0)]",
      "text-[var(--success-green-4)]"
    );
  });

  it("applies correct status styles for cancelled", () => {
    render(
      <AppointmentInfoCard
        {...mockProps}
        status={AppointmentStatus.CANCELLED}
      />
    );

    const statusBadges = screen.getAllByText("Cancelled");
    const headerBadge = statusBadges[0]; // First one is the header badge
    expect(headerBadge).toHaveClass(
      "bg-[var(--error-red-0)]",
      "text-[var(--error-red-4)]"
    );
  });

  it("renders with custom className", () => {
    const { container } = render(
      <AppointmentInfoCard {...mockProps} className="custom-class" />
    );

    expect(container.firstChild).toHaveClass("custom-class");
  });

  it("handles unpaid payment status", () => {
    render(<AppointmentInfoCard {...mockProps} paymentStatus="unpaid" />);

    expect(screen.getByText("unpaid")).toBeInTheDocument();
  });

  it("renders all field labels", () => {
    render(<AppointmentInfoCard {...mockProps} />);

    expect(screen.getByText("Doctor Name:")).toBeInTheDocument();
    expect(screen.getByText("Specialization:")).toBeInTheDocument();
    expect(screen.getByText("Clinic:")).toBeInTheDocument();
    expect(screen.getByText("Consultation Type:")).toBeInTheDocument();
    expect(screen.getByText("Appointment Date:")).toBeInTheDocument();
    expect(screen.getByText("Time:")).toBeInTheDocument();
    expect(screen.getByText("Appointment ID:")).toBeInTheDocument();
    expect(screen.getByText("Status:")).toBeInTheDocument();
    expect(screen.getByText("Duration:")).toBeInTheDocument();
    expect(screen.getByText("Fees:")).toBeInTheDocument();
    expect(screen.getByText("Payment Status:")).toBeInTheDocument();
    expect(screen.getByText("Booking Date:")).toBeInTheDocument();
  });
});
