import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import ScheduleAppointmentButton from "./ScheduleAppointmentButton";

describe("ScheduleAppointmentButton", () => {
  it("renders the button with correct text", () => {
    render(<ScheduleAppointmentButton />);

    expect(screen.getByText("Schedule New Appointment")).toBeInTheDocument();
  });

  it("renders as a button element", () => {
    render(<ScheduleAppointmentButton />);

    const button = screen.getByRole("button");
    expect(button).toBeInTheDocument();
  });

  it("has correct base classes", () => {
    render(<ScheduleAppointmentButton />);

    const button = screen.getByRole("button");
    expect(button).toHaveClass(
      "w-full",
      "border-2",
      "border-dashed",
      "border-[var(--red-6)]",
      "rounded-lg",
      "p-6",
      "flex",
      "flex-col",
      "items-center",
      "justify-center",
      "gap-3",
      "hover:border-[var(--red-7)]",
      "hover:bg-[var(--red-0)]",
      "transition-all",
      "duration-200",
      "cursor-pointer"
    );
  });

  it("calls onClick handler when clicked", () => {
    const mockOnClick = jest.fn();
    render(<ScheduleAppointmentButton onClick={mockOnClick} />);

    const button = screen.getByRole("button");
    fireEvent.click(button);

    expect(mockOnClick).toHaveBeenCalledTimes(1);
  });

  it("renders with custom className", () => {
    render(<ScheduleAppointmentButton className="custom-class" />);

    const button = screen.getByRole("button");
    expect(button).toHaveClass("custom-class");
  });

  it("renders the plus icon", () => {
    render(<ScheduleAppointmentButton />);

    // The PlusIcon from phosphor-icons should be present
    // We can check for the icon container
    const iconContainer = screen.getByRole("button").querySelector("div");
    expect(iconContainer).toHaveClass(
      "w-12",
      "h-12",
      "rounded-full",
      "border-2",
      "border-[var(--red-6)]"
    );
  });

  it("renders without onClick handler", () => {
    render(<ScheduleAppointmentButton />);

    const button = screen.getByRole("button");
    expect(button).toBeInTheDocument();

    // Should not throw error when clicked without handler
    expect(() => fireEvent.click(button)).not.toThrow();
  });

  it("has accessible button role", () => {
    render(<ScheduleAppointmentButton />);

    const button = screen.getByRole("button");
    expect(button).toBeInTheDocument();
  });

  it("has correct text styling", () => {
    render(<ScheduleAppointmentButton />);

    const textElement = screen.getByText("Schedule New Appointment");
    expect(textElement).toHaveClass(
      "text-[var(--red-6)]",
      "font-medium",
      "text-base"
    );
  });
});
