import { Meta, StoryObj } from "@storybook/nextjs";
import ScheduleAppointmentButton from "./ScheduleAppointmentButton";

const meta: Meta<typeof ScheduleAppointmentButton> = {
  title: "Components/Appointments/ScheduleAppointmentButton",
  component: ScheduleAppointmentButton,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
  argTypes: {
    onClick: { action: "clicked" },
  },
};

export default meta;

type Story = StoryObj<typeof ScheduleAppointmentButton>;

export const Default: Story = {
  args: {
    onClick: () => console.log("Schedule appointment button clicked"),
  },
};

export const WithCustomClassName: Story = {
  args: {
    onClick: () => console.log("Schedule appointment button clicked"),
    className: "max-w-md",
  },
};

export const WithoutClickHandler: Story = {
  args: {},
};
