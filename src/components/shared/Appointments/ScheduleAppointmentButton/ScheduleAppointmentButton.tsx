import React from "react";
import { PlusIcon } from "@phosphor-icons/react";
import { useScreenWidth } from "../../../../hooks/useScreenWidth";

interface ScheduleAppointmentButtonProps {
  onClick?: () => void;
  className?: string;
}

const ScheduleAppointmentButton: React.FC<ScheduleAppointmentButtonProps> = ({
  onClick,
  className = "",
}) => {
  const screenWidth = useScreenWidth();
  const iconSize = screenWidth < 768 ? 27.63 : 35.75;

  return (
    <button
      onClick={onClick}
      className={`w-[22.625rem] h-[6.813rem] md:h-[12.19rem] md:w-[13.625rem] xl:w-[16.375rem] border-2 border-dashed border-[var(--primary-pink)] rounded-[0.5rem] py-5 px-7.5 flex flex-col items-center justify-center gap-4 hover:bg-[var(--red-1)] transition-all duration-200 cursor-pointer ${className}`}
    >
      <div className="w-8.5 h-8.5 md:w-11 md:h-11 rounded-full border-2 border-[var(--primary-pink)] flex items-center justify-center">
        <PlusIcon
          size={iconSize}
          className="text-[var(--primary-pink)]"
          weight="bold"
        />
      </div>
      <span className="w-full text-center text-[var(--primary-pink)] font-medium text-base xl:whitespace-nowrap">
        Schedule New Appointment
      </span>
    </button>
  );
};

export default ScheduleAppointmentButton;
