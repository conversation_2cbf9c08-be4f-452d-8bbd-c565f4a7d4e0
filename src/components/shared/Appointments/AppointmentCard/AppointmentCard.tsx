import Image from "next/image";
import React from "react";
import {
  CalendarCheckIcon,
  ClockIcon,
  MoneyWavyIcon,
} from "@phosphor-icons/react";
import Card from "../../Card";

interface AppointmentCardProps {
  doctorImageUrl: string;
  doctorName: string;
  clinicName: string;
  clinicLocation: string;
  appointmentDate: string;
  appointmentTime: string;
  consultationFee: number;
  onReschedule: () => void;
  onCancel: () => void;
  className?: string;
}

const AppointmentCard: React.FC<AppointmentCardProps> = ({
  doctorImageUrl,
  doctorName,
  clinicName,
  clinicLocation,
  appointmentDate,
  appointmentTime,
  consultationFee,
  onReschedule,
  onCancel,
  className,
}) => {
  return (
    <Card className={`px-7.5 py-5 flex flex-col gap-4 ${className}`}>
      {/* Doctor Info Section */}
      <div className="w-full flex items-center gap-3">
        <Image
          src={doctorImageUrl}
          alt={doctorName}
          width={55}
          height={55}
          className="rounded-full object-cover"
        />
        <div className="flex flex-col gap-0.5">
          <h3 className="font-bold text-[var(--grey-7)] text-lg xl:text-xl">
            {doctorName}
          </h3>
          <p className="text-[var(--grey-6)] text-sm xl:text-base font-medium">
            {clinicName}, {clinicLocation}
          </p>
        </div>
      </div>

      {/* Appointment Details */}
      <div className="flex items-center justify-start gap-3 xl:gap-4">
        <div className="flex items-center gap-2">
          <CalendarCheckIcon
            size={16}
            weight="bold"
            className="text-[var(--violet-11)]"
          />
          <span className="text-[var(--grey-7)] text-sm xl:text-base font-medium whitespace-nowrap">
            {appointmentDate}
          </span>
        </div>
        <div className="w-px h-4 bg-[var(--grey-3)]"></div>
        <div className="flex items-center gap-2">
          <ClockIcon
            size={16}
            weight="bold"
            className="text-[var(--violet-11)]"
          />
          <span className="text-[var(--grey-7)] text-sm xl:text-base font-medium whitespace-nowrap">
            {appointmentTime}
          </span>
        </div>
      </div>

      <div className="flex items-center gap-2">
        <MoneyWavyIcon
          size={16}
          weight="bold"
          className="text-[var(--violet-11)]"
        />
        <span className="text-[var(--grey-7)] text-sm xl:text-base font-medium whitespace-nowrap">
          ₹{consultationFee} Consultation fee at clinic
        </span>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-around gap-4">
        <button
          onClick={onReschedule}
          className="text-[var(--primary-pink)] text-base font-medium cursor-pointer py-3 px-5"
        >
          Reschedule
        </button>
        <button
          onClick={onCancel}
          className="text-[var(--grey-6)] text-sm font-medium cursor-pointer py-3 px-5"
        >
          Cancel Appointment
        </button>
      </div>
    </Card>
  );
};

export default AppointmentCard;
