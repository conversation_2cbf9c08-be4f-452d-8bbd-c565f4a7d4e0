import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import AppointmentCard from "./AppointmentCard";

describe("AppointmentCard", () => {
  const mockProps = {
    doctorImageUrl: "/test-image.jpg",
    doctorName: "Dr. <PERSON><PERSON><PERSON>",
    clinicName: "Gunjan IVF Clinic",
    clinicLocation: "Vijay Nagar Ghaziabad",
    appointmentDate: "20 July 2025",
    appointmentTime: "03:00 PM - 03:30 PM",
    onReschedule: jest.fn(),
    onCancel: jest.fn(),
  };

  it("renders appointment details correctly", () => {
    render(<AppointmentCard {...mockProps} />);

    // Check if doctor image is rendered
    expect(screen.getByAltText("Dr. Neh<PERSON> Bansal")).toBeInTheDocument();

    // Check if doctor name is rendered
    expect(screen.getByText("Dr. <PERSON>eh<PERSON> Bansal")).toBeInTheDocument();

    // Check if clinic details are rendered
    expect(
      screen.getByText("Gunjan IVF Clinic, Vijay Nagar Ghaziabad")
    ).toBeInTheDocument();

    // Check if appointment date is rendered
    expect(screen.getByText("20 July 2025")).toBeInTheDocument();

    // Check if appointment time is rendered
    expect(screen.getByText("03:00 PM - 03:30 PM")).toBeInTheDocument();
  });

  it("renders action buttons correctly", () => {
    render(<AppointmentCard {...mockProps} />);

    // Check if reschedule button is rendered
    expect(screen.getByText("Reschedule")).toBeInTheDocument();

    // Check if cancel button is rendered
    expect(screen.getByText("Cancel Appointment")).toBeInTheDocument();
  });

  it("calls onReschedule when reschedule button is clicked", () => {
    render(<AppointmentCard {...mockProps} />);

    const rescheduleButton = screen.getByText("Reschedule");
    fireEvent.click(rescheduleButton);

    expect(mockProps.onReschedule).toHaveBeenCalledTimes(1);
  });

  it("calls onCancel when cancel button is clicked", () => {
    render(<AppointmentCard {...mockProps} />);

    const cancelButton = screen.getByText("Cancel Appointment");
    fireEvent.click(cancelButton);

    expect(mockProps.onCancel).toHaveBeenCalledTimes(1);
  });
});
