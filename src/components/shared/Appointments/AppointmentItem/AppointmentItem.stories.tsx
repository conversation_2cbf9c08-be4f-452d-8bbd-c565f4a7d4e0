import { <PERSON>a, StoryObj } from "@storybook/nextjs";
import AppointmentItem from "./AppointmentItem";
import { AppointmentStatus } from "../AppointmentStatus/AppointmentStatus";

const meta: Meta<typeof AppointmentItem> = {
  title: "Components/Appointments/AppointmentItem",
  component: AppointmentItem,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
};

export default meta;

type Story = StoryObj<typeof AppointmentItem>;

export const Upcoming: Story = {
  args: {
    doctorImageUrl: "https://randomuser.me/api/portraits/women/44.jpg",
    doctorName: "Dr. Preety Chaudhary",
    specialization: "Reproductive Endocrinologist",
    appointmentDate: "20 July 2025",
    appointmentTime: "03:00 PM - 03:30 PM",
    clinicLocation: "Gunjan IVF Clinic, Vijay Nagar Ghaziabad",
    status: AppointmentStatus.UPCOMING,
    onDetailsClick: () =>
      console.log("Details clicked for upcoming appointment"),
  },
};

export const Completed: Story = {
  args: {
    doctorImageUrl: "https://randomuser.me/api/portraits/women/45.jpg",
    doctorName: "Dr. A<PERSON>i <PERSON>hra",
    specialization: "Fertility Consultant, Obstetrician",
    appointmentDate: "08 July 2025",
    appointmentTime: "12:00 PM - 12:30 PM",
    clinicLocation: "Gunjan IVF Clinic, Meerut",
    status: AppointmentStatus.COMPLETED,
    onDetailsClick: () =>
      console.log("Details clicked for completed appointment"),
  },
};

export const Cancelled: Story = {
  args: {
    doctorImageUrl: "https://randomuser.me/api/portraits/men/46.jpg",
    doctorName: "Dr. Naresh Mehta",
    specialization: "IVF & IUI Expert",
    appointmentDate: "12 June 2025",
    appointmentTime: "10:00 PM - 10:30 PM",
    clinicLocation: "Gunjan IVF Clinic, Jankpuri, Delhi",
    status: AppointmentStatus.CANCELLED,
    onDetailsClick: () =>
      console.log("Details clicked for cancelled appointment"),
  },
};
