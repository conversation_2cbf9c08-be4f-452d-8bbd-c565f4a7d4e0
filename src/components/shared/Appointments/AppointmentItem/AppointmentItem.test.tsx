import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import AppointmentItem from "./AppointmentItem";
import { AppointmentStatus } from "../AppointmentStatus/AppointmentStatus";

describe("AppointmentItem", () => {
  const mockProps = {
    doctorImageUrl: "/test-doctor.jpg",
    doctorName: "Dr. Preety Cha<PERSON>har<PERSON>",
    specialization: "Reproductive Endocrinologist",
    appointmentDate: "20 July 2025",
    appointmentTime: "03:00 PM - 03:30 PM",
    clinicLocation: "Gunjan IVF Clinic, Vijay Nagar Ghaziabad",
    status: AppointmentStatus.UPCOMING,
    onDetailsClick: jest.fn(),
  };

  it("renders doctor information correctly", () => {
    render(<AppointmentItem {...mockProps} />);

    expect(screen.getByAltText("Dr. Preety Chaudhary")).toBeInTheDocument();
    expect(screen.getByText("Dr. Preety Chaudhary")).toBeInTheDocument();
    expect(
      screen.getByText("Reproductive Endocrinologist")
    ).toBeInTheDocument();
  });

  it("renders appointment details correctly", () => {
    render(<AppointmentItem {...mockProps} />);

    expect(screen.getByText("20 July 2025")).toBeInTheDocument();
    expect(screen.getByText("03:00 PM - 03:30 PM")).toBeInTheDocument();
    expect(
      screen.getByText("Gunjan IVF Clinic, Vijay Nagar Ghaziabad")
    ).toBeInTheDocument();
  });

  it("renders status badge correctly", () => {
    render(<AppointmentItem {...mockProps} />);

    expect(screen.getByText("Upcoming")).toBeInTheDocument();
  });

  it("renders details button", () => {
    render(<AppointmentItem {...mockProps} />);

    expect(screen.getByText("Details")).toBeInTheDocument();
  });

  it("calls onDetailsClick when details button is clicked", () => {
    render(<AppointmentItem {...mockProps} />);

    const detailsButton = screen.getByText("Details");
    fireEvent.click(detailsButton);

    expect(mockProps.onDetailsClick).toHaveBeenCalledTimes(1);
  });

  it("applies correct status styles for upcoming", () => {
    render(<AppointmentItem {...mockProps} />);

    const statusBadge = screen.getByText("Upcoming");
    expect(statusBadge).toHaveClass("bg-blue-50", "text-blue-600");
  });

  it("applies correct status styles for completed", () => {
    render(
      <AppointmentItem {...mockProps} status={AppointmentStatus.COMPLETED} />
    );

    const statusBadge = screen.getByText("Completed");
    expect(statusBadge).toHaveClass("bg-green-50", "text-green-600");
  });

  it("applies correct status styles for cancelled", () => {
    render(
      <AppointmentItem {...mockProps} status={AppointmentStatus.CANCELLED} />
    );

    const statusBadge = screen.getByText("Cancelled");
    expect(statusBadge).toHaveClass("bg-red-50", "text-red-600");
  });

  it("renders with custom className", () => {
    const { container } = render(
      <AppointmentItem {...mockProps} className="custom-class" />
    );

    expect(container.firstChild).toHaveClass("custom-class");
  });

  it("handles missing onDetailsClick prop", () => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { onDetailsClick, ...propsWithoutClick } = mockProps;

    render(<AppointmentItem {...propsWithoutClick} />);

    const detailsButton = screen.getByText("Details");
    fireEvent.click(detailsButton);

    // Should not throw an error when onDetailsClick is not provided
    expect(detailsButton).toBeInTheDocument();
  });
});
