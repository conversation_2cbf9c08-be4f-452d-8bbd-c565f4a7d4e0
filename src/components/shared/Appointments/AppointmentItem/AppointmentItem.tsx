import Image from "next/image";
import React from "react";
import {
  CalendarCheckIcon,
  ClockIcon,
  MapPinSimpleIcon,
} from "@phosphor-icons/react";
import AppointmentStatusComponent, {
  AppointmentStatus,
} from "../AppointmentStatus/AppointmentStatus";
import Card from "../../Card";

interface AppointmentItemProps {
  doctorImageUrl: string;
  doctorName: string;
  specialization: string;
  appointmentDate: string;
  appointmentTime: string;
  clinicLocation: string;
  status: AppointmentStatus;
  onDetailsClick?: () => void;
  className?: string;
}

const AppointmentItem: React.FC<AppointmentItemProps> = ({
  doctorImageUrl,
  doctorName,
  specialization,
  appointmentDate,
  appointmentTime,
  clinicLocation,
  status,
  onDetailsClick,
  className,
}) => {
  return (
    <Card
      className={`h-auto xl:h-[5.938rem] p-5 xl:py-5 xl:px-7.5 ${className} flex flex-col md:flex-row items-center gap-2 md:gap-[21px] xl:gap-27.25`}
    >
      <div className="flex flex-col w-full xl:flex-row items-start xl:items-center gap-4 xl:gap-22.5">
        {/* Doctor Information */}
        <div className="flex gap-3 items-center">
          <Image
            src={doctorImageUrl}
            alt={doctorName}
            width={55}
            height={55}
            className="rounded-full object-cover"
          />
          <div className="w-full flex flex-col justify-center items-start">
            <h3 className="w-full font-bold text-[var(--grey-7)] text-xl">
              {doctorName}
            </h3>
            <p className="max-w-[13.75rem] overflow-hidden text-ellipsis text-[var(--grey-6)] font-medium text-base whitespace-nowrap">
              {specialization}
            </p>
          </div>
        </div>

        {/* Appointment Details */}
        <div className="w-full md:w-auto flex flex-col md:flex-row gap-3 md:gap-4 xl:gap-22.5">
          {/* Date and Time */}
          <div className="w-full flex flex-col gap-2 flex-1">
            <div className="flex items-center gap-2">
              <CalendarCheckIcon
                size={16}
                className="text-[var(--violet-11)]"
                weight="bold"
              />
              <span className="text-[var(--grey-7)] text-sm xl:text-base font-medium whitespace-nowrap">
                {appointmentDate}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <ClockIcon
                size={16}
                className="text-[var(--violet-11)]"
                weight="bold"
              />
              <span className="text-[var(--grey-7)] text-sm xl:text-base font-medium whitespace-nowrap">
                {appointmentTime}
              </span>
            </div>
          </div>

          {/* Clinic Location */}
          <div className="w-60">
            <div className="flex items-center gap-2">
              <MapPinSimpleIcon
                size={24}
                className="text-[var(--violet-11)] flex-shrink-0"
                weight="bold"
              />
              <span className="text-[var(--grey-6)] text-sm xl:text-base font-medium line-clamp-2 max-h-[3rem] overflow-hidden">
                {clinicLocation}
              </span>
            </div>
          </div>
        </div>
      </div>
      {/* Status and Actions */}
      <div className="flex items-center justify-between md:flex-col xl:flex-row md:gap-10 xl:justify-between xl:gap-22.5 w-full xl:w-auto">
        <AppointmentStatusComponent status={status} />
        <button
          onClick={onDetailsClick}
          className="text-[var(--grey-7)] text-base font-medium underline hover:text-[var(--red-6)] transition-colors cursor-pointer"
        >
          Details
        </button>
      </div>
    </Card>
  );
};

export default AppointmentItem;
