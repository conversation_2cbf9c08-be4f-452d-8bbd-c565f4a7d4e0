import React, { useEffect, useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import StepHeader from "@/components/shared/StepHeader/StepHeader";
import Button, { ButtonType } from "@/components/shared/Button/Button";
import ToggleButton from "@/components/shared/ToggleButton/ToggleButton";
import {
  ArrowLeftIcon,
  CaretDownIcon,
  CaretLeftIcon,
  CaretRightIcon,
} from "@phosphor-icons/react";
import { usePageHeader } from "@/contexts/PageHeaderContext";
import { useBreadcrumb } from "@/contexts/BreadcrumbContext";
import { useAppointmentBooking as useAppointmentBookingContext } from "@/contexts/AppointmentBookingContext";
import { useAppointmentBooking } from "@/hooks/useAppointmentBooking";
import { useTimeSlots } from "@/hooks/useTimeSlots";
import { useDoctorDetails } from "@/hooks/useDoctorDetails";
import DoctorProfileCard from "@/components/shared/DoctorProfileCard/DoctorProfileCard";
import AppointmentModal, { AppointmentModalType } from "@/components/shared/Appointments/AppointmentModal/AppointmentModal";
import { TimeSlot } from "@/types/api/public/api-timeslots";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ShadcnUI/popover";
import { format } from "date-fns";
import ToggleButtonShimmer from "@/components/Shimmer/ToggleButtonShimmer";
import { useScreenWidth } from "@/hooks/useScreenWidth";

interface TransformedTimeSlot {
  id: number;
  time: string;
  duration?: number;
}

export interface SelectDateTimeProps {
  doctorId: number;
  clinicId: number;
  onDateTimeSelect: (date: string, time: string) => void;
  onBack: () => void;
  onCancel: () => void;
  onConfirm: () => void;
  selectedDate?: string;
  selectedTime?: string;
}

const SelectDateTime: React.FC<SelectDateTimeProps> = ({
  doctorId,
  clinicId,
  onDateTimeSelect,
  onBack,
  onCancel,
  onConfirm,
  selectedDate,
  selectedTime,
}) => {
  const screenWidth = useScreenWidth();
  const [selectedDateState, setSelectedDateState] = useState<
    string | undefined
  >(selectedDate);
  const [selectedTimeState, setSelectedTimeState] = useState<
    string | undefined
  >(selectedTime);

  // Modal state
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalType, setModalType] = useState<AppointmentModalType>(AppointmentModalType.CONFIRM);

  // Fetch doctor details for modal
  const { doctor, loading: doctorLoading } = useDoctorDetails(doctorId);

  // State for date navigation
  const [currentStartDate, setCurrentStartDate] = useState<string>(() => {
    const today = new Date();
    return today.toISOString().split("T")[0];
  });
  const [navigationHistory, setNavigationHistory] = useState<string[]>([]);
  const [displayDate, setDisplayDate] = useState<string | undefined>(
    new Date().toISOString().split("T")[0]
  );
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);

  // Tooltip states for navigation buttons
  const [leftTooltipOpen, setLeftTooltipOpen] = useState(false);
  const [rightTooltipOpen, setRightTooltipOpen] = useState(false);
  const [disabledDateTooltips, setDisabledDateTooltips] = useState<
    Record<string, boolean>
  >({});

  const router = useRouter();
  const { setTitle, setSubtitle } = usePageHeader();
  const { setBreadcrumbs } = useBreadcrumb();
  const { setAppointmentData } = useAppointmentBookingContext();
  
  // Use appointment booking hook
  const { bookAppointment, isBooking } = useAppointmentBooking();

  const {
    dateSlots,
    loading: slotsLoading,
    error: slotsError,
  } = useTimeSlots(doctorId, clinicId, currentStartDate);

  // Create a map of available dates with their slots for easy lookup
  const groupedByDate = dateSlots.reduce(
    (acc, dateGroup) => {
      acc[dateGroup.date] = dateGroup.available_slots;
      return acc;
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    {} as Record<string, any[]>
  );

  useEffect(() => {
    setTitle(null);
    setSubtitle(null);
    setBreadcrumbs([
      { label: "My Appointments", href: "/user/appointments" },
      { label: "Schedule Appointments", isActive: true },
    ]);
  }, [setTitle, setSubtitle, setBreadcrumbs]);

  // Transform dates from API response
  const generateDates = useCallback(() => {
    const today = new Date().toISOString().split("T")[0];

    // Use the 7 days directly from the API response
    return dateSlots.map((dateGroup) => {
      const date = new Date(dateGroup.date);
      const hasSlots = dateGroup.available_slots && dateGroup.available_slots.length > 0;
      const isDoctorAvailable = dateGroup.status === "available";

      return {
        date: dateGroup.date,
        display: `${date.getDate()} ${date.toLocaleDateString("en-US", { month: "short" })}`,
        day: date.toLocaleDateString("en-US", { weekday: "short" }),
        isToday: dateGroup.date === today,
        hasSlots,
        isDoctorAvailable,
      };
    });
  }, [dateSlots]);

  const dates = generateDates();

  // Auto-select first available date when dates change or on initial render
  useEffect(() => {
    if (dates.length > 0) {
      const firstAvailableDate = dates.find((date) => date.hasSlots);
      if (firstAvailableDate && !selectedDateState) {
        setSelectedDateState(firstAvailableDate.date);
      }
    }
  }, [dates, selectedDateState]);

  // Get time slots for selected date
  const getTimeSlotsForDate = (date: string) => {
    const slots = groupedByDate[date] || [];
    return slots;
  };

  const handleDateSelect = (date: string) => {
    setSelectedDateState(date);
    // Clear selected time when date changes
    setSelectedTimeState(undefined);
    if (selectedTimeState) {
      onDateTimeSelect(date, selectedTimeState);
    }
  };

  const handleCalendarDateSelect = (date: Date | undefined) => {
    if (date) {
      // Create a new date at noon local time to avoid timezone issues
      const localDate = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate(),
        12,
        0,
        0
      );
      const dateString = localDate.toISOString().split("T")[0];

      // Set the selected date from calendar as the starting date for the 7-day period
      setCurrentStartDate(dateString);
      setDisplayDate(dateString);

      // Clear navigation history as we're jumping to a specific date
      setNavigationHistory([]);

      // Clear selected date/time initially - let useEffect handle first available date selection
      setSelectedDateState(undefined);
      setSelectedTimeState(undefined);

      setIsCalendarOpen(false);
    }
  };

  const handleTimeSelect = (time: string) => {
    setSelectedTimeState(time);
    if (selectedDateState) {
      onDateTimeSelect(selectedDateState, time);
    }
  };

  // Modal handlers
  const handleConfirmClick = () => {
    if (selectedDateState && selectedTimeState) {
      setIsModalOpen(true);
      setModalType(AppointmentModalType.CONFIRM);
    }
  };

  const handleModalConfirm = async () => {
    if (selectedDateState && selectedTimeState && doctor) {
      // Find the selected slot to get the full slot data
      const selectedSlot = getTimeSlotsForDate(selectedDateState).find(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (slot: any) => slot.start_time === selectedTimeState
      );

      if (selectedSlot) {
        try {
          // Book the appointment directly
          const result = await bookAppointment({
            doctor_id: doctorId,
            clinic_id: clinicId,
            start_time: selectedSlot.start_time,
            end_time: selectedSlot.end_time,
            duration: selectedSlot.duration,
            appointment_date: selectedDateState, // Include the selected date
          });

          if (result.success) {
            // Show success modal without closing
            setModalType(AppointmentModalType.CONFIRMED);
          } else {
            alert(`Failed to book appointment: ${result.error}`);
          }
        } catch (error) {
          console.error("Error booking appointment:", error);
          alert(`Failed to book appointment: ${error instanceof Error ? error.message : "Unknown error"}`);
        }
      }
    }
  };

  const handleModalBack = () => {
    setIsModalOpen(false);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    // If we're in confirmed state, navigate to appointments page
    if (modalType === AppointmentModalType.CONFIRMED) {
      router.push("/user/appointments");
    }
  };

  const handleTodayClick = () => {
    const today = new Date().toISOString().split("T")[0];

    // Always navigate to today as the start of the 7-day period
    setCurrentStartDate(today);
    setDisplayDate(today);
    setNavigationHistory([]);

    // Clear selected date/time initially - let useEffect handle first available date selection
    setSelectedDateState(undefined);
    setSelectedTimeState(undefined);
  };

  // Navigation functions
  const handleNextWeek = useCallback(() => {
    const currentStart = new Date(currentStartDate);
    const nextStart = new Date(currentStart);
    nextStart.setDate(currentStart.getDate() + 7);

    // Add current start date to navigation history
    setNavigationHistory((prev) => [...prev, currentStartDate]);
    setCurrentStartDate(nextStart.toISOString().split("T")[0]);
    setDisplayDate(nextStart.toISOString().split("T")[0]);

    // Clear selected date/time when navigating to new week
    // The useEffect will automatically select the first available date
    setSelectedDateState(undefined);
    setSelectedTimeState(undefined);
  }, [currentStartDate]);

  const handlePreviousWeek = useCallback(() => {
    if (navigationHistory.length > 0) {
      // Go back to previous week from history
      const previousStartDate = navigationHistory[navigationHistory.length - 1];
      setNavigationHistory((prev) => prev.slice(0, -1));
      setCurrentStartDate(previousStartDate);
      setDisplayDate(previousStartDate);
      // Clear selected date/time when navigating to new week
      // The useEffect will automatically select the first available date
      setSelectedDateState(undefined);
      setSelectedTimeState(undefined);
    }
  }, [navigationHistory]);

  // Check if we're on the initial week (today's week)
  const isOnInitialWeek = useCallback(() => {
    const today = new Date().toISOString().split("T")[0];
    const todayDate = new Date(today);
    const currentStart = new Date(currentStartDate);

    // Check if current start date is within the same week as today
    const daysDiff = Math.floor(
      (currentStart.getTime() - todayDate.getTime()) / (1000 * 60 * 60 * 24)
    );
    return daysDiff >= 0 && daysDiff < 7;
  }, [currentStartDate]);

  // Check if next week navigation should be disabled (more than 2 months from today)
  const isNextWeekDisabled = useCallback(() => {
    const today = new Date();
    const startDate = new Date(currentStartDate);
    const endDate = new Date(startDate);
    endDate.setDate(startDate.getDate() + 6); // 7 days total (including start date)
    const twoMonthsFromToday = new Date(today);
    twoMonthsFromToday.setMonth(today.getMonth() + 2);

    return endDate > twoMonthsFromToday;
  }, [currentStartDate]);

  const isDateSelected = (date: string) => selectedDateState === date;
  const isTimeSelected = (time: string) => selectedTimeState === time;

  // Handle tooltip interactions for disabled buttons
  const handleDisabledLeftClick = () => {
    if (screenWidth <= 1024) {
      // Mobile
      setLeftTooltipOpen(!leftTooltipOpen);
      // Auto close after 3 seconds
      setTimeout(() => setLeftTooltipOpen(false), 3000);
    }
  };

  const handleDisabledRightClick = () => {
    if (screenWidth <= 1024) {
      // Mobile
      setRightTooltipOpen(!rightTooltipOpen);
      // Auto close after 3 seconds
      setTimeout(() => setRightTooltipOpen(false), 3000);
    }
  };

  // Fixed mobile tooltip handler for disabled dates
  const handleDisabledDateClick = (date: string) => {
    if (screenWidth <= 1024) {
      // Show tooltip for mobile
      setDisabledDateTooltips((prev) => ({ ...prev, [date]: true }));

      // Auto close after 3 seconds
      setTimeout(() => {
        setDisabledDateTooltips((prev) => ({ ...prev, [date]: false }));
      }, 3000);
    }
  };

  const handleDisabledDateMouseEnter = (date: string) => {
    if (screenWidth > 1024) {
      setDisabledDateTooltips((prev) => ({ ...prev, [date]: true }));
    }
  };

  const handleDisabledDateMouseLeave = (date: string) => {
    if (screenWidth > 1024) {
      setDisabledDateTooltips((prev) => ({ ...prev, [date]: false }));
    }
  };

  return (
    <div className="w-full flex flex-col-reverse xl:flex-row gap-7.5 md:gap-8 xl:gap-10">
      <div>
        <DoctorProfileCard doctorId={doctorId} className="!border-none" />
        {screenWidth <= 1024 && (
          <div className="flex flex-col-reverse md:flex-row items-center justify-between gap-4 pt-4">
            <Button
              type={ButtonType.SECONDARY}
              text="Back"
              icon={<ArrowLeftIcon size={20} />}
              onClick={onBack}
              className="!flex !flex-row-reverse"
            />

            <Button
              type={ButtonType.SECONDARY}
              text="Cancel"
              onClick={onCancel}
            />

            <Button
              type={ButtonType.PRIMARY}
              text="Confirm Appointment"
              onClick={handleConfirmClick}
              disabled={!selectedDateState || !selectedTimeState}
            />
          </div>
        )}
      </div>
      <div className="flex flex-col w-full gap-7.5 md:gap-8 xl:gap-10">
        <div className="flex flex-col items-center gap-2">
          <StepHeader
            currentStep={4}
            totalSteps={4}
            title="Choose Date & Time"
            className="!mb-0"
          />

          <p className="text-center text-[var(--grey-6)] text-base font-medium">
            Choose your preferred date & time
          </p>
        </div>

        <div className="flex flex-col gap-7.5 md:gap-8 xl:gap-10 w-full">
          {/* Date Selection Section */}
          <div className="flex flex-col gap-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2.25 md:gap-8">
                <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
                  <PopoverTrigger asChild>
                    <button className="cursor-pointer flex items-center gap-2 text-[var(--grey-7)] text-base font-medium hover:text-[var(--grey-8)] transition-colors">
                      <span className="w-max text-xl font-bold text-[var(--grey-7)]">
                        {displayDate
                          ? new Date(displayDate).toLocaleDateString("en-US", {
                              day: "numeric",
                              month: "short",
                            }) +
                            ", " +
                            new Date(displayDate).toLocaleDateString("en-US", {
                              weekday: "short",
                            })
                          : "Select a date"}
                      </span>
                      <CaretDownIcon
                        size={20}
                        weight="bold"
                        className="text-[var(--grey-7)]"
                      />
                    </button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={
                        selectedDateState
                          ? new Date(selectedDateState)
                          : undefined
                      }
                      onSelect={handleCalendarDateSelect}
                      disabled={(date) => {
                        const today = new Date();
                        const twoMonthsFromToday = new Date(today);
                        twoMonthsFromToday.setMonth(today.getMonth() + 2);
                        return date < today || date > twoMonthsFromToday;
                      }}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <Button
                  type={ButtonType.SECONDARY}
                  text="Today"
                  onClick={handleTodayClick}
                  size="sm"
                  className="!px-5 !py-2.5 text-base font-bold text-[var(--grey-7)] border-none rounded-[8px]"
                />
              </div>
              <div className="flex items-center gap-2">
                {/* Previous Week Button with Tooltip */}
                <Popover
                  open={leftTooltipOpen}
                  onOpenChange={setLeftTooltipOpen}
                >
                  <PopoverTrigger asChild>
                    <button
                      className={`w-[38.5px] h-[39px] py-2.75 px-3.25 flex items-center justify-center border border-[var(--grey-3)] rounded-[4px] ${
                        navigationHistory.length === 0
                          ? "opacity-50 cursor-not-allowed"
                          : "hover:bg-[var(--grey-2)] cursor-pointer"
                      }`}
                      onClick={
                        navigationHistory.length === 0
                          ? handleDisabledLeftClick
                          : handlePreviousWeek
                      }
                      onMouseEnter={() => {
                        if (
                          screenWidth > 1024 &&
                          navigationHistory.length === 0
                        ) {
                          setLeftTooltipOpen(true);
                        }
                      }}
                      onMouseLeave={() => {
                        if (screenWidth > 1024) {
                          setLeftTooltipOpen(false);
                        }
                      }}
                    >
                      <CaretLeftIcon
                        size={16}
                        weight="bold"
                        className="text-[var(--grey-7)]"
                      />
                    </button>
                  </PopoverTrigger>
                  {navigationHistory.length === 0 && (
                    <PopoverContent
                      className="w-auto p-2 text-sm"
                      side="bottom"
                    >
                      You cannot book appointments for past dates
                    </PopoverContent>
                  )}
                </Popover>

                {/* Next Week Button with Tooltip */}
                <Popover
                  open={rightTooltipOpen}
                  onOpenChange={setRightTooltipOpen}
                >
                  <PopoverTrigger asChild>
                    <button
                      className={`w-[38.5px] h-[39px] py-2.75 px-3.25 flex items-center justify-center border border-[var(--grey-3)] rounded-[4px] ${
                        isNextWeekDisabled()
                          ? "opacity-50 cursor-not-allowed"
                          : "hover:bg-[var(--grey-2)] cursor-pointer"
                      }`}
                      onClick={
                        isNextWeekDisabled()
                          ? handleDisabledRightClick
                          : handleNextWeek
                      }
                      onMouseEnter={() => {
                        if (screenWidth > 1024 && isNextWeekDisabled()) {
                          setRightTooltipOpen(true);
                        }
                      }}
                      onMouseLeave={() => {
                        if (screenWidth > 1024) {
                          setRightTooltipOpen(false);
                        }
                      }}
                    >
                      <CaretRightIcon
                        size={16}
                        weight="bold"
                        className="text-[var(--grey-7)]"
                      />
                    </button>
                  </PopoverTrigger>
                  {isNextWeekDisabled() && (
                    <PopoverContent
                      className="w-auto p-2 text-sm"
                      side="bottom"
                    >
                      Appointments cannot be booked more than 2 months prior
                    </PopoverContent>
                  )}
                </Popover>
              </div>
            </div>

            <div className="flex flex-col gap-2">
              <label className="text-[var(--grey-6)] text-sm font-medium">
                What time best works for you
              </label>
              <div className="grid grid-cols-3 md:grid-cols-5 xl:grid-cols-7 gap-4 md:gap-5.25">
                {dates.map((dateInfo) => {
                  const isDisabled = !dateInfo.hasSlots;
                  const tooltipMessage = dateInfo.isDoctorAvailable
                    ? "Slots are fully booked for this date"
                    : "Doctor is not available on this date";

                  return (
                    <Popover
                      key={dateInfo.date}
                      open={isDisabled && disabledDateTooltips[dateInfo.date]}
                      onOpenChange={(open) => {
                        if (isDisabled) {
                          setDisabledDateTooltips((prev) => ({
                            ...prev,
                            [dateInfo.date]: open,
                          }));
                        }
                      }}
                    >
                      <PopoverTrigger asChild>
                        <div
                          className="cursor-pointer"
                          onMouseEnter={
                            isDisabled && screenWidth > 1024
                              ? () =>
                                  handleDisabledDateMouseEnter(dateInfo.date)
                              : undefined
                          }
                          onMouseLeave={
                            isDisabled && screenWidth > 1024
                              ? () =>
                                  handleDisabledDateMouseLeave(dateInfo.date)
                              : undefined
                          }
                          onClick={
                            isDisabled
                              ? () => handleDisabledDateClick(dateInfo.date)
                              : () => handleDateSelect(dateInfo.date)
                          }
                        >
                          <ToggleButton
                            isSelected={isDateSelected(dateInfo.date)}
                            onClick={() => {}} // Empty onClick since we handle it in the parent div
                            variant="compact"
                            className={`text-sm !py-3.5 pointer-events-none ${
                              isDisabled ? "opacity-50 cursor-not-allowed" : ""
                            }`}
                            disabled={isDisabled}
                          >
                            <div className="flex flex-col items-center gap-1">
                              <span className="text-xs">{dateInfo.day}</span>
                              <span className="text-base font-bold">
                                {dateInfo.display}
                              </span>
                            </div>
                          </ToggleButton>
                        </div>
                      </PopoverTrigger>
                      {isDisabled && (
                        <PopoverContent
                          className="w-auto p-2 text-sm"
                          side="bottom"
                        >
                          {tooltipMessage}
                        </PopoverContent>
                      )}
                    </Popover>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Time Selection Section */}
          <div className="flex flex-col gap-4">
            <label className="text-[var(--grey-6)] text-sm font-medium">
              Select time slot for you
            </label>
            {slotsLoading ? (
              <ToggleButtonShimmer
                count={12}
                variant="default"
                gridCols={3}
                gridColsMd={6}
                className="!p-8"
              />
            ) : slotsError ? (
              <div className="text-center py-8 text-red-600">
                Error loading time slots: {slotsError}
              </div>
            ) : selectedDateState ? (
              <div className="grid grid-cols-3 md:grid-cols-6 gap-3 md:gap-4.25">
                {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
                {getTimeSlotsForDate(selectedDateState).map((slot: any) => (
                  <ToggleButton
                    key={slot.id}
                    isSelected={isTimeSelected(slot.start_time)}
                    onClick={() => handleTimeSelect(slot.start_time)}
                    variant="compact"
                    className="!py-3.5"
                  >
                    <div className="flex flex-col items-center">
                      <span>{slot.start_time_display}</span>
                      {slot.duration && (
                        <span
                          className={`text-xs text-[var(--violet-11)] ${
                            isTimeSelected(slot.start_time) && "text-white"
                          }`}
                        >
                          {slot.duration} mins
                        </span>
                      )}
                    </div>
                  </ToggleButton>
                ))}
                {getTimeSlotsForDate(selectedDateState).length === 0 && (
                  <div className="col-span-full text-center py-4 text-[var(--grey-6)]">
                    No available time slots for this date
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8 text-[var(--grey-6)]">
                Please select a date to view available time slots
              </div>
            )}
          </div>

          {/* Action Buttons */}
          {screenWidth > 1024 && (
            <div className="flex items-center justify-between gap-4 pt-4">
              <Button
                type={ButtonType.SECONDARY}
                text="Back"
                icon={<ArrowLeftIcon size={20} />}
                onClick={onBack}
                className="!flex !flex-row-reverse !px-15 !w-fit"
              />

              <div className="flex items-center gap-10.5">
                <Button
                  type={ButtonType.SECONDARY}
                  text="Cancel"
                  onClick={onCancel}
                  className="!px-15 !w-fit text-[var(--violet-11)]"
                />

                <Button
                  type={ButtonType.PRIMARY}
                  text="Confirm Appointment"
                  onClick={handleConfirmClick}
                  disabled={!selectedDateState || !selectedTimeState}
                  className="!w-fit"
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Appointment Confirmation Modal */}
      {doctor && selectedDateState && selectedTimeState && (
        <AppointmentModal
          isOpen={isModalOpen}
          type={modalType}
          appointmentData={{
            doctorImageUrl: doctor.profile.profile_image || "/assets/avatar.jpg",
            doctorName: doctor.profile.display_name || "Dr. Unknown",
            clinicName: doctor.doctor_clinics?.[0]?.clinic?.clinic_name || "Clinic",
            clinicLocation: doctor.doctor_clinics?.[0]?.clinic?.city?.city_name || "Location",
            appointmentDate: new Date(selectedDateState).toLocaleDateString("en-US", {
              weekday: "long",
              year: "numeric",
              month: "long",
              day: "numeric",
            }),
            appointmentTime: (() => {
              const selectedSlot = getTimeSlotsForDate(selectedDateState).find(
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                (slot: any) => slot.start_time === selectedTimeState
              );
              if (selectedSlot) {
                return `${selectedSlot.start_time_display} - ${selectedSlot.end_time_display}`;
              }
              return selectedTimeState;
            })(),
            consultationFee: doctor.consultation_fees || 0,
          }}
          onClose={handleModalClose}
          onConfirm={handleModalConfirm}
          onBack={handleModalBack}
          isLoading={isBooking}
        />
      )}
    </div>
  );
};

export default SelectDateTime;
