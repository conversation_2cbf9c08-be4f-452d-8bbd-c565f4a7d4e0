import React, { useEffect, useState } from "react";
import StepHeader from "@/components/shared/StepHeader/StepHeader";
import Button, { ButtonType } from "@/components/shared/Button/Button";
import Doctor<PERSON><PERSON> from "@/components/shared/DoctorCard/DoctorCard";
import { useDoctors, DoctorType } from "@/hooks/useDoctors";
import { ArrowLeftIcon } from "@phosphor-icons/react";
import useGetClinic from "@/hooks/useGetClinic";
import { usePageHeader } from "@/contexts/PageHeaderContext";
import { useBreadcrumb } from "@/contexts/BreadcrumbContext";
import ToggleButtonShimmer from "@/components/Shimmer/ToggleButtonShimmer";

export interface SelectDoctorProps {
  centerId: number;
  onDoctorSelect: (doctorId: string, doctorName: string) => void;
  onBack: () => void;
  selectedDoctorId?: string;
}

const SelectDoctor: React.FC<SelectDoctorProps> = ({
  centerId,
  onDoctorSelect,
  onBack,
  selectedDoctorId,
}) => {
  const { doctors, isLoading } = useDoctors(centerId);
  const clinic = useGetClinic(centerId);
  const [selectedDoctor, setSelectedDoctor] = useState<string | undefined>(
    selectedDoctorId
  );

  const { setTitle, setSubtitle } = usePageHeader();
  const { setBreadcrumbs } = useBreadcrumb();

  useEffect(() => {
    setTitle(null);
    setSubtitle(null);
    setBreadcrumbs([
      { label: "My Appointments", href: "/user/appointments" },
      { label: "Schedule Appointments", isActive: true },
    ]);
  }, [setTitle, setSubtitle]);

  const handleDoctorSelect = (doctorId: string, doctorName: string) => {
    setSelectedDoctor(doctorId);
    onDoctorSelect(doctorId, doctorName);
  };

  return (
    <div className="w-full flex flex-col items-center gap-8 md:gap-12">
      <div className="flex flex-col items-center gap-2">
        <StepHeader
          currentStep={3}
          totalSteps={4}
          title="Select Doctor"
          className="!mb-0"
        />

        <p className="text-center text-[var(--grey-6)] text-base font-medium">
          Choose your preferred doctor to consult in {clinic}
        </p>
      </div>

      <div className="flex flex-col gap-8 xl:gap-12">
        {isLoading ? (
          <ToggleButtonShimmer
            count={6}
            variant="card"
            className="!py-3.5 !px-6"
          />
        ) : (
          <div className="grid grid-cols-2 xl:grid-cols-3 gap-3.5 xl:gap-6">
            {doctors.map((doctor: DoctorType) => (
                <DoctorCard
                  key={doctor.id}
                  imageUrl={doctor.profile?.profile_image || "/assets/avatar.jpg"}
                  name={doctor.profile?.display_name}
                  title={doctor.specialization_name?.toString() || "General Practitioner"}
                  experience={doctor.years_of_experience || 0}
                  consultationFee={doctor.consultation_fees || 0}
                  selected={selectedDoctor === doctor.id.toString()}
                  onClick={() => handleDoctorSelect(doctor.id.toString(), doctor.profile.display_name)}
                />
            ))}
          </div>
        )}
      </div>

      <Button
        type={ButtonType.SECONDARY}
        text="Back"
        icon={<ArrowLeftIcon size={20} />}
        onClick={onBack}
        className="!flex !flex-row-reverse !w-[75%]"
      />
    </div>
  );
};

export default SelectDoctor;
