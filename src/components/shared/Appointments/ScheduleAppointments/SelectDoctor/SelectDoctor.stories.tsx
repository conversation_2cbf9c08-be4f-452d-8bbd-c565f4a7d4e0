import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/nextjs";
import <PERSON>Doctor from "./SelectDoctor";
import { PageHeaderProvider } from "@/contexts/PageHeaderContext";
import { BreadcrumbProvider } from "@/contexts/BreadcrumbContext";
import { AuthProvider } from "@/contexts/AuthContext";
import { ToastProvider } from "@/contexts/ToastContext";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      staleTime: Infinity,
    },
  },
});

const meta: Meta<typeof SelectDoctor> = {
  title: "Components/Appointments/ScheduleAppointments/SelectDoctor",
  component: SelectDoctor,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A component for selecting a doctor in the appointment scheduling flow.",
      },
    },
  },
  argTypes: {
    centerId: {
      control: { type: "select" },
      options: [11, 12, 13, 14, 15, 21, 22, 23, 24, 25],
      description: "Center ID to get doctors for",
    },
    onDoctorSelect: { action: "doctor selected" },
    onBack: { action: "back clicked" },
    selectedDoctorId: {
      control: { type: "select" },
      options: [undefined, "11-a", "11-b", "11-c", "11-d", "12-a", "12-b"],
      description: "Pre-selected doctor ID",
    },
  },
  decorators: [
    (Story) => (
      <QueryClientProvider client={queryClient}>
        <ToastProvider>
          <AuthProvider>
            <PageHeaderProvider>
              <BreadcrumbProvider>
                <div className="w-full max-w-4xl">
                  <Story />
                </div>
              </BreadcrumbProvider>
            </PageHeaderProvider>
          </AuthProvider>
        </ToastProvider>
      </QueryClientProvider>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof SelectDoctor>;

export const Default: Story = {
  args: {
    centerId: 11,
    selectedDoctorId: undefined,
  },
};

export const WithPreselectedDoctor: Story = {
  args: {
    centerId: 11,
    selectedDoctorId: "11-a", // Dr. Aarti Mehra
  },
};

export const DifferentCenter: Story = {
  args: {
    centerId: 12, // Center 2
    selectedDoctorId: undefined,
  },
};

export const Interactive: Story = {
  args: {
    centerId: 11,
    selectedDoctorId: undefined,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Interactive version where you can click on doctors to see the selection behavior.",
      },
    },
  },
};
