import React, { useEffect, useState } from "react";
import StepHeader from "@/components/shared/StepHeader/StepHeader";
import Button, { ButtonType } from "@/components/shared/Button/Button";
import ToggleButton from "@/components/shared/ToggleButton/ToggleButton";
import { useClinics } from "@/hooks/useClinics";
import { ArrowLeftIcon } from "@phosphor-icons/react";
import useGetCity from "@/hooks/useGetCity";
import { usePageHeader } from "@/contexts/PageHeaderContext";
import { useBreadcrumb } from "@/contexts/BreadcrumbContext";
import ToggleButtonShimmer from "@/components/Shimmer/ToggleButtonShimmer";

export interface SelectClinicProps {
  cityId: number;
  onClinicSelect: (clinicId: number, clinicName: string) => void;
  onBack: () => void;
  selectedClinicId?: number;
}

const SelectClinic: React.FC<SelectClinicProps> = ({
  cityId,
  onClinicSelect,
  onBack,
  selectedClinicId,
}) => {
  const { clinics, isLoading } = useClinics(cityId);
  const city = useGetCity(cityId);
  const [selectedClinic, setSelectedClinic] = useState<number | undefined>(
    selectedClinicId
  );

  const { setTitle, setSubtitle } = usePageHeader();
  const { setBreadcrumbs } = useBreadcrumb();

  useEffect(() => {
    setTitle(null);
    setSubtitle(null);
    setBreadcrumbs([
      { label: "My Appointments", href: "/user/appointments" },
      { label: "Schedule Appointments", isActive: true },
    ]);
  }, [setTitle, setSubtitle]);

  const handleClinicSelect = (clinicId: number, clinicName: string) => {
    setSelectedClinic(clinicId);
    onClinicSelect(clinicId, clinicName);
  };

  return (
    <div className="w-full xl:w-[33.125rem] flex flex-col items-center gap-8 md:gap-12">
      <div className="flex flex-col items-center gap-2">
        <StepHeader
          currentStep={2}
          totalSteps={4}
          title="Select Clinic"
          className="!mb-0"
        />

        <p className="text-center text-[var(--grey-6)] text-base font-medium">
          Choose your clinic in {city} to view available doctors
        </p>
      </div>

      <div className="flex flex-col gap-8 md:gap-12">
        <div className="flex flex-col gap-2">
          <label className="text-[var(--grey-6)] text-base font-medium text-left">
            Select Clinic
          </label>

          {isLoading ? (
            <ToggleButtonShimmer
              count={6}
              variant="default"
              className="!py-3.5 !px-6"
            />
          ) : (
            <div className="grid grid-cols-2 md:grid-cols-3 gap-8">
              {clinics.map((clinic) => (
                <ToggleButton
                  key={clinic.centerId}
                  isSelected={selectedClinic === clinic.centerId}
                  onClick={() =>
                    handleClinicSelect(clinic.centerId, clinic.name)
                  }
                  variant="default"
                  className="!py-3.5 !px-6"
                >
                  {clinic.name}
                </ToggleButton>
              ))}
            </div>
          )}
        </div>

        <Button
          type={ButtonType.SECONDARY}
          text="Back"
          icon={<ArrowLeftIcon size={20} />}
          onClick={onBack}
          className="!flex !flex-row-reverse"
        />
      </div>
    </div>
  );
};

export default SelectClinic;
