import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import SelectClinic from "./SelectClinic";
import { PageHeaderProvider } from "@/contexts/PageHeaderContext";
import { BreadcrumbProvider } from "@/contexts/BreadcrumbContext";
import { AuthProvider } from "@/contexts/AuthContext";
import { ToastProvider } from "@/contexts/ToastContext";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      staleTime: Infinity,
    },
  },
});

const meta: Meta<typeof SelectClinic> = {
  title: "Components/Appointments/ScheduleAppointments/SelectClinic",
  component: SelectClinic,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A component for selecting a clinic in the appointment scheduling flow.",
      },
    },
  },
  argTypes: {
    cityId: {
      control: { type: "select" },
      options: [1, 2, 3, 4, 5],
      description: "City ID to get clinics for",
    },
    onClinicSelect: { action: "clinic selected" },
    onBack: { action: "back clicked" },
    selectedClinicId: {
      control: { type: "select" },
      options: [undefined, 11, 12, 13, 14, 15],
      description: "Pre-selected clinic ID",
    },
  },
  decorators: [
    (Story) => (
      <QueryClientProvider client={queryClient}>
        <ToastProvider>
          <AuthProvider>
            <PageHeaderProvider>
              <BreadcrumbProvider>
                <div className="w-full max-w-md">
                  <Story />
                </div>
              </BreadcrumbProvider>
            </PageHeaderProvider>
          </AuthProvider>
        </ToastProvider>
      </QueryClientProvider>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof SelectClinic>;

export const Default: Story = {
  args: {
    cityId: 1,
    selectedClinicId: undefined,
  },
};

export const WithPreselectedClinic: Story = {
  args: {
    cityId: 1,
    selectedClinicId: 11, // Center 1
  },
};

export const DifferentCity: Story = {
  args: {
    cityId: 2, // Noida
    selectedClinicId: undefined,
  },
};

export const Interactive: Story = {
  args: {
    cityId: 1,
    selectedClinicId: undefined,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Interactive version where you can click on clinics to see the selection behavior.",
      },
    },
  },
};
