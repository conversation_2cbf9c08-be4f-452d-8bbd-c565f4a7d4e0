import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import SelectClinic from "./SelectClinic";
import { PageHeaderProvider } from "@/contexts/PageHeaderContext";

const renderWithProvider = (component: React.ReactElement) => {
  return render(<PageHeaderProvider>{component}</PageHeaderProvider>);
};

// Mock the useClinics hook
jest.mock("@/hooks/useClinics", () => ({
  useClinics: (cityId: number) => {
    const clinicsData = {
      1: [
        { centerId: 11, name: "Center 1" },
        { centerId: 12, name: "Center 2" },
        { centerId: 13, name: "Center 3" },
        { centerId: 14, name: "Center 4" },
        { centerId: 15, name: "Center 5" },
      ],
      2: [
        { centerId: 21, name: "Center 1" },
        { centerId: 22, name: "Center 2" },
        { centerId: 23, name: "Center 3" },
        { centerId: 24, name: "Center 4" },
        { centerId: 25, name: "Center 5" },
      ],
    };
    return (
      (clinicsData as Record<number, (typeof clinicsData)[1]>)[cityId] || []
    );
  },
}));

// Mock the StepHeader component
jest.mock("@/components/shared/StepHeader/StepHeader", () => {
  return function MockStepHeader({ title }: { title: string }) {
    return <div data-testid="step-header">{title}</div>;
  };
});

// Mock the Button component
jest.mock("@/components/shared/Button/Button", () => {
  const MockButton = ({
    text,
    onClick,
  }: {
    text: string;
    onClick: () => void;
  }) => {
    return (
      <button data-testid="back-button" onClick={onClick}>
        {text}
      </button>
    );
  };

  MockButton.ButtonType = {
    PRIMARY: "primary",
    SECONDARY: "secondary",
  };

  return MockButton;
});

// Mock the ToggleButton component
jest.mock("@/components/shared/ToggleButton/ToggleButton", () => {
  return function MockToggleButton({
    children,
    isSelected,
    onClick,
  }: {
    children: React.ReactNode;
    isSelected: boolean;
    onClick: () => void;
  }) {
    return (
      <button
        data-testid={`clinic-button-${children}`}
        data-selected={isSelected}
        onClick={onClick}
      >
        {children}
      </button>
    );
  };
});

describe("SelectClinic", () => {
  const mockOnClinicSelect = jest.fn();
  const mockOnBack = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the component with correct title and subtitle", () => {
    renderWithProvider(
      <SelectClinic
        cityId={1}
        onClinicSelect={mockOnClinicSelect}
        onBack={mockOnBack}
      />
    );

    expect(screen.getByTestId("step-header")).toHaveTextContent(
      "Select Clinic"
    );
    expect(
      screen.getByText("Choose your clinic to view available doctor")
    ).toBeInTheDocument();
    expect(
      screen.getByText("Select Clinic", { selector: "label" })
    ).toBeInTheDocument();
  });

  it("renders all clinics from the useClinics hook for city 1", () => {
    renderWithProvider(
      <SelectClinic
        cityId={1}
        onClinicSelect={mockOnClinicSelect}
        onBack={mockOnBack}
      />
    );

    expect(screen.getByTestId("clinic-button-Center 1")).toBeInTheDocument();
    expect(screen.getByTestId("clinic-button-Center 2")).toBeInTheDocument();
    expect(screen.getByTestId("clinic-button-Center 3")).toBeInTheDocument();
    expect(screen.getByTestId("clinic-button-Center 4")).toBeInTheDocument();
    expect(screen.getByTestId("clinic-button-Center 5")).toBeInTheDocument();
  });

  it("renders clinics for different city", () => {
    renderWithProvider(
      <SelectClinic
        cityId={2}
        onClinicSelect={mockOnClinicSelect}
        onBack={mockOnBack}
      />
    );

    expect(screen.getByTestId("clinic-button-Center 1")).toBeInTheDocument();
    expect(screen.getByTestId("clinic-button-Center 2")).toBeInTheDocument();
    expect(screen.getByTestId("clinic-button-Center 3")).toBeInTheDocument();
    expect(screen.getByTestId("clinic-button-Center 4")).toBeInTheDocument();
    expect(screen.getByTestId("clinic-button-Center 5")).toBeInTheDocument();
  });

  it("renders back button", () => {
    renderWithProvider(
      <SelectClinic
        cityId={1}
        onClinicSelect={mockOnClinicSelect}
        onBack={mockOnBack}
      />
    );
    expect(screen.getByTestId("back-button")).toHaveTextContent("Back");
  });

  it("calls onClinicSelect when a clinic is clicked", () => {
    renderWithProvider(
      <SelectClinic
        cityId={1}
        onClinicSelect={mockOnClinicSelect}
        onBack={mockOnBack}
      />
    );

    const center1Button = screen.getByTestId("clinic-button-Center 1");
    fireEvent.click(center1Button);

    expect(mockOnClinicSelect).toHaveBeenCalledWith(11, "Center 1");
  });

  it("calls onBack when back button is clicked", () => {
    renderWithProvider(
      <SelectClinic
        cityId={1}
        onClinicSelect={mockOnClinicSelect}
        onBack={mockOnBack}
      />
    );

    const backButton = screen.getByTestId("back-button");
    fireEvent.click(backButton);

    expect(mockOnBack).toHaveBeenCalled();
  });

  it("shows selected clinic when selectedClinicId is provided", () => {
    renderWithProvider(
      <SelectClinic
        cityId={1}
        onClinicSelect={mockOnClinicSelect}
        onBack={mockOnBack}
        selectedClinicId={11}
      />
    );

    const center1Button = screen.getByTestId("clinic-button-Center 1");
    expect(center1Button).toHaveAttribute("data-selected", "true");
  });

  it("shows no selected clinic when selectedClinicId is not provided", () => {
    renderWithProvider(
      <SelectClinic
        cityId={1}
        onClinicSelect={mockOnClinicSelect}
        onBack={mockOnBack}
      />
    );

    const allClinicButtons = screen.getAllByTestId(/clinic-button-/);
    allClinicButtons.forEach((button) => {
      expect(button).toHaveAttribute("data-selected", "false");
    });
  });

  it("updates selected clinic when a different clinic is clicked", async () => {
    renderWithProvider(
      <SelectClinic
        cityId={1}
        onClinicSelect={mockOnClinicSelect}
        onBack={mockOnBack}
        selectedClinicId={11}
      />
    );

    // Initially Center 1 should be selected
    expect(screen.getByTestId("clinic-button-Center 1")).toHaveAttribute(
      "data-selected",
      "true"
    );

    // Click on Center 2
    const center2Button = screen.getByTestId("clinic-button-Center 2");
    fireEvent.click(center2Button);

    await waitFor(() => {
      expect(mockOnClinicSelect).toHaveBeenCalledWith(12, "Center 2");
    });
  });

  it("handles multiple clinic selections correctly", () => {
    renderWithProvider(
      <SelectClinic
        cityId={1}
        onClinicSelect={mockOnClinicSelect}
        onBack={mockOnBack}
      />
    );

    // Click on first clinic
    fireEvent.click(screen.getByTestId("clinic-button-Center 1"));
    expect(mockOnClinicSelect).toHaveBeenCalledWith(11, "Center 1");

    // Click on second clinic
    fireEvent.click(screen.getByTestId("clinic-button-Center 2"));
    expect(mockOnClinicSelect).toHaveBeenCalledWith(12, "Center 2");

    // Verify both calls were made
    expect(mockOnClinicSelect).toHaveBeenCalledTimes(2);
  });

  it("renders with correct accessibility attributes", () => {
    renderWithProvider(
      <SelectClinic
        cityId={1}
        onClinicSelect={mockOnClinicSelect}
        onBack={mockOnBack}
      />
    );

    // Check that all interactive elements are accessible
    expect(screen.getByTestId("back-button")).toBeInTheDocument();
    expect(screen.getAllByTestId(/clinic-button-/)).toHaveLength(5);
  });

  it("handles empty clinics list gracefully", () => {
    renderWithProvider(
      <SelectClinic
        cityId={999} // Non-existent city ID
        onClinicSelect={mockOnClinicSelect}
        onBack={mockOnBack}
      />
    );

    // Should still render the header and back button
    expect(screen.getByTestId("step-header")).toBeInTheDocument();
    expect(screen.getByTestId("back-button")).toBeInTheDocument();

    // Should not render any clinic buttons
    expect(screen.queryAllByTestId(/clinic-button-/)).toHaveLength(0);
  });
});
