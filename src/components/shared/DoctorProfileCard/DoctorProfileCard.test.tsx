import React from "react";
import { render, screen } from "@testing-library/react";
import DoctorP<PERSON>fileCard from "./DoctorProfileCard";

describe("DoctorProfileCard", () => {
  it("renders doctor profile details correctly", () => {
    render(
      <DoctorProfileCard
        imageUrl="/test-image.jpg"
        name="Dr. <PERSON>"
        specialization="IVF Specialist, Gynaecologist"
        experience="12+ years"
        clinicLocation="Gunjan IVF Clinic, Indrapuram Ghaziabad"
        consultationType="In-Person Consultation"
        bio="Dr. <PERSON><PERSON> is a senior IVF specialist with over 12 years of experience in reproductive medicine and women's health. She has successfully treated over 500 couples and is known for compassionate and personalized care. Her expertise includes IVF, IUI, PCOS management, and high-risk pregnancies."
      />
    );

    // Check if image is rendered
    expect(screen.getByAltText("Dr. <PERSON><PERSON>")).toBeInTheDocument();

    // Check if name is rendered
    expect(screen.getByText("Dr. <PERSON><PERSON>")).toBeInTheDocument();

    // Check if specialization is rendered
    expect(
      screen.getByText("IVF Specialist, Gynaecologist")
    ).toBeInTheDocument();

    // Check if experience is rendered
    expect(screen.getByText("12+ years")).toBeInTheDocument();

    // Check if clinic location is rendered
    expect(
      screen.getByText("Gunjan IVF Clinic, Indrapuram Ghaziabad")
    ).toBeInTheDocument();

    // Check if consultation type is rendered
    expect(screen.getByText("In-Person Consultation")).toBeInTheDocument();

    // Check if bio heading is rendered
    expect(screen.getByText("Bio")).toBeInTheDocument();

    // Check if bio content is rendered
    expect(
      screen.getByText(/Dr. Priya Sharma is a senior IVF specialist/)
    ).toBeInTheDocument();
  });
});
