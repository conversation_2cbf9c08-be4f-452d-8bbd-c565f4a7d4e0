import Image from "next/image";
import React from "react";
import {
  BriefcaseIcon,
  MapPinSimpleIcon,
  MoneyWavyIcon,
  UserCircleIcon,
} from "@phosphor-icons/react";
import { useScreenWidth } from "@/hooks/useScreenWidth";
import { useDoctorDetails } from "@/hooks/useDoctorDetails";
import Card from "../Card";
import DoctorProfileCardShimmer from "../../Shimmer/DoctorProfileCardShimmer";

interface DoctorProfileCardProps {
  doctorId: number;
  className?: string;
}

const DoctorProfileCard: React.FC<DoctorProfileCardProps> = ({
  doctorId,
  className,
}) => {
  const screenWidth = useScreenWidth();
  const { doctor, loading, error } = useDoctorDetails(doctorId);

  // Debug logging to identify the issue
  console.log("DoctorProfileCard - doctor data:", doctor);
  console.log("DoctorProfileCard - loading:", loading);
  console.log("DoctorProfileCard - error:", error);
  if (loading) {
    return <DoctorProfileCardShimmer className={className} />;
  }

  if (error || !doctor) {
    return (
      <Card
        className={`flex flex-col items-center justify-center gap-4 w-[362px] md:w-full xl:w-[434px] px-5 py-7.5 md:px-10 md:py-7.5 ${className}`}
      >
        <p className="text-[var(--error-red-6)] text-base text-center">
          {error || "Doctor not found"}
        </p>
      </Card>
    );
  }

  return (
    <Card
      className={`flex flex-col items-start gap-8 w-[362px] md:w-full xl:w-[434px] px-5 py-7.5 md:px-10 md:py-7.5 ${className}`}
    >
      <div className="flex flex-col gap-4">
        {/* Profile Image */}
        <Image
          src={doctor.profile.profile_image || "/assets/avatar.jpg"}
          alt={doctor.profile.display_name}
          width={screenWidth < 768 ? 144 : 194}
          height={screenWidth < 768 ? 144 : 194}
          className="w-[144px] h-[144px] md:w-[194px] md:h-[194px] rounded-full object-cover"
        />

        {/* Doctor Info */}
        <div className="flex flex-col gap-4">
          <h3 className="text-xl font-bold text-[var(--grey-7)]">
            {doctor.profile.display_name || "Dr. Unknown"}
          </h3>
          <p className="capitalize text-[var(--grey-6)] text-base font-medium">
            {doctor.specialization_name || "General Practitioner"}
          </p>

          {/* Experience */}
          <div className="flex justify-start text-base font-medium items-center text-[var(--grey-6)] gap-2">
            <BriefcaseIcon
              size={16}
              className="!text-[var(--violet-11)]"
              weight="bold"
            />
            {doctor.doctor_clinics?.[0]?.clinic?.city?.city_name ||
              "Location not specified"}
          </div>

          {/* Clinic Location */}
          <div className="flex justify-start text-base font-medium items-center text-[var(--grey-6)] gap-2">
            <MapPinSimpleIcon
              size={20}
              className="!text-[var(--violet-11)]"
              weight="bold"
            />
            {doctor.doctor_clinics?.[0]?.clinic?.address ||
              "Address not specified"}
          </div>

          {/* Consultation Fee */}
          <div className="flex justify-start text-base font-medium items-center text-[var(--grey-6)] gap-2">
            <MoneyWavyIcon
              size={20}
              className="!text-[var(--violet-11)]"
              weight="bold"
            />
            ₹{doctor.consultation_fees || 0} Consultation fee at clinic
          </div>

          {/* Consultation Type */}
          <div className="flex justify-start items-center gap-2">
            <div className="inline-flex items-center gap-1 bg-[var(--violet-1)] text-[var(--violet-11)] px-4 py-2 rounded-[50px] text-base font-medium">
              <UserCircleIcon size={16} weight="bold" />
              {doctor.consultation_mode === "both"
                ? "In-Person & Online"
                : doctor.consultation_mode === "in_person"
                  ? "In-Person"
                  : "Online"}{" "}
              Consultation
            </div>
          </div>
        </div>
      </div>

      {/* Bio Section */}
      {doctor.profile.biography && (
        <div className="flex flex-col gap-[9px]">
          <h4 className="font-bold text-[var(--grey-7)] text-xl">Bio</h4>
          <p className="text-[var(--grey-6)] text-base font-medium leading-relaxed">
            {doctor.profile.biography || "No biography available"}
          </p>
        </div>
      )}
    </Card>
  );
};

export default DoctorProfileCard;
