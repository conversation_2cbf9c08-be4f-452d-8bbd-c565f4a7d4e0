import { <PERSON>a, StoryObj } from "@storybook/nextjs";
import DoctorP<PERSON><PERSON>leC<PERSON> from "./DoctorProfileCard";
import { AuthProvider } from "@/contexts/AuthContext";
import { ToastProvider } from "@/contexts/ToastContext";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      staleTime: Infinity,
    },
  },
});

const meta: Meta<typeof DoctorProfileCard> = {
  title: "Components/Appointments/DoctorProfileCard",
  component: DoctorProfileCard,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
  decorators: [
    (Story) => (
      <QueryClientProvider client={queryClient}>
        <ToastProvider>
          <AuthProvider>
            <Story />
          </AuthProvider>
        </ToastProvider>
      </QueryClientProvider>
    ),
  ],
};

export default meta;

type Story = StoryObj<typeof DoctorProfileCard>;

export const Default: Story = {
  args: {
    doctorId: 11,
    className: "",
  },
};
