import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import Input from "./Input";

describe("Input", () => {
  const mockOnChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Component Rendering", () => {
    it("renders without crashing", () => {
      render(<Input value="" onChange={mockOnChange} />);
      expect(screen.getByRole("textbox")).toBeInTheDocument();
    });

    it("renders with correct default type", () => {
      render(<Input value="" onChange={mockOnChange} />);
      const input = screen.getByRole("textbox");
      expect(input).toHaveAttribute("type", "text");
    });

    it("renders with specified type", () => {
      render(<Input type="email" value="" onChange={mockOnChange} />);
      const input = screen.getByRole("textbox");
      expect(input).toHaveAttribute("type", "email");
    });

    it("renders with placeholder", () => {
      render(
        <Input
          value=""
          onChange={mockOnChange}
          placeholder="Enter your email"
        />
      );
      expect(
        screen.getByPlaceholderText("Enter your email")
      ).toBeInTheDocument();
    });

    it("renders with label", () => {
      render(<Input value="" onChange={mockOnChange} label="Email Address" />);
      expect(screen.getByLabelText("Email Address")).toBeInTheDocument();
    });

    it("renders with label and required indicator", () => {
      render(
        <Input
          value=""
          onChange={mockOnChange}
          label="Email Address"
          required
        />
      );
      expect(screen.getByText("Email Address")).toBeInTheDocument();
      expect(screen.getByText("*")).toBeInTheDocument();
    });
  });

  describe("Input Types", () => {
    it("renders text input correctly", () => {
      render(<Input type="text" value="" onChange={mockOnChange} />);
      const input = screen.getByRole("textbox");
      expect(input).toHaveAttribute("type", "text");
    });

    it("renders email input correctly", () => {
      render(<Input type="email" value="" onChange={mockOnChange} />);
      const input = screen.getByRole("textbox");
      expect(input).toHaveAttribute("type", "email");
    });

    it("renders number input correctly", () => {
      render(<Input type="number" value="" onChange={mockOnChange} />);
      const input = screen.getByRole("spinbutton");
      expect(input).toHaveAttribute("type", "number");
    });

    it("renders password input correctly", () => {
      const { container } = render(
        <Input type="password" value="" onChange={mockOnChange} />
      );
      const input = container.querySelector('input[type="password"]');
      expect(input).toHaveAttribute("type", "password");
    });

    it("renders date input with special styling", () => {
      const { container } = render(
        <Input type="date" value="" onChange={mockOnChange} />
      );
      const input = container.querySelector('input[type="date"]');
      expect(input).toHaveStyle({ colorScheme: "light" });
    });
  });

  describe("User Interactions", () => {
    it("calls onChange when user types", async () => {
      const user = userEvent.setup();
      render(<Input value="" onChange={mockOnChange} />);

      const input = screen.getByRole("textbox");
      await user.type(input, "test");

      expect(mockOnChange).toHaveBeenCalledTimes(4);
      // Check individual character calls
      expect(mockOnChange).toHaveBeenNthCalledWith(1, "t");
      expect(mockOnChange).toHaveBeenNthCalledWith(2, "e");
      expect(mockOnChange).toHaveBeenNthCalledWith(3, "s");
      expect(mockOnChange).toHaveBeenNthCalledWith(4, "t");
    });

    it("handles controlled value correctly", () => {
      render(<Input value="initial value" onChange={mockOnChange} />);
      const input = screen.getByRole("textbox");
      expect(input).toHaveValue("initial value");
    });

    it("calls onChange with correct value on direct change", async () => {
      render(<Input value="" onChange={mockOnChange} />);

      const input = screen.getByRole("textbox");
      fireEvent.change(input, { target: { value: "new value" } });

      expect(mockOnChange).toHaveBeenCalledWith("new value");
    });
  });

  describe("States and Variants", () => {
    it("applies default variant styling", () => {
      render(<Input value="" onChange={mockOnChange} />);
      const input = screen.getByRole("textbox");
      expect(input).toHaveClass("border-[var(--grey-3)]");
    });

    it("applies compact variant styling", () => {
      render(<Input value="" onChange={mockOnChange} variant="compact" />);
      const input = screen.getByRole("textbox");
      expect(input).toHaveClass("border-[var(--grey-2)]");
    });

    it("applies default size styling", () => {
      render(<Input value="" onChange={mockOnChange} />);
      const input = screen.getByRole("textbox");
      expect(input).toHaveClass("h-[3.125rem]");
    });

    it("applies small size styling", () => {
      render(<Input value="" onChange={mockOnChange} size="sm" />);
      const input = screen.getByRole("textbox");
      expect(input).toHaveClass("h-10");
    });

    it("applies large size styling", () => {
      render(<Input value="" onChange={mockOnChange} size="lg" />);
      const input = screen.getByRole("textbox");
      expect(input).toHaveClass("h-14");
    });

    it("shows disabled state correctly", () => {
      render(<Input value="" onChange={mockOnChange} disabled />);
      const input = screen.getByRole("textbox");
      expect(input).toBeDisabled();
      expect(input).toHaveClass("opacity-50", "cursor-not-allowed");
    });

    it("shows error state correctly", () => {
      render(<Input value="" onChange={mockOnChange} error />);
      const input = screen.getByRole("textbox");
      expect(input).toHaveClass("border-[var(--error-red-4)]");
      expect(input).toHaveAttribute("aria-invalid", "true");
    });

    it("displays error message", () => {
      render(
        <Input
          value=""
          onChange={mockOnChange}
          error
          errorMessage="This field is required"
        />
      );
      expect(screen.getByText("This field is required")).toBeInTheDocument();
      expect(screen.getByRole("alert")).toBeInTheDocument();
    });
  });

  describe("Accessibility", () => {
    it("has proper label association", () => {
      render(<Input value="" onChange={mockOnChange} label="Full Name" />);
      const input = screen.getByLabelText("Full Name");
      expect(input).toBeInTheDocument();
    });

    it("has proper error message association", () => {
      render(
        <Input
          value=""
          onChange={mockOnChange}
          error
          errorMessage="Required field"
          id="test-input"
        />
      );
      const input = screen.getByRole("textbox");
      expect(input).toHaveAttribute("aria-describedby", "test-input-error");
    });

    it("generates unique id when not provided", () => {
      const { container } = render(
        <Input value="" onChange={mockOnChange} label="Test" />
      );
      const input = container.querySelector("input");
      const label = container.querySelector("label");

      expect(input).toHaveAttribute("id");
      expect(label).toHaveAttribute("for", input?.getAttribute("id"));
    });

    it("uses provided id", () => {
      render(
        <Input value="" onChange={mockOnChange} label="Test" id="custom-id" />
      );
      const input = screen.getByRole("textbox");
      expect(input).toHaveAttribute("id", "custom-id");
    });
  });

  describe("Custom Props", () => {
    it("applies custom className", () => {
      render(
        <Input value="" onChange={mockOnChange} className="custom-class" />
      );
      const input = screen.getByRole("textbox");
      expect(input).toHaveClass("custom-class");
    });

    it("handles required attribute", () => {
      render(<Input value="" onChange={mockOnChange} required />);
      const input = screen.getByRole("textbox");
      expect(input).toHaveAttribute("required");
    });
  });
});
