"use client";
import React from "react";
import Image from "next/image";
import { useRouter, usePathname } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import Link from "next/link";

export enum NavigationTarget {
  LOGIN = "Login",
  HELP = "Help",
}

export enum HeaderState {
  LOGIN = "login",
  HELP = "help",
}

export interface HeaderProps {
  navigateTo?: (target: NavigationTarget) => void;
  onClick?: () => void;
  state?: HeaderState;
  className?: string;
}

const Header: React.FC<HeaderProps> = ({
  navigateTo,
  onClick,
  state = HeaderState.LOGIN,
  className = "",
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const { user, signOut } = useAuth();
  const isLoggedIn = !!user;

  const handleActionClick = async () => {
    // If onClick is provided, always call it first (for warning alerts, etc.)
    if (onClick) {
      onClick();
      return; // Let the parent component handle the flow
    }

    // Handle logged in user logout
    if (isLoggedIn) {
      await signOut();
      return;
    }

    // Handle custom navigation
    if (navigateTo) {
      const target =
        state === HeaderState.HELP
          ? NavigationTarget.HELP
          : NavigationTarget.LOGIN;
      navigateTo(target);
      return;
    }

    // Default navigation behavior when no custom handlers are provided
    if (state === HeaderState.HELP) {
      router.push("/help-support");
      return;
    }

    // Handle specific IVF assessment pages with from parameter
    if (pathname === "/ivf-assessment/signup") {
      router.push("/login?from=signup");
      return;
    }

    if (pathname === "/ivf-assessment/verify-details") {
      router.push("/login?from=verify-details");
      return;
    }

    // Default login navigation with redirect parameter for all other pages
    // Only add redirect if we're not already on login page
    if (pathname !== "/login") {
      router.push(`/login?redirect=${encodeURIComponent(pathname)}`);
    } else {
      router.push("/login");
    }
  };

  return (
    <header
      className={`sticky top-0 z-30 w-full h-[5rem] bg-white py-2 md:py-4 px-6 md:px-[9.375rem] border-b border-[var(--grey-3)] ${className}`}
    >
      <div className="w-full flex items-center justify-between">
        <div className="flex items-center">
          <Link href="/">
            <Image
              src="/assets/givfLogo.svg"
              alt="GUNJAN IVF World Logo"
              width={98}
              height={48}
              className="h-auto max-h-[58px] md:h-[48px] w-auto max-w-[118px] md:max-w-[98px]"
              priority
            />
          </Link>
        </div>
        <div className="flex flex-col items-end text-base">
          {isLoggedIn ? (
            <button
              type="button"
              onClick={handleActionClick}
              className="text-[var(--red-6)] hover:text-[var(--red-7)] cursor-pointer font-medium transition-colors duration-200 rounded px-1"
            >
              Logout
            </button>
          ) : (
            <>
              <span className="text-[var(--dark-grey)] sm:inline">
                {state === HeaderState.HELP
                  ? "Having Trouble?"
                  : "Have an account?"}
              </span>
              <button
                type="button"
                onClick={handleActionClick}
                className="text-[var(--red-6)] hover:text-[var(--red-7)] cursor-pointer font-medium transition-colors duration-200 rounded px-1"
              >
                {state === HeaderState.HELP ? "Get Help" : "Login"}
              </button>
            </>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;
