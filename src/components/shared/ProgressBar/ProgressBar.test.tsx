import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import ProgressBar from "./ProgressBar";

// Mock for the useRef and DOM measurements
const mockOffsetWidth = 300;

beforeAll(() => {
  Object.defineProperty(HTMLElement.prototype, "offsetWidth", {
    configurable: true,
    value: mockOffsetWidth,
  });
});

describe("ProgressBar", () => {
  const defaultProps = {
    label: "Test Progress",
    valueType: "number" as const,
    startValue: 0,
    endValue: 10,
    currentValue: 5,
    min: 0,
    max: 10,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the component with basic props", () => {
    render(<ProgressBar {...defaultProps} />);

    expect(screen.getByText("Test Progress")).toBeInTheDocument();
    expect(screen.getByRole("slider")).toBeInTheDocument();
  });

  it("renders the label correctly", () => {
    render(<ProgressBar {...defaultProps} label="Custom Label" />);

    expect(screen.getByText("Custom Label")).toBeInTheDocument();
  });

  it("renders number values correctly", () => {
    render(
      <ProgressBar
        {...defaultProps}
        valueType="number"
        startValue={1}
        endValue={100}
        currentValue={50}
      />
    );

    expect(screen.getByText("1")).toBeInTheDocument();
    expect(screen.getByText("100")).toBeInTheDocument();
    expect(screen.getByText("50")).toBeInTheDocument();
  });

  it("renders emoji values correctly", () => {
    render(
      <ProgressBar
        {...defaultProps}
        valueType="emoji"
        startValue="😴"
        endValue="😍"
        currentValue={7}
        displayValue="😊"
      />
    );

    expect(screen.getByText("😴")).toBeInTheDocument();
    expect(screen.getByText("😍")).toBeInTheDocument();
    expect(screen.getByText("😊")).toBeInTheDocument();
  });

  it("falls back to numeric value when displayValue is not provided for emoji type", () => {
    render(
      <ProgressBar
        {...defaultProps}
        valueType="emoji"
        startValue="😴"
        endValue="😍"
        currentValue={7}
      />
    );

    expect(screen.getByText("7")).toBeInTheDocument();
  });

  it("sets slider attributes correctly", () => {
    render(
      <ProgressBar {...defaultProps} min={5} max={15} currentValue={10} />
    );

    const slider = screen.getByRole("slider");
    expect(slider).toHaveAttribute("min", "5");
    expect(slider).toHaveAttribute("max", "15");
    expect(slider).toHaveAttribute("value", "10");
  });

  it("uses default min and max values", () => {
    render(<ProgressBar {...defaultProps} />);

    const slider = screen.getByRole("slider");
    expect(slider).toHaveAttribute("min", "0");
    expect(slider).toHaveAttribute("max", "10");
  });

  it("handles slider change events", () => {
    const mockOnChange = jest.fn();
    render(<ProgressBar {...defaultProps} onChange={mockOnChange} />);

    const slider = screen.getByRole("slider");
    fireEvent.change(slider, { target: { value: "8" } });

    expect(mockOnChange).toHaveBeenCalledWith(8);
  });

  it("handles multiple slider changes", () => {
    const mockOnChange = jest.fn();
    render(<ProgressBar {...defaultProps} onChange={mockOnChange} />);

    const slider = screen.getByRole("slider");

    fireEvent.change(slider, { target: { value: "3" } });
    expect(mockOnChange).toHaveBeenCalledWith(3);

    fireEvent.change(slider, { target: { value: "9" } });
    expect(mockOnChange).toHaveBeenCalledWith(9);

    expect(mockOnChange).toHaveBeenCalledTimes(2);
  });

  it("doesn't call onChange when not provided", () => {
    render(<ProgressBar {...defaultProps} />);

    const slider = screen.getByRole("slider");
    // This should not throw an error
    fireEvent.change(slider, { target: { value: "8" } });
  });

  it("applies custom className", () => {
    const { container } = render(
      <ProgressBar {...defaultProps} className="custom-class" />
    );

    expect(container.firstChild).toHaveClass("custom-class");
  });

  it("has proper label styling", () => {
    render(<ProgressBar {...defaultProps} />);

    const label = screen.getByText("Test Progress");
    expect(label).toHaveClass("block");
    expect(label).toHaveClass("text-[var(--grey-6)]");
    expect(label).toHaveClass("text-base");
    expect(label).toHaveClass("font-medium");
    expect(label).toHaveClass("mb-3");
  });

  it("has proper bubble styling", () => {
    render(<ProgressBar {...defaultProps} currentValue={5} />);

    const bubble = screen.getByText("5").closest("div");
    expect(bubble).toHaveClass("bg-[var(--violet-6)]");
    expect(bubble).toHaveClass("text-white");
    expect(bubble).toHaveClass("rounded-3xl");
  });

  it("positions bubble correctly based on percentage", () => {
    render(<ProgressBar {...defaultProps} currentValue={5} min={0} max={10} />);

    // currentValue=5, min=0, max=10 should give 50%
    const bubble = screen.getByText("5", {
      selector: ".bg-\\[var\\(--violet-6\\)\\]",
    });
    const bubbleContainer = bubble.closest("div")?.parentElement;
    expect(bubbleContainer).toHaveStyle("left: 50%");
  });

  it("calculates percentage correctly for different ranges", () => {
    render(
      <ProgressBar {...defaultProps} currentValue={15} min={10} max={20} />
    );

    // currentValue=15, min=10, max=20 should give 50%
    const bubble = screen.getByText("15", {
      selector: ".bg-\\[var\\(--violet-6\\)\\]",
    });
    const bubbleContainer = bubble.closest("div")?.parentElement;
    expect(bubbleContainer).toHaveStyle("left: 50%");
  });

  it("handles edge case values", () => {
    render(<ProgressBar {...defaultProps} currentValue={0} min={0} max={10} />);

    const bubble = screen.getByText("0", {
      selector: ".bg-\\[var\\(--violet-6\\)\\]",
    });
    const bubbleContainer = bubble.closest("div")?.parentElement;
    expect(bubbleContainer).toHaveStyle("left: 0%");
  });

  it("handles maximum value", () => {
    render(
      <ProgressBar {...defaultProps} currentValue={10} min={0} max={10} />
    );

    const bubble = screen.getByText("10", {
      selector: ".bg-\\[var\\(--violet-6\\)\\]",
    });
    const bubbleContainer = bubble.closest("div")?.parentElement;
    expect(bubbleContainer).toHaveStyle("left: 100%");
  });

  it("renders start and end values with proper styling", () => {
    render(<ProgressBar {...defaultProps} />);

    const startValue = screen.getByText("0");
    const endValue = screen.getByText("10");

    expect(startValue).toHaveClass("text-sm");
    expect(startValue).toHaveClass("text-[var(--grey-5)]");
    expect(endValue).toHaveClass("text-sm");
    expect(endValue).toHaveClass("text-[var(--grey-5)]");
  });

  it("has proper accessibility attributes for emoji values", () => {
    render(
      <ProgressBar
        {...defaultProps}
        valueType="emoji"
        startValue="😴"
        endValue="😍"
        currentValue={7}
        displayValue="😊"
      />
    );

    const emojiElements = screen.getAllByRole("img");
    expect(emojiElements).toHaveLength(3); // start, current, end values

    emojiElements.forEach((emoji) => {
      expect(emoji).toHaveAttribute("aria-label");
    });
  });

  it("maintains slider functionality with string values", () => {
    const mockOnChange = jest.fn();
    render(
      <ProgressBar
        {...defaultProps}
        startValue="Low"
        endValue="High"
        onChange={mockOnChange}
      />
    );

    const slider = screen.getByRole("slider");
    fireEvent.change(slider, { target: { value: "7" } });

    expect(mockOnChange).toHaveBeenCalledWith(7);
  });

  it("renders the speech bubble tail", () => {
    const { container } = render(<ProgressBar {...defaultProps} />);

    // Check for the tail div (triangle)
    const tailElement = container.querySelector(".absolute.top-full");
    expect(tailElement).toBeInTheDocument();
    expect(tailElement).toHaveClass("border-t-[var(--violet-6)]");
  });

  it("handles decimal currentValue correctly", () => {
    render(<ProgressBar {...defaultProps} currentValue={5.5} />);

    expect(screen.getByText("5.5")).toBeInTheDocument();

    const slider = screen.getByRole("slider");
    expect(slider).toHaveAttribute("value", "5.5");
  });

  it("updates when props change", () => {
    const { rerender } = render(
      <ProgressBar {...defaultProps} currentValue={3} />
    );

    expect(screen.getByText("3")).toBeInTheDocument();

    rerender(<ProgressBar {...defaultProps} currentValue={8} />);

    expect(screen.getByText("8")).toBeInTheDocument();
    expect(screen.queryByText("3")).not.toBeInTheDocument();
  });

  it("handles large number ranges", () => {
    render(
      <ProgressBar
        {...defaultProps}
        min={1000}
        max={2000}
        currentValue={1500}
        startValue={1000}
        endValue={2000}
      />
    );

    expect(screen.getByText("1000")).toBeInTheDocument();
    expect(screen.getByText("2000")).toBeInTheDocument();
    expect(screen.getByText("1500")).toBeInTheDocument();
  });

  it("has proper slider styling classes", () => {
    render(<ProgressBar {...defaultProps} />);

    const slider = screen.getByRole("slider");
    expect(slider).toHaveClass("w-full");
    expect(slider).toHaveClass("h-2");
    expect(slider).toHaveClass("bg-[var(--violet-2)]");
    expect(slider).toHaveClass("rounded-lg");
    expect(slider).toHaveClass("appearance-none");
    expect(slider).toHaveClass("cursor-pointer");
    expect(slider).toHaveClass("slider");
  });

  it("renders without crashing with minimal props", () => {
    render(
      <ProgressBar
        label="Minimal"
        valueType="number"
        startValue={0}
        endValue={5}
        currentValue={2}
      />
    );

    expect(screen.getByText("Minimal")).toBeInTheDocument();
    expect(screen.getByRole("slider")).toBeInTheDocument();
  });

  it("handles zero range correctly", () => {
    render(<ProgressBar {...defaultProps} min={5} max={5} currentValue={5} />);

    // When min equals max, percentage should be handled gracefully
    const slider = screen.getByRole("slider");
    expect(slider).toHaveAttribute("min", "5");
    expect(slider).toHaveAttribute("max", "5");
  });

  it("works with negative values", () => {
    render(
      <ProgressBar
        {...defaultProps}
        min={-10}
        max={10}
        currentValue={0}
        startValue={-10}
        endValue={10}
      />
    );

    expect(screen.getByText("-10")).toBeInTheDocument();
    expect(screen.getByText("10")).toBeInTheDocument();
    expect(screen.getByText("0")).toBeInTheDocument();
  });
});
