import React, { useEffect, useRef, useState } from "react";

export interface ProgressBarProps {
  label: string;
  valueType: "number" | "emoji";
  required?: boolean;
  startValue: string | number;
  endValue: string | number;
  currentValue: number; // Always numeric for slider position
  displayValue?: string; // Optional emoji to display in bubble
  min?: number;
  max?: number;
  onChange?: (value: number) => void;
  className?: string;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  label,
  valueType,
  required,
  startValue,
  endValue,
  currentValue,
  displayValue,
  min = 0,
  max = 10,
  onChange,
  className = "",
}) => {
  const sliderRef = useRef<HTMLInputElement>(null);
  const [sliderWidth, setSliderWidth] = useState<number>(0);
  const percentage = ((currentValue - min) / (max - min)) * 100;

  useEffect(() => {
    if (sliderRef.current) {
      setSliderWidth(sliderRef.current.offsetWidth);
    }
  }, []);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = Number(event.target.value);
    if (onChange) {
      onChange(newValue);
    }
  };

  const bubbleDisplayValue =
    valueType === "number"
      ? String(currentValue)
      : displayValue || String(currentValue);

  return (
    <div className={`w-full ${className}`}>
      <label className="block text-[var(--grey-6)] text-base font-medium mb-3">
        {label}
        {required && <span className="text-[var(--error-red-4)] ml-1">*</span>}
      </label>

      <div className="relative">
        {/* Value Display Bubble */}
        <div
          className="relative left-[2.813rem]"
          style={{ width: `${sliderWidth}px` }}
        >
          <div
            className="w-[3.25rem] absolute -top-12 transform -translate-x-1/2 z-10"
            style={{ left: `${percentage}%` }}
          >
            <div className="relative bg-[var(--violet-6)] text-white px-4 py-2 rounded-3xl text-center min-w-[3rem] text-base font-semibold">
              {valueType === "emoji" ? (
                <span role="img" aria-label="current-value">
                  {bubbleDisplayValue}
                </span>
              ) : (
                bubbleDisplayValue
              )}
              {/* Speech bubble tail */}
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-r-[6px] border-t-[6px] border-l-transparent border-r-transparent border-t-[var(--violet-6)]"></div>
            </div>
          </div>
        </div>

        {/* Progress Track Container */}
        <div className="flex items-center gap-3">
          {/* Start Value */}
          <div className="text-sm text-[var(--grey-5)] min-w-[1.5rem] flex justify-center">
            {valueType === "emoji" ? (
              <span role="img" aria-label="start-value">
                {startValue}
              </span>
            ) : (
              startValue
            )}
          </div>

          {/* Progress Bar */}
          <div className="flex-1 relative">
            <input
              type="range"
              ref={sliderRef}
              min={min}
              max={max}
              value={currentValue}
              onChange={handleChange}
              className="w-full h-2 bg-[var(--violet-2)] rounded-lg appearance-none cursor-pointer slider"
              style={{
                background: `linear-gradient(to right, var(--violet-6) 0%, var(--violet-6) ${percentage}%, var(--violet-2) ${percentage}%, var(--violet-2) 100%)`,
              }}
            />

            {/* Custom Slider Thumb */}
            <style jsx>{`
              .slider::-webkit-slider-thumb {
                appearance: none;
                height: 20px;
                width: 20px;
                border-radius: 50%;
                background: var(--violet-6);
                cursor: pointer;
                border: 3px solid white;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
              }

              .slider::-moz-range-thumb {
                height: 20px;
                width: 20px;
                border-radius: 50%;
                background: var(--violet-6);
                cursor: pointer;
                border: 3px solid white;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
              }
            `}</style>
          </div>

          {/* End Value */}
          <div className="text-sm text-[var(--grey-5)] min-w-[1.5rem] flex justify-center">
            {valueType === "emoji" ? (
              <span role="img" aria-label="end-value">
                {endValue}
              </span>
            ) : (
              endValue
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProgressBar;
