import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import React, { useState } from "react";
import Toast, { ToastType } from "./Toast";

const meta: Meta<typeof Toast> = {
  title: "Components/Toast",
  component: Toast,
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

const toastProps = {
  success: {
    type: "success" as ToastType,
    title: "Appointment Confirmed",
    message: "Your appointment has been confirmed.",
  },
  error: {
    type: "error" as ToastType,
    title: "Something Went Wrong",
    message: "We couldn’t process your request. Please try again.",
  },
  warning: {
    type: "warning" as ToastType,
    title: "Unsaved Changes",
    message: "You have unsaved changes. Do you want to leave this page?",
  },
  info: {
    type: "info" as ToastType,
    title: "New Feature Available",
    message: "Check out our new IVF tracking tool in your dashboard.",
  },
  default: {
    type: "default" as ToastType,
    title: "Notification",
    message: "You have a new notification.",
  },
};

export const Success: Story = {
  args: {
    ...toastProps.success,
    onClose: () => alert("Closed"),
  },
};

export const Error: Story = {
  args: {
    ...toastProps.error,
    onClose: () => alert("Closed"),
  },
};

export const Warning: Story = {
  args: {
    ...toastProps.warning,
    onClose: () => alert("Closed"),
  },
};

export const Info: Story = {
  args: {
    ...toastProps.info,
    onClose: () => alert("Closed"),
  },
};

export const Default: Story = {
  args: {
    ...toastProps.default,
    onClose: () => alert("Closed"),
  },
};

export const Closable: Story = {
  render: (args) => {
    const [open, setOpen] = useState(true);
    return open ? (
      <Toast {...args} onClose={() => setOpen(false)} />
    ) : (
      <button onClick={() => setOpen(true)}>Show Toast</button>
    );
  },
  args: {
    ...toastProps.success,
  },
};

export const AllTypes: Story = {
  render: () => (
    <div className="space-y-8">
      <Toast {...toastProps.success} onClose={() => {}} />
      <Toast {...toastProps.error} onClose={() => {}} />
      <Toast {...toastProps.warning} onClose={() => {}} />
      <Toast {...toastProps.info} onClose={() => {}} />
      <Toast {...toastProps.default} onClose={() => {}} />
    </div>
  ),
};
