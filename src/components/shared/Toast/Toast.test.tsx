import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import Toast, { ToastType } from "./Toast";

describe("Toast", () => {
  const baseProps = {
    title: "Test Title",
    message: "Test message body.",
    onClose: jest.fn(),
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("renders title and message", () => {
    render(<Toast {...baseProps} />);
    expect(screen.getByText("Test Title")).toBeInTheDocument();
    expect(screen.getByText("Test message body.")).toBeInTheDocument();
  });

  it("renders close button and calls onClose", () => {
    render(<Toast {...baseProps} />);
    const closeBtn = screen.getByRole("button", { name: /close/i });
    fireEvent.click(closeBtn);
    expect(baseProps.onClose).toHaveBeenCalledTimes(1);
  });

  it("does not render close button if onClose is not provided", () => {
    render(<Toast title="No Close" message="No close button" />);
    expect(
      screen.queryByRole("button", { name: /close/i })
    ).not.toBeInTheDocument();
  });

  it("applies correct styles for each type", () => {
    const types: ToastType[] = [
      "success",
      "error",
      "warning",
      "info",
      "default",
    ];
    types.forEach((type) => {
      const { container } = render(<Toast {...baseProps} type={type} />);
      // Check icon and title color
      const icon = container.querySelector("svg");
      expect(icon).toBeInTheDocument();
      const titles = screen.getAllByText("Test Title");
      expect(titles.length).toBeGreaterThan(0);
    });
  });

  it("renders correct icon for each type", () => {
    const types: ToastType[] = [
      "success",
      "error",
      "warning",
      "info",
      "default",
    ];
    types.forEach((type) => {
      const { container, unmount } = render(
        <Toast {...baseProps} type={type} />
      );
      const icon = container.querySelector("svg");
      expect(icon).toBeInTheDocument();
      unmount();
    });
  });

  it("has role alert for accessibility", () => {
    render(<Toast {...baseProps} />);
    expect(screen.getByRole("alert")).toBeInTheDocument();
  });

  it("applies custom className if provided", () => {
    const { container } = render(
      <Toast {...baseProps} className="custom-toast" />
    );
    expect(container.firstChild).toHaveClass("custom-toast");
  });
});
