import { CheckIcon, XIcon } from "@phosphor-icons/react";
import React from "react";

export type ToastType = "success" | "error" | "warning" | "info" | "default";

export interface ToastProps {
  type?: ToastType;
  title: string;
  message: string;
  onClose?: () => void;
  className?: string;
}

const typeStyles: Record<
  ToastType,
  {
    iconBg: string;
    title: string;
  }
> = {
  success: {
    iconBg: "bg-[var(--success-green-4)]",
    title: "text-[var(--success-green-4)]",
  },
  error: {
    iconBg: "bg-[var(--error-red-5)]",
    title: "text-[var(--error-red-5)]",
  },
  warning: {
    iconBg: "bg-[var(--warning-yellow-4)]",
    title: "text-[var(--warning-yellow-4)]",
  },
  info: {
    iconBg: "bg-[var(--violet-7)]",
    title: "text-[var(--violet-7)]",
  },
  default: {
    iconBg: "bg-[var(--grey-7)]",
    title: "text-[var(--grey-7)]",
  },
};

const Toast: React.FC<ToastProps> = ({
  type = "default",
  title,
  message,
  onClose,
  className = "",
}) => {
  const styles = typeStyles[type];

  return (
    <div
      className={`w-[419px] flex flex-col items-start gap-4 p-5 border-1 border-[var(--grey-2)] shadow-xl shadow-[#9CA3AF14] rounded-lg bg-white ${className}`}
      role="alert"
    >
      {/* Icon and Title */}
      <div className={`w-full flex items-center justify-between`}>
        <div className="flex items-center gap-5.5">
          <span className={`w-4.875 h-4.875 p-1 rounded-full ${styles.iconBg}`}>
            <CheckIcon size={19.5} className="font-bold text-white" />
          </span>
          <div
            className={`flex items-center text-base font-bold ${styles.title}`}
          >
            {title}
          </div>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="ml-2 mt-1 text-[var(--grey-5)] cursor-pointer rounded-full p-1 transition-colors"
            aria-label="Close"
          >
            <XIcon size={20} className="font-bold" />
          </button>
        )}
      </div>
      {/* Content */}
      <div className="pl-11.5 pr-10">
        <div className="text-sm font-medium text-[var(--grey-6)] leading-relaxed">
          {message}
        </div>
      </div>
      {/* Close Button */}
    </div>
  );
};

export default Toast;
