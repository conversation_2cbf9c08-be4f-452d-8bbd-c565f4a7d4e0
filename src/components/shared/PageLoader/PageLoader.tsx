import "@/styles/spinner.css";

interface LoaderProps extends React.HTMLAttributes<HTMLDivElement> {
  text?: string;
  enableText?: boolean;
  fillHeight?: boolean;
}

const PageLoader = ({
  text = "Loading...",
  enableText = true,
  className = "",
  fillHeight = true,
  ...props
}: LoaderProps) => {
  return (
    <div
      className={`flex flex-col ${fillHeight ? "h-screen" : ""} justify-center items-center w-full ${className}`}
      role="status"
      aria-label="Loading content"
      {...props}
    >
      <span className="loading-spinner" aria-hidden="true"></span>
      <div className="text-[var(--grey-6)] text-base my-4">{enableText && text}</div>
    </div>
  );
};

export default PageLoader;
