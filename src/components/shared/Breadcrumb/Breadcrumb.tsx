import React from "react";
import { CaretRightIcon } from "@phosphor-icons/react";
import { useRouter } from "next/navigation";
import { BreadcrumbItem } from "@/contexts/BreadcrumbContext";

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({ items, className = "" }) => {
  const router = useRouter();

  const handleItemClick = (item: BreadcrumbItem) => {
    if (item.href && !item.isActive) {
      router.push(item.href);
    }
  };

  return (
    <nav className={`flex items-center gap-2 ${className}`}>
      {items.map((item, index) => (
        <React.Fragment key={index}>
          <button
            onClick={() => handleItemClick(item)}
            className={`text-sm font-medium transition-colors ${
              item.isActive
                ? "text-[var(--grey-7)] cursor-default"
                : item.href
                  ? "text-[var(--grey-5)] hover:text-[var(--grey-6)] cursor-pointer"
                  : "text-[var(--grey-5)] cursor-default"
            }`}
            disabled={item.isActive || !item.href}
          >
            {item.label}
          </button>
          {index < items.length - 1 && (
            <CaretRightIcon
              size={14}
              className="text-[var(--grey-4)]"
              weight="bold"
            />
          )}
        </React.Fragment>
      ))}
    </nav>
  );
};

export default Breadcrumb;
