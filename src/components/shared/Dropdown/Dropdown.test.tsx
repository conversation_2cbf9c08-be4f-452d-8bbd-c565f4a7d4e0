import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import Dropdown, { DropdownOption } from "./Dropdown";

const mockOptions: DropdownOption[] = [
  { id: "1", text: "Option 1", value: "option1" },
  { id: "2", text: "Option 2", value: "option2" },
  { id: "3", text: "Option 3", value: "option3" },
];

const mockOnChange = jest.fn();

describe("Dropdown Component", () => {
  beforeEach(() => {
    mockOnChange.mockClear();
  });

  it("renders correctly with basic props", () => {
    render(
      <Dropdown
        value=""
        onChange={mockOnChange}
        options={mockOptions}
        placeholder="Select an option"
      />
    );

    const select = screen.getByRole("combobox");
    expect(select).toBeInTheDocument();
    expect(select).toHaveDisplayValue("Select an option");
  });

  it("displays label when provided", () => {
    render(
      <Dropdown
        value=""
        onChange={mockOnChange}
        options={mockOptions}
        label="Test Label"
        placeholder="Select an option"
      />
    );

    expect(screen.getByText("Test Label")).toBeInTheDocument();
  });

  it("shows required asterisk when required is true", () => {
    render(
      <Dropdown
        value=""
        onChange={mockOnChange}
        options={mockOptions}
        label="Test Label"
        required={true}
        placeholder="Select an option"
      />
    );

    expect(screen.getByText("*")).toBeInTheDocument();
  });

  it("renders all options correctly", () => {
    render(
      <Dropdown
        value=""
        onChange={mockOnChange}
        options={mockOptions}
        placeholder="Select an option"
      />
    );

    // Check placeholder option
    expect(screen.getByText("Select an option")).toBeInTheDocument();

    // Check all provided options
    mockOptions.forEach((option) => {
      expect(screen.getByText(option.text)).toBeInTheDocument();
    });
  });

  it("displays selected value correctly", () => {
    render(
      <Dropdown
        value="option2"
        onChange={mockOnChange}
        options={mockOptions}
        placeholder="Select an option"
      />
    );

    const select = screen.getByRole("combobox");
    expect(select).toHaveDisplayValue("Option 2");
  });

  it("calls onChange when selection changes", () => {
    render(
      <Dropdown
        value=""
        onChange={mockOnChange}
        options={mockOptions}
        placeholder="Select an option"
      />
    );

    const select = screen.getByRole("combobox");
    fireEvent.change(select, { target: { value: "option1" } });

    expect(mockOnChange).toHaveBeenCalledWith("option1");
    expect(mockOnChange).toHaveBeenCalledTimes(1);
  });

  it("can be disabled", () => {
    render(
      <Dropdown
        value=""
        onChange={mockOnChange}
        options={mockOptions}
        disabled={true}
        placeholder="Select an option"
      />
    );

    const select = screen.getByRole("combobox");
    expect(select).toBeDisabled();
  });

  it("displays error state correctly", () => {
    render(
      <Dropdown
        value=""
        onChange={mockOnChange}
        options={mockOptions}
        error={true}
        errorMessage="This field is required"
        placeholder="Select an option"
      />
    );

    expect(screen.getByText("This field is required")).toBeInTheDocument();
    expect(screen.getByRole("alert")).toBeInTheDocument();

    const select = screen.getByRole("combobox");
    expect(select).toHaveClass("border-red-300");
  });

  it("applies custom className", () => {
    render(
      <Dropdown
        value=""
        onChange={mockOnChange}
        options={mockOptions}
        className="custom-class"
        placeholder="Select an option"
      />
    );

    const select = screen.getByRole("combobox");
    expect(select).toHaveClass("custom-class");
  });

  it("renders dropdown arrow icon", () => {
    render(
      <Dropdown
        value=""
        onChange={mockOnChange}
        options={mockOptions}
        placeholder="Select an option"
      />
    );

    const arrow = document.querySelector("svg");
    expect(arrow).toBeInTheDocument();
  });

  it("has proper accessibility attributes", () => {
    render(
      <Dropdown
        value=""
        onChange={mockOnChange}
        options={mockOptions}
        error={true}
        errorMessage="Error message"
        placeholder="Select an option"
      />
    );

    const select = screen.getByRole("combobox");
    expect(select).toHaveAttribute("aria-invalid", "true");
    expect(select).toHaveAttribute("aria-describedby");
  });

  it("handles different size variants", () => {
    const { rerender } = render(
      <Dropdown
        value=""
        onChange={mockOnChange}
        options={mockOptions}
        size="sm"
        placeholder="Select an option"
      />
    );

    let select = screen.getByRole("combobox");
    expect(select).toHaveClass("h-10");

    rerender(
      <Dropdown
        value=""
        onChange={mockOnChange}
        options={mockOptions}
        size="lg"
        placeholder="Select an option"
      />
    );

    select = screen.getByRole("combobox");
    expect(select).toHaveClass("h-14");
  });

  it("handles different variants", () => {
    const { rerender } = render(
      <Dropdown
        value=""
        onChange={mockOnChange}
        options={mockOptions}
        variant="compact"
        placeholder="Select an option"
      />
    );

    let select = screen.getByRole("combobox");
    expect(select).toHaveClass("border-[var(--grey-2)]");

    rerender(
      <Dropdown
        value=""
        onChange={mockOnChange}
        options={mockOptions}
        variant="default"
        placeholder="Select an option"
      />
    );

    select = screen.getByRole("combobox");
    expect(select).toHaveClass("border-[var(--grey-3)]");
  });

  it("handles empty options array", () => {
    render(
      <Dropdown
        value=""
        onChange={mockOnChange}
        options={[]}
        placeholder="No options available"
      />
    );

    const select = screen.getByRole("combobox");
    expect(select).toBeInTheDocument();
    expect(screen.getByText("No options available")).toBeInTheDocument();
  });

  it("can reset to empty value", () => {
    render(
      <Dropdown
        value="option1"
        onChange={mockOnChange}
        options={mockOptions}
        placeholder="Select an option"
      />
    );

    const select = screen.getByRole("combobox");
    fireEvent.change(select, { target: { value: "" } });

    expect(mockOnChange).toHaveBeenCalledWith("");
  });
});
