import React from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ShadcnUI/select";
import { cn } from "@/lib/utils";

export interface DropdownOption {
  id: string;
  text: string;
  value: string;
}

export interface DropdownProps {
  value: string;
  onChange: (value: string) => void;
  options: DropdownOption[];
  placeholder?: string;
  label?: string;
  id?: string;
  disabled?: boolean;
  required?: boolean;
  error?: boolean;
  errorMessage?: string;
  className?: string;
  variant?: "default" | "compact";
  size?: "sm" | "default" | "lg";
}

const Dropdown: React.FC<DropdownProps> = ({
  value,
  onChange,
  options,
  placeholder = "Select an option",
  label,
  id,
  disabled = false,
  required = false,
  error = false,
  errorMessage,
  className = "",
  variant = "default",
  size = "default",
}) => {
  const selectId = id || `dropdown-${Math.random().toString(36).substr(2, 9)}`;

  // Custom styling to match the original design
  const getTriggerClasses = () => {
    const baseClasses =
      "w-full !px-5 border rounded-sm text-[var(--grey-7)] text-base bg-white focus:outline-none focus:ring-[1/2] transition-all duration-200 appearance-none";

    const variantClasses = {
      default:
        "border-[var(--grey-3)] focus:ring-[var(--violet-11)] focus:border-[var(--violet-11)]",
      compact:
        "border-[var(--grey-2)] focus:ring-[var(--violet-11)] focus:border-[var(--violet-11)]",
    };

    const sizeClasses = {
      sm: "!h-10 !py-2 text-sm",
      default: "!h-[50px] !py-3.5 text-base font-bold",
      lg: "!h-14 !py-4 text-lg",
    };

    const stateClasses = error
      ? "border-red-300 focus:ring-red-500 focus:border-red-500"
      : variantClasses[variant];

    const disabledClasses = disabled
      ? "opacity-50 cursor-not-allowed bg-[var(--grey-1)]"
      : "";

    return cn(
      baseClasses,
      sizeClasses[size],
      stateClasses,
      disabledClasses,
      className
    );
  };

  return (
    <div className="w-full">
      {label && (
        <label
          htmlFor={selectId}
          className="block text-[var(--grey-6)] text-base font-medium mb-2"
        >
          {label}
          {required && (
            <span className="text-[var(--error-red-4)] ml-1">*</span>
          )}
        </label>
      )}

      <Select
        value={value}
        onValueChange={onChange}
        disabled={disabled}
        required={required}
      >
        <SelectTrigger
          id={selectId}
          className={getTriggerClasses()}
          aria-invalid={error}
          aria-describedby={
            error && errorMessage ? `${selectId}-error` : undefined
          }
        >
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem key={option.id} value={option.value}>
              {option.text}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {error && errorMessage && (
        <p
          id={`${selectId}-error`}
          className="mt-1 text-sm text-[var(--error-red-4)]"
          role="alert"
        >
          {errorMessage}
        </p>
      )}
    </div>
  );
};

export default Dropdown;
