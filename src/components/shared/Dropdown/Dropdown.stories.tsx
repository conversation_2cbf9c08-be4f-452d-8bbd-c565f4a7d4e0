import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import Dropdown from "./Dropdown";
import { DropdownOption } from "./Dropdown";

const meta = {
  title: "Components/Shared/Dropdown",
  component: Dropdown,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: { type: "select" },
      options: ["default", "compact"],
    },
    size: {
      control: { type: "select" },
      options: ["sm", "default", "lg"],
    },
    error: {
      control: { type: "boolean" },
    },
    disabled: {
      control: { type: "boolean" },
    },
    required: {
      control: { type: "boolean" },
    },
  },
} satisfies Meta<typeof Dropdown>;

export default meta;
type Story = StoryObj<typeof meta>;

const sampleOptions: DropdownOption[] = [
  { id: "1", text: "Option 1", value: "option1" },
  { id: "2", text: "Option 2", value: "option2" },
  { id: "3", text: "Option 3", value: "option3" },
  {
    id: "4",
    text: "A very long option text to test wrapping",
    value: "long-option",
  },
];

export const Default: Story = {
  args: {
    value: "",
    options: sampleOptions,
    placeholder: "Select an option",
    onChange: () => {},
  },
};

export const WithLabel: Story = {
  args: {
    value: "",
    label: "Choose your preference",
    options: sampleOptions,
    placeholder: "Select an option",
    onChange: () => {},
  },
};

export const Required: Story = {
  args: {
    value: "",
    label: "Required Field",
    required: true,
    options: sampleOptions,
    placeholder: "Select an option",
    onChange: () => {},
  },
};

export const WithValue: Story = {
  args: {
    value: "option2",
    label: "Pre-selected Option",
    options: sampleOptions,
    placeholder: "Select an option",
    onChange: () => {},
  },
};

export const Error: Story = {
  args: {
    value: "",
    label: "Field with Error",
    required: true,
    error: true,
    errorMessage: "This field is required",
    options: sampleOptions,
    placeholder: "Select an option",
    onChange: () => {},
  },
};

export const Disabled: Story = {
  args: {
    value: "option1",
    label: "Disabled Field",
    disabled: true,
    options: sampleOptions,
    placeholder: "Select an option",
    onChange: () => {},
  },
};

export const Compact: Story = {
  args: {
    value: "",
    label: "Compact Variant",
    variant: "compact",
    options: sampleOptions,
    placeholder: "Select an option",
    onChange: () => {},
  },
};

export const Small: Story = {
  args: {
    value: "",
    label: "Small Size",
    size: "sm",
    options: sampleOptions,
    placeholder: "Select an option",
    onChange: () => {},
  },
};

export const Large: Story = {
  args: {
    value: "",
    label: "Large Size",
    size: "lg",
    options: sampleOptions,
    placeholder: "Select an option",
    onChange: () => {},
  },
};

export const FewOptions: Story = {
  args: {
    value: "",
    label: "Few Options",
    options: [
      { id: "yes", text: "Yes", value: "yes" },
      { id: "no", text: "No", value: "no" },
    ],
    placeholder: "Select Yes or No",
    onChange: () => {},
  },
};

export const ManyOptions: Story = {
  args: {
    value: "",
    label: "Many Options",
    options: Array.from({ length: 20 }, (_, i) => ({
      id: `option-${i + 1}`,
      text: `Option ${i + 1}`,
      value: `option-${i + 1}`,
    })),
    placeholder: "Select from many options",
    onChange: () => {},
  },
};
