import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import ErrorMessage from "./ErrorMessage";

// Mock Next.js navigation
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  }),
}));

describe("ErrorMessage", () => {
  it("renders with required props", () => {
    render(<ErrorMessage message="Test error message" />);

    expect(screen.getByText("Test error message")).toBeInTheDocument();
  });

  it("applies default centered styling", () => {
    const { container } = render(<ErrorMessage message="Test error message" />);

    const errorDiv = container.firstChild;
    expect(errorDiv).toHaveClass("text-center");
  });

  it("applies left-aligned styling when centered is false", () => {
    const { container } = render(
      <ErrorMessage message="Test error message" centered={false} />
    );

    const errorDiv = container.firstChild;
    expect(errorDiv).not.toHaveClass("text-center");
  });

  it("applies custom className", () => {
    const { container } = render(
      <ErrorMessage message="Test error message" className="custom-class" />
    );

    const errorDiv = container.firstChild;
    expect(errorDiv).toHaveClass("custom-class");
  });

  it("applies default styling classes", () => {
    const { container } = render(<ErrorMessage message="Test error message" />);

    const errorDiv = container.firstChild;
    expect(errorDiv).toHaveClass(
      "text-[var(--error-red-4)]",
      "text-sm",
      "bg-[var(--error-red-1)]",
      "border",
      "border-[var(--error-red-2)]",
      "rounded-sm",
      "p-3",
      "mb-6"
    );
  });

  it("renders long error messages correctly", () => {
    const longMessage =
      "This is a very long error message that should still be displayed correctly and maintain all styling properties.";

    render(<ErrorMessage message={longMessage} />);

    expect(screen.getByText(longMessage)).toBeInTheDocument();
  });

  it("renders all props together correctly", () => {
    const { container } = render(
      <ErrorMessage
        message="Test error message"
        centered={false}
        className="mt-4"
      />
    );

    const errorDiv = container.firstChild;
    expect(screen.getByText("Test error message")).toBeInTheDocument();
    expect(errorDiv).not.toHaveClass("text-center");
    expect(errorDiv).toHaveClass("mt-4");
  });

  it("handles empty message gracefully", () => {
    const { container } = render(<ErrorMessage message="" />);

    const errorDiv = container.firstChild;
    expect(errorDiv).toBeInTheDocument();
    expect(errorDiv).toHaveTextContent("");
  });

  it("preserves message content exactly", () => {
    const messageWithSpecialChars =
      "Error: 404 - Resource not found (check URL)";

    render(<ErrorMessage message={messageWithSpecialChars} />);

    expect(screen.getByText(messageWithSpecialChars)).toBeInTheDocument();
  });
});
