import type { Meta, StoryObj } from "@storybook/nextjs";
import ErrorMessage from "./ErrorMessage";

const meta: Meta<typeof ErrorMessage> = {
  title: "Components/Shared/ErrorMessage",
  component: ErrorMessage,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    message: {
      control: "text",
      description: "The error message to display",
    },
    centered: {
      control: "boolean",
      description: "Whether to center the text",
    },
    className: {
      control: "text",
      description: "Additional CSS classes",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    message: "Please fix the errors below and try again.",
  },
};

export const LeftAligned: Story = {
  args: {
    message: "This error message is left-aligned.",
    centered: false,
  },
};

export const ValidationError: Story = {
  args: {
    message: "Please fill in all required fields.",
  },
};

export const LongErrorMessage: Story = {
  args: {
    message:
      "This is a longer error message that demonstrates how the component handles extended content. It provides detailed information about what went wrong and how to fix it.",
  },
};

export const SubmissionError: Story = {
  args: {
    message:
      "Failed to submit form. Please check your connection and try again.",
  },
};

export const WithCustomClass: Story = {
  args: {
    message: "Error with custom styling applied.",
    className: "mt-8",
  },
};
