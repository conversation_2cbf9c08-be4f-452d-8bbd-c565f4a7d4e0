import React from "react";

interface ErrorMessageProps {
  message: string;
  className?: string;
  centered?: boolean;
}

const ErrorMessage: React.FC<ErrorMessageProps> = ({
  message,
  className = "",
  centered = true,
}) => {
  return (
    <div
      className={`text-[var(--error-red-4)] text-sm bg-[var(--error-red-1)] border border-[var(--error-red-2)] rounded-sm p-3 mb-6 ${
        centered ? "text-center" : ""
      } ${className}`}
    >
      {message}
    </div>
  );
};

export default ErrorMessage;
