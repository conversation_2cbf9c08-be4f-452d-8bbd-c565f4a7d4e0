import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import NeedHelp from "./NeedHelp";

describe("NeedHelp Component", () => {
  describe("Rendering", () => {
    it("renders the 'Need Help?' text", () => {
      render(<NeedHelp />);
      expect(screen.getByText("Need Help?")).toBeInTheDocument();
    });

    it("renders the default phone number", () => {
      render(<NeedHelp />);
      expect(screen.getByText("+91-9990044555")).toBeInTheDocument();
    });

    it("renders custom phone number when provided", () => {
      render(<NeedHelp phoneNumber="******-567-8900" />);
      expect(screen.getByText("******-567-8900")).toBeInTheDocument();
    });

    it("renders phone number as a button", () => {
      render(<NeedHelp />);
      const phoneButton = screen.getByRole("button", {
        name: "+91-9990044555",
      });
      expect(phoneButton).toBeInTheDocument();
      expect(phoneButton).toHaveAttribute("type", "button");
    });
  });

  describe("Interactions", () => {
    it("calls onPhoneClick when phone button is clicked", () => {
      const mockOnPhoneClick = jest.fn();
      render(<NeedHelp onPhoneClick={mockOnPhoneClick} />);

      const phoneButton = screen.getByRole("button", {
        name: "+91-9990044555",
      });
      fireEvent.click(phoneButton);

      expect(mockOnPhoneClick).toHaveBeenCalledTimes(1);
      expect(mockOnPhoneClick).toHaveBeenCalledWith("+91-9990044555");
    });

    it("calls onPhoneClick with custom phone number", () => {
      const mockOnPhoneClick = jest.fn();
      const customNumber = "******-567-8900";
      render(
        <NeedHelp phoneNumber={customNumber} onPhoneClick={mockOnPhoneClick} />
      );

      const phoneButton = screen.getByRole("button", { name: customNumber });
      fireEvent.click(phoneButton);

      expect(mockOnPhoneClick).toHaveBeenCalledWith(customNumber);
    });

    it("calls default behavior when no onPhoneClick provided", () => {
      // Test that clicking without onPhoneClick doesn't throw an error
      render(<NeedHelp />);

      const phoneButton = screen.getByRole("button", {
        name: "+91-9990044555",
      });

      // This should not throw an error even though window.location.href will be set
      expect(() => {
        fireEvent.click(phoneButton);
      }).not.toThrow();
    });

    it("handles multiple phone clicks", () => {
      const mockOnPhoneClick = jest.fn();
      render(<NeedHelp onPhoneClick={mockOnPhoneClick} />);

      const phoneButton = screen.getByRole("button", {
        name: "+91-9990044555",
      });

      fireEvent.click(phoneButton);
      fireEvent.click(phoneButton);
      fireEvent.click(phoneButton);

      expect(mockOnPhoneClick).toHaveBeenCalledTimes(3);
    });
  });

  describe("Accessibility", () => {
    it("phone button is focusable", () => {
      render(<NeedHelp />);
      const phoneButton = screen.getByRole("button", {
        name: "+91-9990044555",
      });
      phoneButton.focus();
      expect(phoneButton).toHaveFocus();
    });

    it("phone button has focus styles", () => {
      render(<NeedHelp />);
      const phoneButton = screen.getByRole("button", {
        name: "+91-9990044555",
      });
      expect(phoneButton).toHaveClass(
        "text-[var(--grey-7)]",
        "text-lg",
        "font-medium",
        "hover:text-[var(--red-6)]"
      );
    });

    it("has proper button role", () => {
      render(<NeedHelp />);
      const phoneButton = screen.getByRole("button", {
        name: "+91-9990044555",
      });
      expect(phoneButton).toBeInTheDocument();
    });
  });

  describe("Styling", () => {
    it("has correct container classes", () => {
      render(<NeedHelp />);
      const container = screen.getByText("Need Help?").parentElement;
      expect(container).toHaveClass(
        "text-left",
        "flex",
        "flex-col",
        "items-start"
      );
    });

    it("has correct help text styling", () => {
      render(<NeedHelp />);
      const helpText = screen.getByText("Need Help?");
      expect(helpText).toHaveClass("text-[var(--grey-5)]", "text-base", "mb-2");
    });

    it("has correct phone button styling", () => {
      render(<NeedHelp />);
      const phoneButton = screen.getByRole("button", {
        name: "+91-9990044555",
      });
      expect(phoneButton).toHaveClass(
        "text-[var(--grey-7)]",
        "text-lg",
        "font-medium",
        "hover:text-[var(--red-6)]",
        "transition-colors",
        "duration-200"
      );
    });

    it("merges custom className with base classes", () => {
      render(<NeedHelp className="mt-4 shadow-lg" />);
      const container = screen.getByText("Need Help?").parentElement;
      expect(container).toHaveClass(
        "text-left",
        "flex",
        "flex-col",
        "items-start",
        "mt-4",
        "shadow-lg"
      );
    });
  });

  describe("Props", () => {
    it("uses default phone number when not provided", () => {
      render(<NeedHelp />);
      expect(screen.getByText("+91-9990044555")).toBeInTheDocument();
    });

    it("accepts custom phone number", () => {
      render(<NeedHelp phoneNumber="+44-20-1234-5678" />);
      expect(screen.getByText("+44-20-1234-5678")).toBeInTheDocument();
      expect(screen.queryByText("+91-9990044555")).not.toBeInTheDocument();
    });

    it("accepts custom className", () => {
      render(<NeedHelp className="custom-class" />);
      const container = screen.getByText("Need Help?").parentElement;
      expect(container).toHaveClass("custom-class");
    });

    it("works without any props", () => {
      render(<NeedHelp />);
      expect(screen.getByText("Need Help?")).toBeInTheDocument();
      expect(screen.getByText("+91-9990044555")).toBeInTheDocument();
    });
  });
});
