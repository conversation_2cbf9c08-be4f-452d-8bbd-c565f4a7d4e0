"use client";
import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, use<PERSON>ap<PERSON>vents } from "react-leaflet";
import "leaflet/dist/leaflet.css";
import { <PERSON><PERSON> } from "@/components/ShadcnUI/button";
import { MapPin, Check } from "lucide-react";

// Fix for default markers in react-leaflet
import L from "leaflet";
// eslint-disable-next-line @typescript-eslint/no-explicit-any
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png",
  iconUrl: "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png",
  shadowUrl: "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png",
});

interface MapPickerProps {
  latitude?: number;
  longitude?: number;
  onLocationSelect: (lat: number, lng: number) => void;
  onClose: () => void;
}

// Component to handle map click events
function MapClickHandler({ onLocationSelect }: { onLocationSelect: (lat: number, lng: number) => void }) {
  useMapEvents({
    click: (e) => {
      const { lat, lng } = e.latlng;
      onLocationSelect(lat, lng);
    },
  });
  return null;
}

export default function MapPicker({ latitude, longitude, onLocationSelect, onClose }: MapPickerProps) {
  const [selectedPosition, setSelectedPosition] = useState<[number, number] | null>(null);

  // Initialize with provided coordinates or default to New York
  const initialPosition: [number, number] = latitude && longitude 
    ? [latitude, longitude] 
    : [40.7128, -74.0060];

  useEffect(() => {
    if (latitude && longitude) {
      setSelectedPosition([latitude, longitude]);
    }
  }, [latitude, longitude]);

  const handleMapClick = (lat: number, lng: number) => {
    setSelectedPosition([lat, lng]);
  };

  const handleConfirm = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (selectedPosition) {
      onLocationSelect(selectedPosition[0], selectedPosition[1]);
      onClose();
    }
  };

  const handleGetCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          setSelectedPosition([latitude, longitude]);
        },
        (error) => {
          console.error("Error getting current location:", error);
          alert("Unable to get your current location. Please click on the map to select a location.");
        }
      );
    } else {
      alert("Geolocation is not supported by this browser.");
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
      <div className="bg-white rounded-lg shadow-xl w-[90vw] h-[80vh] max-w-4xl flex flex-col">
        <div className="p-4 border-b">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Select Location</h3>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
          </div>
          <p className="text-sm text-gray-600 mt-1">
            Click on the map to select a location, or use your current location
          </p>
        </div>
        
        <div className="flex-1 relative">
          <MapContainer
            center={selectedPosition || initialPosition}
            zoom={13}
            style={{ height: "100%", width: "100%" }}
            key={selectedPosition ? selectedPosition.join(",") : "initial"}
          >
            <TileLayer
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            />
            <MapClickHandler onLocationSelect={handleMapClick} />
            {selectedPosition && (
              <Marker position={selectedPosition} />
            )}
          </MapContainer>
        </div>

        <div className="p-4 border-t bg-gray-50">
          <div className="flex justify-between items-center">
            <div className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={handleGetCurrentLocation}
                className="flex items-center gap-2"
              >
                <MapPin className="h-4 w-4" />
                Use Current Location
              </Button>
            </div>
            
            <div className="flex gap-2">
              {selectedPosition && (
                <div className="text-sm text-gray-600 mr-4">
                  Selected: {selectedPosition[0].toFixed(6)}, {selectedPosition[1].toFixed(6)}
                </div>
              )}
              <Button
                type="button"
                onClick={handleConfirm}
                disabled={!selectedPosition}
                className="flex items-center gap-2"
              >
                <Check className="h-4 w-4" />
                Confirm Location
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
