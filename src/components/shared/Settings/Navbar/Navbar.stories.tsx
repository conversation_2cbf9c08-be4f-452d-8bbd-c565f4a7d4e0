import React from "react";
import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import Navbar from "./Navbar";

const meta: Meta<typeof Navbar> = {
  title: "Components/Settings/Navbar",
  component: Navbar,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A navigation component for settings pages with different menu items and states.",
      },
    },
  },
  argTypes: {
    activeItem: {
      control: {
        type: "select",
      },
      options: [
        "",
        "personal-details",
        "medical-fertility-info",
        "change-password",
        "help-support",
        "notification-preferences",
        "delete-account",
      ],
      description: "The ID of the currently active/selected item",
    },
    onItemClick: {
      description: "Callback function called when a nav item is clicked",
    },
    className: {
      control: "text",
      description: "Additional CSS classes to apply to the component",
    },
  },
  decorators: [
    (Story) => (
      <div style={{ width: "320px", padding: "20px" }}>
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    onItemClick: () => console.log("nav-item-clicked"),
  },
  parameters: {
    docs: {
      description: {
        story: "Default navbar with no active item selected.",
      },
    },
  },
};

export const WithPersonalDetailsActive: Story = {
  args: {
    activeItem: "personal-details",
    onItemClick: () => console.log("nav-item-clicked"),
  },
  parameters: {
    docs: {
      description: {
        story: "Navbar with 'Personal Details' item selected/active.",
      },
    },
  },
};

export const WithMedicalInfoActive: Story = {
  args: {
    activeItem: "medical-fertility-info",
    onItemClick: () => console.log("nav-item-clicked"),
  },
  parameters: {
    docs: {
      description: {
        story: "Navbar with 'Medical and Fertility Info' item selected/active.",
      },
    },
  },
};

export const WithChangePasswordActive: Story = {
  args: {
    activeItem: "change-password",
    onItemClick: () => console.log("nav-item-clicked"),
  },
  parameters: {
    docs: {
      description: {
        story: "Navbar with 'Change Password' item selected/active.",
      },
    },
  },
};

export const WithHelpSupportActive: Story = {
  args: {
    activeItem: "help-support",
    onItemClick: () => console.log("nav-item-clicked"),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Navbar with 'Help & Support' (danger variant) item selected/active.",
      },
    },
  },
};

export const WithNotificationPreferencesActive: Story = {
  args: {
    activeItem: "notification-preferences",
    onItemClick: () => console.log("nav-item-clicked"),
  },
  parameters: {
    docs: {
      description: {
        story: "Navbar with 'Notification Preferences' item selected/active.",
      },
    },
  },
};

export const WithDeleteAccountActive: Story = {
  args: {
    activeItem: "delete-account",
    onItemClick: () => console.log("nav-item-clicked"),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Navbar with 'Delete My Account' (danger variant) item selected/active.",
      },
    },
  },
};

export const WithCustomClassName: Story = {
  args: {
    activeItem: "personal-details",
    onItemClick: () => console.log("nav-item-clicked"),
    className: "shadow-lg border-2 border-purple-200",
  },
  parameters: {
    docs: {
      description: {
        story: "Navbar with custom styling applied via className prop.",
      },
    },
  },
};

export const Interactive: Story = {
  args: {
    onItemClick: () => console.log("nav-item-clicked"),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Interactive navbar - click on different items to see the action logs and hover effects.",
      },
    },
  },
  render: (args) => {
    const [activeItem, setActiveItem] = React.useState<string>("");

    const handleItemClick = (itemId: string) => {
      setActiveItem(itemId);
      args.onItemClick?.(itemId);
    };

    return (
      <Navbar {...args} activeItem={activeItem} onItemClick={handleItemClick} />
    );
  },
};
