import React from "react";
import Card from "../../Card/Card";
import Icon from "../../Icon/Icon";
import {
  BellRingingIcon,
  LifebuoyIcon,
  LockIcon,
  SignOutIcon,
  ThermometerIcon,
  UserIcon,
} from "@phosphor-icons/react";

interface NavbarItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  onClick?: () => void;
  variant?: "default" | "danger";
}

interface NavbarProps {
  activeItem?: string;
  currentPathname?: string;
  onItemClick?: (itemId: string) => void;
  className?: string;
}

const Navbar: React.FC<NavbarProps> = ({
  activeItem,
  currentPathname,
  onItemClick,
}) => {
  const navItems: NavbarItem[] = [
    {
      id: "personal-details",
      label: "Personal Details",
      icon: <UserIcon size={16} weight="bold" />,
    },
    {
      id: "medical-fertility-info",
      label: "Medical and Fertility Info",
      icon: <ThermometerIcon size={16} weight="bold" />,
    },
    {
      id: "change-password",
      label: "Change Password",
      icon: <LockIcon size={16} weight="bold" />,
    },
    {
      id: "help-support",
      label: "Help & Support",
      icon: <LifebuoyIcon size={16} weight="bold" />,
    },
    {
      id: "notification-preferences",
      label: "Notification Preferences",
      icon: <BellRingingIcon size={16} weight="bold" />,
    },
    {
      id: "delete-account",
      label: "Delete My Account",
      icon: <SignOutIcon size={16} weight="bold" />,
      variant: "danger",
    },
  ];

  // Helper function to determine if an item is active based on currentPathname
  const isActive = (itemId: string, currentPathname?: string) => {
    if (!currentPathname) return false;

    const routeMap: Record<string, string> = {
      "personal-details": "/user/settings/personal-details",
      "medical-fertility-info": "/user/settings/medical-info",
      "change-password": "/user/settings/change-password",
      "help-support": "/user/settings/help-support",
      "notification-preferences": "/user/settings/notification",
      "delete-account": "/user/settings/delete-account",
    };

    const expectedPath = routeMap[itemId];
    return expectedPath ? currentPathname.includes(expectedPath) : false;
  };

  const handleItemClick = (itemId: string) => {
    if (onItemClick) {
      onItemClick(itemId);
    }
  };

  return (
    <Card className="w-full xl:w-[280px] xl:h-[523px] px-6 py-2 border-none">
      <nav className="w-full flex flex-col gap-2">
        {navItems.map((item, index) => (
          <div
            key={item.id}
            className={`py-4 ${index === navItems.length - 2 ? "border-b border-[var(--grey-3)]" : ""}`}
          >
            <button
              onClick={() => handleItemClick(item.id)}
              className={`
                cursor-pointer w-full flex items-center gap-4 text-left transition-colors duration-200
                ${
                  activeItem === item.id || isActive(item.id, currentPathname)
                    ? "text-[var(--red-6)]"
                    : "hover:text-[var(--grey-10)]"
                }
                ${
                  item.variant === "danger"
                    ? "text-[var(--red-6)] hover:text-[var(--red-7)]"
                    : "text-[var(--grey-7)]"
                }
              `}
            >
              <Icon>{item.icon}</Icon>
              <span className="text-base font-medium">{item.label}</span>
            </button>
          </div>
        ))}
      </nav>
    </Card>
  );
};

export default Navbar;
