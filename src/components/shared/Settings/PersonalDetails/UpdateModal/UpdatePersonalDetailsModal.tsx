import React, { useState, useEffect } from "react";
import { XIcon } from "@phosphor-icons/react";
import Button, { ButtonType } from "../../../Button/Button";
import Input from "../../../Input/Input";
import Card from "../../../Card/Card";
import RadioSelect from "../../../RadioSelect/RadioSelect";
import {
  isValidEmail,
  isValidName,
  isValidPhoneNumber,
} from "@/lib/utils/inputValidations";

interface PersonalDetailsData {
  fullName: string;
  email: string;
  phoneNumber: string;
  dateOfBirth: string;
  gender: "Male" | "Female";
  maritalStatus: "Single" | "Married";
  address: string;
}

interface UpdatePersonalDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  data: PersonalDetailsData;
  onUpdate: (updatedData: PersonalDetailsData) => void;
}

// Function to calculate age from date of birth
const calculateAge = (dateOfBirth: string): number => {
  if (!dateOfBirth) return 0;

  const birthDate = new Date(dateOfBirth);
  const today = new Date();

  // Check if the date is valid
  if (isNaN(birthDate.getTime())) return 0;

  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();

  // If birthday hasn't occurred this year yet, subtract 1
  if (
    monthDiff < 0 ||
    (monthDiff === 0 && today.getDate() < birthDate.getDate())
  ) {
    age--;
  }

  // Return 0 for negative ages (future dates)
  return age >= 0 ? age : 0;
};

const UpdatePersonalDetailsModal: React.FC<UpdatePersonalDetailsModalProps> = ({
  isOpen,
  onClose,
  data,
  onUpdate,
}) => {
  const [formData, setFormData] = useState<PersonalDetailsData>(data);
  const [errors, setErrors] = useState<
    Partial<Record<keyof PersonalDetailsData, string>>
  >({});
  const [showValidation, setShowValidation] = useState(false);
  const [generalError, setGeneralError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset form data when modal opens with new data
  useEffect(() => {
    if (isOpen) {
      setFormData(data);
      setErrors({});
      setShowValidation(false);
      setGeneralError(null);
      setIsSubmitting(false);
    }
  }, [isOpen, data]);

  // Check if form is valid for real-time button state
  const isFormValid = (): boolean => {
    return !!(
      isValidName(formData.fullName) &&
      isValidEmail(formData.email) &&
      isValidPhoneNumber(formData.phoneNumber) &&
      formData.dateOfBirth.trim() &&
      formData.gender &&
      formData.maritalStatus &&
      Object.keys(errors).length === 0
    );
  };

  const handleInputChange = (
    field: keyof PersonalDetailsData,
    value: string
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Real-time validation for immediate feedback
    const newErrors = { ...errors };

    if (field === "fullName") {
      if (value.trim() && !isValidName(value)) {
        newErrors.fullName =
          "Name must be 2-100 characters, letters and spaces only";
      } else {
        delete newErrors.fullName;
      }
    }

    if (field === "email") {
      if (value.trim() && !isValidEmail(value)) {
        newErrors.email = "Please enter a valid email address";
      } else {
        delete newErrors.email;
      }
    }

    if (field === "phoneNumber") {
      if (value.trim() && !isValidPhoneNumber(value)) {
        newErrors.phoneNumber =
          "Phone must be 10-15 digits, no repeated numbers";
      } else {
        delete newErrors.phoneNumber;
      }
    }

    if (field === "dateOfBirth") {
      if (value.trim()) {
        const birthDate = new Date(value);
        const today = new Date();
        if (birthDate > today) {
          newErrors.dateOfBirth = "Date of birth cannot be in the future";
        } else if (isNaN(birthDate.getTime())) {
          newErrors.dateOfBirth = "Please enter a valid date";
        } else {
          const calculatedAge = calculateAge(value);
          if (calculatedAge < 16) {
            newErrors.dateOfBirth = "You must be at least 16 years old";
          } else if (calculatedAge > 100) {
            newErrors.dateOfBirth = "Please enter a valid date of birth";
          } else {
            delete newErrors.dateOfBirth;
          }
        }
      } else {
        delete newErrors.dateOfBirth;
      }
    }

    if (field === "address") {
      if (value.length > 200) {
        newErrors.address = "Address must be less than 200 characters";
      } else {
        delete newErrors.address;
      }
    }

    setErrors(newErrors);
  };

  const handleGenderChange = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      gender: value as "Male" | "Female",
    }));

    // Remove gender error if it exists
    const newErrors = { ...errors };
    delete newErrors.gender;
    setErrors(newErrors);
  };

  const handleMaritalStatusChange = (value: string) => {
    setFormData((prev) => ({
      ...prev,
      maritalStatus: value as "Single" | "Married",
    }));

    // Remove marital status error if it exists
    const newErrors = { ...errors };
    delete newErrors.maritalStatus;
    setErrors(newErrors);
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof PersonalDetailsData, string>> = {};
    setGeneralError(null);

    // Full Name validation
    if (!formData.fullName.trim()) {
      newErrors.fullName = "Full name is required";
    } else if (!isValidName(formData.fullName)) {
      newErrors.fullName =
        "Name must be 2-100 characters, letters and spaces only";
    }

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!isValidEmail(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    // Phone Number validation
    if (!formData.phoneNumber.trim()) {
      newErrors.phoneNumber = "Phone number is required";
    } else if (!isValidPhoneNumber(formData.phoneNumber)) {
      newErrors.phoneNumber =
        "Phone must be 10-15 digits, all repeated digits are not allowed";
    }

    // Date of Birth validation
    if (!formData.dateOfBirth.trim()) {
      newErrors.dateOfBirth = "Date of birth is required";
    } else {
      const birthDate = new Date(formData.dateOfBirth);
      const today = new Date();
      if (birthDate > today) {
        newErrors.dateOfBirth = "Date of birth cannot be in the future";
      } else if (isNaN(birthDate.getTime())) {
        newErrors.dateOfBirth = "Please enter a valid date";
      } else {
        const calculatedAge = calculateAge(formData.dateOfBirth);
        if (calculatedAge < 16) {
          newErrors.dateOfBirth = "You must be at least 16 years old";
        } else if (calculatedAge > 100) {
          newErrors.dateOfBirth = "Please enter a valid date of birth";
        }
      }
    }

    // Gender validation
    if (!formData.gender) {
      newErrors.gender = "Gender is required";
    }

    // Marital Status validation
    if (!formData.maritalStatus) {
      newErrors.maritalStatus = "Marital status is required";
    }

    // Address validation (optional but if provided should be valid)
    if (formData.address && formData.address.length > 200) {
      newErrors.address = "Address must be less than 200 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleUpdate = async () => {
    setShowValidation(true);
    if (validateForm()) {
      setIsSubmitting(true);
      try {
        await onUpdate(formData);
        onClose();
      } catch (error) {
        console.error("Failed to update personal details:", error);
        setGeneralError("Failed to update personal details. Please try again.");
      } finally {
        setIsSubmitting(false);
      }
    } else {
      setGeneralError("Please fix the errors above to continue.");
    }
  };

  const handleCancel = () => {
    setFormData(data); // Reset to original data
    setErrors({});
    setShowValidation(false);
    setGeneralError(null);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-[362px] md:w-[760px] bg-white rounded-lg shadow-lg max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold text-[var(--grey-7)]">
              Update Personal Details
            </h2>
            <button
              onClick={onClose}
              className="cursor-pointer w-10 h-10 p-1 bg-[var(--grey-2)] hover:bg-[var(--grey-3)] rounded-full flex items-center justify-center"
            >
              <XIcon size={24} className="text-[var(--grey-6)]" />
            </button>
          </div>

          {/* Form */}
          <div className="space-y-4">
            {/* Full Name */}
            <Input
              type="text"
              label="Full Name"
              required
              value={formData.fullName}
              onChange={(value) => handleInputChange("fullName", value)}
              placeholder="Enter your full name"
              error={showValidation && !!errors.fullName}
              errorMessage={showValidation ? errors.fullName : undefined}
              maxLength={100}
            />

            {/* Email */}
            <Input
              type="email"
              label="Email"
              required
              value={formData.email}
              onChange={(value) => handleInputChange("email", value)}
              placeholder="Enter your email"
              error={showValidation && !!errors.email}
              errorMessage={showValidation ? errors.email : undefined}
              maxLength={100}
            />

            {/* Phone Number */}
            <Input
              type="tel"
              label="Phone Number"
              required
              value={formData.phoneNumber}
              onChange={(value) => handleInputChange("phoneNumber", value)}
              placeholder="Enter your phone number"
              error={showValidation && !!errors.phoneNumber}
              errorMessage={showValidation ? errors.phoneNumber : undefined}
              maxLength={15}
            />

            {/* Date of Birth */}
            <Input
              type="date"
              label="Date of Birth"
              required
              value={formData.dateOfBirth}
              onChange={(value) => handleInputChange("dateOfBirth", value)}
              error={showValidation && !!errors.dateOfBirth}
              errorMessage={showValidation ? errors.dateOfBirth : undefined}
            />

            {/* Gender */}
            <div>
              <RadioSelect
                label="Gender"
                required
                value={formData.gender}
                onChange={handleGenderChange}
                options={[
                  { id: "female", text: "Female", value: "Female" },
                  { id: "male", text: "Male", value: "Male" },
                ]}
                variant="compact"
                direction="horizontal"
              />
              {showValidation && errors.gender && (
                <div className="text-[var(--error-red-4)] text-xs mt-1">
                  {errors.gender}
                </div>
              )}
            </div>

            {/* Marital Status */}
            <div>
              <RadioSelect
                label="Marital Status"
                required
                value={formData.maritalStatus}
                onChange={handleMaritalStatusChange}
                options={[
                  { id: "married", text: "Married", value: "Married" },
                  { id: "single", text: "Single", value: "Single" },
                ]}
                variant="compact"
                direction="horizontal"
              />
              {showValidation && errors.maritalStatus && (
                <div className="text-[var(--error-red-4)] text-xs mt-1">
                  {errors.maritalStatus}
                </div>
              )}
            </div>

            {/* Address */}
            <Input
              type="text"
              label="Address"
              value={formData.address}
              onChange={(value) => handleInputChange("address", value)}
              placeholder="Enter your address"
              error={showValidation && !!errors.address}
              errorMessage={showValidation ? errors.address : undefined}
              maxLength={200}
            />
          </div>

          {/* General Error Message */}
          {showValidation && generalError && (
            <div className="text-[var(--error-red-4)] text-sm mt-4 p-3 bg-red-50 rounded-lg border border-red-200">
              {generalError}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-3 mt-6">
            <Button
              type={ButtonType.SECONDARY}
              text="Cancel"
              onClick={handleCancel}
              className="flex-1"
              disabled={isSubmitting}
            />
            <Button
              type={ButtonType.PRIMARY}
              text={isSubmitting ? "Updating..." : "Update"}
              onClick={handleUpdate}
              className="flex-1"
              disabled={(!isFormValid() && showValidation) || isSubmitting}
            />
          </div>
        </div>
      </Card>
    </div>
  );
};

export default UpdatePersonalDetailsModal;
