import type { Meta, StoryObj } from "@storybook/nextjs";
import UpdatePersonalDetailsModal from "./UpdatePersonalDetailsModal";
import { useState } from "react";

const meta: Meta<typeof UpdatePersonalDetailsModal> = {
  title: "Components/Settings/PersonalDetails/UpdatePersonalDetailsModal",
  component: UpdatePersonalDetailsModal,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A modal component for updating personal details with form fields and toggle buttons.",
      },
    },
  },
  argTypes: {
    isOpen: {
      control: { type: "boolean" },
      description: "Controls whether the modal is open or closed",
    },
    onClose: { action: "modal closed" },
    onUpdate: { action: "data updated" },
    data: {
      control: { type: "object" },
      description: "Initial personal details data",
    },
  },
  decorators: [
    (Story) => (
      <div className="w-full h-screen bg-gray-100">
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof UpdatePersonalDetailsModal>;

// Sample data for stories
const sampleData = {
  fullName: "<PERSON><PERSON><PERSON>",
  email: "<EMAIL>",
  phoneNumber: "9056789876",
  dateOfBirth: "1996-12-22",
  gender: "Female" as const,
  maritalStatus: "Married" as const,
  address: "H-43, Sector 63, Noida (201302)",
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const InteractiveWrapper = (args: any) => {
  const [isOpen, setIsOpen] = useState(args.isOpen);
  const [data, setData] = useState(args.data);

  const handleClose = () => {
    setIsOpen(false);
    args.onClose?.();
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleUpdate = (updatedData: any) => {
    setData(updatedData);
    args.onUpdate?.(updatedData);
  };

  return (
    <div>
      <button
        onClick={() => setIsOpen(true)}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        Open Modal
      </button>
      <UpdatePersonalDetailsModal
        {...args}
        isOpen={isOpen}
        data={data}
        onClose={handleClose}
        onUpdate={handleUpdate}
      />
    </div>
  );
};

export const Default: Story = {
  args: {
    isOpen: true,
    data: sampleData,
  },
  render: InteractiveWrapper,
};

export const Closed: Story = {
  args: {
    isOpen: false,
    data: sampleData,
  },
  render: InteractiveWrapper,
};

export const MaleSingle: Story = {
  args: {
    isOpen: true,
    data: {
      ...sampleData,
      fullName: "Rajesh Kumar",
      email: "<EMAIL>",
      gender: "Male" as const,
      maritalStatus: "Single" as const,
    },
  },
  render: InteractiveWrapper,
};

export const EmptyFields: Story = {
  args: {
    isOpen: true,
    data: {
      fullName: "",
      email: "",
      phoneNumber: "",
      dateOfBirth: "",
      gender: "Female" as const,
      maritalStatus: "Single" as const,
      address: "",
    },
  },
  render: InteractiveWrapper,
};

export const LongData: Story = {
  args: {
    isOpen: true,
    data: {
      ...sampleData,
      fullName: "Dr. Priyanka Sharma-Mehra",
      email: "<EMAIL>",
      phoneNumber: "+91-98765-43210",
      address:
        "Flat No. 1234, Tower A, Building B, Complex C, Sector 45, Gurgaon, Haryana - 122001, India",
    },
  },
  render: InteractiveWrapper,
};

export const Interactive: Story = {
  args: {
    isOpen: false,
    data: sampleData,
  },
  render: InteractiveWrapper,
  parameters: {
    docs: {
      description: {
        story:
          "Interactive version where you can open the modal and test the form functionality.",
      },
    },
  },
};
