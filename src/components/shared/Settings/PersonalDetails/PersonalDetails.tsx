import React, { useState } from "react";
import Card from "../../Card/Card";
import Button, { ButtonType } from "../../Button/Button";
import Avatar from "../../Avatar/Avatar";
import {
  PencilIcon,
  PencilLineIcon,
  TrashIcon,
  UploadIcon,
} from "@phosphor-icons/react";
import { useScreenWidth } from "@/hooks/useScreenWidth";
import UpdatePersonalDetailsModal from "./UpdateModal/UpdatePersonalDetailsModal";

interface PersonalDetailsData {
  fullName: string;
  email: string;
  phoneNumber: string;
  dateOfBirth: string;
  gender: "Male" | "Female";
  address: string;
  maritalStatus: "Single" | "Married";
  profileImage?: string;
}

interface PersonalDetailsProps {
  data: PersonalDetailsData;
  onEditClick?: () => void;
  onReplaceImage?: () => void;
  onRemoveImage?: () => void;
  onDataUpdate?: (updatedData: PersonalDetailsData) => void;
  className?: string;
}

const PersonalDetails: React.FC<PersonalDetailsProps> = ({
  data,
  onEditClick,
  onReplaceImage,
  onRemoveImage,
  onDataUpdate,
  className = "",
}) => {
  const screenWidth = useScreenWidth();
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleEditClick = () => {
    setIsModalOpen(true);
    onEditClick?.();
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  const handleDataUpdate = (updatedData: PersonalDetailsData) => {
    onDataUpdate?.(updatedData);
  };

  const personalDetails = [
    { key: "fullName", label: "Full Name", value: data.fullName },
    { key: "email", label: "Email", value: data.email },
    {
      key: "phoneNumber",
      label: "Phone Number",
      value: data.phoneNumber,
    },
    {
      key: "dateOfBirth",
      label: "Date of Birth",
      value: data.dateOfBirth,
    },
    { key: "gender", label: "Gender", value: data.gender },
    {
      key: "maritalStatus",
      label: "Marital Status",
      value: data.maritalStatus,
    },
    {
      key: "address",
      label: "Address",
      value: data.address,
      fullWidth: true,
    },
  ];
  return (
    <div className={`w-full flex flex-col gap-2.5 md:gap-4`}>
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-[var(--grey-7)]">
          Personal Details
        </h1>
        <div>
          <Button
            type={ButtonType.PRIMARY}
            text={`${screenWidth <= 768 ? "" : "Edit Details"}`}
            icon={screenWidth <= 768 ? <PencilIcon size={20} /> : ""}
            onClick={handleEditClick}
            className="!font-bold"
          />
        </div>
      </div>

      {/* Profile Section */}
      <Card className="w-full py-7.5 px-5 md:py-10 md:px-17.5 border-none">
        <div className="w-full flex flex-col gap-2.5">
          {/* Profile Image Section */}
          <div className="flex items-center gap-10">
            <Avatar
              src={data.profileImage || "/assets/avatar.jpg"}
              alt={data.fullName}
              width={124}
              height={124}
              className="!border-2 !border-white"
            />

            {/* Image Action Buttons */}
            <div className="flex flex-col md:flex-row gap-2.5 md:gap-6">
              <button
                onClick={onReplaceImage}
                className="flex items-center gap-2 px-5 py-3 text-[var(--grey-6)] border-[1px] border-[var(--grey-2)] bg-[white] hover:bg-[var(--grey-2)] rounded-[4px] transition-colors duration-200"
              >
                <UploadIcon size={16} />
                <span className="text-sm font-medium">Replace</span>
              </button>
              <button
                onClick={onRemoveImage}
                className="flex items-center gap-2 px-5 py-3 text-[var(--grey-6)] border-[1px] border-[var(--grey-2)] bg-[var(--grey-1)] hover:bg-[var(--grey-2)] rounded-[4px] transition-colors duration-200"
              >
                <TrashIcon size={16} />
                <span className="text-sm font-medium">Remove</span>
              </button>
            </div>
          </div>

          {/* User Information Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-7.5 py-5 pr-7.5">
            {personalDetails.map((field) => (
              <div
                key={field.key}
                className={field.fullWidth ? "md:col-span-2" : ""}
              >
                <label className="block text-[var(--grey-6)] text-sm font-medium">
                  {field.label}
                </label>
                <p className="text-[var(--grey-7)] text-base font-bold">
                  {field.value}
                </p>
              </div>
            ))}
          </div>
        </div>
      </Card>

      {/* Update Modal */}
      <UpdatePersonalDetailsModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        data={data}
        onUpdate={handleDataUpdate}
      />
    </div>
  );
};

export default PersonalDetails;
