import React from "react";
import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import PersonalDetails from "./PersonalDetails";

const meta: Meta<typeof PersonalDetails> = {
  title: "Components/Settings/PersonalDetails",
  component: PersonalDetails,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A component that displays user's personal details with profile image and editable information fields.",
      },
    },
  },
  argTypes: {
    data: {
      description: "User's personal information data",
      control: "object",
    },
    onEditClick: {
      description:
        "Callback function called when Edit Details button is clicked",
    },
    onReplaceImage: {
      description:
        "Callback function called when Replace image button is clicked",
    },
    onRemoveImage: {
      description:
        "Callback function called when Remove image button is clicked",
    },
    className: {
      control: "text",
      description: "Additional CSS classes to apply to the component",
    },
  },
  decorators: [
    (Story) => (
      <div style={{ width: "800px", padding: "20px" }}>
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Sample data for stories
const sampleUserData = {
  fullName: "<PERSON><PERSON> Patel",
  email: "<EMAIL>",
  phoneNumber: "90 4664 8873",
  dateOfBirth: "22.04.1994",
  gender: "Female" as const,
  address: "H-43, Sector 63, Noida (201302)",
  maritalStatus: "Married" as const,
  profileImage: "/assets/avatar.jpg",
};

const incompleteUserData = {
  fullName: "John Doe",
  email: "<EMAIL>",
  phoneNumber: "Not provided",
  dateOfBirth: "Not provided",
  gender: "Male" as const,
  address: "Not provided",
  maritalStatus: "Single" as const,
};

const longDataUser = {
  fullName: "Dr. Anastasia Wilhelmina Richardson-Thompson",
  email: "<EMAIL>",
  phoneNumber: "****** 567 8900 ext. 1234",
  dateOfBirth: "15.03.1985",
  gender: "Female" as const,
  address:
    "Apartment 42B, Building Complex Name, Very Long Street Name, City District, State Name, Country (Postal Code 123456)",
  maritalStatus: "Married" as const,
  profileImage: "/assets/avatar.jpg",
};

export const Default: Story = {
  args: {
    data: sampleUserData,
    onEditClick: () => console.log("Edit clicked"),
    onReplaceImage: () => console.log("Replace image clicked"),
    onRemoveImage: () => console.log("Remove image clicked"),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Default personal details component with complete user information.",
      },
    },
  },
};

export const WithoutProfileImage: Story = {
  args: {
    data: {
      ...sampleUserData,
      profileImage: undefined,
    },
    onEditClick: () => console.log("Edit clicked"),
    onReplaceImage: () => console.log("Replace image clicked"),
    onRemoveImage: () => console.log("Remove image clicked"),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Personal details component without a profile image (uses default avatar).",
      },
    },
  },
};

export const IncompleteData: Story = {
  args: {
    data: incompleteUserData,
    onEditClick: () => console.log("Edit clicked"),
    onReplaceImage: () => console.log("Replace image clicked"),
    onRemoveImage: () => console.log("Remove image clicked"),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Personal details component with incomplete user data showing 'Not provided' values.",
      },
    },
  },
};

export const LongDataContent: Story = {
  args: {
    data: longDataUser,
    onEditClick: () => console.log("Edit clicked"),
    onReplaceImage: () => console.log("Replace image clicked"),
    onRemoveImage: () => console.log("Remove image clicked"),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Personal details component with very long content to test text wrapping and layout.",
      },
    },
  },
};

export const WithCustomClassName: Story = {
  args: {
    data: sampleUserData,
    onEditClick: () => console.log("Edit clicked"),
    onReplaceImage: () => console.log("Replace image clicked"),
    onRemoveImage: () => console.log("Remove image clicked"),
    className: "border-2 border-purple-200 rounded-lg",
  },
  parameters: {
    docs: {
      description: {
        story:
          "Personal details component with custom styling applied via className prop.",
      },
    },
  },
};

export const Interactive: Story = {
  args: {
    data: sampleUserData,
    onEditClick: () => console.log("Edit clicked"),
    onReplaceImage: () => console.log("Replace image clicked"),
    onRemoveImage: () => console.log("Remove image clicked"),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Interactive personal details component - click buttons to see action logs.",
      },
    },
  },
  render: (args) => {
    const [userData, setUserData] = React.useState(args.data);

    const handleEditClick = () => {
      console.log("Edit Details clicked - would open edit modal/form");
      args.onEditClick?.();
    };

    const handleReplaceImage = () => {
      console.log("Replace Image clicked - would open file picker");
      // Simulate image replacement
      setUserData((prev) => ({
        ...prev,
        profileImage: "/assets/avatar.jpg", // This would be the new image URL
      }));
      args.onReplaceImage?.();
    };

    const handleRemoveImage = () => {
      console.log("Remove Image clicked - image removed");
      setUserData((prev) => ({
        ...prev,
        profileImage: undefined,
      }));
      args.onRemoveImage?.();
    };

    return (
      <PersonalDetails
        {...args}
        data={userData}
        onEditClick={handleEditClick}
        onReplaceImage={handleReplaceImage}
        onRemoveImage={handleRemoveImage}
      />
    );
  },
};

export const MobileView: Story = {
  args: {
    data: sampleUserData,
    onEditClick: () => console.log("Edit clicked"),
    onReplaceImage: () => console.log("Replace image clicked"),
    onRemoveImage: () => console.log("Remove image clicked"),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Personal details component in mobile viewport to test responsive design.",
      },
    },
    viewport: {
      defaultViewport: "mobile1",
    },
  },
  decorators: [(Story) => <Story />],
};
