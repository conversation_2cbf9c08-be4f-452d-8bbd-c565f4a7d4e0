import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/nextjs";
import Sidebar from "./Sidebar";
import {
  LayoutGrid,
  Target,
  Palmtree,
  PersonStanding,
  Utensils,
  Baby,
  HandHeart,
  Users,
  Bell,
  LogOut,
} from "lucide-react";

const meta: Meta<typeof Sidebar> = {
  title: "Components/Shared/Sidebar",
  component: Sidebar,
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component:
          "A collapsible sidebar navigation component that can show either icons only or icons with text.",
      },
    },
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof Sidebar>;

const mockItems = [
  {
    icon: <LayoutGrid />,
    title: "Dashboard",
    pathname: "/dashboard",
    onClick: () => console.log("Dashboard clicked"),
  },
  {
    icon: <Target />,
    title: "IVF Success Score",
    pathname: "/ivf-success-score",
    onClick: () => console.log("IVF Success Score clicked"),
  },
  {
    icon: <Palmtree />,
    title: "IVF Journey",
    pathname: "/ivf-journey",
    onClick: () => console.log("IVF Journey clicked"),
  },
  {
    icon: <PersonStanding />,
    title: "Wellness & Lifestyle",
    pathname: "/wellness-lifestyle",
    onClick: () => console.log("Wellness & Lifestyle clicked"),
  },
  {
    icon: <Utensils />,
    title: "Fertility Diet Plan",
    pathname: "/fertility-diet-plan",
    onClick: () => console.log("Fertility Diet Plan clicked"),
  },
  {
    icon: <Baby />,
    title: "Good News Wall",
    pathname: "/good-news-wall",
    onClick: () => console.log("Good News Wall clicked"),
  },
  {
    icon: <HandHeart />,
    title: "Period Tracker",
    pathname: "/period-tracker",
    onClick: () => console.log("Period Tracker clicked"),
  },
  {
    icon: <Users />,
    title: "Community",
    pathname: "/community",
    onClick: () => console.log("Community clicked"),
  },
  {
    icon: <Bell />,
    title: "Notification",
    pathname: "/notification",
    onClick: () => console.log("Notification clicked"),
  },
  {
    icon: <LogOut />,
    title: "Logout",
    pathname: "/logout",
    onClick: () => console.log("Logout clicked"),
  },
];

export const Default: Story = {
  args: {
    items: mockItems,
    currentPathname: "/dashboard",
  },
};
