import React from "react";
import PopUpSidebar from "./PopUpSidebar";
import { GearIcon, LifebuoyIcon } from "@phosphor-icons/react";

const items = [
  {
    icon: <GearIcon />,
    title: "Dashboard",
    pathname: "/dashboard",
    onClick: () => {},
  },
  {
    icon: <LifebuoyIcon />,
    title: "Support",
    pathname: "/support",
    onClick: () => {},
  },
];

export default {
  title: "Components/Shared/PopUpSidebar",
  component: PopUpSidebar,
};

export const Default = () => (
  <PopUpSidebar
    items={items}
    isOpen={true}
    onClose={() => {}}
    currentPathname="/dashboard"
  />
);
