import React from "react";
import { render, screen } from "@testing-library/react";
import PopUpSidebar from "./PopUpSidebar";
import { GearIcon, LifebuoyIcon } from "@phosphor-icons/react";

// Mock Next.js router
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  }),
}));

describe("PopUpSidebar", () => {
  const items = [
    {
      icon: <GearIcon />,
      title: "Dashboard",
      pathname: "/dashboard",
      onClick: jest.fn(),
    },
    {
      icon: <LifebuoyIcon />,
      title: "Support",
      pathname: "/support",
      onClick: jest.fn(),
    },
  ];

  it("renders the sidebar and items", () => {
    render(
      <PopUpSidebar
        items={items}
        isOpen={true}
        onClose={jest.fn()}
        currentPathname="/dashboard"
      />
    );
    expect(screen.getByText("Dashboard")).toBeInTheDocument();
    expect(screen.getByText("Support")).toBeInTheDocument();
  });

  it("highlights the selected item", () => {
    render(
      <PopUpSidebar
        items={items}
        isOpen={true}
        onClose={jest.fn()}
        currentPathname="/support"
      />
    );
    const selected = screen.getByText("Support");
    expect(selected.parentElement).toHaveClass("bg-[var(--red-1)]");
  });
});
