import React from "react";
import { render, screen } from "@testing-library/react";
import Doctor<PERSON><PERSON> from "./DoctorCard";

describe("DoctorCard", () => {
  it("renders doctor details correctly", () => {
    render(
      <DoctorCard
        imageUrl="/test-image.jpg"
        name="Dr. Aarti <PERSON>"
        title="Fertility Consultant, Obstetrician"
        experience="9+ years"
      />
    );
    expect(screen.getByAltText("Dr. Aarti Mehra")).toBeInTheDocument();
    expect(screen.getByText("Dr. Aarti Mehra")).toBeInTheDocument();
    expect(
      screen.getByText("Fertility Consultant, Obstetrician")
    ).toBeInTheDocument();
    expect(screen.getByText("9+ years")).toBeInTheDocument();
  });
});
