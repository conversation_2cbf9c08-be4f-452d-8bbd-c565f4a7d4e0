import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import UserProfileDropdown from "./UserProfileDropdown";

const mockUser = {
  name: "<PERSON><PERSON>",
  email: "<EMAIL>",
  avatar: "/assets/avatar.jpg",
};

const defaultProps = {
  isOpen: true,
  onClose: jest.fn(),
  user: mockUser,
  onProfileClick: jest.fn(),
  onSettingsClick: jest.fn(),
  onHelpSupportClick: jest.fn(),
  onNotificationPreferencesClick: jest.fn(),
  onLogoutClick: jest.fn(),
};

describe("UserProfileDropdown", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders user profile information correctly", () => {
    render(<UserProfileDropdown {...defaultProps} />);

    expect(screen.getByText("<PERSON><PERSON> Patel")).toBeInTheDocument();
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
  });

  it("renders all menu items", () => {
    render(<UserProfileDropdown {...defaultProps} />);

    expect(screen.getByText("Profile")).toBeInTheDocument();
    expect(screen.getByText("Settings")).toBeInTheDocument();
    expect(screen.getByText("Help & Support")).toBeInTheDocument();
    expect(screen.getByText("Notification Preferences")).toBeInTheDocument();
    expect(screen.getByText("Logout")).toBeInTheDocument();
  });

  it("does not render when isOpen is false", () => {
    render(<UserProfileDropdown {...defaultProps} isOpen={false} />);

    expect(screen.queryByText("Priya Patel")).not.toBeInTheDocument();
  });

  it("calls onClose when overlay is clicked", () => {
    render(<UserProfileDropdown {...defaultProps} />);

    const overlay = document.querySelector(".fixed.inset-0");
    fireEvent.click(overlay!);

    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it("calls correct callback and closes dropdown when Profile is clicked", () => {
    render(<UserProfileDropdown {...defaultProps} />);

    const profileButton = screen.getByText("Profile");
    fireEvent.click(profileButton);

    expect(defaultProps.onProfileClick).toHaveBeenCalledTimes(1);
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it("calls correct callback and closes dropdown when Settings is clicked", () => {
    render(<UserProfileDropdown {...defaultProps} />);

    const settingsButton = screen.getByText("Settings");
    fireEvent.click(settingsButton);

    expect(defaultProps.onSettingsClick).toHaveBeenCalledTimes(1);
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it("calls correct callback and closes dropdown when Help & Support is clicked", () => {
    render(<UserProfileDropdown {...defaultProps} />);

    const helpButton = screen.getByText("Help & Support");
    fireEvent.click(helpButton);

    expect(defaultProps.onHelpSupportClick).toHaveBeenCalledTimes(1);
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it("calls correct callback and closes dropdown when Notification Preferences is clicked", () => {
    render(<UserProfileDropdown {...defaultProps} />);

    const notificationButton = screen.getByText("Notification Preferences");
    fireEvent.click(notificationButton);

    expect(defaultProps.onNotificationPreferencesClick).toHaveBeenCalledTimes(
      1
    );
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it("calls correct callback and closes dropdown when Logout is clicked", () => {
    render(<UserProfileDropdown {...defaultProps} />);

    const logoutButton = screen.getByText("Logout");
    fireEvent.click(logoutButton);

    expect(defaultProps.onLogoutClick).toHaveBeenCalledTimes(1);
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it("uses default user data when no user prop is provided", () => {
    const propsWithoutUser = {
      isOpen: defaultProps.isOpen,
      onClose: defaultProps.onClose,
      onProfileClick: defaultProps.onProfileClick,
      onSettingsClick: defaultProps.onSettingsClick,
      onHelpSupportClick: defaultProps.onHelpSupportClick,
      onNotificationPreferencesClick:
        defaultProps.onNotificationPreferencesClick,
      onLogoutClick: defaultProps.onLogoutClick,
    };

    render(<UserProfileDropdown {...propsWithoutUser} />);

    expect(screen.getByText("Priya Patel")).toBeInTheDocument();
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
  });

  it("handles menu item click when callback is undefined", () => {
    const propsWithoutCallbacks = {
      ...defaultProps,
      onProfileClick: undefined,
    };

    render(<UserProfileDropdown {...propsWithoutCallbacks} />);

    const profileButton = screen.getByText("Profile");

    // Should not throw error when callback is undefined
    expect(() => fireEvent.click(profileButton)).not.toThrow();
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });
});
