import React from "react";
import Avatar from "../Avatar/Avatar";
import ProfileMenuItem from "../ProfileMenuItem";
import {
  BellRingingIcon,
  GearSixIcon,
  LifebuoyIcon,
  SignOutIcon,
  UserIcon,
} from "@phosphor-icons/react";
import { Badge } from "@/components/ShadcnUI/badge";

export interface UserProfileDropdownProps {
  isOpen: boolean;
  onClose: () => void;
  user?: {
    name: string;
    email: string;
    avatar?: string;
    role?: string
  };
  onProfileClick?: () => void;
  onSettingsClick?: () => void;
  onHelpSupportClick?: () => void;
  onNotificationPreferencesClick?: () => void;
  onLogoutClick?: () => void;
}

const UserProfileDropdown: React.FC<UserProfileDropdownProps> = ({
  isOpen,
  onClose,
  user = {
    name: "<PERSON><PERSON>",
    email: "<EMAIL>",
    avatar: "/assets/avatar.jpg"
  },
  onProfileClick,
  onSettingsClick,
  onHelpSupportClick,
  onNotificationPreferencesClick,
  onLogoutClick,
}) => {
  if (!isOpen) return null;

  const handleItemClick = (callback?: () => void) => {
    callback?.();
    onClose();
  };

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 z-40" onClick={onClose} />

      {/* Dropdown */}
      <div className="absolute right-0 top-full mt-2 w-75.75 bg-white py-2 px-6 rounded-md shadow-md shadow-[#CBCBDD36] z-50 flex flex-col gap-2">
        {/* User Profile Section */}
        <div className="py-3">
          <div className="flex items-center gap-4">
            <Avatar src={user.avatar} alt={user.name} width={46} height={46} />
            <div className="flex-1 flex flex-col gap-0.5">
              <div className="text-base font-bold text-[var(--grey-7)]">
                {user.name}
              </div>
              <div className="text-sm font-medium text-[var(--grey-6)]">
                {user.email}
              </div>
              {user.role && (
                <Badge variant="secondary">{user.role}</Badge>
              )}
            </div>
          </div>
        </div>

        {/* Menu Items */}
        <div className="py-4 border-b-1 border-t-1 border-[var(--grey-2)] flex flex-col justify-around gap-6">
          {/* Profile */}
          <ProfileMenuItem
            icon={<UserIcon size={16} />}
            text="Profile"
            onClick={() => handleItemClick(onProfileClick)}
          />

          {/* Settings */}
          <ProfileMenuItem
            icon={<GearSixIcon size={16} />}
            text="Settings"
            onClick={() => handleItemClick(onSettingsClick)}
          />

          {/* Help & Support */}
          <ProfileMenuItem
            icon={<LifebuoyIcon size={16} />}
            text="Help & Support"
            onClick={() => handleItemClick(onHelpSupportClick)}
          />

          {/* Notification Preferences */}
          <ProfileMenuItem
            icon={<BellRingingIcon size={16} />}
            text="Notification Preferences"
            onClick={() => handleItemClick(onNotificationPreferencesClick)}
          />
        </div>
        <div className="py-3">
          {/* Logout */}
          <ProfileMenuItem
            icon={<SignOutIcon size={16} />}
            text="Logout"
            variant="primary"
            onClick={() => handleItemClick(onLogoutClick)}
          />
        </div>
      </div>
    </>
  );
};

export default UserProfileDropdown;
