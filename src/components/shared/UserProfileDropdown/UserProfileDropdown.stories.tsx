import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import UserProfileDropdown from "./UserProfileDropdown";

const meta: Meta<typeof UserProfileDropdown> = {
  title: "Components/Shared/UserProfileDropdown",
  component: UserProfileDropdown,
  parameters: {
    layout: "padded",
    docs: {
      description: {
        component:
          "A user profile dropdown component that displays user information and navigation menu items. Features avatar display, user name and email, and menu options for profile management.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    isOpen: {
      control: { type: "boolean" },
      description: "Controls whether the dropdown is visible",
    },
    user: {
      control: { type: "object" },
      description: "User information including name, email, and avatar",
    },
    onClose: {
      action: "onClose",
      description: "Callback when dropdown should be closed",
    },
    onProfileClick: {
      action: "onProfileClick",
      description: "Callback when Profile menu item is clicked",
    },
    onSettingsClick: {
      action: "onSettingsClick",
      description: "Callback when Settings menu item is clicked",
    },
    onHelpSupportClick: {
      action: "onHelpSupportClick",
      description: "Callback when Help & Support menu item is clicked",
    },
    onNotificationPreferencesClick: {
      action: "onNotificationPreferencesClick",
      description:
        "Callback when Notification Preferences menu item is clicked",
    },
    onLogoutClick: {
      action: "onLogoutClick",
      description: "Callback when Logout menu item is clicked",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    isOpen: true,
    user: {
      name: "Priya Patel",
      email: "<EMAIL>",
      avatar: "/assets/avatar.jpg",
    },
  },
  decorators: [
    (Story) => (
      <div className="relative w-80 h-96 border border-gray-200 bg-gray-50 p-4">
        <div className="absolute top-4 right-4">
          <Story />
        </div>
      </div>
    ),
  ],
};

export const CustomUser: Story = {
  args: {
    isOpen: true,
    user: {
      name: "John Doe",
      email: "<EMAIL>",
      avatar: "/assets/avatar.jpg",
    },
  },
  decorators: [
    (Story) => (
      <div className="relative w-80 h-96 border border-gray-200 bg-gray-50 p-4">
        <div className="absolute top-4 right-4">
          <Story />
        </div>
      </div>
    ),
  ],
};

export const Closed: Story = {
  args: {
    isOpen: false,
    user: {
      name: "Priya Patel",
      email: "<EMAIL>",
      avatar: "/assets/avatar.jpg",
    },
  },
  decorators: [
    (Story) => (
      <div className="relative w-80 h-96 border border-gray-200 bg-gray-50 p-4">
        <div className="text-center text-gray-500 mt-8">
          Dropdown is closed - set isOpen to true to see it
        </div>
        <div className="absolute top-4 right-4">
          <Story />
        </div>
      </div>
    ),
  ],
};
