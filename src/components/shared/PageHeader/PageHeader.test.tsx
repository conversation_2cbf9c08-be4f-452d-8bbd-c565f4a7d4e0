import React from "react";
import { render, screen } from "@testing-library/react";
import PageHeader from "./PageHeader";

// Mock Next.js Image component
jest.mock("next/image", () => ({
  __esModule: true,
  default: ({
    src,
    alt,
    width,
    height,
    className,
  }: {
    src: string;
    alt: string;
    width: number;
    height: number;
    className?: string;
  }) => (

    // eslint-disable-next-line @next/next/no-img-element
    <img
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={className}
      data-testid="page-header-image"
    />
  ),
}));

describe("PageHeader", () => {
  it("renders the component with title", () => {
    render(<PageHeader title="Test Title" />);
    expect(
      screen.getByRole("heading", { name: "Test Title Decorative line" })
    ).toBeInTheDocument();
  });

  it("renders the decorative line image", () => {
    render(<PageHeader title="Test Title" />);
    const image = screen.getByTestId("page-header-image");
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute("src", "/assets/loginPage/Line.png");
    expect(image).toHaveAttribute("alt", "Decorative line");
  });

  it("applies default styles", () => {
    render(<PageHeader title="Test Title" />);
    const heading = screen.getByRole("heading", {
      name: "Test Title Decorative line",
    });
    expect(heading).toHaveClass("text-[var(--grey-7)]");
    expect(heading).toHaveClass("text-2xl");
    expect(heading).toHaveClass("md:text-[1.75rem]");
    expect(heading).toHaveClass("font-bold");
    expect(heading).toHaveClass("mb-3");
  });

  it("accepts custom className", () => {
    render(<PageHeader title="Test Title" className="custom-class" />);
    const heading = screen.getByRole("heading", {
      name: "Test Title Decorative line",
    });
    expect(heading).toHaveClass("custom-class");
  });

  it("renders with different titles", () => {
    const { rerender } = render(<PageHeader title="First Title" />);
    expect(screen.getByText("First Title")).toBeInTheDocument();

    rerender(<PageHeader title="Second Title" />);
    expect(screen.getByText("Second Title")).toBeInTheDocument();
    expect(screen.queryByText("First Title")).not.toBeInTheDocument();
  });

  it("has proper semantic structure", () => {
    render(<PageHeader title="Test Title" />);
    const heading = screen.getByRole("heading", { level: 1 });
    expect(heading).toBeInTheDocument();
  });

  it("decorative line has proper styling", () => {
    render(<PageHeader title="Test Title" />);
    const image = screen.getByTestId("page-header-image");
    expect(image).toHaveClass("h-1");
    expect(image).toHaveClass("w-16");
  });

  it("decorative line container has proper styling", () => {
    render(<PageHeader title="Test Title" />);
    const lineContainer = screen
      .getByTestId("page-header-image")
      .closest("div");
    expect(lineContainer).toHaveClass("flex");
    expect(lineContainer).toHaveClass("justify-center");
    expect(lineContainer).toHaveClass("py-1");
  });

  it("handles empty title", () => {
    render(<PageHeader title="" />);
    const heading = screen.getByRole("heading");
    expect(heading).toBeInTheDocument();
    expect(heading).toHaveTextContent("");
  });

  it("handles long titles", () => {
    const longTitle =
      "This is a very long title that might wrap to multiple lines";
    render(<PageHeader title={longTitle} />);
    expect(screen.getByText(longTitle)).toBeInTheDocument();
  });

  it("has responsive text sizing", () => {
    render(<PageHeader title="Test Title" />);
    const heading = screen.getByRole("heading", {
      name: "Test Title Decorative line",
    });
    expect(heading).toHaveClass("text-2xl");
    expect(heading).toHaveClass("md:text-[1.75rem]");
  });

  it("image has correct dimensions", () => {
    render(<PageHeader title="Test Title" />);
    const image = screen.getByTestId("page-header-image");
    expect(image).toHaveAttribute("width", "64");
    expect(image).toHaveAttribute("height", "4");
  });

  it("combines custom className with default classes", () => {
    render(<PageHeader title="Test Title" className="text-center" />);
    const heading = screen.getByRole("heading", {
      name: "Test Title Decorative line",
    });
    expect(heading).toHaveClass("text-center");
    expect(heading).toHaveClass("text-[var(--grey-7)]");
    expect(heading).toHaveClass("font-bold");
  });

  it("renders special characters in title", () => {
    const specialTitle = "Create Your GIVF Account! & More";
    render(<PageHeader title={specialTitle} />);
    expect(screen.getByText(specialTitle)).toBeInTheDocument();
  });

  it("has proper accessibility attributes", () => {
    render(<PageHeader title="Test Title" />);
    const image = screen.getByTestId("page-header-image");
    expect(image).toHaveAttribute("alt", "Decorative line");
  });

  it("maintains structure when title changes", () => {
    const { rerender } = render(<PageHeader title="Original Title" />);
    expect(screen.getByTestId("page-header-image")).toBeInTheDocument();

    rerender(<PageHeader title="New Title" />);
    expect(screen.getByTestId("page-header-image")).toBeInTheDocument();
    expect(screen.getByText("New Title")).toBeInTheDocument();
  });

  it("handles undefined className gracefully", () => {
    render(<PageHeader title="Test Title" className={undefined} />);
    const heading = screen.getByRole("heading", {
      name: "Test Title Decorative line",
    });
    expect(heading).toHaveClass("text-[var(--grey-7)]");
  });
});
