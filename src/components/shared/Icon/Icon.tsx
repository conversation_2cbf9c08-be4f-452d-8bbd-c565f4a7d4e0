import React from "react";

interface IconProps {
  children: React.ReactNode;
  className?: string;
  size?: number;
  color?: string;
}

const Icon: React.FC<IconProps> = ({
  children,
  className = "",
  size = 16,
  color,
}) => {
  return (
    <span
      className={`inline-flex items-center justify-center ${className}`}
      style={{
        width: size,
        height: size,
        color: color,
      }}
    >
      {children}
    </span>
  );
};

export default Icon;
