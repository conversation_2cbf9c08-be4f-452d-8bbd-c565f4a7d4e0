import React from "react";

export enum ButtonType {
  PRIMARY = "primary",
  SECONDARY = "secondary",
}

export enum ButtonSubmitType {
  SUBMIT = "submit",
  BUTTON = "button",
}

export interface ButtonProps {
  type: ButtonType;
  submitType?: ButtonSubmitType;
  text?: string;
  icon?: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
  size?: "sm" | "default" | "lg";
  loading?: boolean;
  loadingText?: string;
}

const Button: React.FC<ButtonProps> = ({
  type,
  submitType = ButtonSubmitType.BUTTON,
  text,
  icon,
  onClick,
  disabled = false,
  className = "",
  size = "default",
  loading = false,
  loadingText,
}) => {
  const baseClasses =
    "w-full flex items-center justify-center gap-2 font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed rounded-[0.25rem] cursor-pointer";

  const typeClasses = {
    [ButtonType.PRIMARY]:
      "bg-[var(--red-6)] text-white hover:bg-[var(--red-7)] border-0",
    [ButtonType.SECONDARY]:
      "bg-white text-[var(--darker-grey)] border-[1px] border-[var(--light-grey)] hover:bg-[var(--light-grey)] hover:border-[var(--light-grey)] focus:ring-[var(--light-grey)]",
  };

  const sizeClasses = {
    sm: "px-4 py-2 text-sm h-10",
    default: "px-7.5 py-3.5 text-base h-[3.125rem]",
    lg: "px-[1.875rem] py-[0.875rem] text-xl h-16",
  };

  const buttonClasses = `${baseClasses} ${typeClasses[type]} ${sizeClasses[size]} ${className}`;

  const displayText = loading ? (loadingText || text) : text;
  const isDisabled = disabled || loading;

  return (
    <button
      type={submitType}
      className={buttonClasses}
      onClick={onClick}
      disabled={isDisabled}
    >
      {loading && (
        <svg
          className="animate-spin -ml-1 mr-2 h-4 w-4"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          ></circle>
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
      )}
      {displayText}
      {icon && !loading && <span className={`${text ? "ml-2" : ""}`}>{icon}</span>}
    </button>
  );
};

export default Button;
