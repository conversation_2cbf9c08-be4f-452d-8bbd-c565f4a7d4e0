import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import RadioSelect from "./RadioSelect";
import { RadioOption } from "./RadioSelect";

const meta = {
  title: "Components/Shared/RadioSelect",
  component: RadioSelect,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    variant: {
      control: { type: "select" },
      options: ["default", "compact"],
    },
    direction: {
      control: { type: "select" },
      options: ["horizontal", "vertical"],
    },
    error: {
      control: { type: "boolean" },
    },
    disabled: {
      control: { type: "boolean" },
    },
    required: {
      control: { type: "boolean" },
    },
  },
} satisfies Meta<typeof RadioSelect>;

export default meta;
type Story = StoryObj<typeof meta>;

const sampleOptions: RadioOption[] = [
  { id: "1", text: "Option 1", value: "option1" },
  { id: "2", text: "Option 2", value: "option2" },
  { id: "3", text: "Option 3", value: "option3" },
];

const yesNoOptions: RadioOption[] = [
  { id: "yes", text: "Yes", value: "yes" },
  { id: "no", text: "No", value: "no" },
];

const frequencyOptions: RadioOption[] = [
  { id: "daily", text: "Daily", value: "daily" },
  { id: "weekly", text: "2-3 times/week", value: "weekly" },
  { id: "rarely", text: "Rarely/Never", value: "rarely" },
];

export const Default: Story = {
  args: {
    value: "",
    options: sampleOptions,
    onChange: () => {},
  },
};

export const WithLabel: Story = {
  args: {
    value: "",
    label: "Choose your preference",
    options: sampleOptions,
    onChange: () => {},
  },
};

export const Required: Story = {
  args: {
    value: "",
    label: "Required Selection",
    required: true,
    options: sampleOptions,
    onChange: () => {},
  },
};

export const WithValue: Story = {
  args: {
    value: "option2",
    label: "Pre-selected Option",
    options: sampleOptions,
    onChange: () => {},
  },
};

export const Error: Story = {
  args: {
    value: "",
    label: "Field with Error",
    required: true,
    error: true,
    errorMessage: "Please select an option",
    options: sampleOptions,
    onChange: () => {},
  },
};

export const Disabled: Story = {
  args: {
    value: "option1",
    label: "Disabled Field",
    disabled: true,
    options: sampleOptions,
    onChange: () => {},
  },
};

export const Vertical: Story = {
  args: {
    value: "",
    label: "Vertical Layout",
    direction: "vertical",
    options: sampleOptions,
    onChange: () => {},
  },
};

export const DefaultVariant: Story = {
  args: {
    value: "",
    label: "Default Variant",
    variant: "default",
    options: sampleOptions,
    onChange: () => {},
  },
};

export const YesNo: Story = {
  args: {
    value: "",
    label: "Yes/No Question",
    options: yesNoOptions,
    onChange: () => {},
  },
};

export const FrequencySelection: Story = {
  args: {
    value: "",
    label: "Exercise Frequency",
    options: frequencyOptions,
    onChange: () => {},
  },
};

export const ManyOptions: Story = {
  args: {
    value: "",
    label: "Many Options (wrapping)",
    options: [
      { id: "1", text: "Very Short", value: "short" },
      { id: "2", text: "Medium Length Option", value: "medium" },
      { id: "3", text: "This is a very long option text", value: "long" },
      { id: "4", text: "Another", value: "another" },
      {
        id: "5",
        text: "Extra Long Option That Tests Wrapping",
        value: "extra-long",
      },
      { id: "6", text: "Final", value: "final" },
    ],
    onChange: () => {},
  },
};

export const VerticalManyOptions: Story = {
  args: {
    value: "",
    label: "Many Options (vertical)",
    direction: "vertical",
    options: [
      { id: "1", text: "First Option", value: "first" },
      { id: "2", text: "Second Option with longer text", value: "second" },
      { id: "3", text: "Third Option", value: "third" },
      {
        id: "4",
        text: "Fourth Option with even longer text that might wrap",
        value: "fourth",
      },
      { id: "5", text: "Fifth Option", value: "fifth" },
    ],
    onChange: () => {},
  },
};
