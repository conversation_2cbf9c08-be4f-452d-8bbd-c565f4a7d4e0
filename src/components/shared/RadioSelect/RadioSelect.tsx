import React from "react";
import ToggleButton from "@/components/shared/ToggleButton/ToggleButton";

export interface RadioOption {
  id: string;
  text: string;
  value: string;
}

export interface RadioSelectProps {
  value: string;
  onChange: (value: string) => void;
  options: RadioOption[];
  label?: string;
  id?: string;
  disabled?: boolean;
  required?: boolean;
  error?: boolean;
  errorMessage?: string;
  className?: string;
  variant?: "default" | "compact";
  direction?: "horizontal" | "vertical";
}

const RadioSelect: React.FC<RadioSelectProps> = ({
  value,
  onChange,
  options,
  label,
  id,
  disabled = false,
  required = false,
  error = false,
  errorMessage,
  className = "",
  variant = "compact",
  direction = "horizontal",
}) => {
  const radioGroupId =
    id || `radio-group-${Math.random().toString(36).substr(2, 9)}`;

  const containerClasses =
    direction === "horizontal" ? "flex gap-2 flex-wrap" : "flex flex-col gap-2";

  const handleToggleClick = (optionValue: string) => {
    if (disabled) return;

    // If the clicked option is already selected, deselect it (pass empty string)
    // Otherwise, select the new option
    if (value === optionValue) {
      onChange(""); // Deselect by passing empty string
    } else {
      onChange(optionValue); // Select the new option
    }
  };

  return (
    <div className={`w-full ${className}`}>
      {label && (
        <label
          htmlFor={radioGroupId}
          className="block text-[var(--grey-6)] text-base font-medium mb-3"
        >
          {label}
          {required && (
            <span className="text-[var(--error-red-4)] ml-1">*</span>
          )}
        </label>
      )}

      <div
        id={radioGroupId}
        role="radiogroup"
        aria-labelledby={label ? `${radioGroupId}-label` : undefined}
        aria-invalid={error}
        aria-describedby={
          error && errorMessage ? `${radioGroupId}-error` : undefined
        }
        className={containerClasses}
      >
        {options.map((option) => (
          <ToggleButton
            key={option.id}
            isSelected={value === option.value}
            onClick={() => handleToggleClick(option.value)}
            variant={variant}
            disabled={disabled}
          >
            {option.text}
          </ToggleButton>
        ))}
      </div>

      {error && errorMessage && (
        <p
          id={`${radioGroupId}-error`}
          className="mt-2 text-sm text-[var(--error-red-4)]"
          role="alert"
        >
          {errorMessage}
        </p>
      )}
    </div>
  );
};

export default RadioSelect;
