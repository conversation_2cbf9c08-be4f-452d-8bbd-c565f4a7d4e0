import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import RadioSelect, { RadioOption } from "./RadioSelect";

const mockOptions: RadioOption[] = [
  { id: "1", text: "Option 1", value: "option1" },
  { id: "2", text: "Option 2", value: "option2" },
  { id: "3", text: "Option 3", value: "option3" },
];

const mockOnChange = jest.fn();

describe("RadioSelect Component", () => {
  beforeEach(() => {
    mockOnChange.mockClear();
  });

  it("renders correctly with basic props", () => {
    render(
      <RadioSelect value="" onChange={mockOnChange} options={mockOptions} />
    );

    const radioGroup = screen.getByRole("radiogroup");
    expect(radioGroup).toBeInTheDocument();
  });

  it("displays label when provided", () => {
    render(
      <RadioSelect
        value=""
        onChange={mockOnChange}
        options={mockOptions}
        label="Test Label"
      />
    );

    expect(screen.getByText("Test Label")).toBeInTheDocument();
  });

  it("shows required asterisk when required is true", () => {
    render(
      <RadioSelect
        value=""
        onChange={mockOnChange}
        options={mockOptions}
        label="Test Label"
        required={true}
      />
    );

    expect(screen.getByText("*")).toBeInTheDocument();
  });

  it("renders all options correctly", () => {
    render(
      <RadioSelect value="" onChange={mockOnChange} options={mockOptions} />
    );

    mockOptions.forEach((option) => {
      expect(screen.getByText(option.text)).toBeInTheDocument();
    });
  });

  it("displays selected value correctly", () => {
    render(
      <RadioSelect
        value="option2"
        onChange={mockOnChange}
        options={mockOptions}
      />
    );

    // Check that the correct option is selected (ToggleButton should have selected styling)
    const option2Button = screen.getByText("Option 2");
    expect(option2Button).toBeInTheDocument();
  });

  it("calls onChange when option is clicked", () => {
    render(
      <RadioSelect value="" onChange={mockOnChange} options={mockOptions} />
    );

    const option1Button = screen.getByText("Option 1");
    fireEvent.click(option1Button);

    expect(mockOnChange).toHaveBeenCalledWith("option1");
    expect(mockOnChange).toHaveBeenCalledTimes(1);
  });

  it("can change selection", () => {
    const { rerender } = render(
      <RadioSelect
        value="option1"
        onChange={mockOnChange}
        options={mockOptions}
      />
    );

    const option2Button = screen.getByText("Option 2");
    fireEvent.click(option2Button);

    expect(mockOnChange).toHaveBeenCalledWith("option2");

    // Simulate the parent component updating the value
    rerender(
      <RadioSelect
        value="option2"
        onChange={mockOnChange}
        options={mockOptions}
      />
    );

    // Now click option 3
    const option3Button = screen.getByText("Option 3");
    fireEvent.click(option3Button);

    expect(mockOnChange).toHaveBeenCalledWith("option3");
  });

  it("can be disabled", () => {
    render(
      <RadioSelect
        value=""
        onChange={mockOnChange}
        options={mockOptions}
        disabled={true}
      />
    );

    const option1Button = screen.getByText("Option 1");
    fireEvent.click(option1Button);

    // Should not call onChange when disabled
    expect(mockOnChange).not.toHaveBeenCalled();
  });

  it("displays error state correctly", () => {
    render(
      <RadioSelect
        value=""
        onChange={mockOnChange}
        options={mockOptions}
        error={true}
        errorMessage="Please select an option"
      />
    );

    expect(screen.getByText("Please select an option")).toBeInTheDocument();
    expect(screen.getByRole("alert")).toBeInTheDocument();

    const radioGroup = screen.getByRole("radiogroup");
    expect(radioGroup).toHaveAttribute("aria-invalid", "true");
  });

  it("applies custom className", () => {
    render(
      <RadioSelect
        value=""
        onChange={mockOnChange}
        options={mockOptions}
        className="custom-class"
      />
    );

    const container = screen.getByRole("radiogroup").parentElement;
    expect(container).toHaveClass("custom-class");
  });

  it("handles horizontal direction (default)", () => {
    render(
      <RadioSelect
        value=""
        onChange={mockOnChange}
        options={mockOptions}
        direction="horizontal"
      />
    );

    const radioGroup = screen.getByRole("radiogroup");
    expect(radioGroup).toHaveClass("flex", "gap-2", "flex-wrap");
  });

  it("handles vertical direction", () => {
    render(
      <RadioSelect
        value=""
        onChange={mockOnChange}
        options={mockOptions}
        direction="vertical"
      />
    );

    const radioGroup = screen.getByRole("radiogroup");
    expect(radioGroup).toHaveClass("flex", "flex-col", "gap-2");
  });

  it("uses compact variant by default", () => {
    render(
      <RadioSelect value="" onChange={mockOnChange} options={mockOptions} />
    );

    // This test assumes ToggleButton passes variant prop correctly
    // We can verify the component renders without errors
    expect(screen.getByRole("radiogroup")).toBeInTheDocument();
  });

  it("handles default variant", () => {
    render(
      <RadioSelect
        value=""
        onChange={mockOnChange}
        options={mockOptions}
        variant="default"
      />
    );

    // This test assumes ToggleButton passes variant prop correctly
    // We can verify the component renders without errors
    expect(screen.getByRole("radiogroup")).toBeInTheDocument();
  });

  it("has proper accessibility attributes", () => {
    render(
      <RadioSelect
        value=""
        onChange={mockOnChange}
        options={mockOptions}
        label="Test Label"
        error={true}
        errorMessage="Error message"
      />
    );

    const radioGroup = screen.getByRole("radiogroup");
    expect(radioGroup).toHaveAttribute("aria-invalid", "true");
    expect(radioGroup).toHaveAttribute("aria-describedby");
  });

  it("handles empty options array", () => {
    render(<RadioSelect value="" onChange={mockOnChange} options={[]} />);

    const radioGroup = screen.getByRole("radiogroup");
    expect(radioGroup).toBeInTheDocument();
    expect(radioGroup.children).toHaveLength(0);
  });

  it("handles single option", () => {
    const singleOption = [{ id: "1", text: "Only Option", value: "only" }];

    render(
      <RadioSelect value="" onChange={mockOnChange} options={singleOption} />
    );

    expect(screen.getByText("Only Option")).toBeInTheDocument();

    const onlyButton = screen.getByText("Only Option");
    fireEvent.click(onlyButton);

    expect(mockOnChange).toHaveBeenCalledWith("only");
  });

  it("works with yes/no options", () => {
    const yesNoOptions = [
      { id: "yes", text: "Yes", value: "yes" },
      { id: "no", text: "No", value: "no" },
    ];

    render(
      <RadioSelect
        value=""
        onChange={mockOnChange}
        options={yesNoOptions}
        label="Do you agree?"
      />
    );

    expect(screen.getByText("Yes")).toBeInTheDocument();
    expect(screen.getByText("No")).toBeInTheDocument();

    const yesButton = screen.getByText("Yes");
    fireEvent.click(yesButton);

    expect(mockOnChange).toHaveBeenCalledWith("yes");
  });
});
