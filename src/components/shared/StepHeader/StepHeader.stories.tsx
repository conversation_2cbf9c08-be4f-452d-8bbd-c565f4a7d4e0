import type { Meta, StoryObj } from "@storybook/nextjs";
import StepHeader from "./StepHeader";

const meta: Meta<typeof StepHeader> = {
  title: "Components/Shared/StepHeader",
  component: StepHeader,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    currentStep: {
      control: {
        type: "number",
        min: 1,
        max: 10,
      },
    },
    totalSteps: {
      control: {
        type: "number",
        min: 1,
        max: 10,
      },
    },
    title: {
      control: "text",
    },
    className: {
      control: "text",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    currentStep: 3,
    totalSteps: 5,
    title: "Environmental & Socioeconomic Factors",
  },
};

export const FirstStep: Story = {
  args: {
    currentStep: 1,
    totalSteps: 5,
    title: "Personal Information",
  },
};

export const LastStep: Story = {
  args: {
    currentStep: 5,
    totalSteps: 5,
    title: "Review & Submit",
  },
};

export const ShortTitle: Story = {
  args: {
    currentStep: 2,
    totalSteps: 4,
    title: "Basic Info",
  },
};

export const LongTitle: Story = {
  args: {
    currentStep: 4,
    totalSteps: 7,
    title: "Comprehensive Medical History and Previous Treatment Information",
  },
};

export const ManySteps: Story = {
  args: {
    currentStep: 8,
    totalSteps: 15,
    title: "Advanced Configuration",
  },
};

export const WithCustomClassName: Story = {
  args: {
    currentStep: 2,
    totalSteps: 3,
    title: "Custom Styling",
    className: "border-2 border-purple-300 p-4 rounded-lg",
  },
};
