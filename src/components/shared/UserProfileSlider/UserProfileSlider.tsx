import React from "react";
import Avatar from "../Avatar/Avatar";
import ProfileMenuItem from "../ProfileMenuItem";
import Button, { ButtonType } from "../Button/Button";
import {
  BellRingingIcon,
  GearSixIcon,
  LifebuoyIcon,
  SignOutIcon,
  UserIcon,
  XIcon,
  CalendarIcon,
} from "@phosphor-icons/react";

export interface UserProfileSliderProps {
  isOpen: boolean;
  onClose: () => void;
  user?: {
    name: string;
    email: string;
    avatar?: string;
  };
  onProfileClick?: () => void;
  onSettingsClick?: () => void;
  onHelpSupportClick?: () => void;
  onNotificationPreferencesClick?: () => void;
  onScheduleAppointmentClick?: () => void;
  onLogoutClick?: () => void;
}

const UserProfileSlider: React.FC<UserProfileSliderProps> = ({
  isOpen,
  onClose,
  user = {
    name: "<PERSON><PERSON> <PERSON>",
    email: "<EMAIL>",
    avatar: "/assets/avatar.jpg",
  },
  onProfileClick,
  onSettingsClick,
  onHelpSupportClick,
  onNotificationPreferencesClick,
  onScheduleAppointmentClick,
  onLogoutClick,
}) => {
  const handleItemClick = (callback?: () => void) => {
    callback?.();
    onClose();
  };

  return (
    <div
      className={`fixed inset-0 z-40 ${!isOpen ? "pointer-events-none" : ""}`}
    >
      {/* Overlay */}
      <div
        className={`fixed inset-0 bg-black transition-opacity duration-1000 ${isOpen ? "opacity-50" : "opacity-0"} ${!isOpen ? "pointer-events-none" : ""}`}
        onClick={onClose}
      />
      <div className="fixed inset-0 z-50 flex justify-end">
        {/* Slider */}
        <div
          className={`bg-white w-100.5 h-full flex flex-col gap-3 py-2 px-5 transform transition-transform duration-1000 ease-out ${
            isOpen ? "translate-x-0" : "translate-x-full"
          }`}
        >
          {/* Header with User Profile */}
          <div className="w-full flex items-center justify-between py-3">
            <div className="flex items-center gap-4">
              <Avatar
                src={user.avatar}
                alt={user.name}
                width={46}
                height={46}
              />
              <div className="flex-1">
                <div className="text-lg font-semibold text-[var(--grey-8)] mb-1">
                  {user.name}
                </div>
                <div className="text-sm text-[var(--grey-6)]">{user.email}</div>
              </div>
            </div>
            <button
              onClick={onClose}
              className="flex items-center justify-center w-10 h-10 bg-[var(--grey-2)] rounded-full hover:bg-[var(--grey-3)] transition-colors"
              aria-label="Close"
            >
              <XIcon size={24} className="text-[var(--grey-6)] font-bold" />
            </button>
          </div>

          {/* Menu Items */}
          <div className="flex-1 flex flex-col gap-6 py-9 border-t-1 border-[var(--grey-2)]">
            <div className="space-y-6 flex flex-col gap-4">
              {/* Profile */}
              <ProfileMenuItem
                icon={<UserIcon size={20} />}
                text="Profile"
                onClick={() => handleItemClick(onProfileClick)}
              />

              {/* Settings */}
              <ProfileMenuItem
                icon={<GearSixIcon size={20} />}
                text="Setting"
                onClick={() => handleItemClick(onSettingsClick)}
              />

              {/* Help & Support */}
              <ProfileMenuItem
                icon={<LifebuoyIcon size={20} />}
                text="Help & Support"
                onClick={() => handleItemClick(onHelpSupportClick)}
              />

              {/* Notification Preferences */}
              <ProfileMenuItem
                icon={<BellRingingIcon size={20} />}
                text="Notification Preferences"
                onClick={() => handleItemClick(onNotificationPreferencesClick)}
              />
            </div>

            {/* Schedule Appointment Button */}
            <div className="mt-8 mb-8">
              <Button
                type={ButtonType.SECONDARY}
                text="Schedule Appointment"
                icon={<CalendarIcon size={20} />}
                onClick={() => handleItemClick(onScheduleAppointmentClick)}
                className="!text-[var(--primary-pink)] !rounded-lg !hover:bg-[var(--red-1)] !transition-none"
              />
            </div>

            {/* Logout */}
            <ProfileMenuItem
              icon={<SignOutIcon size={20} />}
              text="Logout"
              variant="primary"
              onClick={() => handleItemClick(onLogoutClick)}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfileSlider;
