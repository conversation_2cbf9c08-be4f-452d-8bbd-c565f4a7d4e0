import React from "react";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import UserProfileSlider from "./UserProfileSlider";

const meta: Meta<typeof UserProfileSlider> = {
  title: "Components/Shared/UserProfileSlider",
  component: UserProfileSlider,
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component:
          "A mobile-friendly user profile slider that slides in from the right side of the screen. Features user information, navigation menu items, and a special appointment scheduling button.",
      },
    },
    backgrounds: {
      default: "light",
    },
  },
  tags: ["autodocs"],
  argTypes: {
    isOpen: {
      control: { type: "boolean" },
      description: "Controls whether the slider is visible",
    },
    user: {
      control: { type: "object" },
      description: "User information including name, email, and avatar",
    },
    onClose: {
      action: "onClose",
      description: "Callback when slider should be closed",
    },
    onProfileClick: {
      action: "onProfileClick",
      description: "Callback when Profile menu item is clicked",
    },
    onSettingsClick: {
      action: "onSettingsClick",
      description: "Callback when Settings menu item is clicked",
    },
    onHelpSupportClick: {
      action: "onHelpSupportClick",
      description: "Callback when Help & Support menu item is clicked",
    },
    onNotificationPreferencesClick: {
      action: "onNotificationPreferencesClick",
      description:
        "Callback when Notification Preferences menu item is clicked",
    },
    onScheduleAppointmentClick: {
      action: "onScheduleAppointmentClick",
      description: "Callback when Schedule Appointment button is clicked",
    },
    onLogoutClick: {
      action: "onLogoutClick",
      description: "Callback when Logout menu item is clicked",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    isOpen: true,
    user: {
      name: "Priya Patel",
      email: "<EMAIL>",
      avatar: "/assets/avatar.jpg",
    },
  },
  decorators: [
    (Story) => (
      <div className="relative h-screen w-full bg-gray-100">
        <Story />
      </div>
    ),
  ],
};

export const CustomUser: Story = {
  args: {
    isOpen: true,
    user: {
      name: "John Doe",
      email: "<EMAIL>",
      avatar: "/assets/avatar.jpg",
    },
  },
  decorators: [
    (Story) => (
      <div className="relative h-screen w-full bg-gray-100">
        <Story />
      </div>
    ),
  ],
};

export const Closed: Story = {
  args: {
    isOpen: false,
    user: {
      name: "Priya Patel",
      email: "<EMAIL>",
      avatar: "/assets/avatar.jpg",
    },
  },
  decorators: [
    (Story) => (
      <div className="relative h-screen w-full bg-gray-100 flex items-center justify-center">
        <div className="text-center text-gray-500 max-w-md">
          <h3 className="text-lg font-semibold mb-2">Slider is Closed</h3>
          <p className="text-sm">
            This story shows the closed state. Set isOpen to true in the
            controls to see the slider.
          </p>
        </div>
        <Story />
      </div>
    ),
  ],
};

export const MobileDemo: Story = {
  args: {
    isOpen: true,
    user: {
      name: "Priya Patel",
      email: "<EMAIL>",
      avatar: "/assets/avatar.jpg",
    },
  },
  decorators: [
    (Story) => (
      <div className="relative h-screen w-full bg-gray-100">
        <div className="absolute top-4 left-4 text-gray-600 text-sm">
          Mobile viewport demo - slider slides in from right
        </div>
        <Story />
      </div>
    ),
  ],
  parameters: {
    viewport: {
      defaultViewport: "mobile1",
    },
  },
};

export const InteractiveDemo: Story = {
  render: (args) => {
    const [isOpen, setIsOpen] = React.useState(false);

    return (
      <div className="relative h-screen w-full bg-gray-100">
        <div className="flex items-center justify-center h-full">
          <button
            onClick={() => setIsOpen(true)}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Open Profile Slider
          </button>
        </div>
        <UserProfileSlider
          {...args}
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
        />
      </div>
    );
  },
  args: {
    user: {
      name: "Priya Patel",
      email: "<EMAIL>",
      avatar: "/assets/avatar.jpg",
    },
  },
};
