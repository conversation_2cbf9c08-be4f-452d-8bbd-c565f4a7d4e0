import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import UserProfileSlider from "./UserProfileSlider";

const mockUser = {
  name: "<PERSON><PERSON>",
  email: "<EMAIL>",
  avatar: "/assets/avatar.jpg",
};

const defaultProps = {
  isOpen: true,
  onClose: jest.fn(),
  user: mockUser,
  onProfileClick: jest.fn(),
  onSettingsClick: jest.fn(),
  onHelpSupportClick: jest.fn(),
  onNotificationPreferencesClick: jest.fn(),
  onScheduleAppointmentClick: jest.fn(),
  onLogoutClick: jest.fn(),
};

describe("UserProfileSlider", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders user profile information correctly", () => {
    render(<UserProfileSlider {...defaultProps} />);

    expect(screen.getByText("<PERSON><PERSON> Patel")).toBeInTheDocument();
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
  });

  it("renders all menu items", () => {
    render(<UserProfileSlider {...defaultProps} />);

    expect(screen.getByText("Profile")).toBeInTheDocument();
    expect(screen.getByText("Setting")).toBeInTheDocument();
    expect(screen.getByText("Help & Support")).toBeInTheDocument();
    expect(screen.getByText("Notification Preferences")).toBeInTheDocument();
    expect(screen.getByText("Schedule Appointment")).toBeInTheDocument();
    expect(screen.getByText("Logout")).toBeInTheDocument();
  });

  it("calls onClose when overlay is clicked", () => {
    render(<UserProfileSlider {...defaultProps} />);

    const overlay = document.querySelector(".bg-black");
    fireEvent.click(overlay!);

    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it("calls onClose when X button is clicked", () => {
    render(<UserProfileSlider {...defaultProps} />);

    const closeButton = screen.getByRole("button", { name: "Close" });
    fireEvent.click(closeButton);

    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it("calls correct callback and closes slider when Profile is clicked", () => {
    render(<UserProfileSlider {...defaultProps} />);

    const profileButton = screen.getByText("Profile");
    fireEvent.click(profileButton);

    expect(defaultProps.onProfileClick).toHaveBeenCalledTimes(1);
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it("calls correct callback and closes slider when Schedule Appointment is clicked", () => {
    render(<UserProfileSlider {...defaultProps} />);

    const scheduleButton = screen.getByText("Schedule Appointment");
    fireEvent.click(scheduleButton);

    expect(defaultProps.onScheduleAppointmentClick).toHaveBeenCalledTimes(1);
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it("calls correct callback and closes slider when Logout is clicked", () => {
    render(<UserProfileSlider {...defaultProps} />);

    const logoutButton = screen.getByText("Logout");
    fireEvent.click(logoutButton);

    expect(defaultProps.onLogoutClick).toHaveBeenCalledTimes(1);
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it("uses default user data when no user prop is provided", () => {
    const propsWithoutUser = {
      isOpen: defaultProps.isOpen,
      onClose: defaultProps.onClose,
      onProfileClick: defaultProps.onProfileClick,
      onSettingsClick: defaultProps.onSettingsClick,
      onHelpSupportClick: defaultProps.onHelpSupportClick,
      onNotificationPreferencesClick:
        defaultProps.onNotificationPreferencesClick,
      onScheduleAppointmentClick: defaultProps.onScheduleAppointmentClick,
      onLogoutClick: defaultProps.onLogoutClick,
    };

    render(<UserProfileSlider {...propsWithoutUser} />);

    expect(screen.getByText("Priya Patel")).toBeInTheDocument();
    expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
  });

  it("handles menu item click when callback is undefined", () => {
    const propsWithoutCallbacks = {
      ...defaultProps,
      onProfileClick: undefined,
    };

    render(<UserProfileSlider {...propsWithoutCallbacks} />);

    const profileButton = screen.getByText("Profile");

    // Should not throw error when callback is undefined
    expect(() => fireEvent.click(profileButton)).not.toThrow();
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it("applies correct styling to slider container", () => {
    render(<UserProfileSlider {...defaultProps} />);

    const slider = document.querySelector(".fixed.inset-0.z-50");
    expect(slider).toBeInTheDocument();

    const sliderContent = document.querySelector(".bg-white.w-100\\.5");
    expect(sliderContent).toBeInTheDocument();
  });
});
