"use client";
import React, { useEffect, useState } from "react";
import SearchableSelect, { SearchableSelectOption } from "../SearchableSelect/SearchableSelect";

export interface City {
  id: number;
  city_name: string;
  state_name?: string;
}

interface CitySearchableSelectProps {
  value?: string | number;
  onCitySelect: (city: City | null) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  clearable?: boolean;
  useAdminApi?: boolean; // New prop to use admin API for all cities
}

export default function CitySearchableSelect({
  value,
  onCitySelect,
  placeholder = "Search and select a city...",
  className,
  disabled = false,
  clearable = true,
  useAdminApi = false,
}: CitySearchableSelectProps) {
  const [cities, setCities] = useState<City[]>([]);
  const [loading, setLoading] = useState(false);

  // Fetch cities from API
  useEffect(() => {
    const fetchCities = async () => {
      try {
        setLoading(true);

        // Use admin API if specified, otherwise use public API
        const apiUrl = useAdminApi
          ? '/api/v1/admin/cities?per_page=1000' // Get more cities for admin
          : '/api/v1/public/cities';

        const response = await fetch(apiUrl);
        const data = await response.json();

        if (data.success) {
          // Handle different response structures
          if (useAdminApi && data.data.cities) {
            setCities(data.data.cities);
          } else if (!useAdminApi && Array.isArray(data.data)) {
            setCities(data.data);
          } else {
            console.error('Unexpected response structure:', data);
            setCities([]);
          }
        } else {
          console.error('Failed to fetch cities:', data);
          setCities([]);
        }
      } catch (error) {
        console.error('Error fetching cities:', error);
        setCities([]);
      } finally {
        setLoading(false);
      }
    };

    fetchCities();
  }, [useAdminApi]);

  // Convert cities to SearchableSelect options
  const cityOptions: SearchableSelectOption[] = cities.map(city => ({
    id: city.id,
    label: city.city_name,
    value: city.id,
    subtitle: city.state_name,
    data: city,
  }));

  const handleSelect = (option: SearchableSelectOption | null) => {
    if (option) {
      onCitySelect(option.data as City);
    } else {
      onCitySelect(null);
    }
  };

  return (
    <SearchableSelect
      options={cityOptions}
      value={value}
      onSelect={handleSelect}
      placeholder={placeholder}
      searchPlaceholder="Search cities..."
      className={className}
      disabled={disabled}
      clearable={clearable}
      loading={loading}
      emptyMessage="No cities found"
    />
  );
}
