/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import Modal from "./Modal";
import Image from "next/image";

// Mock next/image to avoid errors in tests
jest.mock("next/image", () => ({
  __esModule: true,
  default: (props: any) => <Image {...props} alt="Modal Icon" />,
}));

describe("Modal", () => {
  const baseProps = {
    open: true,
    title: "Test Modal Title",
    message: "Test modal message body.",
    onClose: jest.fn(),
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("renders title and message", () => {
    render(<Modal {...baseProps} />);
    expect(screen.getByText("Test Modal Title")).toBeInTheDocument();
    expect(screen.getByText("Test modal message body.")).toBeInTheDocument();
  });

  it("renders the close button and calls onClose", () => {
    render(<Modal {...baseProps} />);
    const closeBtn = screen.getByRole("button", { name: /close/i });
    expect(closeBtn).toBeInTheDocument();
    fireEvent.click(closeBtn);
    expect(baseProps.onClose).toHaveBeenCalledTimes(1);
  });

  it("does not render when open is false", () => {
    render(<Modal {...baseProps} open={false} />);
    expect(screen.queryByText("Test Modal Title")).not.toBeInTheDocument();
    expect(
      screen.queryByText("Test modal message body.")
    ).not.toBeInTheDocument();
  });

  it("renders the icon image", () => {
    render(<Modal {...baseProps} />);
    expect(screen.getByAltText("Modal Icon")).toBeInTheDocument();
  });

  it("has proper accessibility role and structure", () => {
    render(<Modal {...baseProps} />);
    expect(screen.getByRole("button", { name: /close/i })).toBeInTheDocument();
    expect(screen.getByText("Test Modal Title").tagName).toBe("H2");
  });
});
