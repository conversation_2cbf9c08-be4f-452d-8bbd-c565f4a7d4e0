import React from "react";
import Button, { ButtonType } from "../Button/Button";
import Image from "next/image";

export interface ModalProps {
  open: boolean;
  title: string;
  message: string;
  onClose: () => void;
}

const Modal: React.FC<ModalProps> = ({ open, title, message, onClose }) => {
  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="bg-white rounded-xl shadow-2xl max-w-2xl w-full mx-4 p-10 flex flex-col gap-4 items-center border border-gray-100 relative">
        {/* Icon with decorative elements */}
        <Image
          src="/assets/modalLogo.svg"
          alt="Modal Icon"
          width={150}
          height={120}
        />
        {/* Title */}
        <h2 className="text-2xl font-bold text-[var(--grey-7)] text-center">
          {title}
        </h2>
        {/* Message */}
        <div className="text-base font-medium text-[var(--grey-6)] leading-8 text-center whitespace-pre-line">
          {message}
        </div>
        {/* Actions */}
        <Button
          type={ButtonType.PRIMARY}
          text="Close"
          onClick={onClose}
          className="!w-fit px-10"
        />
      </div>
    </div>
  );
};

export default Modal;
