import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import React, { useState } from "react";
import Modal from "./Modal";

const meta: Meta<typeof Modal> = {
  title: "Components/Modal",
  component: Modal,
  parameters: {
    layout: "fullscreen",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

const successProps = {
  open: true,
  title: "Consultation Request Submitted",
  message:
    "Thank you for requesting a session with our Nutrition Consultant.\nOur care team will contact you shortly to guide you through the next steps.\nFor urgent queries, please reach out to us at +91-XXXXXXXXXX.",
};

export const Success: Story = {
  args: successProps,
};

export const Closable: Story = {
  render: (args) => {
    const [open, setOpen] = useState(true);
    return (
      <>
        <button onClick={() => setOpen(true)}>Show Modal</button>
        <Modal {...args} open={open} />
      </>
    );
  },
  args: successProps,
};
