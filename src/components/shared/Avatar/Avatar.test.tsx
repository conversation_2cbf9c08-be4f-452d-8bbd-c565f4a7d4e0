import React from "react";
import { render, screen } from "@testing-library/react";
import Avatar from "./Avatar";

describe("Avatar", () => {
  it("renders the default avatar image", () => {
    render(<Avatar />);
    const img = screen.getByAltText("User");
    expect(img).toBeInTheDocument();
  });

  it("renders a custom avatar image and alt text", () => {
    render(
      <Avatar
        src="/assets/user-avatar.png"
        alt="Profile"
        width={48}
        height={48}
      />
    );
    const img = screen.getByAltText("Profile");
    expect(img).toBeInTheDocument();
    expect(img).toHaveAttribute("src", expect.stringContaining("user-avatar"));
  });
});
