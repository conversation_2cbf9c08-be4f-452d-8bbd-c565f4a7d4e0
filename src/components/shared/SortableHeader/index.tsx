"use client";

import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { ArrowDown, ArrowUp } from "lucide-react";
import { Button } from "@/components/ShadcnUI/button";

interface SortableHeaderProps {
  label: string;
  value: string;
}

export default function SortableHeader({ label, value }: SortableHeaderProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const sortBy = searchParams.get("sortBy");
  const sortDirection = searchParams.get("sortDirection");

  const isSorted = sortBy === value;
  const isAsc = sortDirection === "asc";

  const handleClick = () => {
    const params = new URLSearchParams(searchParams.toString());
    if (isSorted) {
      params.set("sortDirection", isAsc ? "desc" : "asc");
    } else {
      params.set("sortBy", value);
      params.set("sortDirection", "asc");
    }
    router.push(`${pathname}?${params.toString()}`);
  };

  return (
    <Button variant="ghost" onClick={()=>handleClick()} className="px-2 py-1 h-auto">
      <span className="font-semibold">{label}</span>
      {isSorted && (
        <span className="ml-2">
          {isAsc ? <ArrowUp size={16} /> : <ArrowDown size={16} />}
        </span>
      )}
    </Button>
  );
}
