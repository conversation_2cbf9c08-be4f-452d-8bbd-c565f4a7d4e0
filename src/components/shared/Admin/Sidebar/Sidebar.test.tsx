import { render, screen, fireEvent } from "@testing-library/react";
import { LayoutGrid, Target } from "lucide-react";
import Sidebar from "./Sidebar";

// Mock Next.js router
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  }),
}));

// Mock next/image
jest.mock("next/image", () => ({
  __esModule: true,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @next/next/no-img-element
  default: (props: any) => <img alt={props.alt || ""} {...props} />,
}));

describe("Sidebar", () => {
  const mockItems = [
    {
      icon: <LayoutGrid />,
      title: "Dashboard",
      pathname: "/dashboard",
      onClick: jest.fn(),
    },
    {
      icon: <Target />,
      title: "IVF Success Score",
      pathname: "/ivf-success-score",
      onClick: jest.fn(),
    },
  ];

  it("renders all sidebar items", () => {
    render(<Sidebar items={mockItems} currentPathname="/dashboard" />);
    expect(screen.getByText("Dashboard")).toBeInTheDocument();
    expect(screen.getByText("IVF Success Score")).toBeInTheDocument();
  });

  it("shows logo when expanded", () => {
    render(<Sidebar items={mockItems} currentPathname="/dashboard" />);
    expect(screen.getByAltText("GIVF Logo")).toBeInTheDocument();
  });

  it("collapses and expands when toggle button is clicked", () => {
    render(<Sidebar items={mockItems} currentPathname="/dashboard" />);

    // Initially expanded
    expect(screen.getByText("Dashboard")).toBeInTheDocument();

    // Click to collapse
    fireEvent.click(screen.getByLabelText("Collapse sidebar"));
    expect(screen.queryByText("Dashboard")).not.toBeInTheDocument();
    expect(screen.queryByAltText("GIVF Logo")).not.toBeInTheDocument();

    // Click to expand
    fireEvent.click(screen.getByLabelText("Expand sidebar"));
    expect(screen.getByText("Dashboard")).toBeInTheDocument();
    expect(screen.getByAltText("GIVF Logo")).toBeInTheDocument();
  });

  it("highlights the active item based on current pathname", () => {
    render(<Sidebar items={mockItems} currentPathname="/ivf-success-score" />);
    const items = screen.getAllByRole("button");
    // Skip the collapse button which is also a button
    expect(items[1]).not.toHaveClass("bg-[var(--red-1)]");
    expect(items[2]).toHaveClass("bg-[var(--red-1)]");
  });

  it("calls onClick handler when item is clicked", () => {
    render(<Sidebar items={mockItems} currentPathname="/dashboard" />);
    fireEvent.click(screen.getByText("Dashboard"));
    expect(mockItems[0].onClick).toHaveBeenCalledTimes(1);
  });
});
