import React from "react";

export interface SidebarItemProps {
  isSelected: boolean;
  icon: React.ReactNode;
  title: string;
  onClick: () => void;
  className?: string;
  children?: React.ReactNode;
}

const SidebarItem: React.FC<SidebarItemProps> = ({
  isSelected,
  icon,
  title,
  onClick,
  className = "",
  children,
}) => {
  return (
    <button
      onClick={onClick}
      className={`
        w-full flex items-center gap-3 px-4 py-3 transition-colors duration-200 rounded-sm
        ${
          isSelected
            ? "bg-[var(--red-1)] text-[var(--red-6)]"
            : "text-[var(--grey-6)] hover:bg-[var(--grey-3)]"
        }
        ${className}
      `}
    >
      <span className="w-5 h-5">{icon}</span>
      <span className="flex-1 text-base font-medium whitespace-nowrap text-left">{title}</span>
      {children}
    </button>
  );
};

export default SidebarItem;
