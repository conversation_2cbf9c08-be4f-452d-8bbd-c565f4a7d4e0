import React, { useState } from "react";
import SidebarItem from "./SidebarItem/SidebarItem";
import Image from "next/image";
import { GearIcon, LifebuoyIcon } from "@phosphor-icons/react";
import { useRouter } from "next/navigation";
import { useAuth, UserWithRole } from "@/contexts/AuthContext";

export interface SidebarProps {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  items: any[];
  isCollapsedState?: boolean;
  currentPathname: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  renderSidebarItems: (items: any[], isCollapsed: boolean) => React.ReactNode;
}

const isActive = (pathname: string, currentPathname: string) => {
  return currentPathname.includes(pathname);
};

const panelText = (user : UserWithRole | null)=>{
  if(user?.user_role === 'admin'){
    return 'Admin Panel';
  }
  return '';
}

const Sidebar: React.FC<SidebarProps> = ({
  items,
  currentPathname,
  renderSidebarItems,
}) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const router = useRouter();
  const { user } = useAuth();
  return (
    <div
      className={`
        flex flex-col bg-white shadow-lg transition-all duration-600 pt-6 pb-8
        ${isCollapsed ? "w-[6rem]" : "w-64"}
      `}
    >
      {/* Logo Section */}
      <div
        className={`flex items-center ${isCollapsed ? "justify-center" : "justify-between gap-8"}`}
      >
        {!isCollapsed && (
          <div className="pl-[1.25rem]">
            <Image
              src="/assets/givfLogo.svg"
              alt="GIVF Logo"
              width={120}
              height={40}
              className="object-contain"
            />
            <div className="bg-gray-200 my-2 text-sm text-gray-600 text-center rounded-lg">
              {panelText(user)}
            </div>
          </div>
        )}
        <button
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="p-2 cursor-pointer rounded-sm"
          aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          <Image
            src="/assets/menu.svg"
            alt="Toggle sidebar"
            width={24}
            height={24}
            className={`transition-transform duration-300 ${isCollapsed ? "rotate-180" : ""}`}
          />
        </button>
      </div>

      {/* Main Navigation Items */}
      <nav className="flex-1 overflow-y-auto py-6 px-[1.25rem]">
        <div className="space-y-2">
          {renderSidebarItems(items, isCollapsed)}
        </div>
      </nav>

      {/* Fixed Bottom Items */}
      <div className="px-[1.25rem] py-6 mt-auto border-t border-[var(--grey-3)]">
        <div className="space-y-2">
          <SidebarItem
            icon={<GearIcon />}
            title={isCollapsed ? "" : "Settings"}
            isSelected={isActive("/user/settings", currentPathname)}
            onClick={() => router.push("/user/settings")}
            className="cursor-pointer"
          />
          <SidebarItem
            icon={<LifebuoyIcon />}
            title={isCollapsed ? "" : "Help & Support"}
            isSelected={isActive("/user/help-support", currentPathname)}
            onClick={() => router.push("/user/help-support")}
            className="cursor-pointer"
          />
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
