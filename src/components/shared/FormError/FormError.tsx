import React from "react";

interface FormErrorProps {
  error: string;
  onRetry?: () => void;
  className?: string;
  title?: string;
}

const FormError: React.FC<FormErrorProps> = ({
  error,
  onRetry,
  className = "",
  title = "Error loading questions",
}) => {
  return (
    <div
      className={`bg-red-50 border border-red-200 rounded-lg p-4 mb-6 ${className}`}
    >
      <div className="text-red-600 text-base font-medium">{title}</div>
      <div className="text-red-500 text-sm mt-1">{error}</div>
      {onRetry && (
        <button
          onClick={onRetry}
          className="mt-3 px-4 py-2 bg-red-600 text-white text-sm rounded-lg hover:bg-red-700 transition-colors"
        >
          Retry
        </button>
      )}
    </div>
  );
};

export default FormError;
