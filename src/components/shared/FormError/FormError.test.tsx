import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import FormError from "./FormError";

// Mock Next.js navigation
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  }),
}));

describe("FormError", () => {
  const mockOnRetry = jest.fn();

  beforeEach(() => {
    mockOnRetry.mockClear();
  });

  it("renders with required props", () => {
    render(<FormError error="Test error message" />);

    expect(screen.getByText("Error loading questions")).toBeInTheDocument();
    expect(screen.getByText("Test error message")).toBeInTheDocument();
  });

  it("renders with custom title", () => {
    render(<FormError error="Test error message" title="Custom Error Title" />);

    expect(screen.getByText("Custom Error Title")).toBeInTheDocument();
    expect(screen.getByText("Test error message")).toBeInTheDocument();
  });

  it("shows retry button when onRetry is provided", () => {
    render(<FormError error="Test error message" onRetry={mockOnRetry} />);

    const retryButton = screen.getByRole("button", { name: "Retry" });
    expect(retryButton).toBeInTheDocument();
  });

  it("does not show retry button when onRetry is not provided", () => {
    render(<FormError error="Test error message" />);

    const retryButton = screen.queryByRole("button", { name: "Retry" });
    expect(retryButton).not.toBeInTheDocument();
  });

  it("calls onRetry when retry button is clicked", () => {
    render(<FormError error="Test error message" onRetry={mockOnRetry} />);

    const retryButton = screen.getByRole("button", { name: "Retry" });
    fireEvent.click(retryButton);

    expect(mockOnRetry).toHaveBeenCalledTimes(1);
  });

  it("applies custom className", () => {
    const { container } = render(
      <FormError error="Test error message" className="custom-class" />
    );

    const errorDiv = container.firstChild;
    expect(errorDiv).toHaveClass("custom-class");
  });

  it("applies default styling classes", () => {
    const { container } = render(<FormError error="Test error message" />);

    const errorDiv = container.firstChild;
    expect(errorDiv).toHaveClass(
      "bg-red-50",
      "border",
      "border-red-200",
      "rounded-lg",
      "p-4",
      "mb-6"
    );
  });

  it("renders all props together correctly", () => {
    render(
      <FormError
        error="Network connection failed"
        title="Connection Error"
        onRetry={mockOnRetry}
        className="mt-4"
      />
    );

    expect(screen.getByText("Connection Error")).toBeInTheDocument();
    expect(screen.getByText("Network connection failed")).toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Retry" })).toBeInTheDocument();
  });

  it("has correct text styling for title and error message", () => {
    render(<FormError error="Test error message" title="Test Title" />);

    const title = screen.getByText("Test Title");
    const errorMessage = screen.getByText("Test error message");

    expect(title).toHaveClass("text-red-600", "text-base", "font-medium");
    expect(errorMessage).toHaveClass("text-red-500", "text-sm", "mt-1");
  });

  it("has correct button styling", () => {
    render(<FormError error="Test error message" onRetry={mockOnRetry} />);

    const retryButton = screen.getByRole("button", { name: "Retry" });
    expect(retryButton).toHaveClass(
      "mt-3",
      "px-4",
      "py-2",
      "bg-red-600",
      "text-white",
      "text-sm",
      "rounded-lg",
      "hover:bg-red-700",
      "transition-colors"
    );
  });
});
