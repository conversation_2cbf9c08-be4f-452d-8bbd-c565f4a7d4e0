import type { Meta, StoryObj } from "@storybook/nextjs";
import FormError from "./FormError";

const meta: Meta<typeof FormError> = {
  title: "Components/Shared/FormError",
  component: FormError,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    error: {
      control: "text",
      description: "The error message to display",
    },
    title: {
      control: "text",
      description: "The title of the error message",
    },
    onRetry: {
      action: "retry clicked",
      description: "Function to call when retry button is clicked",
    },
    className: {
      control: "text",
      description: "Additional CSS classes",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    error: "Something went wrong while loading the data.",
    onRetry: () => console.log("Retry clicked"),
  },
};

export const WithCustomTitle: Story = {
  args: {
    error: "Failed to connect to the server.",
    title: "Connection Error",
    onRetry: () => console.log("Retry clicked"),
  },
};

export const WithoutRetryButton: Story = {
  args: {
    error: "This error cannot be retried.",
    title: "Fatal Error",
    // No onRetry prop - button should not appear
  },
};

export const LongErrorMessage: Story = {
  args: {
    error:
      "A very long error message that demonstrates how the component handles extended text content. This could be a detailed error message from an API that provides specific information about what went wrong during the operation.",
    title: "Detailed Error Information",
    onRetry: () => console.log("Retry clicked"),
  },
};

export const NetworkError: Story = {
  args: {
    error: "Unable to reach the server. Please check your internet connection.",
    title: "Network Error",
    onRetry: () => console.log("Retry clicked"),
  },
};

export const ValidationError: Story = {
  args: {
    error: "Please fill in all required fields before proceeding.",
    title: "Validation Error",
    // No retry button for validation errors
  },
};
