import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import ConfirmationModal from "./ConfirmationModal";
import Image from "next/image";

// Mock Next.js Image component
jest.mock("next/image", () => {
  return function MockImage({ src, alt }: { src: string; alt: string }) {
    return <Image src={src} alt={alt} />;
  };
});

// Mock Button component
jest.mock("../Button/Button", () => {
  return function MockButton({ 
    text, 
    onClick, 
    type 
  }: { 
    text: string; 
    onClick: () => void; 
    type: string; 
  }) {
    return (
      <button onClick={onClick} data-testid={`button-${type}`}>
        {text}
      </button>
    );
  };
});

describe("ConfirmationModal", () => {
  const mockOnConfirm = jest.fn();
  const mockOnCancel = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should not render when open is false", () => {
    render(
      <ConfirmationModal
        open={false}
        title="Test Title"
        message="Test message"
        onConfirm={mockOnConfirm}
        onCancel={mockOnCancel}
      />
    );

    expect(screen.queryByText("Test Title")).not.toBeInTheDocument();
  });

  it("should render with default props when open is true", () => {
    render(
      <ConfirmationModal
        open={true}
        title="Test Title"
        message="Test message"
        onConfirm={mockOnConfirm}
        onCancel={mockOnCancel}
      />
    );

    expect(screen.getByText("Test Title")).toBeInTheDocument();
    expect(screen.getByText("Test message")).toBeInTheDocument();
    expect(screen.getByText("Confirm")).toBeInTheDocument();
    expect(screen.getByText("Cancel")).toBeInTheDocument();
  });

  it("should render with custom button text", () => {
    render(
      <ConfirmationModal
        open={true}
        title="Test Title"
        message="Test message"
        confirmText="Overwrite"
        cancelText="Keep Existing"
        onConfirm={mockOnConfirm}
        onCancel={mockOnCancel}
      />
    );

    expect(screen.getByText("Overwrite")).toBeInTheDocument();
    expect(screen.getByText("Keep Existing")).toBeInTheDocument();
  });

  it("should call onConfirm when confirm button is clicked", () => {
    render(
      <ConfirmationModal
        open={true}
        title="Test Title"
        message="Test message"
        onConfirm={mockOnConfirm}
        onCancel={mockOnCancel}
      />
    );

    const confirmButton = screen.getByText("Confirm");
    fireEvent.click(confirmButton);

    expect(mockOnConfirm).toHaveBeenCalledTimes(1);
  });

  it("should call onCancel when cancel button is clicked", () => {
    render(
      <ConfirmationModal
        open={true}
        title="Test Title"
        message="Test message"
        onConfirm={mockOnConfirm}
        onCancel={mockOnCancel}
      />
    );

    const cancelButton = screen.getByText("Cancel");
    fireEvent.click(cancelButton);

    expect(mockOnCancel).toHaveBeenCalledTimes(1);
  });

  it("should render modal with proper styling classes", () => {
    render(
      <ConfirmationModal
        open={true}
        title="Test Title"
        message="Test message"
        onConfirm={mockOnConfirm}
        onCancel={mockOnCancel}
      />
    );

    // Check for modal overlay
    const modalOverlay = screen.getByText("Test Title").closest('.fixed');
    expect(modalOverlay).toBeInTheDocument();

    // Check for modal content container
    const modalContent = screen.getByText("Test Title").closest('.bg-white');
    expect(modalContent).toBeInTheDocument();
  });

  it("should handle long messages properly", () => {
    const longMessage = "This is a very long message that should wrap properly in the modal. It contains multiple sentences to test the text wrapping functionality of the confirmation modal component.";
    
    render(
      <ConfirmationModal
        open={true}
        title="Test Title"
        message={longMessage}
        onConfirm={mockOnConfirm}
        onCancel={mockOnCancel}
      />
    );

    expect(screen.getByText(longMessage)).toBeInTheDocument();
  });

  it("should render with default button text when confirmText and cancelText are not provided", () => {
    render(
      <ConfirmationModal
        open={true}
        title="Test Title"
        message="Test message"
        onConfirm={mockOnConfirm}
        onCancel={mockOnCancel}
      />
    );

    expect(screen.getByText("Confirm")).toBeInTheDocument();
    expect(screen.getByText("Cancel")).toBeInTheDocument();
  });
}); 