import React from "react";
import Button, { ButtonType } from "../Button/Button";
import Image from "next/image";

export interface ConfirmationModalProps {
  open: boolean;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void;
  onCancel: () => void;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({ 
  open, 
  title, 
  message, 
  confirmText = "Confirm", 
  cancelText = "Cancel",
  onConfirm, 
  onCancel 
}) => {
  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="bg-white rounded-xl shadow-2xl max-w-2xl w-full mx-4 p-10 flex flex-col gap-4 items-center border border-gray-100 relative">
        {/* Icon with decorative elements */}
        <Image
          src="/assets/modalLogo.svg"
          alt="Modal Icon"
          width={150}
          height={120}
        />
        {/* Title */}
        <h2 className="text-2xl font-bold text-[var(--grey-7)] text-center">
          {title}
        </h2>
        {/* Message */}
        <div className="text-base font-medium text-[var(--grey-6)] leading-8 text-center whitespace-pre-line">
          {message}
        </div>
        {/* Actions */}
        <div className="flex gap-3 justify-center">
          <Button
            type={ButtonType.SECONDARY}
            text={cancelText}
            onClick={onCancel}
            className="!w-fit px-6"
          />
          <Button
            type={ButtonType.PRIMARY}
            text={confirmText}
            onClick={onConfirm}
            className="!w-fit px-6"
          />
        </div>
      </div>
    </div>
  );
};

export default ConfirmationModal; 