import React from "react";
import Button, { ButtonType } from "../Button/Button";

interface WarningAlertProps {
  title: string;
  description: string;
  buttons?: {
    text: string;
    type: ButtonType;
    onClick: () => void;
    disabled?: boolean;
  }[];
  className?: string;
}

const WarningAlert: React.FC<WarningAlertProps> = ({
  title,
  description,
  buttons = [],
  className = "",
}) => {
  return (
    <div
      className={`bg-yellow-50 border border-yellow-200 rounded-lg p-6 ${className}`}
    >
      {/* Warning Icon */}
      <div className="flex items-start mb-4">
        <div className="flex-shrink-0">
          <svg
            className="h-6 w-6 text-yellow-600"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        </div>

        {/* Content */}
        <div className="ml-3 flex-1">
          <h3 className="text-lg font-semibold text-yellow-800 mb-2">
            {title}
          </h3>
          <p className="text-yellow-700 text-sm">{description}</p>
        </div>
      </div>

      {/* Buttons */}
      {buttons.length > 0 && (
        <div className="flex gap-3 mt-4">
          {buttons.map((button, index) => (
            <Button
              key={index}
              type={button.type}
              text={button.text}
              onClick={button.onClick}
              disabled={button.disabled}
              size="sm"
              className="flex-1"
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default WarningAlert;
