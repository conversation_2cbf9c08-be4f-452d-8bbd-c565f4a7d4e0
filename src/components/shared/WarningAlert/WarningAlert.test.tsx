import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import WarningAlert from "./WarningAlert";
import { ButtonType } from "../Button/Button";

describe("WarningAlert", () => {
  const mockButtons = [
    {
      text: "Cancel",
      type: ButtonType.SECONDARY,
      onClick: jest.fn(),
    },
    {
      text: "Proceed",
      type: ButtonType.PRIMARY,
      onClick: jest.fn(),
    },
  ];

  it("renders title and description correctly", () => {
    render(
      <WarningAlert
        title="Test Warning"
        description="This is a test warning message"
      />
    );

    expect(screen.getByText("Test Warning")).toBeInTheDocument();
    expect(
      screen.getByText("This is a test warning message")
    ).toBeInTheDocument();
  });

  it("renders buttons when provided", () => {
    render(
      <WarningAlert
        title="Test Warning"
        description="This is a test warning message"
        buttons={mockButtons}
      />
    );

    expect(screen.getByText("Cancel")).toBeInTheDocument();
    expect(screen.getByText("Proceed")).toBeInTheDocument();
  });

  it("does not render buttons when not provided", () => {
    render(
      <WarningAlert
        title="Test Warning"
        description="This is a test warning message"
      />
    );

    expect(screen.queryByText("Cancel")).not.toBeInTheDocument();
    expect(screen.queryByText("Proceed")).not.toBeInTheDocument();
  });

  it("calls button onClick when buttons are clicked", () => {
    render(
      <WarningAlert
        title="Test Warning"
        description="This is a test warning message"
        buttons={mockButtons}
      />
    );

    const cancelButton = screen.getByText("Cancel");
    const proceedButton = screen.getByText("Proceed");

    fireEvent.click(cancelButton);
    expect(mockButtons[0].onClick).toHaveBeenCalledTimes(1);

    fireEvent.click(proceedButton);
    expect(mockButtons[1].onClick).toHaveBeenCalledTimes(1);
  });

  it("renders with custom className", () => {
    const { container } = render(
      <WarningAlert
        title="Test Warning"
        description="This is a test warning message"
        className="custom-class"
      />
    );

    expect(container.firstChild).toHaveClass("custom-class");
  });
});
