import { <PERSON>a, StoryObj } from "@storybook/nextjs";
import WarningAlert from "./WarningAlert";
import { ButtonType } from "../Button/Button";

const meta: Meta<typeof WarningAlert> = {
  title: "Shared/WarningAlert",
  component: WarningAlert,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
};

export default meta;

type Story = StoryObj<typeof WarningAlert>;

export const Default: Story = {
  args: {
    title: "Warning",
    description:
      "This action cannot be undone. Are you sure you want to proceed?",
    buttons: [
      {
        text: "Cancel",
        type: ButtonType.SECONDARY,
        onClick: () => console.log("Cancel clicked"),
      },
      {
        text: "Proceed",
        type: ButtonType.PRIMARY,
        onClick: () => console.log("Proceed clicked"),
      },
    ],
  },
};

export const SingleButton: Story = {
  args: {
    title: "Session Expired",
    description: "Your session has expired. Please log in again to continue.",
    buttons: [
      {
        text: "Login",
        type: ButtonType.PRIMARY,
        onClick: () => console.log("Login clicked"),
      },
    ],
  },
};

export const NoButtons: Story = {
  args: {
    title: "Information",
    description: "This is an informational warning without any action buttons.",
  },
};
