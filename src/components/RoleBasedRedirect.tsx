"use client";
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";

interface RoleBasedRedirectProps {
  children: React.ReactNode;
  allowedRoles?: string[];
  redirectPath?: string;
}

export default function RoleBasedRedirect({ 
  children, 
  allowedRoles = ["admin"], 
  redirectPath = "/doctor" 
}: RoleBasedRedirectProps) {
  const { user, isLoading: loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && user) {
      // If user has a role and it's not in the allowed roles, redirect
      if (user.user_role && !allowedRoles.includes(user.user_role)) {
        router.push(redirectPath);
      }
    }
  }, [user, loading, router, allowedRoles, redirectPath]);

  // Show loading while checking authentication
  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="text-lg">Loading...</div>
      </div>
    );
  }

  // If user is not authenticated or doesn't have the right role, don't render children
  if (!user || (user.user_role && !allowedRoles.includes(user.user_role))) {
    return null;
  }

  return <>{children}</>;
}
