import { usePageHeader } from "@/contexts/PageHeaderContext";
import React, { useEffect, useState } from "react";
import NoAppointments from "../shared/Appointments/NoAppointments/NoAppointments";
import { useRouter } from "next/navigation";
import { useBreadcrumb } from "@/contexts/BreadcrumbContext";
import { useAppointments, Appointment } from "@/hooks/useAppointments";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ShadcnUI/table";
import { Button } from "@/components/ShadcnUI/button";
import { Badge } from "@/components/ShadcnUI/badge";
import {
  Eye,
  Calendar,
  Clock,
  MapPin,
  User,
  ChevronLeft,
  ChevronRight,
  Filter
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ShadcnUI/select";
import { format } from "date-fns";
import AppointmentDetailsModal from "./AppointmentDetailsModal";

const Appointments: React.FC = () => {
  const { setTitle, setSubtitle } = usePageHeader();

  // Check if breadcrumb context is available
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let setBreadcrumbs: ((breadcrumbs: any) => void) | undefined;
  try {
    const breadcrumbContext = useBreadcrumb();
    setBreadcrumbs = breadcrumbContext.setBreadcrumbs;
  } catch (error) {
    // Context not available, component will still work without breadcrumbs
    console.warn("Breadcrumb context not available in Appointments component");
  }

  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState<'completed' | 'upcoming' | 'cancelled' | undefined>(undefined);
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);

  // Fetch appointments using the hook
  const {
    appointments,
    total,
    totalPages,
    loading,
    error
  } = useAppointments({
    page: currentPage,
    perPage: 10,
    status: statusFilter
  });

  useEffect(() => {
    setTitle("My Appointments");
    setSubtitle(
      "View and manage your upcoming and past appointments."
    );
    if (setBreadcrumbs) {
      setBreadcrumbs(null);
    }
  }, [setTitle, setSubtitle, setBreadcrumbs]);

  const handleScheduleClick = () => {
    router.push("/user/schedule-appointments/city");
  };

  const handleViewDetails = (appointment: Appointment) => {
    setSelectedAppointment(appointment);
    setIsDetailsModalOpen(true);
  };

  const handleCloseDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedAppointment(null);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch {
      return dateString;
    }
  };

  const formatTime = (timeString: string) => {
    try {
      return format(new Date(timeString), 'h:mm a');
    } catch {
      return timeString;
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="w-full h-[67vh] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">Loading appointments...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="w-full h-[67vh] flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error loading appointments: {error}</p>
          <Button onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  // If no appointments, show the NoAppointments component
  if (appointments.length === 0) {
    return (
      <>
        <div className="w-full h-[67vh] flex items-center justify-center">
          <NoAppointments onScheduleClick={handleScheduleClick} />
        </div>
        <AppointmentDetailsModal
          appointment={selectedAppointment}
          isOpen={isDetailsModalOpen}
          onClose={handleCloseDetailsModal}
        />
      </>
    );
  }

  // Main appointments table view
  return (
    <div className="space-y-6">
      {/* Header with filters */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
        <div>
          <h2 className="text-xl font-semibold">My Appointments</h2>
          <p className="text-gray-600 text-sm">Total: {total} appointments</p>
        </div>

        <div className="flex gap-3 items-center">
          <Select value={statusFilter || "all"} onValueChange={(value) => {
            setStatusFilter(value === "all" ? undefined : value as any);
            setCurrentPage(1);
          }}>
            <SelectTrigger className="w-40">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="upcoming">Upcoming</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>

          <Button onClick={handleScheduleClick}>
            <Calendar className="h-4 w-4 mr-2" />
            Schedule New
          </Button>
        </div>
      </div>

      {/* Appointments Table */}
      <div className="bg-white rounded-lg border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="min-w-[200px]">Doctor</TableHead>
                <TableHead className="min-w-[180px]">Clinic</TableHead>
                <TableHead className="min-w-[150px]">Date & Time</TableHead>
                <TableHead className="min-w-[100px]">Status</TableHead>
                <TableHead className="min-w-[100px]">Fee</TableHead>
                <TableHead className="text-right min-w-[80px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
          <TableBody>
            {appointments.map((appointment) => (
              <TableRow key={appointment.id}>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <div className="bg-blue-100 p-2 rounded-full">
                      <User className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <div className="font-medium">
                        {appointment.doctor.profile.display_name}
                      </div>
                      {appointment.doctor.specialization_name && (
                        <div className="text-sm text-gray-500">
                          {appointment.doctor.specialization_name}
                        </div>
                      )}
                    </div>
                  </div>
                </TableCell>

                <TableCell>
                  <div className="flex items-start gap-2">
                    <MapPin className="h-4 w-4 text-gray-400 mt-0.5" />
                    <div>
                      <div className="font-medium text-sm">
                        {appointment.clinic.clinic_name}
                      </div>
                      {appointment.clinic.city && (
                        <div className="text-xs text-gray-500">
                          {appointment.clinic.city.city_name}
                          {appointment.clinic.city.state_name &&
                            `, ${appointment.clinic.city.state_name}`
                          }
                        </div>
                      )}
                    </div>
                  </div>
                </TableCell>

                <TableCell>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-gray-400" />
                    <div>
                      <div className="font-medium text-sm">
                        {formatDate(appointment.appointment_date)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {formatTime(appointment.start_time)} - {formatTime(appointment.end_time)}
                      </div>
                    </div>
                  </div>
                </TableCell>

                <TableCell>
                  <Badge className={getStatusColor(appointment.status)}>
                    {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
                  </Badge>
                </TableCell>

                <TableCell>
                  <div className="font-medium">
                    {appointment.currency} {appointment.fees}
                  </div>
                  <div className="text-xs text-gray-500 capitalize">
                    {appointment.payment_status}
                  </div>
                </TableCell>

                <TableCell className="text-right">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleViewDetails(appointment)}
                    className="h-8 w-8 p-0"
                  >
                    <Eye className="h-4 w-4" />
                    <span className="sr-only">View details</span>
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            Page {currentPage} of {totalPages}
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages}
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Appointment Details Modal */}
      <AppointmentDetailsModal
        appointment={selectedAppointment}
        isOpen={isDetailsModalOpen}
        onClose={handleCloseDetailsModal}
      />
    </div>
  );
};

export default Appointments;
