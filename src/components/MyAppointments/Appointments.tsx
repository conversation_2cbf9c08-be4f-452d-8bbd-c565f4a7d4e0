import { use<PERSON>ageHeader } from "@/contexts/PageHeaderContext";
import React, { useEffect, useState } from "react";
import NoAppointments from "../shared/Appointments/NoAppointments/NoAppointments";
import { useRouter } from "next/navigation";
import { useBreadcrumb } from "@/contexts/BreadcrumbContext";

const Appointments: React.FC = () => {
  const { setTitle, setSubtitle } = usePageHeader();
  
  // Check if breadcrumb context is available
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let setBreadcrumbs: ((breadcrumbs: any) => void) | undefined;
  try {
    const breadcrumbContext = useBreadcrumb();
    setBreadcrumbs = breadcrumbContext.setBreadcrumbs;
  } catch (error) {
    // Context not available, component will still work without breadcrumbs
    console.warn("Breadcrumb context not available in Appointments component");
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any
  const [appointments, setAppointments] = useState<any[]>([]); // Replace with actual appointments data
  const router = useRouter();

  useEffect(() => {
    setTitle("My Appointments");
    setSubtitle(
      "Get expert answers and connect with others on the same IVF journey."
    );
    if (setBreadcrumbs) {
      setBreadcrumbs(null);
    }
  }, [setTitle, setSubtitle, setBreadcrumbs]);

  const handleScheduleClick = () => {
    // Handle schedule appointment click
    router.push("/user/schedule-appointments/city");
  };

  // If no appointments, show the NoAppointments component
  if (appointments.length === 0) {
    return (
      <div className="w-full h-[67vh] flex items-center justify-center">
        <NoAppointments onScheduleClick={handleScheduleClick} />
      </div>
    );
  }

  return <div>Appointments</div>;
};

export default Appointments;
