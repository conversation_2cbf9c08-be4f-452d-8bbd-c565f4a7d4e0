import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ShadcnUI/dialog";
import { Badge } from "@/components/ShadcnUI/badge";
import { Button } from "@/components/ShadcnUI/button";
import { 
  Calendar, 
  Clock, 
  MapPin, 
  User, 
  CreditCard, 
  Phone,
  Mail,
  Stethoscope,
  Building
} from "lucide-react";
import { format } from "date-fns";
import { Appointment } from "@/hooks/useAppointments";

interface AppointmentDetailsModalProps {
  appointment: Appointment | null;
  isOpen: boolean;
  onClose: () => void;
}

const AppointmentDetailsModal: React.FC<AppointmentDetailsModalProps> = ({
  appointment,
  isOpen,
  onClose,
}) => {
  if (!appointment) return null;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'unpaid':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'EEEE, MMMM d, yyyy');
    } catch {
      return dateString;
    }
  };

  const formatTime = (timeString: string) => {
    try {
      return format(new Date(timeString), 'h:mm a');
    } catch {
      return timeString;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Appointment Details
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Status and Payment */}
          <div className="flex gap-3">
            <Badge className={getStatusColor(appointment.status)}>
              {appointment.status.charAt(0).toUpperCase() + appointment.status.slice(1)}
            </Badge>
            <Badge className={getPaymentStatusColor(appointment.payment_status)}>
              Payment: {appointment.payment_status.charAt(0).toUpperCase() + appointment.payment_status.slice(1)}
            </Badge>
          </div>

          {/* Doctor Information */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
              <User className="h-5 w-5" />
              Doctor Information
            </h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Stethoscope className="h-4 w-4 text-gray-500" />
                <span className="font-medium">{appointment.doctor.profile.display_name}</span>
              </div>
              {appointment.doctor.specialization_name && (
                <div className="text-sm text-gray-600">
                  Specialization: {appointment.doctor.specialization_name}
                </div>
              )}
              {appointment.doctor.years_of_experience && (
                <div className="text-sm text-gray-600">
                  Experience: {appointment.doctor.years_of_experience} years
                </div>
              )}
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Mail className="h-4 w-4" />
                {appointment.doctor.profile.email}
              </div>
            </div>
          </div>

          {/* Clinic Information */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
              <Building className="h-5 w-5" />
              Clinic Information
            </h3>
            <div className="space-y-2">
              <div className="font-medium">{appointment.clinic.clinic_name}</div>
              {appointment.clinic.address && (
                <div className="flex items-start gap-2 text-sm text-gray-600">
                  <MapPin className="h-4 w-4 mt-0.5" />
                  <div>
                    {appointment.clinic.address}
                    {appointment.clinic.city && (
                      <div>
                        {appointment.clinic.city.city_name}
                        {appointment.clinic.city.state_name && 
                          `, ${appointment.clinic.city.state_name}`
                        }
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Appointment Details */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Appointment Details
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="text-sm text-gray-600 mb-1">Date</div>
                <div className="font-medium">{formatDate(appointment.appointment_date)}</div>
              </div>
              <div>
                <div className="text-sm text-gray-600 mb-1">Time</div>
                <div className="font-medium flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  {formatTime(appointment.start_time)} - {formatTime(appointment.end_time)}
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-600 mb-1">Duration</div>
                <div className="font-medium">{appointment.duration} minutes</div>
              </div>
              <div>
                <div className="text-sm text-gray-600 mb-1">Consultation Type</div>
                <div className="font-medium capitalize">
                  {appointment.consultation_type.replace('_', ' ')}
                </div>
              </div>
            </div>
          </div>

          {/* Payment Information */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Payment Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="text-sm text-gray-600 mb-1">Consultation Fee</div>
                <div className="font-medium text-lg">
                  {appointment.currency} {appointment.fees}
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-600 mb-1">Booking Date</div>
                <div className="font-medium">
                  {formatDate(appointment.booking_date)}
                </div>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
            {appointment.status === 'upcoming' && (
              <Button variant="destructive">
                Cancel Appointment
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AppointmentDetailsModal;
