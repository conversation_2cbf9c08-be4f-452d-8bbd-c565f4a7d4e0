import React from "react";
import Card from "../shared/Card";
import "./shimmer.css";

interface DoctorProfileCardShimmerProps {
  className?: string;
}

const DoctorProfileCardShimmer: React.FC<DoctorProfileCardShimmerProps> = ({
  className = "",
}) => {
  return (
    <Card
      className={`flex flex-col items-start gap-8 w-[362px] md:w-full xl:w-[434px] px-5 py-7.5 md:px-10 md:py-7.5 ${className}`}
    >
      <div className="flex flex-col gap-4">
        {/* Profile Image Shimmer */}
        <div className="w-[144px] h-[144px] md:w-[194px] md:h-[194px] rounded-full shimmer-animated"></div>

        {/* Doctor Info Shimmer */}
        <div className="flex flex-col gap-4">
          {/* Name */}
          <div className="h-6 w-48 shimmer-animated rounded"></div>

          {/* Specialization */}
          <div className="h-5 w-36 shimmer-animated rounded"></div>

          {/* Experience */}
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 shimmer-animated rounded"></div>
            <div className="h-5 w-32 shimmer-animated rounded"></div>
          </div>

          {/* Clinic Location */}
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 shimmer-animated rounded"></div>
            <div className="h-5 w-40 shimmer-animated rounded"></div>
          </div>

          {/* Consultation Fee */}
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 shimmer-animated rounded"></div>
            <div className="h-5 w-44 shimmer-animated rounded"></div>
          </div>

          {/* Consultation Type */}
          <div className="h-10 w-48 shimmer-animated rounded-[50px]"></div>
        </div>
      </div>

      {/* Bio Section Shimmer */}
      <div className="flex flex-col gap-[9px]">
        <div className="h-6 w-12 shimmer-animated rounded"></div>
        <div className="space-y-2">
          <div className="h-4 w-full shimmer-animated rounded"></div>
          <div className="h-4 w-full shimmer-animated rounded"></div>
          <div className="h-4 w-3/4 shimmer-animated rounded"></div>
        </div>
      </div>
    </Card>
  );
};

export default DoctorProfileCardShimmer;
