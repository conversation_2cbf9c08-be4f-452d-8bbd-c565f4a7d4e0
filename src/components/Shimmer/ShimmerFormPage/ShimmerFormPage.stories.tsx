import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import ShimmerFormPage from "./ShimmerFormPage";
import { AuthProvider } from "@/contexts/AuthContext"; // Adjust the import path as needed
import { ToastProvider } from "@/contexts/ToastContext";

const meta: Meta<typeof ShimmerFormPage> = {
  title: "Components/ShimmerFormPage",
  component: ShimmerFormPage,
  parameters: {
    layout: "fullscreen",
  },
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <ToastProvider duration={4000} maxToasts={5}>
        <AuthProvider>
          <Story />
        </AuthProvider>
      </ToastProvider>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  render: () => <ShimmerFormPage />,
};
