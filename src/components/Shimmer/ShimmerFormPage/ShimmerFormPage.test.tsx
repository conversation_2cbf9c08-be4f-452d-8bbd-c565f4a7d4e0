import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import ShimmerFormPage from "./ShimmerFormPage";

// Mock Header and Footer to avoid AuthProvider/Supabase dependency
jest.mock("../../shared/Header", () => {
  const MockHeader = () => (<div data-testid="header">Header</div>);
  MockHeader.displayName = "MockHeader";
  return MockHeader;
});
jest.mock("../../shared/Footer", () => {
  const MockFooter = () => <div data-testid="footer">Footer</div>;
  MockFooter.displayName = "MockFooter";
  return { Footer: MockFooter };
});

// Mock CSS import if needed
jest.mock("../../Shimmer/shimmer.css", () => ({}));

describe("ShimmerFormPage", () => {
  it("renders header, shimmer fields, and footer", () => {
    render(<ShimmerFormPage />);
    // Header
    expect(screen.getByTestId("header")).toBeInTheDocument();
    // Footer
    expect(screen.getByTestId("footer")).toBeInTheDocument();
    // Shimmer fields (at least one)
    const shimmerDivs = document.querySelectorAll(".shimmer-animated");
    expect(shimmerDivs.length).toBeGreaterThan(0);
  });

  it("renders multiple shimmer rectangles", () => {
    render(<ShimmerFormPage />);
    // There should be several elements with shimmer-animated class
    const shimmerDivs = document.querySelectorAll(".shimmer-animated");
    expect(shimmerDivs.length).toBeGreaterThan(5);
  });
});
