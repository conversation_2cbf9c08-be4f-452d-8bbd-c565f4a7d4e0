import React from "react";
import { Footer } from "../../shared/Footer";
import Header from "../../shared/Header";
import "../../Shimmer/shimmer.css";

const shimmer = "shimmer-animated rounded";

const ShimmerFormPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-[#ffffff] flex flex-col justify-between relative overflow-x-auto">
      {/* Header */}
      <Header />

      {/* Main Content */}
      <div className="flex-1 flex flex-col items-center justify-center px-2 sm:px-4 py-4 relative">
        {/* Subtle right-side gradient */}
        <div className="absolute inset-0 pointer-events-none" />
        <div className="w-full md:w-[50%] m-0 flex flex-col items-center gap-5 sm:gap-7 z-10 px-0">
          {/* Top shimmer (small) */}
          <div
            className={`h-3 w-20 sm:h-4 sm:w-28 mx-auto mb-1 sm:mb-2 ${shimmer}`}
          />
          {/* Main shimmer fields */}
          <div
            className={`h-5 w-1/2 sm:h-7 sm:w-1/3 mx-auto mb-2 sm:mb-4 ${shimmer}`}
          />
          <div className={`h-5 w-2/3 sm:h-7 sm:w-2/3 mx-auto ${shimmer}`} />
          <div className={`h-5 w-2/3 sm:h-7 sm:w-2/3 mx-auto ${shimmer}`} />
          <div className={`h-5 w-2/3 sm:h-7 sm:w-2/3 mx-auto ${shimmer}`} />
          <div className="flex gap-4 w-2/3">
            <div className={`h-5 w-1/2 sm:h-7 sm:w-1/4 ${shimmer}`} />
            <div className={`h-5 w-1/2 sm:h-7 sm:w-1/4 ${shimmer}`} />
          </div>
          <div className="flex gap-4 w-2/3">
            <div className={`h-7 w-1/6 ${shimmer}`} />
            <div className={`h-7 w-1/6 ${shimmer}`} />
            <div className={`h-7 w-1/6 ${shimmer}`} />
          </div>
          <div className={`h-5 w-2/3 sm:h-7 sm:w-2/3 mx-auto ${shimmer}`} />
          <div className={`h-5 w-2/3 sm:h-7 sm:w-2/3 mx-auto ${shimmer}`} />
          <div className={`h-5 w-2/3 sm:h-7 sm:w-2/3 mx-auto ${shimmer}`} />
        </div>
        {/* Bottom shimmer lines above footer */}
      </div>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default ShimmerFormPage;
