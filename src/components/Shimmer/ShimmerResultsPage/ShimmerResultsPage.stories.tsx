import React from "react";
import ShimmerResultsPage from "./ShimmerResultsPage";
import { AuthProvider } from "@/contexts/AuthContext";
import { Meta, StoryObj } from "@storybook/nextjs";
import { ToastProvider } from "@/contexts/ToastContext";

const meta: Meta<typeof ShimmerResultsPage> = {
  title: "Shimmer/ShimmerResultsPage",
  component: ShimmerResultsPage,
  parameters: {
    layout: "fullscreen",
  },
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <ToastProvider duration={4000} maxToasts={5}>
        <AuthProvider>
          <Story />
        </AuthProvider>
      </ToastProvider>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  render: () => <ShimmerResultsPage />,
};
