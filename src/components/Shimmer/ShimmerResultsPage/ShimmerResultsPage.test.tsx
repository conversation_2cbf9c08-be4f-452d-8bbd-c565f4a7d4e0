import React from "react";
import { render } from "@testing-library/react";
import ShimmerResultsPage from "./ShimmerResultsPage";

// Mock Header and Foot<PERSON> to avoid Auth/Supabase dependency
jest.mock("../../shared/Header/Header", () => {
  const MockHeader = () => (<div data-testid="header">Header</div>);
  MockHeader.displayName = "MockHeader";
  return MockHeader;
});
jest.mock("../../shared/Footer/Footer", () => {
  const MockFooter = () => (<div data-testid="footer">Footer</div>);
  MockFooter.displayName = "MockFooter";
  return MockFooter;
});

// Mock CSS import to avoid Jest errors
jest.mock("../shimmer.css", () => ({}));

describe("ShimmerResultsPage", () => {
  it("renders header, shimmer skeleton, and footer", () => {
    render(<ShimmerResultsPage />);
    // Shimmer skeleton (at least one)
    expect(document.querySelector(".shimmer-animated")).toBeInTheDocument();
  });

  it("renders multiple shimmer rectangles", () => {
    render(<ShimmerResultsPage />);
    // There should be several elements with shimmer-animated class
    const shimmerDivs = document.querySelectorAll(".shimmer-animated");
    expect(shimmerDivs.length).toBeGreaterThan(5);
  });
});
