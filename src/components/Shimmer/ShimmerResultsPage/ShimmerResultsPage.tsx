import React from "react";
import "../shimmer.css";

const shimmer = "shimmer-animated rounded";

const ShimmerResultsPage: React.FC<{ noBackground?: boolean }> = ({
  noBackground = false,
}) => {
  return (
    <div
      className={`${noBackground ? "" : "bg-[#ffffff]"} min-h-screen flex flex-col justify-between relative overflow-x-auto`}
    >
      {/* Main Content */}
      <div className="flex-1 flex flex-col items-center justify-center px-2 sm:px-4 py-4 sm:py-8 relative">
        {/* Subtle background gradients */}
        <div className="absolute inset-0 pointer-events-none" />
        <div className="w-full sm:w-[60%] sm:min-w-[500px] sm:max-w-2xl m-0 flex flex-col items-center gap-6 sm:gap-8 z-10 px-0">
          {/* Top shimmer lines */}
          <div
            className={`h-4 w-24 sm:h-5 sm:w-20 mx-auto mt-6 sm:mt-8 mb-2 sm:mb-4 rounded-[10px] ${shimmer}`}
          />
          <div
            className={`h-6 w-40 sm:h-8.5 sm:w-56 mx-auto rounded-[8px] ${shimmer}`}
          />

          {/* Large semi-circle shimmer */}
          <div className="relative flex flex-col items-center mb-6 sm:mb-8 w-full">
            <div
              className="w-48 h-24 sm:w-72 sm:h-36 shimmer-animated"
              style={{
                borderTopLeftRadius: "120px",
                borderTopRightRadius: "120px",
                borderBottomLeftRadius: 0,
                borderBottomRightRadius: 0,
              }}
            />
          </div>

          {/* Two horizontal shimmer lines */}
          <div className="flex gap-4 sm:gap-8 w-full justify-center mb-4 sm:mb-6">
            <div
              className={`h-6 w-24 sm:h-8.5 sm:w-40 rounded-[10px] ${shimmer}`}
            />
            <div
              className={`h-6 w-24 sm:h-8.5 sm:w-40 rounded-[10px] ${shimmer}`}
            />
          </div>

          {/* First large card block */}
          <div className="bg-[#f5f6fa] rounded-lg w-full p-4 sm:p-8 flex flex-col sm:flex-row gap-4 sm:gap-8 items-start mb-4">
            <div className="flex-1 flex flex-col gap-2 sm:gap-3 w-full">
              <div className={`h-4 w-16 ${shimmer}`} />
              <div className={`h-5 w-32 ${shimmer}`} />
            </div>
            <div className="flex-1 flex flex-col gap-2 sm:gap-3 items-end w-full">
              <div className={`h-4 w-10 ${shimmer}`} />
              <div className={`h-5 w-24 ${shimmer}`} />
            </div>
          </div>

          {/* Second large card block */}
          <div className="bg-[#f5f6fa] rounded-lg w-full p-4 sm:p-8 flex flex-col gap-3 sm:gap-4 mb-4">
            <div className={`h-4 w-32 ${shimmer} mb-1 sm:mb-2`} />
            <div className={`h-3 w-2/3 sm:w-3/4 ${shimmer} mb-2 sm:mb-4`} />
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
              <div className={`h-8 w-full sm:w-1/2 ${shimmer}`} />
              <div className={`h-8 w-full sm:w-1/2 ${shimmer}`} />
            </div>
          </div>

          {/* Bottom shimmer lines/buttons */}
          <div className="flex flex-row items-center gap-3 sm:gap-4 w-full mt-4 sm:mt-6 mb-2">
            <div className={`h-3 w-24 ${shimmer}`} />
            <div className={`h-8 w-12 ${shimmer}`} />
            <div className={`h-8 w-12 ${shimmer}`} />
          </div>
          <div className="flex justify-end w-full mb-6 sm:mb-8">
            <div className={`h-8 w-40 sm:w-48 ${shimmer}`} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShimmerResultsPage;
