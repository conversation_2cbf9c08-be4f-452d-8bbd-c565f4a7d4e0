import React from "react";
import type { Meta, StoryObj } from "@storybook/nextjs";
import ToggleButtonShimmer from "./ToggleButtonShimmer";

const meta: Meta<typeof ToggleButtonShimmer> = {
  title: "Components/Shimmer/ToggleButtonShimmer",
  component: ToggleButtonShimmer,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "A shimmer loading component that mimics the shape and size of toggle buttons with animated shimmer effect.",
      },
    },
  },
  argTypes: {
    count: {
      control: { type: "number", min: 1, max: 12 },
      description: "Number of shimmer buttons to display",
    },
    variant: {
      control: { type: "select" },
      options: ["default", "compact"],
      description: "Variant of the toggle button shimmer",
    },
    className: {
      control: "text",
      description: "Additional CSS classes to apply to the shimmer buttons",
    },
  },
  decorators: [
    (Story) => (
      <div style={{ padding: "20px", maxWidth: "600px" }}>
        <Story />
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    count: 6,
    variant: "default",
  },
  parameters: {
    docs: {
      description: {
        story:
          "Default toggle button shimmer with 6 buttons in default variant.",
      },
    },
  },
};

export const Compact: Story = {
  args: {
    count: 4,
    variant: "compact",
  },
  parameters: {
    docs: {
      description: {
        story: "Compact variant of toggle button shimmer with 4 buttons.",
      },
    },
  },
};

export const FewButtons: Story = {
  args: {
    count: 3,
    variant: "default",
  },
  parameters: {
    docs: {
      description: {
        story: "Toggle button shimmer with only 3 buttons.",
      },
    },
  },
};

export const ManyButtons: Story = {
  args: {
    count: 9,
    variant: "default",
  },
  parameters: {
    docs: {
      description: {
        story:
          "Toggle button shimmer with 9 buttons to show how it handles larger counts.",
      },
    },
  },
};

export const CompactMany: Story = {
  args: {
    count: 8,
    variant: "compact",
  },
  parameters: {
    docs: {
      description: {
        story:
          "Compact variant with 8 buttons showing how compact buttons wrap.",
      },
    },
  },
};

export const WithCustomStyling: Story = {
  args: {
    count: 5,
    variant: "default",
    className: "!py-3.5 !px-6",
  },
  parameters: {
    docs: {
      description: {
        story:
          "Toggle button shimmer with custom styling applied via className prop.",
      },
    },
  },
};

export const SingleButton: Story = {
  args: {
    count: 1,
    variant: "default",
  },
  parameters: {
    docs: {
      description: {
        story: "Single toggle button shimmer for minimal loading states.",
      },
    },
  },
};

export const GridLayout: Story = {
  args: {
    count: 6,
    variant: "default",
    className: "grid grid-cols-2 md:grid-cols-3 gap-8",
  },
  parameters: {
    docs: {
      description: {
        story:
          "Toggle button shimmer arranged in a grid layout similar to the SelectCity component.",
      },
    },
  },
  decorators: [
    (Story) => (
      <div style={{ padding: "20px", maxWidth: "800px" }}>
        <div className="mb-4">
          <h3 className="text-lg font-semibold mb-2">Grid Layout Example</h3>
          <p className="text-sm text-gray-600">
            This shows how the shimmer would appear in a grid layout like the
            city selection.
          </p>
        </div>
        <Story />
      </div>
    ),
  ],
};

export const LoadingState: Story = {
  args: {
    count: 6,
    variant: "default",
  },
  parameters: {
    docs: {
      description: {
        story:
          "Simulated loading state showing how the shimmer appears while data is being fetched.",
      },
    },
  },
  decorators: [
    (Story) => (
      <div style={{ padding: "20px", maxWidth: "600px" }}>
        <div className="mb-4">
          <h3 className="text-lg font-semibold mb-2">Loading Cities...</h3>
          <p className="text-sm text-gray-600 mb-4">
            This is how the shimmer appears while cities are being loaded.
          </p>
        </div>
        <Story />
        <div className="mt-4 text-xs text-gray-500">
          💡 The shimmer animation will continue until real data loads
        </div>
      </div>
    ),
  ],
};
