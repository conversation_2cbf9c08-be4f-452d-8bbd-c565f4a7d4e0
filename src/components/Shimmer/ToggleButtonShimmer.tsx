import React from "react";
import "./shimmer.css";

interface ToggleButtonShimmerProps {
  count?: number;
  variant?: "default" | "compact" | "card";
  className?: string;
  gridCols?: number;
  gridColsMd?: number;
}

const ToggleButtonShimmer: React.FC<ToggleButtonShimmerProps> = ({
  count = 6,
  variant = "default",
  className = "",
  gridCols = 2,
  gridColsMd = 3,
}) => {
  const baseClasses = "rounded-[0.25rem] shimmer-animated";

  const variantClasses = {
    default: "py-3 w-[8.438rem] h-[3rem]",
    card: "w-[16rem] h-[22rem]",
    compact: "px-6 py-3 h-[3rem]",
  };

  const combinedClasses = `${baseClasses} ${variantClasses[variant]} ${className}`;
  return (
    <div
      className={`grid grid-cols-${gridCols} md:grid-cols-${gridColsMd} gap-8`}
    >
      {Array.from({ length: count }, (_, index) => (
        <div key={index} className={combinedClasses} />
      ))}
    </div>
  );
};

export default ToggleButtonShimmer;
