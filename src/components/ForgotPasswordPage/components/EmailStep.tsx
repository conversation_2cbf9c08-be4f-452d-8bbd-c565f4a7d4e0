import React from "react";
import Button, { ButtonType } from "../../shared/Button/Button";
import PageHeader from "@/components/shared/PageHeader";
import { isValidEmail } from "@/lib/utils/inputValidations";

interface EmailStepProps {
  localEmail: string;
  setLocalEmail: (email: string) => void;
  handleEmailSubmit: (e: React.FormEvent) => void;
  isLoading: boolean;
  errorMessage: string;
  handleSendOTP: () => void;
}

const EmailStep: React.FC<EmailStepProps> = ({
  localEmail,
  setLocalEmail,
  handleEmailSubmit,
  isLoading,
  errorMessage,
  handleSendOTP,
}) => (
  <>
    <div className="text-center flex flex-col">
      <PageHeader title="Forgot Password?" />
      <p className="text-[var(--grey-6)] text-base font-medium">
        No Worries! We'll help you securely reset your password.
      </p>
    </div>

    <form onSubmit={handleEmailSubmit}>
      <div className="space-y-6">
        <div className="flex flex-col gap-2">
          <label
            htmlFor="email"
            className="block text-base font-medium text-[var(--grey-6)]"
          >
            Email
          </label>
          <input
            type="email"
            id="email"
            value={localEmail}
            onChange={(e) => setLocalEmail(e.target.value)}
            className="w-full px-[1.25rem] py-[0.875rem] border border-[var(--grey-3)] rounded-sm focus:outline-none focus:ring-1 focus:ring-[var(--violet-11)] focus:border-transparent text-[var(--grey-7)] text-base"
            placeholder="Enter your email address"
            required
            disabled={isLoading}
          />
        </div>

        {errorMessage && (
          <div className="text-[var(--red-6)] text-sm text-center bg-red-50 border border-red-200 rounded-sm p-3">
            {errorMessage}
          </div>
        )}

        <div className="pt-2">
          <Button
            type={ButtonType.PRIMARY}
            text={isLoading ? "Sending..." : "Send OTP"}
            onClick={handleSendOTP}
            disabled={
              isLoading || !localEmail.trim() || !isValidEmail(localEmail)
            }
          />
        </div>
      </div>
    </form>
  </>
);

export default EmailStep;
