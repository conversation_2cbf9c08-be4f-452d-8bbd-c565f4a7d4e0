import React, { useState } from "react";
import Link from "next/link";
import StepIndicator from "./components/StepIndicator";
import EmailStep from "./components/EmailStep";
import OtpStep from "./components/OtpStep";
import PasswordResetStepComponent from "./components/PasswordResetStep";
import SuccessStep from "./components/SuccessStep";
import { ForgotPasswordPageProps, PasswordResetStep } from "./types";
import Header, { HeaderState } from "@/components/shared/Header";
import { Footer } from "@/components/shared/Footer";

const ForgotPasswordPage: React.FC<ForgotPasswordPageProps> = ({
  onSendOTP,
  onVerifyOTP,
  onResetPassword,
  onBackToLogin,
  onGetHelp,
  currentStep = PasswordResetStep.EMAIL_INPUT,
  email = "",
  isLoading = false,
  successMessage = "",
  errorMessage = "",
  remainingAttempts,
}) => {
  const [localEmail, setLocalEmail] = useState(email);
  const [otp, setOtp] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  // Email validation function
  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.trim());
  };

  const handleSendOTP = () => {
    if (onSendOTP && localEmail.trim() && isValidEmail(localEmail)) {
      onSendOTP(localEmail.trim());
    }
  };

  const handleVerifyOTP = () => {
    if (onVerifyOTP && otp.length === 6) {
      onVerifyOTP(localEmail, otp);
    }
  };

  const handleResetPassword = () => {
    if (onResetPassword && newPassword && newPassword === confirmPassword) {
      onResetPassword(localEmail, newPassword);
    }
  };

  const handleEmailSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSendOTP();
  };

  const handleOTPSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleVerifyOTP();
  };

  const handlePasswordSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleResetPassword();
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case PasswordResetStep.EMAIL_INPUT:
        return (
          <EmailStep
            localEmail={localEmail}
            setLocalEmail={setLocalEmail}
            handleEmailSubmit={handleEmailSubmit}
            isLoading={isLoading}
            errorMessage={errorMessage}
            handleSendOTP={handleSendOTP}
          />
        );
      case PasswordResetStep.OTP_VERIFICATION:
        return (
          <OtpStep
            localEmail={localEmail}
            otp={otp}
            setOtp={setOtp}
            handleOTPSubmit={handleOTPSubmit}
            isLoading={isLoading}
            errorMessage={errorMessage}
            successMessage={successMessage}
            remainingAttempts={remainingAttempts}
            handleSendOTP={handleSendOTP}
            handleVerifyOTP={handleVerifyOTP}
          />
        );
      case PasswordResetStep.PASSWORD_RESET:
        return (
          <PasswordResetStepComponent
            newPassword={newPassword}
            setNewPassword={setNewPassword}
            confirmPassword={confirmPassword}
            setConfirmPassword={setConfirmPassword}
            handlePasswordSubmit={handlePasswordSubmit}
            isLoading={isLoading}
            errorMessage={errorMessage}
            handleResetPassword={handleResetPassword}
          />
        );
      case PasswordResetStep.SUCCESS:
        return (
          <SuccessStep
            successMessage={successMessage}
            onBackToLogin={onBackToLogin}
          />
        );
      default:
        return (
          <EmailStep
            localEmail={localEmail}
            setLocalEmail={setLocalEmail}
            handleEmailSubmit={handleEmailSubmit}
            isLoading={isLoading}
            errorMessage={errorMessage}
            handleSendOTP={handleSendOTP}
          />
        );
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header state={HeaderState.HELP} onClick={onGetHelp} />

      <main className="flex-1 flex items-center justify-center px-6 py-16">
        <div className="w-full flex justify-center">
          <div className="w-[20.063rem] md:w-[26.5rem] flex flex-col justify-center gap-8">
            <StepIndicator currentStep={currentStep} />
            {renderCurrentStep()}

            {(currentStep === PasswordResetStep.EMAIL_INPUT ||
              currentStep === PasswordResetStep.OTP_VERIFICATION) && (
              <>
                <div className="flex items-center my-2">
                  <div className="flex-grow h-px bg-gray-200" />
                  <span className="mx-3 text-gray-400 text-base font-medium">
                    OR
                  </span>
                  <div className="flex-grow h-px bg-gray-200" />
                </div>
                <p className="text-[var(--grey-6)] text-base font-medium text-center pt-4">
                  Remember Your Password ?{" "}
                  <Link
                    className="text-[var(--red-6)] hover:text-[var(--red-7)] underline"
                    href="/login"
                  >
                    Login
                  </Link>
                </p>
              </>
            )}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default ForgotPasswordPage;
