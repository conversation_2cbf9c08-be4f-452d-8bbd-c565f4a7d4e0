/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";

// Mock Next.js Link
jest.mock("next/link", () => {
  const MockLink = ({ children, href, className }: any) => (
    <a href={href} className={className} data-testid="login-link">
      {children}
    </a>
  );
  MockLink.displayName = "MockLink";
  return MockLink;
});

// Mock sub-components
jest.mock("./components/StepIndicator", () => {
  interface StepIndicatorProps {
    currentStep: number;
  }
  const MockStepIndicator = ({ currentStep }: StepIndicatorProps) => (
    <div data-testid="step-indicator">Step {currentStep}</div>
  );
  MockStepIndicator.displayName = "MockStepIndicator";
  return MockStepIndicator;
});

jest.mock("./components/EmailStep", () => {
  interface EmailStepProps {
    localEmail: string;
    setLocalEmail: (email: string) => void;
    handleEmailSubmit: (e: React.FormEvent) => void;
    isLoading: boolean;
    errorMessage: string;
    handleSendOTP: () => void;
  }
  const MockEmailStep = ({
    localEmail,
    setLocalEmail,
    handleEmailSubmit,
    isLoading,
    errorMessage,
  }: EmailStepProps) => (
    <div data-testid="email-step">
      <input
        data-testid="email-input"
        value={localEmail}
        onChange={(e) => setLocalEmail(e.target.value)}
      />
      <form onSubmit={handleEmailSubmit}>
        <button
          type="submit"
          disabled={isLoading}
          data-testid="send-otp-button"
        >
          {isLoading ? "Sending..." : "Send OTP"}
        </button>
      </form>
      {errorMessage && <div data-testid="error-message">{errorMessage}</div>}
    </div>
  );
  MockEmailStep.displayName = "MockEmailStep";
  return MockEmailStep;
});

jest.mock("./components/OtpStep", () => {
  interface OtpStepProps {
    localEmail: string;
    otp: string;
    setOtp: (otp: string) => void;
    handleOTPSubmit: (e: React.FormEvent) => void;
    isLoading: boolean;
    errorMessage: string;
    successMessage: string;
    remainingAttempts?: number;
    handleSendOTP: () => void;
    handleVerifyOTP: () => void;
  }
  const MockOtpStep = ({
    otp,
    setOtp,
    handleOTPSubmit,
    isLoading,
    errorMessage,
    successMessage,
  }: OtpStepProps) => (
    <div data-testid="otp-step">
      <input
        data-testid="otp-input"
        value={otp}
        onChange={(e) => setOtp(e.target.value)}
        maxLength={6}
      />
      <form onSubmit={handleOTPSubmit}>
        <button
          type="submit"
          disabled={isLoading}
          data-testid="verify-otp-button"
        >
          {isLoading ? "Verifying..." : "Verify OTP"}
        </button>
      </form>
      {errorMessage && <div data-testid="error-message">{errorMessage}</div>}
      {successMessage && (
        <div data-testid="success-message">{successMessage}</div>
      )}
    </div>
  );
  MockOtpStep.displayName = "MockOtpStep";
  return MockOtpStep;
});

jest.mock("./components/PasswordResetStep", () => {
  interface PasswordResetStepProps {
    newPassword: string;
    setNewPassword: (password: string) => void;
    confirmPassword: string;
    setConfirmPassword: (password: string) => void;
    handlePasswordSubmit: (e: React.FormEvent) => void;
    isLoading: boolean;
    errorMessage: string;
    handleResetPassword: () => void;
  }
  const MockPasswordResetStep = ({
    newPassword,
    setNewPassword,
    confirmPassword,
    setConfirmPassword,
    handlePasswordSubmit,
    isLoading,
    errorMessage,
  }: PasswordResetStepProps) => (
    <div data-testid="password-reset-step">
      <input
        data-testid="new-password-input"
        type="password"
        value={newPassword}
        onChange={(e) => setNewPassword(e.target.value)}
      />
      <input
        data-testid="confirm-password-input"
        type="password"
        value={confirmPassword}
        onChange={(e) => setConfirmPassword(e.target.value)}
      />
      <form onSubmit={handlePasswordSubmit}>
        <button
          type="submit"
          disabled={isLoading}
          data-testid="reset-password-button"
        >
          {isLoading ? "Resetting..." : "Reset Password"}
        </button>
      </form>
      {errorMessage && <div data-testid="error-message">{errorMessage}</div>}
    </div>
  );
  MockPasswordResetStep.displayName = "MockPasswordResetStep";
  return MockPasswordResetStep;
});

jest.mock("./components/SuccessStep", () => {
  interface SuccessStepProps {
    successMessage: string;
    onBackToLogin?: () => void;
  }
  const MockSuccessStep = ({
    successMessage,
    onBackToLogin,
  }: SuccessStepProps) => (
    <div data-testid="success-step">
      <div data-testid="success-message">{successMessage}</div>
      <button onClick={onBackToLogin} data-testid="back-to-login-button">
        Back to Login
      </button>
    </div>
  );
  MockSuccessStep.displayName = "MockSuccessStep";
  return MockSuccessStep;
});

jest.mock("@/components/shared/Header", () => {
  interface HeaderProps {
    state: string;
    onClick?: () => void;
  }
  const MockHeader = ({ onClick }: HeaderProps) => (
    <div data-testid="header" onClick={onClick}>
      Header
    </div>
  );
  MockHeader.displayName = "MockHeader";
  return {
    __esModule: true,
    default: MockHeader,
    HeaderState: { HELP: "HELP" },
  };
});

jest.mock("@/components/shared/Footer", () => {
  const MockFooter = () => <div data-testid="footer">Footer</div>;
  MockFooter.displayName = "MockFooter";
  return {
    Footer: MockFooter,
  };
});

import ForgotPasswordPage from "./ForgotPasswordPage";
import { PasswordResetStep } from "./types";

describe("ForgotPasswordPage", () => {
  const defaultProps = {
    onSendOTP: jest.fn(),
    onVerifyOTP: jest.fn(),
    onResetPassword: jest.fn(),
    onBackToLogin: jest.fn(),
    onGetHelp: jest.fn(),
    currentStep: PasswordResetStep.EMAIL_INPUT,
    email: "",
    isLoading: false,
    successMessage: "",
    errorMessage: "",
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Component Rendering", () => {
    it("renders all main components", () => {
      render(<ForgotPasswordPage {...defaultProps} />);

      expect(screen.getByTestId("header")).toBeInTheDocument();
      expect(screen.getByTestId("footer")).toBeInTheDocument();
      expect(screen.getByTestId("step-indicator")).toBeInTheDocument();
    });

    it("renders login link for non-success steps", () => {
      render(<ForgotPasswordPage {...defaultProps} />);

      expect(screen.getByText("Remember Your Password ?")).toBeInTheDocument();
      expect(screen.getByTestId("login-link")).toBeInTheDocument();
      expect(screen.getByTestId("login-link")).toHaveAttribute(
        "href",
        "/login"
      );
    });

    it("does not render login link for success step", () => {
      render(
        <ForgotPasswordPage
          {...defaultProps}
          currentStep={PasswordResetStep.SUCCESS}
        />
      );

      expect(
        screen.queryByText("Remember Your Password ?")
      ).not.toBeInTheDocument();
      expect(screen.queryByTestId("login-link")).not.toBeInTheDocument();
    });
  });

  describe("Email Step", () => {
    it("renders email step by default", () => {
      render(<ForgotPasswordPage {...defaultProps} />);

      expect(screen.getByTestId("email-step")).toBeInTheDocument();
      expect(screen.getByTestId("email-input")).toBeInTheDocument();
      expect(screen.getByTestId("send-otp-button")).toBeInTheDocument();
    });

    it("updates email when typing", () => {
      render(<ForgotPasswordPage {...defaultProps} />);

      const emailInput = screen.getByTestId("email-input");
      fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });

      expect(emailInput).toHaveValue("<EMAIL>");
    });

    it("calls onSendOTP when form is submitted", () => {
      render(<ForgotPasswordPage {...defaultProps} />);

      const emailInput = screen.getByTestId("email-input");
      fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });

      const form = screen.getByTestId("send-otp-button").closest("form");
      fireEvent.submit(form!);

      expect(defaultProps.onSendOTP).toHaveBeenCalledWith("<EMAIL>");
    });

    it("trims email before sending OTP", () => {
      render(<ForgotPasswordPage {...defaultProps} />);

      const emailInput = screen.getByTestId("email-input");
      fireEvent.change(emailInput, {
        target: { value: "  <EMAIL>  " },
      });

      const form = screen.getByTestId("send-otp-button").closest("form");
      fireEvent.submit(form!);

      expect(defaultProps.onSendOTP).toHaveBeenCalledWith("<EMAIL>");
    });

    it("shows loading state", () => {
      render(<ForgotPasswordPage {...defaultProps} isLoading={true} />);

      expect(screen.getByText("Sending...")).toBeInTheDocument();
      expect(screen.getByTestId("send-otp-button")).toBeDisabled();
    });

    it("displays error message", () => {
      render(
        <ForgotPasswordPage
          {...defaultProps}
          errorMessage="Invalid email address"
        />
      );

      expect(screen.getByTestId("error-message")).toHaveTextContent(
        "Invalid email address"
      );
    });
  });

  describe("OTP Step", () => {
    it("renders OTP step when currentStep is OTP_VERIFICATION", () => {
      render(
        <ForgotPasswordPage
          {...defaultProps}
          currentStep={PasswordResetStep.OTP_VERIFICATION}
        />
      );

      expect(screen.getByTestId("otp-step")).toBeInTheDocument();
      expect(screen.getByTestId("otp-input")).toBeInTheDocument();
      expect(screen.getByTestId("verify-otp-button")).toBeInTheDocument();
    });

    it("updates OTP when typing", () => {
      render(
        <ForgotPasswordPage
          {...defaultProps}
          currentStep={PasswordResetStep.OTP_VERIFICATION}
        />
      );

      const otpInput = screen.getByTestId("otp-input");
      fireEvent.change(otpInput, { target: { value: "123456" } });

      expect(otpInput).toHaveValue("123456");
    });

    it("calls onVerifyOTP when form is submitted with valid OTP", () => {
      render(
        <ForgotPasswordPage
          {...defaultProps}
          currentStep={PasswordResetStep.OTP_VERIFICATION}
          email="<EMAIL>"
        />
      );

      const otpInput = screen.getByTestId("otp-input");
      fireEvent.change(otpInput, { target: { value: "123456" } });

      const form = screen.getByTestId("verify-otp-button").closest("form");
      fireEvent.submit(form!);

      expect(defaultProps.onVerifyOTP).toHaveBeenCalledWith(
        "<EMAIL>",
        "123456"
      );
    });

    it("does not call onVerifyOTP when OTP is incomplete", () => {
      render(
        <ForgotPasswordPage
          {...defaultProps}
          currentStep={PasswordResetStep.OTP_VERIFICATION}
        />
      );

      const otpInput = screen.getByTestId("otp-input");
      fireEvent.change(otpInput, { target: { value: "123" } });

      const form = screen.getByTestId("verify-otp-button").closest("form");
      fireEvent.submit(form!);

      expect(defaultProps.onVerifyOTP).not.toHaveBeenCalled();
    });

    it("shows success message", () => {
      render(
        <ForgotPasswordPage
          {...defaultProps}
          currentStep={PasswordResetStep.OTP_VERIFICATION}
          successMessage="OTP sent successfully"
        />
      );

      expect(screen.getByTestId("success-message")).toHaveTextContent(
        "OTP sent successfully"
      );
    });
  });

  describe("Password Reset Step", () => {
    it("renders password reset step when currentStep is PASSWORD_RESET", () => {
      render(
        <ForgotPasswordPage
          {...defaultProps}
          currentStep={PasswordResetStep.PASSWORD_RESET}
        />
      );

      expect(screen.getByTestId("password-reset-step")).toBeInTheDocument();
      expect(screen.getByTestId("new-password-input")).toBeInTheDocument();
      expect(screen.getByTestId("confirm-password-input")).toBeInTheDocument();
      expect(screen.getByTestId("reset-password-button")).toBeInTheDocument();
    });

    it("updates password fields when typing", () => {
      render(
        <ForgotPasswordPage
          {...defaultProps}
          currentStep={PasswordResetStep.PASSWORD_RESET}
        />
      );

      const newPasswordInput = screen.getByTestId("new-password-input");
      const confirmPasswordInput = screen.getByTestId("confirm-password-input");

      fireEvent.change(newPasswordInput, {
        target: { value: "newpassword123" },
      });
      fireEvent.change(confirmPasswordInput, {
        target: { value: "newpassword123" },
      });

      expect(newPasswordInput).toHaveValue("newpassword123");
      expect(confirmPasswordInput).toHaveValue("newpassword123");
    });

    it("calls onResetPassword when passwords match", () => {
      render(
        <ForgotPasswordPage
          {...defaultProps}
          currentStep={PasswordResetStep.PASSWORD_RESET}
          email="<EMAIL>"
        />
      );

      const newPasswordInput = screen.getByTestId("new-password-input");
      const confirmPasswordInput = screen.getByTestId("confirm-password-input");

      fireEvent.change(newPasswordInput, {
        target: { value: "newpassword123" },
      });
      fireEvent.change(confirmPasswordInput, {
        target: { value: "newpassword123" },
      });

      const form = screen.getByTestId("reset-password-button").closest("form");
      fireEvent.submit(form!);

      expect(defaultProps.onResetPassword).toHaveBeenCalledWith(
        "<EMAIL>",
        "newpassword123"
      );
    });

    it("does not call onResetPassword when passwords don't match", () => {
      render(
        <ForgotPasswordPage
          {...defaultProps}
          currentStep={PasswordResetStep.PASSWORD_RESET}
        />
      );

      const newPasswordInput = screen.getByTestId("new-password-input");
      const confirmPasswordInput = screen.getByTestId("confirm-password-input");

      fireEvent.change(newPasswordInput, {
        target: { value: "newpassword123" },
      });
      fireEvent.change(confirmPasswordInput, {
        target: { value: "differentpassword" },
      });

      const form = screen.getByTestId("reset-password-button").closest("form");
      fireEvent.submit(form!);

      expect(defaultProps.onResetPassword).not.toHaveBeenCalled();
    });
  });

  describe("Success Step", () => {
    it("renders success step when currentStep is SUCCESS", () => {
      render(
        <ForgotPasswordPage
          {...defaultProps}
          currentStep={PasswordResetStep.SUCCESS}
          successMessage="Password reset successful"
        />
      );

      expect(screen.getByTestId("success-step")).toBeInTheDocument();
      expect(screen.getByTestId("success-message")).toHaveTextContent(
        "Password reset successful"
      );
      expect(screen.getByTestId("back-to-login-button")).toBeInTheDocument();
    });

    it("calls onBackToLogin when back to login button is clicked", () => {
      render(
        <ForgotPasswordPage
          {...defaultProps}
          currentStep={PasswordResetStep.SUCCESS}
        />
      );

      const backToLoginButton = screen.getByTestId("back-to-login-button");
      fireEvent.click(backToLoginButton);

      expect(defaultProps.onBackToLogin).toHaveBeenCalledTimes(1);
    });
  });

  describe("Header Interaction", () => {
    it("calls onGetHelp when header is clicked", () => {
      render(<ForgotPasswordPage {...defaultProps} />);

      const header = screen.getByTestId("header");
      fireEvent.click(header);

      expect(defaultProps.onGetHelp).toHaveBeenCalledTimes(1);
    });
  });

  describe("Edge Cases", () => {
    it("handles missing email prop", () => {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { email, ...propsWithoutEmail } = defaultProps;

      render(<ForgotPasswordPage {...propsWithoutEmail} />);

      const emailInput = screen.getByTestId("email-input");
      expect(emailInput).toHaveValue("");
    });

    it("handles missing callback props", () => {
      const propsWithoutCallbacks = {
        ...defaultProps,
        onSendOTP: undefined,
        onVerifyOTP: undefined,
        onResetPassword: undefined,
      };

      expect(() => {
        render(<ForgotPasswordPage {...propsWithoutCallbacks} />);
      }).not.toThrow();
    });

    it("defaults to EMAIL_INPUT step for invalid step", () => {
      render(<ForgotPasswordPage {...defaultProps} currentStep={999 as any} />);

      expect(screen.getByTestId("email-step")).toBeInTheDocument();
    });

    it("handles empty email submission", () => {
      render(<ForgotPasswordPage {...defaultProps} />);

      const form = screen.getByTestId("send-otp-button").closest("form");
      fireEvent.submit(form!);

      expect(defaultProps.onSendOTP).not.toHaveBeenCalled();
    });

    it("handles remainingAttempts prop", () => {
      render(
        <ForgotPasswordPage
          {...defaultProps}
          currentStep={PasswordResetStep.OTP_VERIFICATION}
          remainingAttempts={3}
        />
      );

      expect(screen.getByTestId("otp-step")).toBeInTheDocument();
    });
  });

  describe("Form Validations", () => {
    it("validates email is not empty before sending OTP", () => {
      render(<ForgotPasswordPage {...defaultProps} />);

      const emailInput = screen.getByTestId("email-input");
      fireEvent.change(emailInput, { target: { value: "   " } }); // Only whitespace

      const form = screen.getByTestId("send-otp-button").closest("form");
      fireEvent.submit(form!);

      expect(defaultProps.onSendOTP).not.toHaveBeenCalled();
    });

    it("validates OTP length before verification", () => {
      render(
        <ForgotPasswordPage
          {...defaultProps}
          currentStep={PasswordResetStep.OTP_VERIFICATION}
        />
      );

      const otpInput = screen.getByTestId("otp-input");
      fireEvent.change(otpInput, { target: { value: "12345" } }); // Only 5 digits

      const form = screen.getByTestId("verify-otp-button").closest("form");
      fireEvent.submit(form!);

      expect(defaultProps.onVerifyOTP).not.toHaveBeenCalled();
    });

    it("validates passwords are not empty before reset", () => {
      render(
        <ForgotPasswordPage
          {...defaultProps}
          currentStep={PasswordResetStep.PASSWORD_RESET}
        />
      );

      const form = screen.getByTestId("reset-password-button").closest("form");
      fireEvent.submit(form!);

      expect(defaultProps.onResetPassword).not.toHaveBeenCalled();
    });
  });
});
