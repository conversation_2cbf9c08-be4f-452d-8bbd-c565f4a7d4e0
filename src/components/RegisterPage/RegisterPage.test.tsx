/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";

// Mock Next.js Image component
jest.mock("next/image", () => {
  const MockImage = ({ src, alt, className, width, height }: any) => (
    <Image
      src={src}
      alt={alt}
      className={className}
      width={width}
      height={height}
      data-testid="mock-image"
    />
  );
  MockImage.displayName = "MockImage";
  return MockImage;
});

// Mock sub-components
jest.mock("../shared/Header/Header", () => {
  interface HeaderProps {
    state: string;
    onClick?: () => void;
  }
  const MockHeader = ({ onClick }: HeaderProps) => (
    <div data-testid="header" onClick={onClick}>
      Header
    </div>
  );
  MockHeader.displayName = "MockHeader";
  return {
    __esModule: true,
    default: MockHeader,
    HeaderState: { HELP: "HELP" },
  };
});

jest.mock("../shared/Footer/Footer", () => {
  const MockFooter = () => <div data-testid="footer">Footer</div>;
  MockFooter.displayName = "MockFooter";
  return {
    __esModule: true,
    default: MockFooter,
  };
});

jest.mock("../shared/Button/Button", () => {
  interface ButtonProps {
    type: string;
    text: string;
    onClick?: () => void;
    disabled?: boolean;
    className?: string;
  }
  const MockButton = ({ text, onClick, disabled, className }: ButtonProps) => (
    <button
      onClick={onClick}
      disabled={disabled}
      className={className}
      data-testid={`button-${text.toLowerCase().replace(/\s+/g, "-")}`}
    >
      {text}
    </button>
  );
  MockButton.displayName = "MockButton";
  return {
    __esModule: true,
    default: MockButton,
    ButtonType: { PRIMARY: "primary", SECONDARY: "secondary" },
  };
});

jest.mock("../shared/ToggleButton/ToggleButton", () => {
  interface ToggleButtonProps {
    variant: string;
    isSelected: boolean;
    onClick: () => void;
    children: React.ReactNode;
  }
  const MockToggleButton = ({
    isSelected,
    onClick,
    children,
  }: ToggleButtonProps) => (
    <button
      onClick={onClick}
      className={isSelected ? "bg-[var(--violet-6)]" : "border-[var(--grey-3)]"}
      data-testid={`toggle-${children?.toString().toLowerCase()}`}
    >
      {children}
    </button>
  );
  MockToggleButton.displayName = "MockToggleButton";
  return {
    __esModule: true,
    default: MockToggleButton,
  };
});

jest.mock("../shared/Input/Input", () => {
  interface InputProps {
    type: string;
    label?: string;
    value: string;
    onChange: (value: string) => void;
    placeholder: string;
    required?: boolean;
    error?: boolean;
    errorMessage?: string;
  }
  const MockInput = ({
    label,
    value,
    onChange,
    placeholder,
    type,
    error,
    errorMessage,
  }: InputProps) => (
    <div data-testid="input-wrapper">
      {label && <label>{label}</label>}
      <input
        type={type}
        data-testid={`input-${
          placeholder
            ? placeholder
                .toLowerCase()
                .replace(/\s+/g, "-")
                .replace(/[^a-z0-9-]/g, "")
            : "default"
        }`}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
      />
      {error && errorMessage && (
        <div data-testid="error-message">{errorMessage}</div>
      )}
    </div>
  );
  MockInput.displayName = "MockInput";
  return {
    __esModule: true,
    default: MockInput,
  };
});

jest.mock("../shared/PageHeader/PageHeader", () => {
  interface PageHeaderProps {
    title: string;
  }
  const MockPageHeader = ({ title }: PageHeaderProps) => (
    <h1 data-testid="page-header">{title}</h1>
  );
  MockPageHeader.displayName = "MockPageHeader";
  return {
    __esModule: true,
    default: MockPageHeader,
  };
});

jest.mock("../shared/PasswordInput", () => {
  interface PasswordInputProps {
    id: string;
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    placeholder: string;
    minLength?: number;
    required?: boolean;
    error?: boolean;
    errorMessage?: string;
  }
  const MockPasswordInput = ({
    id,
    value,
    onChange,
    placeholder,
    error,
    errorMessage,
  }: PasswordInputProps) => (
    <div>
      <input
        id={id}
        type="password"
        data-testid="password-input"
        value={value}
        onChange={onChange}
        placeholder={placeholder}
      />
      {error && errorMessage && (
        <div data-testid="password-error">{errorMessage}</div>
      )}
    </div>
  );
  MockPasswordInput.displayName = "MockPasswordInput";
  return {
    __esModule: true,
    default: MockPasswordInput,
  };
});

import RegisterPage from "./RegisterPage";
import Image from "next/image";

describe("RegisterPage", () => {
  const defaultProps = {
    onRegister: jest.fn(),
    onLoginClick: jest.fn(),
    className: "",
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Component Rendering", () => {
    it("renders all main components", () => {
      render(<RegisterPage {...defaultProps} />);

      expect(screen.getByTestId("header")).toBeInTheDocument();
      expect(screen.getByTestId("footer")).toBeInTheDocument();
      expect(screen.getByTestId("page-header")).toBeInTheDocument();
      expect(screen.getByText("Create Your GIVF Account")).toBeInTheDocument();
    });

    it("renders the welcome message", () => {
      render(<RegisterPage {...defaultProps} />);

      expect(screen.getByText("Create Your GIVF Account")).toBeInTheDocument();
      expect(
        screen.getByText(/Let's begin your journey with GIVF/)
      ).toBeInTheDocument();
    });

    it("renders all form fields", () => {
      render(<RegisterPage {...defaultProps} />);

      expect(
        screen.getByTestId("input-enter-your-full-name")
      ).toBeInTheDocument();
      expect(
        screen.getByTestId("input-enter-your-email-address")
      ).toBeInTheDocument();
      expect(
        screen.getByTestId("input-enter-your-phone-number")
      ).toBeInTheDocument();
      expect(screen.getByTestId("input-enter-your-age")).toBeInTheDocument();
      expect(screen.getByTestId("toggle-female")).toBeInTheDocument();
      expect(screen.getByTestId("toggle-male")).toBeInTheDocument();
      expect(screen.getByTestId("password-input")).toBeInTheDocument();
    });

    it("renders form labels correctly", () => {
      render(<RegisterPage {...defaultProps} />);

      expect(screen.getByText("Full Name")).toBeInTheDocument();
      expect(screen.getByText("Email")).toBeInTheDocument();
      expect(screen.getByText("Number")).toBeInTheDocument();
      expect(screen.getByText("(Optional)")).toBeInTheDocument();
      expect(screen.getByText("Age")).toBeInTheDocument();
      expect(screen.getByText("Sex")).toBeInTheDocument();
      expect(screen.getByText("Date of Birth")).toBeInTheDocument();
      expect(screen.getByText("Password")).toBeInTheDocument();
    });

    it("renders password requirements section", () => {
      render(<RegisterPage {...defaultProps} />);

      expect(
        screen.getByText("Must have at least 8 character")
      ).toBeInTheDocument();
      expect(
        screen.getByText("To make your password Stronger:")
      ).toBeInTheDocument();
      expect(screen.getByText("At least 8 character")).toBeInTheDocument();
      expect(screen.getByText("1 Lowercase letter")).toBeInTheDocument();
      expect(screen.getByText("1 Upper case letter")).toBeInTheDocument();
      expect(screen.getByText("1 A symbol (@&%$)")).toBeInTheDocument();
    });

    it("renders terms acceptance checkbox", () => {
      render(<RegisterPage {...defaultProps} />);

      expect(screen.getByRole("checkbox")).toBeInTheDocument();
      expect(screen.getByText(/I accept the/)).toBeInTheDocument();
      expect(screen.getByText("Terms")).toBeInTheDocument();
      expect(screen.getByText("Privacy Policy")).toBeInTheDocument();
    });

    it("renders submit button", () => {
      render(<RegisterPage {...defaultProps} />);

      expect(screen.getByTestId("button-continue")).toBeInTheDocument();
      expect(screen.getByTestId("button-continue")).toBeDisabled();
    });

    it("renders login link section", () => {
      render(<RegisterPage {...defaultProps} />);

      expect(screen.getByText("Already have an account?")).toBeInTheDocument();
      expect(screen.getByText("Login")).toBeInTheDocument();
    });
  });

  describe("Form Interactions", () => {
    it("updates full name when input changes", () => {
      render(<RegisterPage {...defaultProps} />);

      const nameInput = screen.getByTestId("input-enter-your-full-name");
      fireEvent.change(nameInput, { target: { value: "John Doe" } });

      expect(nameInput).toHaveValue("John Doe");
    });

    it("updates email when input changes", () => {
      render(<RegisterPage {...defaultProps} />);

      const emailInput = screen.getByTestId("input-enter-your-email-address");
      fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });

      expect(emailInput).toHaveValue("<EMAIL>");
    });

    it("updates phone number when input changes", () => {
      render(<RegisterPage {...defaultProps} />);

      const phoneInput = screen.getByTestId("input-enter-your-phone-number");
      fireEvent.change(phoneInput, { target: { value: "1234567890" } });

      expect(phoneInput).toHaveValue("1234567890");
    });

    it("updates age when input changes", () => {
      render(<RegisterPage {...defaultProps} />);

      const ageInput = screen.getByTestId("input-enter-your-age");
      fireEvent.change(ageInput, { target: { value: "28" } });

      expect(ageInput).toHaveValue(28);
    });

    it("toggles sex selection", () => {
      render(<RegisterPage {...defaultProps} />);

      const femaleButton = screen.getByTestId("toggle-female");
      const maleButton = screen.getByTestId("toggle-male");

      // Female should be selected by default
      expect(femaleButton).toHaveClass("bg-[var(--violet-6)]");
      expect(maleButton).toHaveClass("border-[var(--grey-3)]");

      // Click male
      fireEvent.click(maleButton);

      expect(maleButton).toHaveClass("bg-[var(--violet-6)]");
      expect(femaleButton).toHaveClass("border-[var(--grey-3)]");
    });

    it("updates password when input changes", () => {
      render(<RegisterPage {...defaultProps} />);

      const passwordInput = screen.getByTestId("password-input");
      fireEvent.change(passwordInput, { target: { value: "Password123!" } });

      expect(passwordInput).toHaveValue("Password123!");
    });

    it("toggles terms acceptance checkbox", () => {
      render(<RegisterPage {...defaultProps} />);

      const checkbox = screen.getByRole("checkbox");
      const submitButton = screen.getByTestId("button-continue");

      expect(checkbox).not.toBeChecked();
      expect(submitButton).toBeDisabled();

      fireEvent.click(checkbox);

      expect(checkbox).toBeChecked();
      expect(submitButton).toBeEnabled();
    });

    it("updates date of birth when input changes", () => {
      render(<RegisterPage {...defaultProps} />);

      // Date input is rendered by the actual component, not mocked
      const dateInput = screen.getByTestId("input-default");
      fireEvent.change(dateInput, { target: { value: "1995-06-15" } });

      expect(dateInput).toHaveValue("1995-06-15");
    });
  });

  describe("Password Requirements", () => {
    it("validates password length requirement", () => {
      render(<RegisterPage {...defaultProps} />);

      const passwordInput = screen.getByTestId("password-input");

      // Short password
      fireEvent.change(passwordInput, { target: { value: "1234567" } });
      // Length requirement should be invalid (we can't test icon state easily in mocked component)

      // Long enough password
      fireEvent.change(passwordInput, { target: { value: "12345678" } });
      // Length requirement should be valid
    });

    it("validates all password requirements with strong password", () => {
      render(<RegisterPage {...defaultProps} />);

      const passwordInput = screen.getByTestId("password-input");
      fireEvent.change(passwordInput, { target: { value: "Password123@" } });

      // All requirements should be met with this password
      expect(passwordInput).toHaveValue("Password123@");
    });

    it("shows password strength indicator", () => {
      render(<RegisterPage {...defaultProps} />);

      // Password strength bar should be present
      expect(
        screen.getByText("Must have at least 8 character")
      ).toBeInTheDocument();
    });
  });

  describe("Form Validation", () => {
    it("shows validation errors when form is submitted with empty fields", async () => {
      render(<RegisterPage {...defaultProps} />);

      // Accept terms to enable submit button
      const checkbox = screen.getByRole("checkbox");
      fireEvent.click(checkbox);

      const submitButton = screen.getByTestId("button-continue");
      fireEvent.click(submitButton);

      // Wait for validation to show
      await waitFor(() => {
        expect(
          screen.getByText("Please fix the errors above to continue.")
        ).toBeInTheDocument();
      });
    });

    it("validates email format", async () => {
      render(<RegisterPage {...defaultProps} />);

      const emailInput = screen.getByTestId("input-enter-your-email-address");
      fireEvent.change(emailInput, { target: { value: "invalid-email" } });

      const checkbox = screen.getByRole("checkbox");
      fireEvent.click(checkbox);

      const submitButton = screen.getByTestId("button-continue");
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(
          screen.getByText("Please enter a valid email address")
        ).toBeInTheDocument();
      });
    });

    it("validates age format", async () => {
      render(<RegisterPage {...defaultProps} />);

      const ageInput = screen.getByTestId("input-enter-your-age");
      fireEvent.change(ageInput, { target: { value: "invalid" } });

      const checkbox = screen.getByRole("checkbox");
      fireEvent.click(checkbox);

      const submitButton = screen.getByTestId("button-continue");
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(
          screen.getByText("Please fix the errors above to continue.")
        ).toBeInTheDocument();
      });
    });

    it("requires terms acceptance", async () => {
      render(<RegisterPage {...defaultProps} />);

      const submitButton = screen.getByTestId("button-continue");

      // Submit button should be disabled when terms not accepted
      expect(submitButton).toBeDisabled();
    });
  });

  describe("Form Submission", () => {
    const fillValidForm = () => {
      const nameInput = screen.getByTestId("input-enter-your-full-name");
      const emailInput = screen.getByTestId("input-enter-your-email-address");
      const phoneInput = screen.getByTestId("input-enter-your-phone-number");
      const ageInput = screen.getByTestId("input-enter-your-age");
      const passwordInput = screen.getByTestId("password-input");
      const maleButton = screen.getByTestId("toggle-male");
      const checkbox = screen.getByRole("checkbox");

      fireEvent.change(nameInput, { target: { value: "John Doe" } });
      fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
      fireEvent.change(phoneInput, { target: { value: "1234567890" } });
      fireEvent.change(ageInput, { target: { value: "28" } });
      fireEvent.change(passwordInput, { target: { value: "Password123@" } });
      fireEvent.click(maleButton);
      fireEvent.click(checkbox);
    };

    it("calls onRegister with form data when valid form is submitted", async () => {
      render(<RegisterPage {...defaultProps} />);

      fillValidForm();

      const submitButton = screen.getByTestId("button-continue");
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(defaultProps.onRegister).toHaveBeenCalledWith({
          fullName: "John Doe",
          email: "<EMAIL>",
          phoneNumber: "1234567890",
          age: "28",
          sex: "male",
          dateOfBirth: "",
          password: "Password123@",
          acceptsTerms: true,
        });
      });
    });

    it("shows submitting state during form submission", async () => {
      const slowOnRegister = jest.fn(
        () => new Promise((resolve) => setTimeout(resolve, 100))
      );
      render(<RegisterPage {...defaultProps} onRegister={slowOnRegister} />);

      fillValidForm();

      const submitButton = screen.getByTestId("button-continue");
      fireEvent.click(submitButton);

      expect(screen.getByTestId("button-submitting...")).toBeInTheDocument();
    });

    it("handles form submission without onRegister callback", async () => {
      render(<RegisterPage onLoginClick={defaultProps.onLoginClick} />);

      const checkbox = screen.getByRole("checkbox");
      fireEvent.click(checkbox);

      const submitButton = screen.getByTestId("button-continue");

      expect(() => {
        fireEvent.click(submitButton);
      }).not.toThrow();
    });
  });

  describe("Button Interactions", () => {
    it("calls onLoginClick when login link is clicked", () => {
      render(<RegisterPage {...defaultProps} />);

      const loginLink = screen.getByText("Login");
      fireEvent.click(loginLink);

      expect(defaultProps.onLoginClick).toHaveBeenCalledTimes(1);
    });

    it("handles missing onLoginClick callback", () => {
      render(<RegisterPage onRegister={defaultProps.onRegister} />);

      const loginLink = screen.getByText("Login");

      expect(() => {
        fireEvent.click(loginLink);
      }).not.toThrow();
    });
  });

  describe("Edge Cases", () => {
    it("handles missing callback props gracefully", () => {
      expect(() => {
        render(<RegisterPage />);
      }).not.toThrow();
    });

    it("applies custom className", () => {
      const { container } = render(<RegisterPage className="custom-class" />);

      expect(container.firstChild).toHaveClass("custom-class");
    });

    it("clears field errors when user starts typing", async () => {
      render(<RegisterPage {...defaultProps} />);

      // Submit form to trigger validation
      const checkbox = screen.getByRole("checkbox");
      fireEvent.click(checkbox);

      const submitButton = screen.getByTestId("button-continue");
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText("Full name is required")).toBeInTheDocument();
      });

      // Start typing in name field
      const nameInput = screen.getByTestId("input-enter-your-full-name");
      fireEvent.change(nameInput, { target: { value: "J" } });

      // Error should be cleared (though we can't easily test this with mocked components)
      expect(nameInput).toHaveValue("J");
    });

    it("handles password requirements correctly", () => {
      render(<RegisterPage {...defaultProps} />);

      const passwordInput = screen.getByTestId("password-input");

      // Test various password combinations
      fireEvent.change(passwordInput, { target: { value: "weak" } });
      expect(passwordInput).toHaveValue("weak");

      fireEvent.change(passwordInput, {
        target: { value: "StrongPassword123@" },
      });
      expect(passwordInput).toHaveValue("StrongPassword123@");
    });
  });

  describe("Accessibility", () => {
    it("has proper labels for form inputs", () => {
      render(<RegisterPage {...defaultProps} />);

      expect(screen.getByText("Full Name")).toBeInTheDocument();
      expect(screen.getByText("Email")).toBeInTheDocument();
      expect(screen.getByText("Number")).toBeInTheDocument();
      expect(screen.getByText("Age")).toBeInTheDocument();
      expect(screen.getByText("Sex")).toBeInTheDocument();
      expect(screen.getByText("Date of Birth")).toBeInTheDocument();
      expect(screen.getByText("Password")).toBeInTheDocument();
    });

    it("has proper button roles", () => {
      render(<RegisterPage {...defaultProps} />);

      expect(screen.getByTestId("toggle-female")).toBeInTheDocument();
      expect(screen.getByTestId("toggle-male")).toBeInTheDocument();
      expect(screen.getByTestId("button-continue")).toBeInTheDocument();
      expect(screen.getByText("Login")).toBeInTheDocument();
    });

    it("has proper checkbox role and labels", () => {
      render(<RegisterPage {...defaultProps} />);

      const checkbox = screen.getByRole("checkbox");
      expect(checkbox).toBeInTheDocument();
      expect(checkbox).toHaveAttribute("id", "terms");
    });

    it("has proper links for terms and privacy policy", () => {
      render(<RegisterPage {...defaultProps} />);

      const termsLink = screen.getByText("Terms");
      const privacyLink = screen.getByText("Privacy Policy");

      expect(termsLink).toBeInTheDocument();
      expect(privacyLink).toBeInTheDocument();
    });
  });

  describe("State Management", () => {
    it("maintains independent state for all form fields", () => {
      render(<RegisterPage {...defaultProps} />);

      const nameInput = screen.getByTestId("input-enter-your-full-name");
      const emailInput = screen.getByTestId("input-enter-your-email-address");
      const ageInput = screen.getByTestId("input-enter-your-age");

      fireEvent.change(nameInput, { target: { value: "John" } });
      fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
      fireEvent.change(ageInput, { target: { value: "25" } });

      expect(nameInput).toHaveValue("John");
      expect(emailInput).toHaveValue("<EMAIL>");
      expect(ageInput).toHaveValue(25);

      // Change one field, others should remain unchanged
      fireEvent.change(nameInput, { target: { value: "Jane" } });

      expect(nameInput).toHaveValue("Jane");
      expect(emailInput).toHaveValue("<EMAIL>");
      expect(ageInput).toHaveValue(25);
    });

    it("maintains sex selection state", () => {
      render(<RegisterPage {...defaultProps} />);

      const femaleButton = screen.getByTestId("toggle-female");
      const maleButton = screen.getByTestId("toggle-male");

      // Initially female should be selected
      expect(femaleButton).toHaveClass("bg-[var(--violet-6)]");

      fireEvent.click(maleButton);
      expect(maleButton).toHaveClass("bg-[var(--violet-6)]");
      expect(femaleButton).toHaveClass("border-[var(--grey-3)]");

      fireEvent.click(femaleButton);
      expect(femaleButton).toHaveClass("bg-[var(--violet-6)]");
      expect(maleButton).toHaveClass("border-[var(--grey-3)]");
    });

    it("maintains checkbox state independently", () => {
      render(<RegisterPage {...defaultProps} />);

      const checkbox = screen.getByRole("checkbox");
      const nameInput = screen.getByTestId("input-enter-your-full-name");

      fireEvent.click(checkbox);
      fireEvent.change(nameInput, { target: { value: "Test" } });

      expect(checkbox).toBeChecked();
      expect(nameInput).toHaveValue("Test");
    });
  });
});
