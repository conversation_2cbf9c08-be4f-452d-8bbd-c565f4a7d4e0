import React, { useEffect, useState } from "react";
import Header, { HeaderState } from "../shared/Header/Header";
import Footer from "../shared/Footer/Footer";
import Button, { ButtonType } from "../shared/Button/Button";
import ToggleButton from "../shared/ToggleButton/ToggleButton";
import Input from "../shared/Input/Input";
import PageHeader from "../shared/PageHeader/PageHeader";
import PasswordInput from "../shared/PasswordInput";
import {
  isValidEmail,
  isValidName,
  isValidPhoneNumber,
} from "@/lib/utils/inputValidations";

export interface RegisterPageProps {
  onRegister?: (data: RegistrationData) => void;
  onLoginClick?: () => void;
  className?: string;
}
interface PasswordRequirement {
  id: string;
  text: string;
  isValid: boolean;
}

// Check icon component
const CheckIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
    <circle cx="12" cy="12" r="10" fill="#10B981" />
    <path
      d="M9 12l2 2 4-4"
      stroke="white"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

// Unchecked circle icon component
const UncheckedIcon = () => (
  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
    <circle cx="12" cy="12" r="10" stroke="#D1D5DB" strokeWidth="2" />
  </svg>
);

// Define requirements structure as constant to avoid dependency issues
const INITIAL_REQUIREMENTS = [
  { id: "length", text: "At least 12 character", isValid: false },
  { id: "lowercase", text: "1 Lowercase letter", isValid: false },
  { id: "uppercase", text: "1 Upper case letter", isValid: false },
  { id: "digit", text: "1 Digit", isValid: false },
  { id: "symbol", text: "1 A symbol (@&%$)", isValid: false },
];

export interface RegistrationData {
  fullName: string;
  email: string;
  phoneNumber: string;
  age: string;
  sex: "female" | "male";
  dateOfBirth: string;
  password: string;
  acceptsTerms: boolean;
}

// Function to calculate age from date of birth
const calculateAge = (dateOfBirth: string): string => {
  if (!dateOfBirth) return "";

  const birthDate = new Date(dateOfBirth);
  const today = new Date();

  // Check if the date is valid
  if (isNaN(birthDate.getTime())) return "";

  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();

  // If birthday hasn't occurred this year yet, subtract 1
  if (
    monthDiff < 0 ||
    (monthDiff === 0 && today.getDate() < birthDate.getDate())
  ) {
    age--;
  }

  // Return empty string for negative ages (future dates)
  return age >= 0 ? age.toString() : "";
};

const RegisterPage: React.FC<RegisterPageProps> = ({
  onRegister,
  onLoginClick,
  className = "",
}) => {
  const [formData, setFormData] = useState<RegistrationData>({
    fullName: "",
    email: "",
    phoneNumber: "",
    age: "",
    sex: "female",
    dateOfBirth: "",
    password: "",
    acceptsTerms: false,
  });
  const [requirements, setRequirements] =
    useState<PasswordRequirement[]>(INITIAL_REQUIREMENTS);
  const [errors, setErrors] = useState<
    Partial<Record<keyof RegistrationData, string>>
  >({});
  const [showValidation, setShowValidation] = useState(false);
  const [generalError, setGeneralError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Check if form is valid for real-time button state
  const isFormValid = (): boolean => {
    return !!(
      isValidName(formData.fullName) &&
      isValidEmail(formData.email) &&
      isValidPhoneNumber(formData.phoneNumber) &&
      formData.dateOfBirth.trim() &&
      formData.sex &&
      formData.password &&
      requirements.every((req) => req.isValid) &&
      formData.acceptsTerms
    );
  };

  useEffect(() => {
    const newRequirements = INITIAL_REQUIREMENTS.map((req) => {
      switch (req.id) {
        case "length":
          return { ...req, isValid: formData.password.length >= 12 };
        case "lowercase":
          return { ...req, isValid: /[a-z]/.test(formData.password) };
        case "uppercase":
          return { ...req, isValid: /[A-Z]/.test(formData.password) };
        case "digit":
          return { ...req, isValid: /\d/.test(formData.password) };
        case "symbol":
          return { ...req, isValid: /[@&%$#!*]/.test(formData.password) };
        default:
          return req;
      }
    });
    setRequirements(newRequirements);
  }, [formData.password]);

  // Auto-calculate age when date of birth changes
  useEffect(() => {
    if (formData.dateOfBirth) {
      const calculatedAge = calculateAge(formData.dateOfBirth);
      setFormData((prev) => ({
        ...prev,
        age: calculatedAge,
      }));
    }
  }, [formData.dateOfBirth]);

  const handleInputChange = (
    field: keyof RegistrationData,
    value: string | boolean
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Real-time validation for immediate feedback
    const newErrors = { ...errors };

    if (field === "fullName" && typeof value === "string") {
      if (value.trim() && !isValidName(value)) {
        newErrors.fullName =
          "Name must be 2-100 characters, letters and spaces only";
      } else {
        delete newErrors.fullName;
      }
    }

    if (field === "email" && typeof value === "string") {
      if (value.trim() && !isValidEmail(value)) {
        newErrors.email = "Please enter a valid email address";
      } else {
        delete newErrors.email;
      }
    }

    if (field === "phoneNumber" && typeof value === "string") {
      if (value.trim() && !isValidPhoneNumber(value)) {
        newErrors.phoneNumber =
          "Phone must be 10-15 digits, no repeated numbers";
      } else {
        delete newErrors.phoneNumber;
      }
    }

    if (field === "dateOfBirth" && typeof value === "string") {
      if (value.trim()) {
        const birthDate = new Date(value);
        const today = new Date();
        if (birthDate > today) {
          newErrors.dateOfBirth = "Date of birth cannot be in the future";
        } else if (isNaN(birthDate.getTime())) {
          newErrors.dateOfBirth = "Please enter a valid date";
        } else {
          const calculatedAge = parseInt(calculateAge(value));
          if (calculatedAge < 16) {
            newErrors.dateOfBirth = "You must be at least 16 years old";
          } else if (calculatedAge > 100) {
            newErrors.dateOfBirth = "Please enter a valid date of birth";
          } else {
            delete newErrors.dateOfBirth;
          }
        }
      } else {
        delete newErrors.dateOfBirth;
      }
    }

    setErrors(newErrors);
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof RegistrationData, string>> = {};
    setGeneralError(null);

    if (!formData.fullName.trim()) {
      newErrors.fullName = "Full name is required";
    } else if (!isValidName(formData.fullName)) {
      newErrors.fullName =
        "Name must be 2-100 characters, letters and spaces only";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!isValidEmail(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    if (!formData.phoneNumber.trim()) {
      newErrors.phoneNumber = "Phone number is required";
    } else if (!isValidPhoneNumber(formData.phoneNumber)) {
      newErrors.phoneNumber =
        "Phone must be 10-15 digits, all repeated digits are not allowed";
    }

    // Date of Birth is now required
    if (!formData.dateOfBirth.trim()) {
      newErrors.dateOfBirth = "Date of birth is required";
    } else {
      // Validate that the date is not in the future
      const birthDate = new Date(formData.dateOfBirth);
      const today = new Date();
      if (birthDate > today) {
        newErrors.dateOfBirth = "Date of birth cannot be in the future";
      } else if (isNaN(birthDate.getTime())) {
        newErrors.dateOfBirth = "Please enter a valid date";
      } else {
        // Validate age is between 16 and 100
        const calculatedAge = parseInt(calculateAge(formData.dateOfBirth));
        if (calculatedAge < 16) {
          newErrors.dateOfBirth = "You must be at least 16 years old";
        } else if (calculatedAge > 100) {
          newErrors.dateOfBirth = "Please enter a valid date of birth";
        }
      }
    }

    // Age is now optional - no validation needed
    // But if manually entered, it should be valid
    if (
      formData.age.trim() &&
      (isNaN(Number(formData.age)) || Number(formData.age) <= 0)
    ) {
      newErrors.age = "Please enter a valid age";
    }

    if (!formData.sex) {
      newErrors.sex = "Sex is required";
    }
    if (!formData.password) {
      newErrors.password = "Password is required";
    } else {
      // Check all password requirements
      const failed = requirements.filter((r) => !r.isValid);
      if (failed.length > 0) {
        newErrors.password = "Password does not meet all requirements";
      }
    }
    if (!formData.acceptsTerms) {
      newErrors.acceptsTerms = "You must accept the Terms and Privacy Policy";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    setShowValidation(true);
    if (validateForm()) {
      setIsSubmitting(true);
      try {
        if (onRegister) {
          await onRegister(formData);
        }
      } finally {
        setIsSubmitting(false);
      }
    } else {
      setGeneralError("Please fix the errors above to continue.");
    }
  };

  const getPasswordStrength = () => {
    const validCount = requirements.filter((req) => req.isValid).length;
    const percentage = (validCount / requirements.length) * 100;

    let color = "#EF4444"; // Red for weak
    if (percentage >= 75) {
      color = "#10B981"; // Green for strong
    } else if (percentage >= 50) {
      color = "#F59E0B"; // Yellow for medium
    }

    return { percentage, color };
  };

  return (
    <div className={`min-h-screen flex flex-col ${className}`}>
      <Header state={HeaderState.HELP} />

      <main className="flex-1 flex justify-center py-8 px-[1.063rem] md:py-12 md:px-[9.375rem]">
        <div className="max-w-[26.5rem] w-full">
          <div className="text-center mb-12">
            <PageHeader title="Create Your GIVF Account" />
            <p className="text-[var(--grey-6)] text-base font-medium leading-relaxed">
              Let's begin your journey with GIVF. Create your secure account to
              access your IVF tools and progress.
            </p>
          </div>

          {/* Registration Form */}
          <form className="space-y-6" onSubmit={handleSubmit} noValidate>
            {/* Full Name */}
            <Input
              id="name"
              type="text"
              label="Full Name"
              value={formData.fullName}
              onChange={(value) => handleInputChange("fullName", value)}
              placeholder="Enter your full name"
              required={true}
              error={!!errors.fullName}
              errorMessage={errors.fullName}
              maxLength={50}
            />

            {/* Email */}
            <Input
              id="email"
              type="email"
              label="Email"
              value={formData.email}
              onChange={(value) => handleInputChange("email", value)}
              placeholder="Enter your email address"
              required={true}
              error={!!errors.email}
              errorMessage={errors.email}
              maxLength={50}
            />

            {/* Phone Number */}

            <Input
              id="phone"
              type="tel"
              label="Phone Number"
              value={formData.phoneNumber}
              onChange={(value) => handleInputChange("phoneNumber", value)}
              placeholder="Enter your phone number"
              required={true}
              error={!!errors.phoneNumber}
              errorMessage={errors.phoneNumber}
              maxLength={15}
            />

            {/* Date of Birth - Now Required */}
            <Input
              type="date"
              label="Date of Birth"
              value={formData.dateOfBirth}
              onChange={(value) => handleInputChange("dateOfBirth", value)}
              required={true}
              error={!!errors.dateOfBirth}
              errorMessage={errors.dateOfBirth}
            />

            {/* Age - Read-only and Auto-calculated */}
            <div>
              <label className="block text-[var(--grey-6)] text-base font-medium mb-2">
                Age
              </label>
              <Input
                type="text"
                value={formData.age}
                onChange={(value) => handleInputChange("age", value)}
                placeholder="Will be calculated from date of birth"
                required={false}
                disabled={true} // Always read-only
                error={showValidation && !!errors.age}
                errorMessage={
                  showValidation && errors.age ? errors.age : undefined
                }
              />
              {formData.dateOfBirth && formData.age && (
                <p className="text-[var(--grey-5)] text-sm mt-1">
                  Calculated from your date of birth
                </p>
              )}
            </div>

            {/* Sex */}
            <div>
              <label className="block text-[var(--grey-6)] text-base font-medium mb-3">
                Sex
              </label>
              <div className="flex gap-4">
                <ToggleButton
                  variant="compact"
                  isSelected={formData.sex === "female"}
                  onClick={() => handleInputChange("sex", "female")}
                >
                  Female
                </ToggleButton>
                <ToggleButton
                  variant="compact"
                  isSelected={formData.sex === "male"}
                  onClick={() => handleInputChange("sex", "male")}
                >
                  Male
                </ToggleButton>
              </div>
              {showValidation && errors.sex && (
                <div className="text-[var(--error-red-4)] text-xs mt-1">
                  {errors.sex}
                </div>
              )}
            </div>

            {/* Password Fields */}
            <div className="space-y-4 text-left">
              {/* New Password */}
              <div>
                <label
                  htmlFor="new-password"
                  className="block text-[var(--grey-6)] text-base font-medium mb-2"
                >
                  Password
                  <span className="text-[var(--error-red-4)] ml-1">*</span>
                </label>
                <PasswordInput
                  id="new-password"
                  value={formData.password}
                  onChange={(e) =>
                    handleInputChange("password", e.target.value)
                  }
                  placeholder="Create a password"
                  minLength={12}
                  required={true}
                  error={showValidation && !!errors.password}
                  errorMessage={
                    showValidation && errors.password
                      ? errors.password
                      : undefined
                  }
                />
                {showValidation && errors.password && (
                  <div className="text-[var(--error-red-4)] text-xs mt-1">
                    {errors.password}
                  </div>
                )}
              </div>
            </div>
            {/* Password Requirements */}
            <div className="text-left space-y-4">
              <div>
                <p className="text-[var(--grey-6)] text-sm">
                  Must have at least 12 character
                </p>
                <div className="w-full h-1 bg-gray-200 rounded-full mt-2">
                  <div
                    className="h-full rounded-full transition-all duration-300 ease-in-out"
                    style={{
                      width: `${getPasswordStrength().percentage}%`,
                      backgroundColor: getPasswordStrength().color,
                    }}
                  ></div>
                </div>
              </div>

              <div>
                <p className="text-[var(--grey-6)] text-sm mb-3">
                  To make your password Stronger:
                </p>
                <div className="grid grid-cols-2 gap-x-8 gap-y-3">
                  {requirements.map((requirement) => (
                    <div
                      key={requirement.id}
                      className="flex items-center gap-3"
                    >
                      {requirement.isValid ? <CheckIcon /> : <UncheckedIcon />}
                      <span
                        className={`text-sm ${
                          requirement.isValid
                            ? "text-[var(--grey-7)]"
                            : "text-[var(--grey-5)]"
                        }`}
                      >
                        {requirement.text}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Terms Acceptance */}
            <div className="flex items-start gap-3">
              <input
                type="checkbox"
                id="terms"
                checked={formData.acceptsTerms}
                onChange={(e) =>
                  handleInputChange("acceptsTerms", e.target.checked)
                }
                className="mt-0.5 h-4 w-4 text-[var(--violet-6)] focus:ring-[var(--violet-6)] border-[var(--grey-3)] rounded"
              />
              <label
                htmlFor="terms"
                className="text-[var(--grey-6)] text-base font-medium"
              >
                I accept the{" "}
                <a
                  href="/terms-conditions"
                  className="text-[var(--violet-6)] hover:text-[var(--violet-7)] underline"
                >
                  Terms
                </a>{" "}
                &{" "}
                <a
                  href="/privacy-policy"
                  className="text-[var(--violet-6)] hover:text-[var(--violet-7)] underline"
                >
                  Privacy Policy
                </a>
              </label>
            </div>
            {showValidation && errors.acceptsTerms && (
              <div className="text-[var(--error-red-4)] text-xs mt-1">
                {errors.acceptsTerms}
              </div>
            )}

            {/* General Error Message */}
            {showValidation && generalError && (
              <div className="text-[var(--error-red-4)] text-sm mt-2">
                {generalError}
              </div>
            )}

            {/* Submit Button */}
            <div className="pt-2">
              <Button
                type={ButtonType.PRIMARY}
                text={isSubmitting ? "Submitting..." : "Continue"}
                onClick={handleSubmit}
                disabled={!isFormValid() || isSubmitting}
                className="h-[3.125rem]"
              />
            </div>
          </form>

          {/* Divider */}
          <div className="flex items-center my-6">
            <div className="flex-1 border-t border-[var(--grey-3)]"></div>
            <span className="px-3 text-[var(--grey-5)] text-base">or</span>
            <div className="flex-1 border-t border-[var(--grey-3)]"></div>
          </div>

          {/* Login Link */}
          <div className="text-center">
            <span className="text-[var(--grey-6)] text-base">
              Already have an account?{" "}
              <button
                type="button"
                onClick={onLoginClick}
                className="cursor-pointer text-[var(--violet-6)] hover:text-[var(--violet-7)] font-medium underline"
              >
                Login
              </button>
            </span>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default RegisterPage;
