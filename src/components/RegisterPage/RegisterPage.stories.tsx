import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import RegisterPage from "./RegisterPage";
import { AuthProvider } from "@/contexts/AuthContext";
import { ToastProvider } from "@/contexts/ToastContext";

const meta: Meta<typeof RegisterPage> = {
  title: "Pages/RegisterPage",
  component: RegisterPage,
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component:
          "A complete registration page that collects user information for IVF account creation. Features form validation, responsive design, and integration with Header, Footer, Button, and ToggleButton components.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    onRegister: {
      description:
        "Callback function called when registration form is submitted",
      action: "onRegister",
    },
    onLoginClick: {
      description: "Callback function called when login link is clicked",
      action: "onLoginClick",
    },
    className: {
      description: "Additional CSS classes to apply to the component",
      control: "text",
    },
  },
  decorators: [
    (Story) => (
      <ToastProvider duration={4000} maxToasts={5}>
        <AuthProvider>
          <Story />
        </AuthProvider>
      </ToastProvider>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof RegisterPage>;

export const Default: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story: "Default registration page with all form fields and validation.",
      },
    },
  },
};

export const WithHandlers: Story = {
  args: {
    onRegister: (data) => {
      console.log("Registration data:", data);
      alert(`Registration submitted for ${data.fullName} (${data.email})`);
    },
    onLoginClick: () => {
      console.log("Login link clicked");
      alert("Redirecting to login page...");
    },
  },
  parameters: {
    docs: {
      description: {
        story:
          "Registration page with event handlers that show alerts and log data to console.",
      },
    },
  },
};

export const CustomClassName: Story = {
  args: {
    className: "bg-gradient-to-br from-blue-50 to-purple-50",
  },
  parameters: {
    docs: {
      description: {
        story:
          "Registration page with custom background styling using className prop.",
      },
    },
  },
};

export const Mobile: Story = {
  args: {},
  parameters: {
    viewport: {
      defaultViewport: "mobile1",
    },
    docs: {
      description: {
        story: "Registration page optimized for mobile viewing experience.",
      },
    },
  },
};

export const Tablet: Story = {
  args: {},
  parameters: {
    viewport: {
      defaultViewport: "tablet",
    },
    docs: {
      description: {
        story: "Registration page displayed on tablet-sized screens.",
      },
    },
  },
};

export const Interactive: Story = {
  args: {
    onRegister: (data) => {
      console.log("Complete registration data:", data);
    },
    onLoginClick: () => {
      console.log("Navigate to login");
    },
  },
  parameters: {
    docs: {
      description: {
        story:
          "Fully interactive registration page for testing form flows and user interactions.",
      },
    },
  },
};
