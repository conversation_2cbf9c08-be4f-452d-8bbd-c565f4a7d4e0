"use client";
import React from "react";
import Image from "next/image";
import Header from "../shared/Header/Header";
import Footer from "../shared/Footer/Footer";
import Button, { ButtonType } from "../shared/Button/Button";
import NeedHelp from "../shared/NeedHelp/NeedHelp";
import PageHeader from "../shared/PageHeader/PageHeader";
import { ArrowRightIcon } from "@phosphor-icons/react";

export interface LandingPageProps {
  onStartScore?: () => void;
  onLogin?: () => void;
  onPhoneClick?: (phoneNumber: string) => void;
}

const LandingPage: React.FC<LandingPageProps> = ({ onStartScore, onLogin }) => {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Header */}
      <Header onClick={onLogin} />

      {/* Main Content */}
      <main className="flex-1 flex items-center justify-center px-6 md:px-[9.375rem] py-16">
        <div className="w-full max-w-4xl text-center flex flex-col items-center">
          {/* Hero Image */}
          <div className="flex justify-center">
            <div className="relative">
              {/* Decorative ellipse dots */}
              <div className="absolute z-10 top-[-0.325rem] left-1/2 transform -translate-x-1/2">
                <Image
                  src="/assets/landingPage/ellipse.png"
                  alt="Decorative dot"
                  width={13}
                  height={13}
                  className="w-3 h-3"
                />
              </div>
              <div className="absolute z-10 left-[1.625rem] top-1/2 transform -translate-y-1/2">
                <Image
                  src="/assets/landingPage/ellipse.png"
                  alt="Decorative dot"
                  width={13}
                  height={13}
                  className="w-3 h-3"
                />
              </div>
              <div className="absolute z-10 right-[1.625rem] top-1/2 transform -translate-y-1/2">
                <Image
                  src="/assets/landingPage/ellipse.png"
                  alt="Decorative dot"
                  width={13}
                  height={13}
                  className="w-3 h-3"
                />
              </div>

              {/* Main image */}
              <div className="relative w-64 h-64 mx-auto">
                <Image
                  src="/assets/landingPage/hero-image.png"
                  alt="Pregnant woman"
                  width={361}
                  height={361}
                  className="w-full h-full object-cover rounded-full"
                  priority
                />
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="w-[22.125rem] md:w-[39.125rem] flex flex-col gap-8 mt-[-1rem] z-10">
            <div className="flex flex-col gap-4">
              <PageHeader title="Understand Your IVF Success Chances" />

              <p className="text-base md:text-xl text-[var(--grey-6)]">
                This score is based on your medical, lifestyle, and
                environment-related factors.
              </p>
            </div>
            {/* CTA Button */}
            <div>
              <div className="max-w-xs mx-auto">
                <Button
                  type={ButtonType.PRIMARY}
                  text="Start My Test"
                  icon={<ArrowRightIcon size={20} />}
                  onClick={
                    onStartScore || (() => console.log("Start Score clicked"))
                  }
                />
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <NeedHelp className="ml-6 md:ml-37.5 md:my-10 my-6" />
      <Footer />
    </div>
  );
};

export default LandingPage;
