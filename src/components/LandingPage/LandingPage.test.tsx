import { render, screen, fireEvent } from "@testing-library/react";
import LandingPage from "./LandingPage";

// Mock next/image
jest.mock("next/image", () => ({
  __esModule: true,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @next/next/no-img-element
  default: (props: any) => <img alt={props.alt || ""} {...props} />,
}));

// Mock Next.js router
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  }),
}));

// Mock AuthProvider to avoid Supabase dependency
jest.mock("@/contexts/AuthContext", () => ({
  useAuth: () => ({ user: null, loading: false }), // user: null means logged out
  AuthProvider: ({ children }: { children: React.ReactNode }) => (
    <>{children}</>
  ),
}));

// Set dummy env vars for Supabase if needed
process.env.NEXT_PUBLIC_SUPABASE_URL = "http://localhost:54321";
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = "test_anon_key";

describe("LandingPage", () => {
  it("renders the main heading", () => {
    render(<LandingPage />);
    expect(
      screen.getByText("Understand Your IVF Success Chances")
    ).toBeInTheDocument();
  });

  it("renders the description text", () => {
    render(<LandingPage />);
    expect(
      screen.getByText(
        "This score is based on your medical, lifestyle, and environment-related factors."
      )
    ).toBeInTheDocument();
  });

  it("renders the Start My Score button", () => {
    render(<LandingPage />);
    expect(
      screen.getByRole("button", { name: /start my test/i })
    ).toBeInTheDocument();
  });

  it("renders the pregnant woman image", () => {
    render(<LandingPage />);
    expect(screen.getByAltText("Pregnant woman")).toBeInTheDocument();
  });

  it("renders the header component", () => {
    render(<LandingPage />);
    expect(screen.getByRole("banner")).toBeInTheDocument();
  });

  it("renders the footer component", () => {
    render(<LandingPage />);
    expect(screen.getByText("© 2025 Gunjan IVF")).toBeInTheDocument();
  });

  it("renders the need help component", () => {
    render(<LandingPage />);
    expect(screen.getByText("Need Help?")).toBeInTheDocument();
  });

  it("calls onStartScore when Start My Score button is clicked", () => {
    const mockOnStartScore = jest.fn();
    render(<LandingPage onStartScore={mockOnStartScore} />);

    fireEvent.click(screen.getByRole("button", { name: /start my test/i }));
    expect(mockOnStartScore).toHaveBeenCalledTimes(1);
  });

  it("calls onLogin when login button is clicked", () => {
    const mockOnLogin = jest.fn();
    render(<LandingPage onLogin={mockOnLogin} />);

    fireEvent.click(screen.getByRole("button", { name: "Login" }));
    expect(mockOnLogin).toHaveBeenCalledTimes(1);
  });

  it("has proper layout structure", () => {
    render(<LandingPage />);
    const main = screen.getByRole("main");
    expect(main).toHaveClass(
      "flex-1",
      "flex",
      "items-center",
      "justify-center"
    );
  });

  it("has responsive design classes", () => {
    render(<LandingPage />);
    const main = screen.getByRole("main");
    expect(main).toHaveClass("px-6", "md:px-[9.375rem]");
  });
});
