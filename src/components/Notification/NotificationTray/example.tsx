/* eslint-disable react/no-unescaped-entities */
import React, { useState } from "react";
import NotificationTray, { NotificationTrayIcons } from "./NotificationTray";
import type { NotificationItemData } from "../NotificationItem/NotificationItem";

// Example usage of the NotificationTray component
const NotificationTrayExample: React.FC = () => {
  const [notifications, setNotifications] = useState<NotificationItemData[]>([
    {
      id: "1",
      title: "Appointment Confirmed",
      description:
        "Your appointment with Dr. <PERSON><PERSON><PERSON> is scheduled for 27 June at 10:00 AM.",
      timestamp: "2 hours ago",
      icon: NotificationTrayIcons.appointment,
      isUnread: true,
      type: "appointment",
    },
    {
      id: "2",
      title: "Stage Unlocked",
      description:
        "You've unlocked Stage 4 — Embryo Growth in your IVF journey.",
      timestamp: "Yesterday",
      icon: NotificationTrayIcons.stage,
      isUnread: true,
      type: "stage",
    },
    {
      id: "3",
      title: "New Diet Plan Available",
      description:
        "Your personalized 7-day diet plan is now ready to download.",
      timestamp: "2 days ago",
      icon: NotificationTrayIcons.diet,
      isUnread: false,
      type: "diet",
    },
    {
      id: "4",
      title: "Good News Wall Update",
      description:
        "Baby Aarav's IVF story has been added to the Good News Wall.",
      timestamp: "3 days ago",
      icon: NotificationTrayIcons.news,
      isUnread: false,
      type: "news",
    },
    {
      id: "5",
      title: "Wellness Tip of the Day",
      description: '"Breathe. Believe. Receive." Your daily quote is ready.',
      timestamp: "3 days ago",
      icon: NotificationTrayIcons.wellness,
      isUnread: false,
      type: "wellness",
    },
  ]);

  const handleNotificationClick = (notification: NotificationItemData) => {
    // Mark individual notification as read
    setNotifications((prev) =>
      prev.map((n) =>
        n.id === notification.id ? { ...n, isUnread: false } : n
      )
    );

    // Handle navigation or other actions based on notification type
    switch (notification.type) {
      case "appointment":
        console.log("Navigate to appointments page");
        // Example: router.push('/appointments');
        break;
      case "stage":
        console.log("Navigate to IVF journey tracker");
        // Example: router.push('/ivf-journey');
        break;
      case "diet":
        console.log("Navigate to diet plan");
        // Example: router.push('/diet-plan');
        break;
      case "news":
        console.log("Navigate to good news wall");
        // Example: router.push('/good-news');
        break;
      case "wellness":
        console.log("Navigate to wellness tips");
        // Example: router.push('/wellness');
        break;
      default:
        console.log("Default notification action");
    }
  };

  const handleMarkAllAsRead = () => {
    setNotifications((prev) =>
      prev.map((notification) => ({ ...notification, isUnread: false }))
    );
  };

  const addNewNotification = () => {
    const newNotification: NotificationItemData = {
      id: Date.now().toString(),
      title: "New Test Notification",
      description:
        "This is a new test notification to demonstrate dynamic updates.",
      timestamp: "Just now",
      icon: NotificationTrayIcons.other,
      isUnread: true,
      type: "other",
    };

    setNotifications((prev) => [newNotification, ...prev]);
  };

  const clearAllNotifications = () => {
    setNotifications([]);
  };

  const unreadCount = notifications.filter((n) => n.isUnread).length;

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Notifications
              </h2>
              <p className="text-sm text-gray-500">
                {notifications.length} total, {unreadCount} unread
              </p>
            </div>

            <div className="flex gap-2">
              {unreadCount > 0 && (
                <button
                  onClick={handleMarkAllAsRead}
                  className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  Mark All Read
                </button>
              )}
              <button
                onClick={addNewNotification}
                className="px-3 py-1 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
              >
                Add Test
              </button>
              <button
                onClick={clearAllNotifications}
                className="px-3 py-1 text-sm bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
              >
                Clear All
              </button>
            </div>
          </div>
        </div>

        {/* Notification Tray */}
        <NotificationTray
          notifications={notifications}
          onNotificationClick={handleNotificationClick}
          className="bg-white"
        />
      </div>

      {/* Usage Information */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="font-medium text-gray-900 mb-2">Usage Instructions:</h3>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>
            • Click on any notification to mark it as read and handle the click
          </li>
          <li>• Blue dots indicate unread notifications</li>
          <li>• Unread notifications have a light blue background</li>
          <li>• Use "Mark All Read" to mark all notifications as read</li>
          <li>• Use "Add Test" to add a new notification</li>
          <li>• Use "Clear All" to remove all notifications</li>
        </ul>
      </div>
    </div>
  );
};

export default NotificationTrayExample;
