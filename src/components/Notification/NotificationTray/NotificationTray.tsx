/* eslint-disable react/no-unescaped-entities */
import React from "react";
import NotificationItem from "../NotificationItem/NotificationItem";

export interface NotificationItemType {
  id: string;
  title: string;
  description: string;
  timestamp: string;
  icon: React.ReactNode;
  isUnread: boolean;
  type: "appointment" | "stage" | "diet" | "news" | "wellness" | "other";
}

export interface NotificationTrayProps {
  notifications: NotificationItemType[];
  onNotificationClick?: (notification: NotificationItemType) => void;
  className?: string;
  showEmptyState?: boolean;
  emptyStateText?: string;
}

const NotificationTray: React.FC<NotificationTrayProps> = ({
  notifications,
  onNotificationClick,
  className = "",
  showEmptyState = true,
  emptyStateText = "No notifications yet",
}) => {
  if (notifications.length === 0 && !showEmptyState) {
    return null;
  }

  return (
    <div className={`space-y-0 ${className} flex flex-col gap-2`}>
      {notifications.length === 0 ? (
        <div className="text-center py-12 text-gray-500">
          <svg
            className="w-12 h-12 mx-auto mb-4 text-gray-300"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M15 17h5l-3.5-3.5-1.5 1.5V3a2 2 0 00-2-2H9a2 2 0 00-2 2v12.5l-1.5-1.5L2 17h5"
            />
          </svg>
          <p className="text-lg font-medium">{emptyStateText}</p>
          <p className="text-sm text-gray-400 mt-1">
            We'll notify you when something happens
          </p>
        </div>
      ) : (
        notifications.map((notification, index) => (
          <NotificationItem
            key={notification.id}
            notification={notification}
            onNotificationClick={onNotificationClick}
            showBorder={index !== notifications.length - 1}
          />
        ))
      )}
    </div>
  );
};

// Notification icons with proper colors matching the image
export const NotificationTrayIcons = {
  appointment: (
    <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
      <svg
        className="w-6 h-6 text-purple-600"
        viewBox="0 0 24 24"
        fill="currentColor"
      >
        <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 3v2h-4V6h4zm-4 4h4v2h-4v-2zm4 4h-4v2h4v-2z" />
      </svg>
    </div>
  ),
  stage: (
    <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
      <svg
        className="w-6 h-6 text-yellow-600"
        viewBox="0 0 24 24"
        fill="currentColor"
      >
        <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z" />
      </svg>
    </div>
  ),
  diet: (
    <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
      <svg
        className="w-6 h-6 text-red-500"
        viewBox="0 0 24 24"
        fill="currentColor"
      >
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" />
      </svg>
    </div>
  ),
  news: (
    <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
      <svg
        className="w-6 h-6 text-green-600"
        viewBox="0 0 24 24"
        fill="currentColor"
      >
        <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z" />
      </svg>
    </div>
  ),
  wellness: (
    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
      <svg
        className="w-6 h-6 text-blue-600"
        viewBox="0 0 24 24"
        fill="currentColor"
      >
        <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" />
      </svg>
    </div>
  ),
  other: (
    <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
      <svg
        className="w-6 h-6 text-gray-600"
        viewBox="0 0 24 24"
        fill="currentColor"
      >
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
      </svg>
    </div>
  ),
};

export default NotificationTray;
