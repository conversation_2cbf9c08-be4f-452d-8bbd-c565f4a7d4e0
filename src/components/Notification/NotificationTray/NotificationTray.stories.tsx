import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import NotificationTray, {
  NotificationItemType,
  NotificationTrayIcons,
} from "./NotificationTray";

const meta: Meta<typeof NotificationTray> = {
  title: "Components/Notification/NotificationTray",
  component: NotificationTray,
  parameters: {
    layout: "padded",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Sample notifications matching the image
const sampleNotifications: NotificationItemType[] = [
  {
    id: "1",
    title: "Appointment Confirmed",
    description:
      "Your appointment with Dr. <PERSON><PERSON><PERSON> is scheduled for 27 June at 10:00 AM.",
    timestamp: "2 hours ago",
    icon: NotificationTrayIcons.appointment,
    isUnread: true,
    type: "appointment",
  },
  {
    id: "2",
    title: "Stage Unlocked",
    description: "You've unlocked Stage 4 — Embryo Growth in your IVF journey.",
    timestamp: "Yesterday",
    icon: NotificationTrayIcons.stage,
    isUnread: true,
    type: "stage",
  },
  {
    id: "3",
    title: "New Diet Plan Available",
    description: "Your personalized 7-day diet plan is now ready to download.",
    timestamp: "2 days ago",
    icon: NotificationTrayIcons.diet,
    isUnread: false,
    type: "diet",
  },
  {
    id: "4",
    title: "Good News Wall Update",
    description: "Baby Aarav's IVF story has been added to the Good News Wall.",
    timestamp: "3 days ago",
    icon: NotificationTrayIcons.news,
    isUnread: false,
    type: "news",
  },
  {
    id: "5",
    title: "Wellness Tip of the Day",
    description: '"Breathe. Believe. Receive." Your daily quote is ready.',
    timestamp: "3 days ago",
    icon: NotificationTrayIcons.wellness,
    isUnread: false,
    type: "wellness",
  },
];

export const Default: Story = {
  args: {
    notifications: sampleNotifications,
    onNotificationClick: (notification) =>
      console.log("Notification clicked:", notification),
  },
};

export const Empty: Story = {
  args: {
    notifications: [],
    onNotificationClick: (notification) =>
      console.log("Notification clicked:", notification),
  },
};

export const OnlyUnread: Story = {
  args: {
    notifications: sampleNotifications.filter((n) => n.isUnread),
    onNotificationClick: (notification) =>
      console.log("Notification clicked:", notification),
  },
};

export const WithoutClickHandler: Story = {
  args: {
    notifications: sampleNotifications,
  },
};

export const CustomEmptyState: Story = {
  args: {
    notifications: [],
    emptyStateText: "All caught up!",
    onNotificationClick: (notification) =>
      console.log("Notification clicked:", notification),
  },
};

export const LongList: Story = {
  args: {
    notifications: [
      ...sampleNotifications,
      ...sampleNotifications.map((n, i) => ({
        ...n,
        id: `${n.id}-${i}`,
        timestamp: `${i + 4} days ago`,
        isUnread: false,
      })),
    ],
    onNotificationClick: (notification) =>
      console.log("Notification clicked:", notification),
  },
};
