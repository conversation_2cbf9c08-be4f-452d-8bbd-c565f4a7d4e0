/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import NotificationTray, { NotificationTrayIcons } from "./NotificationTray";
import type { NotificationItem } from "./NotificationTray";

const mockNotifications: NotificationItem[] = [
  {
    id: "1",
    title: "Appointment Confirmed",
    description:
      "Your appointment with Dr. <PERSON><PERSON><PERSON> is scheduled for 27 June at 10:00 AM.",
    timestamp: "2 hours ago",
    icon: NotificationTrayIcons.appointment,
    isUnread: true,
    type: "appointment",
  },
  {
    id: "2",
    title: "Stage Unlocked",
    description: "You've unlocked Stage 4 — Embryo Growth in your IVF journey.",
    timestamp: "Yesterday",
    icon: NotificationTrayIcons.stage,
    isUnread: false,
    type: "stage",
  },
  {
    id: "3",
    title: "New Diet Plan Available",
    description: "Your personalized 7-day diet plan is now ready to download.",
    timestamp: "2 days ago",
    icon: NotificationTrayIcons.diet,
    isUnread: true,
    type: "diet",
  },
];

describe("NotificationTray", () => {
  const mockOnNotificationClick = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Component Rendering", () => {
    it("renders all notifications", () => {
      render(
        <NotificationTray
          notifications={mockNotifications}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      expect(screen.getByText("Appointment Confirmed")).toBeInTheDocument();
      expect(screen.getByText("Stage Unlocked")).toBeInTheDocument();
      expect(screen.getByText("New Diet Plan Available")).toBeInTheDocument();
    });

    it("renders notification descriptions correctly", () => {
      render(
        <NotificationTray
          notifications={mockNotifications}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      expect(
        screen.getByText(/Your appointment with Dr. Neha Sharma/)
      ).toBeInTheDocument();
      expect(screen.getByText(/You've unlocked Stage 4/)).toBeInTheDocument();
      expect(
        screen.getByText(/Your personalized 7-day diet plan/)
      ).toBeInTheDocument();
    });

    it("renders timestamps correctly", () => {
      render(
        <NotificationTray
          notifications={mockNotifications}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      expect(screen.getByText("2 hours ago")).toBeInTheDocument();
      expect(screen.getByText("Yesterday")).toBeInTheDocument();
      expect(screen.getByText("2 days ago")).toBeInTheDocument();
    });

    it("applies custom className when provided", () => {
      const { container } = render(
        <NotificationTray
          notifications={mockNotifications}
          className="custom-class"
          onNotificationClick={mockOnNotificationClick}
        />
      );

      expect(container.firstChild).toHaveClass("custom-class");
    });
  });

  describe("Empty State", () => {
    it("renders default empty state when no notifications", () => {
      render(
        <NotificationTray
          notifications={[]}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      expect(screen.getByText("No notifications yet")).toBeInTheDocument();
      expect(
        screen.getByText("We'll notify you when something happens")
      ).toBeInTheDocument();
    });

    it("renders custom empty state text", () => {
      render(
        <NotificationTray
          notifications={[]}
          emptyStateText="All caught up!"
          onNotificationClick={mockOnNotificationClick}
        />
      );

      expect(screen.getByText("All caught up!")).toBeInTheDocument();
      expect(
        screen.getByText("We'll notify you when something happens")
      ).toBeInTheDocument();
    });

    it("does not render when showEmptyState is false and no notifications", () => {
      const { container } = render(
        <NotificationTray
          notifications={[]}
          showEmptyState={false}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      expect(container.firstChild).toBeNull();
    });
  });

  describe("Notification Interactions", () => {
    it("calls onNotificationClick when a notification is clicked", () => {
      render(
        <NotificationTray
          notifications={mockNotifications}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      fireEvent.click(screen.getByText("Appointment Confirmed"));
      expect(mockOnNotificationClick).toHaveBeenCalledTimes(1);
      expect(mockOnNotificationClick).toHaveBeenCalledWith(
        mockNotifications[0]
      );
    });

    it("does not throw error when onNotificationClick is not provided", () => {
      render(<NotificationTray notifications={mockNotifications} />);

      expect(() => {
        fireEvent.click(screen.getByText("Appointment Confirmed"));
      }).not.toThrow();
    });

    it("adds hover styling when onNotificationClick is provided", () => {
      render(
        <NotificationTray
          notifications={mockNotifications}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      const notificationElement = screen
        .getByText("Appointment Confirmed")
        .closest('[class*="flex items-start gap-4"]');
      expect(notificationElement).toHaveClass(
        "hover:bg-gray-50",
        "cursor-pointer"
      );
    });

    it("does not add hover styling when onNotificationClick is not provided", () => {
      render(<NotificationTray notifications={mockNotifications} />);

      const notificationElement = screen
        .getByText("Appointment Confirmed")
        .closest('[class*="flex items-start gap-4"]');
      expect(notificationElement).not.toHaveClass(
        "hover:bg-gray-50",
        "cursor-pointer"
      );
    });
  });

  describe("Unread Indicators", () => {
    it("shows unread indicators for unread notifications", () => {
      render(
        <NotificationTray
          notifications={mockNotifications}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      const unreadIndicators = screen
        .getAllByRole("generic")
        .filter((element) =>
          element.className.includes("bg-blue-600 rounded-full")
        );

      // Two notifications are unread (appointment and diet)
      expect(unreadIndicators).toHaveLength(2);
    });

    it("applies unread styling to unread notifications", () => {
      render(
        <NotificationTray
          notifications={mockNotifications}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      const appointmentContainer = screen
        .getByText("Appointment Confirmed")
        .closest('[class*="flex items-start gap-4"]');
      const stageContainer = screen
        .getByText("Stage Unlocked")
        .closest('[class*="flex items-start gap-4"]');
      const dietContainer = screen
        .getByText("New Diet Plan Available")
        .closest('[class*="flex items-start gap-4"]');

      expect(appointmentContainer).toHaveClass("bg-blue-50/30");
      expect(stageContainer).toHaveClass("bg-white");
      expect(dietContainer).toHaveClass("bg-blue-50/30");
    });
  });

  describe("Layout and Styling", () => {
    it("adds border between notifications except for the last one", () => {
      render(
        <NotificationTray
          notifications={mockNotifications}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      const appointmentContainer = screen
        .getByText("Appointment Confirmed")
        .closest('[class*="flex items-start gap-4"]');
      const stageContainer = screen
        .getByText("Stage Unlocked")
        .closest('[class*="flex items-start gap-4"]');
      const dietContainer = screen
        .getByText("New Diet Plan Available")
        .closest('[class*="flex items-start gap-4"]');

      // First and second notifications should have border-b
      expect(appointmentContainer).toHaveClass("border-b");
      expect(stageContainer).toHaveClass("border-b");

      // Last notification should not have border-b
      expect(dietContainer).not.toHaveClass("border-b");
    });

    it("has proper spacing and layout classes", () => {
      const { container } = render(
        <NotificationTray
          notifications={mockNotifications}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      expect(container.firstChild).toHaveClass("space-y-0");
    });
  });

  describe("Content Display", () => {
    it("displays notification titles with proper styling", () => {
      render(
        <NotificationTray
          notifications={mockNotifications}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      const title = screen.getByText("Appointment Confirmed");
      expect(title).toHaveClass("text-base", "font-semibold", "text-gray-900");
    });

    it("displays notification descriptions with proper styling", () => {
      render(
        <NotificationTray
          notifications={mockNotifications}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      const description = screen.getByText(
        /Your appointment with Dr. Neha Sharma/
      );
      expect(description).toHaveClass("text-sm", "text-gray-600");
    });

    it("displays timestamps with proper styling", () => {
      render(
        <NotificationTray
          notifications={mockNotifications}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      const timestamp = screen.getByText("2 hours ago");
      expect(timestamp).toHaveClass("text-sm", "text-gray-500");
    });
  });

  describe("Icons", () => {
    it("renders notification icons", () => {
      render(
        <NotificationTray
          notifications={mockNotifications}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      // Check that icons are rendered (they should be in the DOM)
      const notificationElements = screen
        .getAllByText(/Appointment|Stage|Diet/)
        .map((el) => el.closest("div")?.previousElementSibling);

      notificationElements.forEach((iconContainer) => {
        expect(iconContainer).toBeInTheDocument();
      });
    });
  });

  describe("Accessibility", () => {
    it("maintains proper structure for screen readers", () => {
      render(
        <NotificationTray
          notifications={mockNotifications}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      // Check that titles are properly structured
      expect(
        screen.getByRole("heading", { level: 3, name: "Appointment Confirmed" })
      ).toBeInTheDocument();
      expect(
        screen.getByRole("heading", { level: 3, name: "Stage Unlocked" })
      ).toBeInTheDocument();
      expect(
        screen.getByRole("heading", {
          level: 3,
          name: "New Diet Plan Available",
        })
      ).toBeInTheDocument();
    });
  });
});
