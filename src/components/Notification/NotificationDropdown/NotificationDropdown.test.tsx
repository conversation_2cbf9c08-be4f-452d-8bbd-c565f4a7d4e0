import React from "react";
import { render, screen } from "@testing-library/react";
import NotificationDropdown, {
  NotificationIcons,
  NotificationItem,
} from "./NotificationDropdown";

const mockNotifications: NotificationItem[] = [
  {
    id: "1",
    title: "Appointment Confirmed",
    description: "...",
    timestamp: "2 hours ago",
    icon: NotificationIcons.appointment,
    isUnread: true,
    type: "appointment", // <-- use one of the allowed values
  },
  {
    id: "2",
    title: "Stage Unlocked",
    description: "...",
    timestamp: "Yesterday",
    icon: NotificationIcons.stage,
    isUnread: false,
    type: "stage", // <-- use one of the allowed values
  },
];
describe("NotificationDropdown", () => {
  const mockOnMarkAllAsRead = jest.fn();
  const mockOnNotificationClick = jest.fn();

  it("renders the dropdown and mark all as read button when isOpen is true", () => {
    render(
      <NotificationDropdown
        notifications={mockNotifications}
        onMarkAllAsRead={mockOnMarkAllAsRead}
        onNotificationClick={mockOnNotificationClick}
        onClose={() => {}}
        isOpen={true}
      />
    );
    expect(screen.getByText(/notifications/i)).toBeInTheDocument();
  });

  it("renders empty state when no notifications", () => {
    render(
      <NotificationDropdown
        notifications={[]}
        onMarkAllAsRead={mockOnMarkAllAsRead}
        onNotificationClick={mockOnNotificationClick}
        onClose={() => {}}
        isOpen={true}
      />
    );
    expect(screen.getByText(/no notifications/i)).toBeInTheDocument();
  });

  it("calls onMarkAllAsRead when button is clicked", () => {
    render(
      <NotificationDropdown
        notifications={mockNotifications}
        onMarkAllAsRead={mockOnMarkAllAsRead}
        onNotificationClick={mockOnNotificationClick}
        onClose={() => {}}
        isOpen={true}
      />
    );
  });

  // ...other tests, using queryByText and robust selectors as above
});
