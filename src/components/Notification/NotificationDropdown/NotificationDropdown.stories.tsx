import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import NotificationDropdown, {
  NotificationIcons,
} from "./NotificationDropdown";
import type { NotificationItem } from "./NotificationDropdown";

const meta: Meta<typeof NotificationDropdown> = {
  title: "Components/Notification/NotificationDropdown",
  component: NotificationDropdown,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Sample notifications matching the image
const sampleNotifications: NotificationItem[] = [
  {
    id: "1",
    title: "Appointment Confirmed",
    description:
      "Your appointment with Dr. <PERSON><PERSON><PERSON> is scheduled for 27 June at 10:00 AM.",
    timestamp: "2 hours ago",
    icon: NotificationIcons.appointment,
    isUnread: true,
    type: "appointment",
  },
  {
    id: "2",
    title: "Stage Unlocked",
    description: "You've unlocked Stage 4 — Embryo Growth in your IVF journey.",
    timestamp: "Yesterday",
    icon: NotificationIcons.stage,
    isUnread: true,
    type: "stage",
  },
  {
    id: "3",
    title: "New Diet Plan Available",
    description: "Your personalized 7-day diet plan is now ready to download.",
    timestamp: "2 days ago",
    icon: NotificationIcons.diet,
    isUnread: false,
    type: "diet",
  },
  {
    id: "4",
    title: "Good News Wall Update",
    description: "Baby Aarav's IVF story has been added to the Good News Wall.",
    timestamp: "3 days ago",
    icon: NotificationIcons.news,
    isUnread: false,
    type: "news",
  },
  {
    id: "5",
    title: "Wellness Tip of the Day",
    description: '"Breathe. Believe. Receive." Your daily quote is ready.',
    timestamp: "3 days ago",
    icon: NotificationIcons.wellness,
    isUnread: false,
    type: "wellness",
  },
  {
    id: "6",
    title: "Good News Wall Update",
    description: "Baby Aarav's IVF story has been added to the Good News Wall.",
    timestamp: "3 days ago",
    icon: NotificationIcons.news,
    isUnread: false,
    type: "news",
  },
  {
    id: "7",
    title: "Wellness Tip of the Day",
    description: '"Breathe. Believe. Receive." Your daily quote is ready.',
    timestamp: "3 days ago",
    icon: NotificationIcons.wellness,
    isUnread: false,
    type: "wellness",
  },
  {
    id: "8",
    title: "Good News Wall Update",
    description: "Baby Aarav's IVF story has been added to the Good News Wall.",
    timestamp: "3 days ago",
    icon: NotificationIcons.news,
    isUnread: false,
    type: "news",
  },
  {
    id: "9",
    title: "Wellness Tip of the Day",
    description: '"Breathe. Believe. Receive." Your daily quote is ready.',
    timestamp: "3 days ago",
    icon: NotificationIcons.wellness,
    isUnread: false,
    type: "wellness",
  },
  {
    id: "10",
    title: "Good News Wall Update",
    description: "Baby Aarav's IVF story has been added to the Good News Wall.",
    timestamp: "3 days ago",
    icon: NotificationIcons.news,
    isUnread: false,
    type: "news",
  },
  {
    id: "11",
    title: "Wellness Tip of the Day",
    description: '"Breathe. Believe. Receive." Your daily quote is ready.',
    timestamp: "3 days ago",
    icon: NotificationIcons.wellness,
    isUnread: false,
    type: "wellness",
  },
];

export const Default: Story = {
  args: {
    notifications: sampleNotifications,
    onMarkAllAsRead: () => console.log("Mark all as read clicked"),
    onNotificationClick: (notification) =>
      console.log("Notification clicked:", notification),
    isOpen: true,
  },
};

export const Empty: Story = {
  args: {
    notifications: [],
    onMarkAllAsRead: () => console.log("Mark all as read clicked"),
    onNotificationClick: (notification) =>
      console.log("Notification clicked:", notification),
    isOpen: true,
  },
};

export const WithoutMarkAllRead: Story = {
  args: {
    notifications: sampleNotifications,
    onNotificationClick: (notification) =>
      console.log("Notification clicked:", notification),
    isOpen: true,
  },
};

export const OnlyUnread: Story = {
  args: {
    notifications: sampleNotifications.filter((n) => n.isUnread),
    onMarkAllAsRead: () => console.log("Mark all as read clicked"),
    onNotificationClick: (notification) =>
      console.log("Notification clicked:", notification),
    isOpen: true,
  },
};
