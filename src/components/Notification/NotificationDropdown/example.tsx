import React, { useState } from "react";
import NotificationDropdown, {
  NotificationIcons,
} from "./NotificationDropdown";
import type { NotificationItem } from "./NotificationDropdown";

// Example usage of the NotificationDropdown component
const NotificationExample: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState<NotificationItem[]>([
    {
      id: "1",
      title: "Appointment Confirmed",
      description:
        "Your appointment with Dr. <PERSON><PERSON><PERSON> is scheduled for 27 June at 10:00 AM.",
      timestamp: "2 hours ago",
      icon: NotificationIcons.appointment,
      isUnread: true,
      type: "appointment",
    },
    {
      id: "2",
      title: "Stage Unlocked",
      description:
        "You've unlocked Stage 4 — Embryo Growth in your IVF journey.",
      timestamp: "Yesterday",
      icon: NotificationIcons.stage,
      isUnread: true,
      type: "stage",
    },
    {
      id: "3",
      title: "New Diet Plan Available",
      description:
        "Your personalized 7-day diet plan is now ready to download.",
      timestamp: "2 days ago",
      icon: NotificationIcons.diet,
      isUnread: false,
      type: "diet",
    },
  ]);

  const handleMarkAllAsRead = () => {
    setNotifications((prev) =>
      prev.map((notification) => ({ ...notification, isUnread: false }))
    );
  };

  const handleNotificationClick = (notification: NotificationItem) => {
    // Mark individual notification as read
    setNotifications((prev) =>
      prev.map((n) =>
        n.id === notification.id ? { ...n, isUnread: false } : n
      )
    );

    // Handle navigation or other actions based on notification type
    switch (notification.type) {
      case "appointment":
        console.log("Navigate to appointments");
        break;
      case "stage":
        console.log("Navigate to IVF journey");
        break;
      case "diet":
        console.log("Navigate to diet plan");
        break;
      default:
        console.log("Default notification action");
    }
  };

  const unreadCount = notifications.filter((n) => n.isUnread).length;

  return (
    <div className="relative">
      {/* Notification Bell Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full"
      >
        <svg
          className="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 17h5l-5 5-5-5h5V3h0zm-4-2V3a4 4 0 118 0v12a3 3 0 01-3 3H6a3 3 0 01-3-3V3a4 4 0 018 0z"
          />
        </svg>

        {/* Unread Count Badge */}
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {unreadCount > 9 ? "9+" : unreadCount}
          </span>
        )}
      </button>

      {/* Dropdown */}
      <div className="absolute right-0 top-full mt-2 z-50">
        <NotificationDropdown
          notifications={notifications}
          isOpen={isOpen}
          onMarkAllAsRead={handleMarkAllAsRead}
          onNotificationClick={handleNotificationClick}
          onClose={() => setIsOpen(false)}
        />
      </div>

      {/* Backdrop to close dropdown */}
      {isOpen && (
        <div className="fixed inset-0 z-40" onClick={() => setIsOpen(false)} />
      )}
    </div>
  );
};

export default NotificationExample;
