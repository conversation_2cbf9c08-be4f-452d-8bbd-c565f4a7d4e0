import React from "react";
import NotificationTray from "../NotificationTray/NotificationTray";
import { ChecksIcon } from "@phosphor-icons/react";

export interface NotificationItem {
  id: string;
  title: string;
  description: string;
  timestamp: string;
  icon: React.ReactNode;
  isUnread: boolean;
  type: "appointment" | "stage" | "diet" | "news" | "wellness" | "other";
}

export interface NotificationDropdownProps {
  notifications: NotificationItem[];
  onMarkAllAsRead?: () => void;
  onNotificationClick?: (notification: NotificationItem) => void;
  isOpen?: boolean;
  onClose: () => void;
  className?: string;
}

const NotificationDropdown: React.FC<NotificationDropdownProps> = ({
  notifications,
  onClose,
  onMarkAllAsRead,
  onNotificationClick,
  isOpen = true,
  className = "",
}) => {
  if (!isOpen) return null;

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 z-40" onClick={onClose} />
      <div
        className={`absolute right-0 top-full bg-white border border-gray-200 rounded-lg shadow-lg w-127.75 overflow-hidden flex flex-col gap-6 px-10 pt-7.5 pb-5 ${className} z-50`}
      >
        {/* Header */}
        <div className="w-full flex items-center justify-between">
          <h3 className="text-2xl font-bold text-[var(--grey-7)]">
            Notifications
          </h3>
          {!onMarkAllAsRead && (
            <button
              onClick={onMarkAllAsRead}
              className="text-sm text- hover:text-blue-700 font-medium flex gap-2 items-center"
            >
              <ChecksIcon size={16} />
              <span className="text-sm font-medium text-[var(--violet-11)]">
                Mark all as read
              </span>
            </button>
          )}
        </div>

        {/* Notifications List */}
        <div className="overflow-y-auto max-h-138">
          <NotificationTray
            notifications={notifications}
            onNotificationClick={onNotificationClick}
            emptyStateText="No notifications"
            showEmptyState={true}
          />
        </div>
      </div>
    </>
  );
};

// Default notification icons
export const NotificationIcons = {
  appointment: (
    <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
      <svg
        className="w-5 h-5 text-purple-600"
        viewBox="0 0 24 24"
        fill="currentColor"
      >
        <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 3v2h-4V6h4zm-4 4h4v2h-4v-2zm4 4h-4v2h4v-2z" />
      </svg>
    </div>
  ),
  stage: (
    <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
      <svg
        className="w-5 h-5 text-yellow-600"
        viewBox="0 0 24 24"
        fill="currentColor"
      >
        <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z" />
      </svg>
    </div>
  ),
  diet: (
    <div className="w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center">
      <svg
        className="w-5 h-5 text-pink-600"
        viewBox="0 0 24 24"
        fill="currentColor"
      >
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
      </svg>
    </div>
  ),
  news: (
    <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
      <svg
        className="w-5 h-5 text-green-600"
        viewBox="0 0 24 24"
        fill="currentColor"
      >
        <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z" />
      </svg>
    </div>
  ),
  wellness: (
    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
      <svg
        className="w-5 h-5 text-blue-600"
        viewBox="0 0 24 24"
        fill="currentColor"
      >
        <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" />
      </svg>
    </div>
  ),
};

export default NotificationDropdown;
