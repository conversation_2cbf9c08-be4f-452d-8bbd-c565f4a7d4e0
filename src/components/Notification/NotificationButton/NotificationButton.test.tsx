import React from "react";
import { render, screen } from "@testing-library/react";
import NotificationButton from "./NotificationButton";

describe("NotificationButton", () => {
  it("renders the bell icon", () => {
    render(<NotificationButton />);
    expect(screen.getByLabelText(/notifications/i)).toBeInTheDocument();
  });

  it("does not render the badge when count is 0", () => {
    render(<NotificationButton count={0} />);
    expect(screen.queryByText("0")).not.toBeInTheDocument();
  });

  it("renders the badge with the correct count", () => {
    render(<NotificationButton count={7} />);
    expect(screen.getByText("7")).toBeInTheDocument();
  });

  it("renders the badge with a large count", () => {
    render(<NotificationButton count={99} />);
    expect(screen.getByText("99")).toBeInTheDocument();
  });
});
