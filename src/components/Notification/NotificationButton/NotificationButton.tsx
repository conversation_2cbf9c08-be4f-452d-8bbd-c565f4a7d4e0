import { BellIcon } from "@phosphor-icons/react";
import React from "react";

interface NotificationButtonProps {
  count?: number;
  onClick?: () => void;
  className?: string;
}

// Add shake animation via <PERSON><PERSON><PERSON> (custom class)
const shakeAnimation = `animate-[shake_0.5s_ease-in-out_0s_2]`;

const NotificationButton: React.FC<NotificationButtonProps> = ({
  count = 0,
  onClick,
  className = "",
}) => (
  <button
    className={`w-[2.25rem] h-[2.5rem] relative p-0.5 bg-transparent border-none outline-none ${className}`}
    onClick={onClick}
    aria-label="Notifications"
  >
    {/* Bell Icon */}
    <BellIcon size={24} className={count > 0 ? shakeAnimation : undefined} />
    {/* Notification Badge */}
    {count > 0 && (
      <span className="h-4 absolute top-[2px] left-[13px] bg-[var(--primary-pink)] text-white text-xs font-bold rounded-full p-1.25 flex items-center justify-center">
        {count}
      </span>
    )}
  </button>
);

export default NotificationButton;
