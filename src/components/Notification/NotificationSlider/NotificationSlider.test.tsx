/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import NotificationSlider from "./NotificationSlider";
import { NotificationTrayIcons } from "../NotificationTray/NotificationTray";
import type { NotificationItemType } from "../NotificationTray/NotificationTray";

const sampleNotifications: NotificationItemType[] = [
  {
    id: "1",
    title: "Appointment Confirmed",
    description:
      "Your appointment with Dr. <PERSON><PERSON><PERSON> is scheduled for 27 June at 10:00 AM.",
    timestamp: "2 hours ago",
    icon: NotificationTrayIcons.appointment,
    isUnread: true,
    type: "appointment",
  },
  {
    id: "2",
    title: "Stage Unlocked",
    description: "You've unlocked Stage 4 — Embryo Growth in your IVF journey.",
    timestamp: "Yesterday",
    icon: NotificationTrayIcons.stage,
    isUnread: true,
    type: "stage",
  },
  {
    id: "3",
    title: "New Diet Plan Available",
    description: "Your personalized 7-day diet plan is now ready to download.",
    timestamp: "2 days ago",
    icon: NotificationTrayIcons.diet,
    isUnread: false,
    type: "diet",
  },
];

describe("NotificationSlider", () => {
  const mockOnClose = jest.fn();
  const mockOnMarkAllAsRead = jest.fn();
  const mockOnNotificationClick = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("does not render when open is false", () => {
    render(
      <NotificationSlider
        isOpen={false}
        notifications={sampleNotifications}
        onClose={mockOnClose}
      />
    );
  });

  it("renders when open is true", () => {
    render(
      <NotificationSlider
        isOpen={true}
        notifications={sampleNotifications}
        onClose={mockOnClose}
      />
    );
    expect(screen.getByText("Notifications")).toBeInTheDocument();
  });

  it("renders all notifications", () => {
    render(
      <NotificationSlider
        isOpen={true}
        notifications={sampleNotifications}
        onClose={mockOnClose}
      />
    );
    expect(screen.getByText("Appointment Confirmed")).toBeInTheDocument();
    expect(screen.getByText("Stage Unlocked")).toBeInTheDocument();
    expect(screen.getByText("New Diet Plan Available")).toBeInTheDocument();
  });

  it("calls onClose when close button is clicked", () => {
    render(
      <NotificationSlider
        isOpen={true}
        notifications={sampleNotifications}
        onClose={mockOnClose}
      />
    );
    const closeBtn = screen.getByRole("button", { name: /close/i });
    fireEvent.click(closeBtn);
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it("calls onClose when backdrop is clicked", () => {
    render(
      <NotificationSlider
        isOpen={true}
        notifications={sampleNotifications}
        onClose={mockOnClose}
      />
    );
    // The backdrop is the first div with role presentation
    const backdrop = screen.getByLabelText("Close notifications");
    fireEvent.click(backdrop);
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it("renders mark all as read button when there are unread notifications and calls handler", () => {
    render(
      <NotificationSlider
        isOpen={true}
        notifications={sampleNotifications}
        onClose={mockOnClose}
        onMarkAllAsRead={mockOnMarkAllAsRead}
      />
    );
    const markAllBtn = screen.getByText(/mark all as read/i);
    expect(markAllBtn).toBeInTheDocument();
    fireEvent.click(markAllBtn);
    expect(mockOnMarkAllAsRead).toHaveBeenCalledTimes(1);
  });

  it("does not render mark all as read button if no unread notifications", () => {
    const allRead = sampleNotifications.map((n) => ({ ...n, isUnread: false }));
    render(
      <NotificationSlider
        isOpen={true}
        notifications={allRead}
        onClose={mockOnClose}
        onMarkAllAsRead={mockOnMarkAllAsRead}
      />
    );
    expect(screen.queryByText(/mark all as read/i)).not.toBeInTheDocument();
  });

  it("calls onNotificationClick when a notification is clicked", () => {
    render(
      <NotificationSlider
        isOpen={true}
        notifications={sampleNotifications}
        onClose={mockOnClose}
        onNotificationClick={mockOnNotificationClick}
      />
    );
    fireEvent.click(screen.getByText("Appointment Confirmed"));
    expect(mockOnNotificationClick).toHaveBeenCalledTimes(1);
    expect(mockOnNotificationClick).toHaveBeenCalledWith(
      sampleNotifications[0]
    );
  });

  it("renders empty state when no notifications", () => {
    render(
      <NotificationSlider
        isOpen={true}
        notifications={[]}
        onClose={mockOnClose}
      />
    );
    expect(screen.getByText(/no notifications/i)).toBeInTheDocument();
  });

  it("has proper accessibility roles and labels", () => {
    render(
      <NotificationSlider
        isOpen={true}
        notifications={sampleNotifications}
        onClose={mockOnClose}
      />
    );
    const closeEls = screen.getAllByLabelText(/close/i);
    expect(closeEls.length).toBeGreaterThanOrEqual(2); // backdrop and button
    expect(screen.getByText("Notifications")).toHaveClass("text-2xl");
  });
});
