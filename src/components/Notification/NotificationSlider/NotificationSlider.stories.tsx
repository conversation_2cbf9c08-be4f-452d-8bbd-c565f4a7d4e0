import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import React, { useState } from "react";
import NotificationSlider from "./NotificationSlider";
import { NotificationTrayIcons } from "../NotificationTray/NotificationTray";
import type { NotificationItemType } from "../NotificationTray/NotificationTray";

const meta: Meta<typeof NotificationSlider> = {
  title: "Components/Notification/NotificationSlider",
  component: NotificationSlider,
  parameters: {
    layout: "fullscreen",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

const sampleNotifications: NotificationItemType[] = [
  {
    id: "1",
    title: "Appointment Confirmed",
    description:
      "Your appointment with <PERSON><PERSON> <PERSON><PERSON><PERSON> is scheduled for 27 June at 10:00 AM.",
    timestamp: "2 hours ago",
    icon: NotificationTrayIcons.appointment,
    isUnread: true,
    type: "appointment",
  },
  {
    id: "2",
    title: "Stage Unlocked",
    description: "You've unlocked Stage 4 — Embryo Growth in your IVF journey.",
    timestamp: "Yesterday",
    icon: NotificationTrayIcons.stage,
    isUnread: true,
    type: "stage",
  },
  {
    id: "3",
    title: "New Diet Plan Available",
    description: "Your personalized 7-day diet plan is now ready to download.",
    timestamp: "2 days ago",
    icon: NotificationTrayIcons.diet,
    isUnread: false,
    type: "diet",
  },
  {
    id: "4",
    title: "Good News Wall Update",
    description: "Baby Aarav's IVF story has been added to the Good News Wall.",
    timestamp: "3 days ago",
    icon: NotificationTrayIcons.news,
    isUnread: false,
    type: "news",
  },
  {
    id: "5",
    title: "Wellness Tip of the Day",
    description: '"Breathe. Believe. Receive." Your daily quote is ready.',
    timestamp: "3 days ago",
    icon: NotificationTrayIcons.wellness,
    isUnread: false,
    type: "wellness",
  },
];

export const Default: Story = {
  render: (args) => {
    const [open, setOpen] = useState(true);
    const [notifications, setNotifications] = useState(sampleNotifications);
    const handleMarkAllAsRead = () =>
      setNotifications((prev) => prev.map((n) => ({ ...n, isUnread: false })));
    const handleNotificationClick = (notification: NotificationItemType) => {
      setNotifications((prev) =>
        prev.map((n) =>
          n.id === notification.id ? { ...n, isUnread: false } : n
        )
      );
    };
    return (
      <>
        <button
          onClick={() => setOpen(true)}
          style={{ position: "fixed", top: 20, right: 20, zIndex: 100 }}
        >
          Open Notifications
        </button>
        <NotificationSlider
          {...args}
          isOpen={open}
          notifications={notifications}
          onClose={() => setOpen(false)}
          onMarkAllAsRead={handleMarkAllAsRead}
          onNotificationClick={handleNotificationClick}
        />
      </>
    );
  },
};

export const Empty: Story = {
  args: {
    isOpen: true,
    notifications: [],
    onClose: () => {},
  },
};

export const OnlyUnread: Story = {
  args: {
    isOpen: true,
    notifications: sampleNotifications.filter((n) => n.isUnread),
    onClose: () => {},
    onMarkAllAsRead: () => {},
  },
};

export const WithoutMarkAllRead: Story = {
  args: {
    isOpen: true,
    notifications: sampleNotifications,
    onClose: () => {},
    onNotificationClick: (notification) =>
      console.log("Notification clicked:", notification),
  },
};
