import React from "react";
import NotificationTray from "../NotificationTray/NotificationTray";
import type { NotificationItemType } from "../NotificationTray/NotificationTray";

export interface NotificationSliderProps {
  isOpen: boolean;
  notifications: NotificationItemType[];
  onClose: () => void;
  onMarkAllAsRead?: () => void;
  onNotificationClick?: (notification: NotificationItemType) => void;
  className?: string;
}

const NotificationSlider: React.FC<NotificationSliderProps> = ({
  isOpen,
  notifications,
  onClose,
  onMarkAllAsRead,
  onNotificationClick,
  className = "",
}) => {
  const unreadCount = notifications.filter((n) => n.isUnread).length;

  return (
    <div className={`fixed inset-0 z-40 pointer-events-none`}>
      {/* Backdrop */}
      <div
        className={`fixed inset-0 bg-black transition-opacity duration-1000 ${isOpen ? "opacity-50 pointer-events-auto" : "opacity-0 pointer-events-none"}`}
        onClick={onClose}
        aria-label="Close notifications"
      />
      <div className="fixed inset-0 z-50 flex justify-end">
        {/* Slider Panel */}
        <div
          className={`bg-white w-100.5 h-full flex flex-col gap-3 py-2 px-5 transform transition-transform duration-1000 ease-in-out ${
            isOpen
              ? "translate-x-0 pointer-events-auto"
              : "translate-x-full pointer-events-none"
          } ${className}`}
        >
          {/* Header */}
          <div className="flex items-center justify-between px-6 py-6 border-b border-gray-100">
            <h2 className="text-2xl font-bold text-gray-900">Notifications</h2>
            <div className="flex items-center gap-4">
              {onMarkAllAsRead && unreadCount > 0 && (
                <button
                  onClick={onMarkAllAsRead}
                  className="text-primary-600 hover:text-primary-700 font-medium text-base flex items-center gap-1"
                >
                  <svg
                    className="w-5 h-5"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                  >
                    <polyline points="20,6 9,17 4,12" />
                  </svg>
                  Mark all as read
                </button>
              )}
              <button
                onClick={onClose}
                className="w-10 h-10 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
                aria-label="Close"
              >
                <svg
                  className="w-6 h-6 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                >
                  <line x1="18" y1="6" x2="6" y2="18" />
                  <line x1="6" y1="6" x2="18" y2="18" />
                </svg>
              </button>
            </div>
          </div>
          {/* Notification List */}
          <div className="flex-1 overflow-y-auto px-2 pb-4">
            <NotificationTray
              notifications={notifications}
              onNotificationClick={onNotificationClick}
              className="bg-white"
              showEmptyState={true}
              emptyStateText="No notifications"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationSlider;
