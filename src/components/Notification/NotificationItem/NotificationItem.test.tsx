/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import NotificationItem from "./NotificationItem";
import type { NotificationItemData } from "./NotificationItem";

const mockIcon = (
  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
    <svg
      className="w-6 h-6 text-purple-600"
      viewBox="0 0 24 24"
      fill="currentColor"
    >
      <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 3v2h-4V6h4zm-4 4h4v2h-4v-2zm4 4h-4v2h4v-2z" />
    </svg>
  </div>
);

const mockUnreadNotification: NotificationItemData = {
  id: "1",
  title: "Appointment Confirmed",
  description:
    "Your appointment with Dr<PERSON> <PERSON><PERSON><PERSON> is scheduled for 27 June at 10:00 AM.",
  timestamp: "2 hours ago",
  icon: mockIcon,
  isUnread: true,
  type: "appointment",
};

const mockReadNotification: NotificationItemData = {
  id: "2",
  title: "Stage Unlocked",
  description: "You've unlocked Stage 4 — Embryo Growth in your IVF journey.",
  timestamp: "Yesterday",
  icon: mockIcon,
  isUnread: false,
  type: "stage",
};

describe("NotificationItem", () => {
  const mockOnNotificationClick = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Component Rendering", () => {
    it("renders notification title and description", () => {
      render(
        <NotificationItem
          notification={mockUnreadNotification}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      expect(screen.getByText("Appointment Confirmed")).toBeInTheDocument();
      expect(
        screen.getByText(/Your appointment with Dr. Neha Sharma/)
      ).toBeInTheDocument();
    });

    it("renders notification timestamp", () => {
      render(
        <NotificationItem
          notification={mockUnreadNotification}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      expect(screen.getByText("2 hours ago")).toBeInTheDocument();
    });

    it("renders notification icon", () => {
      const { container } = render(
        <NotificationItem
          notification={mockUnreadNotification}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      // Check for the SVG icon
      const svgElement = container.querySelector("svg");
      expect(svgElement).toBeInTheDocument();
    });

    it("applies custom className when provided", () => {
      const { container } = render(
        <NotificationItem
          notification={mockUnreadNotification}
          className="custom-class"
          onNotificationClick={mockOnNotificationClick}
        />
      );

      expect(container.firstChild).toHaveClass("custom-class");
    });
  });

  describe("Unread State", () => {
    it("shows unread indicator for unread notifications", () => {
      const { container } = render(
        <NotificationItem
          notification={mockUnreadNotification}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      const indicator = container.querySelector(
        "[class*='bg-blue-600 rounded-full']"
      );
      expect(indicator).toBeInTheDocument();
    });

    it("does not show unread indicator for read notifications", () => {
      const { container } = render(
        <NotificationItem
          notification={mockReadNotification}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      const indicator = Array.from(container.querySelectorAll("div")).find(
        (div) => div.className.includes("w-2.5 h-2.5 bg-blue-600 rounded-full")
      );
      expect(indicator).toBeUndefined();
    });

    it("applies unread background styling to unread notifications", () => {
      const { container } = render(
        <NotificationItem
          notification={mockUnreadNotification}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      expect(container.firstChild).toHaveClass("bg-blue-50/30");
    });

    it("applies read background styling to read notifications", () => {
      const { container } = render(
        <NotificationItem
          notification={mockReadNotification}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      expect(container.firstChild).toHaveClass("bg-white");
    });
  });

  describe("Border Display", () => {
    it("shows border when showBorder is true", () => {
      const { container } = render(
        <NotificationItem
          notification={mockUnreadNotification}
          showBorder={true}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      expect(container.firstChild).toHaveClass("border-b");
    });

    it("does not show border when showBorder is false", () => {
      const { container } = render(
        <NotificationItem
          notification={mockUnreadNotification}
          showBorder={false}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      expect(container.firstChild).not.toHaveClass("border-b");
    });

    it("does not show border by default", () => {
      const { container } = render(
        <NotificationItem
          notification={mockUnreadNotification}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      expect(container.firstChild).not.toHaveClass("border-b");
    });
  });

  describe("Click Interactions", () => {
    it("calls onNotificationClick when notification is clicked", () => {
      render(
        <NotificationItem
          notification={mockUnreadNotification}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      fireEvent.click(screen.getByText("Appointment Confirmed"));
      expect(mockOnNotificationClick).toHaveBeenCalledTimes(1);
      expect(mockOnNotificationClick).toHaveBeenCalledWith(
        mockUnreadNotification
      );
    });

    it("does not throw error when onNotificationClick is not provided", () => {
      render(<NotificationItem notification={mockUnreadNotification} />);

      expect(() => {
        fireEvent.click(screen.getByText("Appointment Confirmed"));
      }).not.toThrow();
    });

    it("adds hover and cursor styling when onNotificationClick is provided", () => {
      const { container } = render(
        <NotificationItem
          notification={mockUnreadNotification}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      expect(container.firstChild).toHaveClass(
        "hover:bg-gray-50",
        "cursor-pointer"
      );
    });

    it("does not add hover and cursor styling when onNotificationClick is not provided", () => {
      const { container } = render(
        <NotificationItem notification={mockUnreadNotification} />
      );

      expect(container.firstChild).not.toHaveClass(
        "hover:bg-gray-50",
        "cursor-pointer"
      );
    });
  });

  describe("Content Display", () => {
    it("displays title with proper styling", () => {
      render(
        <NotificationItem
          notification={mockUnreadNotification}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      const title = screen.getByText("Appointment Confirmed");
      expect(title).toHaveClass("text-base", "font-semibold", "text-gray-900");
    });

    it("displays description with proper styling", () => {
      render(
        <NotificationItem
          notification={mockUnreadNotification}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      const description = screen.getByText(
        /Your appointment with Dr. Neha Sharma/
      );
      expect(description).toHaveClass("text-sm", "text-gray-600");
    });

    it("displays timestamp with proper styling", () => {
      render(
        <NotificationItem
          notification={mockUnreadNotification}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      const timestamp = screen.getByText("2 hours ago");
      expect(timestamp).toHaveClass("text-sm", "text-gray-500");
    });

    it("handles long text content properly", () => {
      const longTextNotification: NotificationItemData = {
        ...mockUnreadNotification,
        description:
          "This is a very long description that should wrap properly and not overflow the container. It contains multiple sentences and should be displayed correctly within the notification item layout.",
      };

      render(
        <NotificationItem
          notification={longTextNotification}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      expect(
        screen.getByText(/This is a very long description/)
      ).toBeInTheDocument();
    });
  });

  describe("Layout and Structure", () => {
    it("has proper layout classes", () => {
      const { container } = render(
        <NotificationItem
          notification={mockUnreadNotification}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      expect(container.firstChild).toHaveClass(
        "flex",
        "items-start",
        "gap-4",
        "py-5",
        "px-0"
      );
    });

    it("has proper transition classes", () => {
      const { container } = render(
        <NotificationItem
          notification={mockUnreadNotification}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      expect(container.firstChild).toHaveClass(
        "transition-colors",
        "duration-200"
      );
    });
  });

  describe("Accessibility", () => {
    it("maintains proper heading structure", () => {
      render(
        <NotificationItem
          notification={mockUnreadNotification}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      expect(
        screen.getByRole("heading", { level: 3, name: "Appointment Confirmed" })
      ).toBeInTheDocument();
    });

    it("has proper text content structure", () => {
      render(
        <NotificationItem
          notification={mockUnreadNotification}
          onNotificationClick={mockOnNotificationClick}
        />
      );

      const title = screen.getByText("Appointment Confirmed");
      const description = screen.getByText(
        /Your appointment with Dr. Neha Sharma/
      );
      const timestamp = screen.getByText("2 hours ago");

      expect(title.tagName).toBe("H3");
      expect(description.tagName).toBe("P");
      expect(timestamp.tagName).toBe("SPAN");
    });
  });
});
