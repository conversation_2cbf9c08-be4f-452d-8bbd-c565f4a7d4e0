import React from "react";

export interface NotificationItemData {
  id: string;
  title: string;
  description: string;
  timestamp: string;
  icon: React.ReactNode;
  isUnread: boolean;
  type: "appointment" | "stage" | "diet" | "news" | "wellness" | "other";
}

export interface NotificationItemProps {
  notification: NotificationItemData;
  onNotificationClick?: (notification: NotificationItemData) => void;
  showBorder?: boolean;
  className?: string;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  onNotificationClick,
  showBorder = false,
  className = "",
}) => {
  return (
    <div
      onClick={() => onNotificationClick?.(notification)}
      className={`
        flex items-start gap-4 py-5 px-0
        ${showBorder ? "border-b border-gray-100" : ""}
        ${onNotificationClick ? "hover:bg-gray-50 cursor-pointer" : ""}
        ${notification.isUnread ? "bg-blue-50/30" : "bg-white"}
        transition-colors duration-200
        ${className}
      `}
    >
      {/* Icon */}
      <div className="flex-shrink-0 mt-1">{notification.icon}</div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <h3 className="text-base font-semibold text-gray-900 mb-1 leading-tight">
          {notification.title}
        </h3>
        <p className="text-sm text-gray-600 leading-relaxed">
          {notification.description}
        </p>
      </div>

      {/* Timestamp and Unread Indicator */}
      <div className="flex-shrink-0 flex flex-col items-end gap-2 ml-4">
        {notification.isUnread && (
          <div className="w-2.5 h-2.5 bg-blue-600 rounded-full"></div>
        )}
        <span className="text-sm text-gray-500 whitespace-nowrap">
          {notification.timestamp}
        </span>
      </div>
    </div>
  );
};

export default NotificationItem;
