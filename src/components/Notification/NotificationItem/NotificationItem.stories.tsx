import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import NotificationItem from "./NotificationItem";
import type { NotificationItemData } from "./NotificationItem";

const meta: Meta<typeof NotificationItem> = {
  title: "Components/Notification/NotificationItem",
  component: NotificationItem,
  parameters: {
    layout: "padded",
  },
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Sample notification icons
const appointmentIcon = (
  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
    <svg
      className="w-6 h-6 text-purple-600"
      viewBox="0 0 24 24"
      fill="currentColor"
    >
      <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 3v2h-4V6h4zm-4 4h4v2h-4v-2zm4 4h-4v2h4v-2z" />
    </svg>
  </div>
);

const stageIcon = (
  <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
    <svg
      className="w-6 h-6 text-yellow-600"
      viewBox="0 0 24 24"
      fill="currentColor"
    >
      <path d="M9 16.2L4.8 12l-1.4 1.4L9 19 21 7l-1.4-1.4L9 16.2z" />
    </svg>
  </div>
);

const dietIcon = (
  <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
    <svg
      className="w-6 h-6 text-red-500"
      viewBox="0 0 24 24"
      fill="currentColor"
    >
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" />
    </svg>
  </div>
);

// Sample notifications
const unreadNotification: NotificationItemData = {
  id: "1",
  title: "Appointment Confirmed",
  description:
    "Your appointment with Dr. Neha Sharma is scheduled for 27 June at 10:00 AM.",
  timestamp: "2 hours ago",
  icon: appointmentIcon,
  isUnread: true,
  type: "appointment",
};

const readNotification: NotificationItemData = {
  id: "2",
  title: "Stage Unlocked",
  description: "You've unlocked Stage 4 — Embryo Growth in your IVF journey.",
  timestamp: "Yesterday",
  icon: stageIcon,
  isUnread: false,
  type: "stage",
};

const longTextNotification: NotificationItemData = {
  id: "3",
  title: "New Diet Plan Available",
  description:
    "Your personalized 7-day diet plan is now ready to download. This plan has been customized based on your current health metrics, dietary preferences, and nutritional requirements. It includes detailed meal plans, shopping lists, and preparation instructions.",
  timestamp: "2 days ago",
  icon: dietIcon,
  isUnread: true,
  type: "diet",
};

export const Unread: Story = {
  args: {
    notification: unreadNotification,
    onNotificationClick: (notification) =>
      console.log("Notification clicked:", notification),
  },
};

export const Read: Story = {
  args: {
    notification: readNotification,
    onNotificationClick: (notification) =>
      console.log("Notification clicked:", notification),
  },
};

export const WithBorder: Story = {
  args: {
    notification: unreadNotification,
    showBorder: true,
    onNotificationClick: (notification) =>
      console.log("Notification clicked:", notification),
  },
};

export const WithoutClickHandler: Story = {
  args: {
    notification: readNotification,
    showBorder: true,
  },
};

export const LongText: Story = {
  args: {
    notification: longTextNotification,
    showBorder: true,
    onNotificationClick: (notification) =>
      console.log("Notification clicked:", notification),
  },
};

export const CustomClassName: Story = {
  args: {
    notification: unreadNotification,
    className: "border border-blue-200 rounded-lg",
    onNotificationClick: (notification) =>
      console.log("Notification clicked:", notification),
  },
};

export const MultipleItems: Story = {
  render: () => (
    <div className="space-y-0 border border-gray-200 rounded-lg overflow-hidden">
      <NotificationItem
        notification={unreadNotification}
        showBorder={true}
        onNotificationClick={(notification) =>
          console.log("Notification clicked:", notification)
        }
      />
      <NotificationItem
        notification={readNotification}
        showBorder={true}
        onNotificationClick={(notification) =>
          console.log("Notification clicked:", notification)
        }
      />
      <NotificationItem
        notification={longTextNotification}
        showBorder={false}
        onNotificationClick={(notification) =>
          console.log("Notification clicked:", notification)
        }
      />
    </div>
  ),
};
