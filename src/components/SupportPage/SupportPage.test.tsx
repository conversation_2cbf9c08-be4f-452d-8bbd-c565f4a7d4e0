import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import SupportPage from "./SupportPage";

// Mock Next.js router
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  }),
}));

// Before your test, override the mock to simulate logged out
jest.mock("@/contexts/AuthContext", () => ({
  useAuth: () => ({ user: null, loading: false }), // <-- user: null means logged out
  AuthProvider: ({ children }: { children: React.ReactNode }) => (
    <>{children}</>
  ),
}));

process.env.NEXT_PUBLIC_SUPABASE_URL = "http://localhost:54321";
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = "test_anon_key";

// Mock window.open for testing
const mockWindowOpen = jest.fn();
Object.defineProperty(window, "open", {
  value: mockWindowOpen,
  writable: true,
});

// Mock console.log for testing
const mockConsoleLog = jest.spyOn(console, "log").mockImplementation(() => {});

describe("SupportPage", () => {
  beforeEach(() => {
    mockWindowOpen.mockClear();
    mockConsoleLog.mockClear();
  });

  afterAll(() => {
    mockConsoleLog.mockRestore();
  });

  describe("Rendering", () => {
    it("renders the main heading", () => {
      render(<SupportPage />);
      expect(screen.getByText("Help & Support")).toBeInTheDocument();
    });

    it("renders the subtitle", () => {
      render(<SupportPage />);
      expect(
        screen.getByText(
          "We're here to help you with any questions or concerns about your fertility journey"
        )
      ).toBeInTheDocument();
    });

    it("renders all three contact option cards", () => {
      render(<SupportPage />);
      expect(screen.getByText("Call Us")).toBeInTheDocument();
      expect(screen.getByText("Email Support")).toBeInTheDocument();
      expect(screen.getByText("Live Chat")).toBeInTheDocument();
    });

    it("renders the FAQ section heading", () => {
      render(<SupportPage />);
      expect(
        screen.getByText("Frequently Asked Questions")
      ).toBeInTheDocument();
    });

    it("renders all FAQ questions", () => {
      render(<SupportPage />);
      expect(
        screen.getByText("How accurate is the IVF fertility assessment?")
      ).toBeInTheDocument();
      expect(
        screen.getByText("How long does the assessment take to complete?")
      ).toBeInTheDocument();
      expect(
        screen.getByText("Is my personal information secure?")
      ).toBeInTheDocument();
      expect(
        screen.getByText("Can I retake the assessment?")
      ).toBeInTheDocument();
      expect(
        screen.getByText("What should I do if I get a low fertility score?")
      ).toBeInTheDocument();
      expect(
        screen.getByText("Do you provide treatment services?")
      ).toBeInTheDocument();
    });

    it("renders the consultation booking section", () => {
      render(<SupportPage />);
      expect(screen.getByText("Need Personalized Help?")).toBeInTheDocument();
      expect(screen.getByText("Book Free Consultation")).toBeInTheDocument();
    });
  });

  describe("Contact Options", () => {
    it("handles call support button click", () => {
      render(<SupportPage />);
      const callButton = screen.getByText("Call Now");

      fireEvent.click(callButton);

      expect(mockWindowOpen).toHaveBeenCalledWith(
        "tel:+91-9990044555",
        "_self"
      );
    });

    it("handles email support button click", () => {
      render(<SupportPage />);
      const emailButton = screen.getByText("Send Email");

      fireEvent.click(emailButton);

      expect(mockWindowOpen).toHaveBeenCalledWith(
        "mailto:<EMAIL>",
        "_self"
      );
    });

    it("live chat button is clickable", () => {
      render(<SupportPage />);
      const chatButton = screen.getByText("Start Chat");

      expect(chatButton).not.toBeDisabled();
      expect(() => fireEvent.click(chatButton)).not.toThrow();
    });
  });

  describe("FAQ Functionality", () => {
    it("expands FAQ when question is clicked", () => {
      render(<SupportPage />);
      const firstQuestion = screen.getByText(
        "How accurate is the IVF fertility assessment?"
      );

      fireEvent.click(firstQuestion);

      expect(
        screen.getByText(
          /Our fertility assessment uses evidence-based algorithms/
        )
      ).toBeInTheDocument();
    });

    it("collapses FAQ when the same question is clicked again", async () => {
      render(<SupportPage />);
      const firstQuestion = screen.getByText(
        "How accurate is the IVF fertility assessment?"
      );

      // Expand
      fireEvent.click(firstQuestion);
      expect(
        screen.getByText(
          /Our fertility assessment uses evidence-based algorithms/
        )
      ).toBeInTheDocument();

      // Collapse
      fireEvent.click(firstQuestion);
      await waitFor(() => {
        expect(
          screen.queryByText(
            /Our fertility assessment uses evidence-based algorithms/
          )
        ).not.toBeInTheDocument();
      });
    });

    it("shows only one FAQ answer at a time", () => {
      render(<SupportPage />);
      const firstQuestion = screen.getByText(
        "How accurate is the IVF fertility assessment?"
      );
      const secondQuestion = screen.getByText(
        "How long does the assessment take to complete?"
      );

      // Expand first FAQ
      fireEvent.click(firstQuestion);
      expect(
        screen.getByText(
          /Our fertility assessment uses evidence-based algorithms/
        )
      ).toBeInTheDocument();

      // Expand second FAQ
      fireEvent.click(secondQuestion);
      expect(
        screen.getByText(
          /The complete assessment typically takes 10-15 minutes/
        )
      ).toBeInTheDocument();

      // First FAQ should be collapsed
      expect(
        screen.queryByText(
          /Our fertility assessment uses evidence-based algorithms/
        )
      ).not.toBeInTheDocument();
    });

    it("displays plus icon when FAQ is collapsed", () => {
      render(<SupportPage />);
      const faqButtons = screen.getAllByRole("button");
      const faqButton = faqButtons.find((button) =>
        button.textContent?.includes(
          "How accurate is the IVF fertility assessment?"
        )
      );

      expect(faqButton).toBeInTheDocument();
      // Plus icon should be present (we can test for SVG structure)
      const svgElement = faqButton?.querySelector("svg");
      expect(svgElement).toBeInTheDocument();
    });
  });

  describe("Action Buttons", () => {
    it("consultation booking button is clickable", () => {
      render(<SupportPage />);
      const bookingButton = screen.getByText("Book Free Consultation");

      expect(bookingButton).not.toBeDisabled();
      expect(() => fireEvent.click(bookingButton)).not.toThrow();
    });
  });

  describe("Header Integration", () => {
    it("renders header with account login option", () => {
      render(<SupportPage />);
      expect(screen.getByText("Have an account?")).toBeInTheDocument();
      expect(screen.getByText("Login")).toBeInTheDocument();
    });
  });

  describe("Responsive Design", () => {
    it("renders contact cards in a grid layout", () => {
      render(<SupportPage />);
      const contactSection = screen
        .getByText("Call Us")
        .closest("div")?.parentElement;
      expect(contactSection).toHaveClass("grid", "md:grid-cols-3");
    });

    it("renders FAQ section with proper spacing", () => {
      render(<SupportPage />);
      const faqSection = screen.getByText(
        "Frequently Asked Questions"
      ).nextElementSibling;
      expect(faqSection).toHaveClass("space-y-4");
    });
  });

  describe("Accessibility", () => {
    it("has proper heading hierarchy", () => {
      render(<SupportPage />);
      const h1 = screen.getByRole("heading", { level: 1 });
      const h2 = screen.getByRole("heading", { level: 2 });
      const h3Elements = screen.getAllByRole("heading", { level: 3 });

      expect(h1).toHaveTextContent("Help & Support");
      expect(h2).toHaveTextContent("Frequently Asked Questions");
      expect(h3Elements).toHaveLength(4); // 3 contact cards + 1 consultation section
    });

    it("has proper button roles for FAQ items", () => {
      render(<SupportPage />);
      const faqButtons = screen.getAllByRole("button");

      // Should have FAQ buttons plus contact buttons plus consultation button
      expect(faqButtons.length).toBeGreaterThan(6);
    });

    it("maintains keyboard navigation support", () => {
      render(<SupportPage />);
      const firstFAQButton = screen.getByText(
        "How accurate is the IVF fertility assessment?"
      );

      // FAQ button should be present and clickable
      expect(firstFAQButton).toBeInTheDocument();
    });
  });

  describe("Content Validation", () => {
    it("displays correct phone number", () => {
      render(<SupportPage />);
      // Check if phone number appears in NeedHelp component
      expect(screen.getByText(/9990044555/)).toBeInTheDocument();
    });

    it("contains all expected FAQ answers", () => {
      render(<SupportPage />);

      // Test just the first FAQ to ensure the mechanism works
      const firstQuestion = screen.getByText(
        "How accurate is the IVF fertility assessment?"
      );
      fireEvent.click(firstQuestion);

      // Check that the answer appears
      expect(
        screen.getByText(
          /Our fertility assessment uses evidence-based algorithms/
        )
      ).toBeInTheDocument();
    });
  });

  describe("Error Handling", () => {
    it("handles missing phone number gracefully", () => {
      render(<SupportPage />);

      // Component should render without crashing
      expect(screen.getByText("Help & Support")).toBeInTheDocument();
      expect(screen.getByText("Call Now")).toBeInTheDocument();
    });
  });
});
