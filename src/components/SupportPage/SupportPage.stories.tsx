import type { Meta, StoryObj } from "@storybook/nextjs";
import SupportPage from "./SupportPage";
import { AuthProvider } from "@/contexts/AuthContext";
import { ToastProvider } from "@/contexts/ToastContext";

const meta = {
  title: "Pages/SupportPage",
  component: SupportPage,
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component: `
# SupportPage Component

A comprehensive help and support page for the IVF assessment application.

## Features
- Multiple contact options (phone, email, live chat)
- Interactive FAQ section with expandable answers
- Professional medical website styling
- Call-to-action for booking consultations
- Responsive design for all devices

## Usage
This page serves as the main support hub for users who need help with:
- IVF fertility assessments
- Technical issues
- General questions about services
- Booking consultations
        `,
      },
    },
  },
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <ToastProvider duration={4000} maxToasts={5}>
        <AuthProvider>
          <Story />
        </AuthProvider>
      </ToastProvider>
    ),
  ],
} satisfies Meta<typeof SupportPage>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default support page with all features
 */
export const Default: Story = {};

/**
 * Support page demonstrating the complete user experience
 */
export const Interactive: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Interactive version showing all functionality including FAQ expansion and contact options.",
      },
    },
  },
};

/**
 * Support page showing the FAQ section in action
 */
export const FAQExpanded: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Shows how the FAQ section works with expandable questions and answers.",
      },
    },
  },
  play: async () => {
    // This would simulate FAQ expansion in a real Storybook environment
    console.log("FAQ interaction demo");
  },
};

/**
 * Support page highlighting contact options
 */
export const ContactOptions: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Focuses on the three main contact methods: phone, email, and live chat.",
      },
    },
  },
};

/**
 * Mobile view of the support page
 */
export const MobileView: Story = {
  parameters: {
    viewport: {
      defaultViewport: "mobile1",
    },
    docs: {
      description: {
        story:
          "Mobile-responsive version of the support page optimized for smaller screens.",
      },
    },
  },
};

/**
 * Support page with focus on accessibility
 */
export const AccessibilityFocus: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates accessibility features including keyboard navigation and screen reader support.",
      },
    },
  },
};

/**
 * Support page showing the consultation booking flow
 */
export const ConsultationBooking: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Highlights the consultation booking section and call-to-action area.",
      },
    },
  },
};

/**
 * Support page demonstrating the complete help journey
 */
export const HelpJourney: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Shows the complete user journey from landing on the support page to finding help.",
      },
    },
  },
};
