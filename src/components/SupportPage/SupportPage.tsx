"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import Header, { HeaderState, NavigationTarget } from "../shared/Header/Header";
import Footer from "../shared/Footer/Footer";
import PageHeader from "../shared/PageHeader/PageHeader";
import <PERSON><PERSON>, { ButtonType } from "../shared/Button/Button";
import NeedHelp from "../shared/NeedHelp/NeedHelp";

// Phone icon component
const PhoneIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
  >
    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z" />
  </svg>
);

// Email icon component
const EmailIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
  >
    <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" />
    <polyline points="22,6 12,13 2,6" />
  </svg>
);

// Chat icon component
const ChatIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
  >
    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
  </svg>
);

// Plus/Minus icon for FAQ
const PlusIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
  >
    <line x1="12" y1="5" x2="12" y2="19" />
    <line x1="5" y1="12" x2="19" y2="12" />
  </svg>
);

const MinusIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
  >
    <line x1="5" y1="12" x2="19" y2="12" />
  </svg>
);

interface FAQItem {
  id: string;
  question: string;
  answer: string;
}

const FAQ_DATA: FAQItem[] = [
  {
    id: "1",
    question: "How accurate is the IVF fertility assessment?",
    answer:
      "Our fertility assessment uses evidence-based algorithms and takes into account multiple factors including biological, lifestyle, and environmental factors. While it provides valuable insights, it should be used as a screening tool and not replace professional medical consultation.",
  },
  {
    id: "2",
    question: "How long does the assessment take to complete?",
    answer:
      "The complete assessment typically takes 10-15 minutes to complete. It consists of 5 steps covering different aspects of your health and lifestyle.",
  },
  {
    id: "3",
    question: "Is my personal information secure?",
    answer:
      "Yes, we take data privacy seriously. All your personal information is encrypted and stored securely. We comply with healthcare data protection standards and will only use your information for consultation purposes with your consent.",
  },
  {
    id: "4",
    question: "Can I retake the assessment?",
    answer:
      "Yes, you can retake the assessment at any time. We recommend retaking it after 3-6 months or after making significant lifestyle changes to track your progress.",
  },
  {
    id: "5",
    question: "What should I do if I get a low fertility score?",
    answer:
      "A low score doesn't mean you can't conceive. It indicates areas where improvements might help. We recommend booking a consultation with our fertility specialists who can provide personalized guidance and treatment options.",
  },
  {
    id: "6",
    question: "Do you provide treatment services?",
    answer:
      "Yes, Gunjan IVF World is a full-service fertility clinic offering comprehensive IVF treatments, consultations, and support services. You can book a consultation through our platform.",
  },
];

const SupportPage = () => {
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);
  const router = useRouter();

  const handleNavigate = (target: NavigationTarget) => {
    if (target === NavigationTarget.HELP) {
      // Already on help page, could navigate to additional resources
      console.log("Navigate to additional help resources");
    } else if (target === NavigationTarget.LOGIN) {
      router.push(`/login?redirect=${encodeURIComponent("/help-support")}`);
    }
  };

  const toggleFAQ = (id: string) => {
    setExpandedFAQ(expandedFAQ === id ? null : id);
  };

  const handleCallSupport = () => {
    window.open("tel:+91-9990044555", "_self");
  };

  const handleEmailSupport = () => {
    window.open("mailto:<EMAIL>", "_self");
  };

  const handleLiveChat = () => {
    console.log("Open live chat");
  };

  const handleBookConsultation = () => {
    // Navigate to IVF assessment to start the consultation process
    router.push("/ivf-assessment/user-type");
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header state={HeaderState.LOGIN} navigateTo={handleNavigate} />

      <main className="flex-1 py-6 px-4 md:px-[9.375rem]">
        <div className="max-w-4xl mx-auto">
          {/* Page Header */}
          <div className="text-center mb-12">
            <PageHeader title="Help & Support" />
            <p className="text-[var(--grey-6)] text-lg mt-4">
              We're here to help you with any questions or concerns about your
              fertility journey
            </p>
          </div>

          {/* Contact Options */}
          <div className="grid md:grid-cols-3 gap-6 mb-12">
            <div className="bg-white border border-[var(--grey-3)] rounded-lg p-6 text-center hover:shadow-md transition-shadow duration-200">
              <div className="flex justify-center mb-4">
                <div className="w-12 h-12 bg-[var(--red-1)] rounded-full flex items-center justify-center">
                  <PhoneIcon />
                </div>
              </div>
              <h3 className="text-[var(--grey-7)] font-semibold mb-2">
                Call Us
              </h3>
              <p className="text-[var(--grey-6)] text-sm mb-4">
                Speak directly with our support team
              </p>
              <Button
                type={ButtonType.SECONDARY}
                text="Call Now"
                onClick={handleCallSupport}
                className="w-full border border-[var(--grey-3)]"
              />
            </div>

            <div className="bg-white border border-[var(--grey-3)] rounded-lg p-6 text-center hover:shadow-md transition-shadow duration-200">
              <div className="flex justify-center mb-4">
                <div className="w-12 h-12 bg-[var(--red-1)] rounded-full flex items-center justify-center">
                  <EmailIcon />
                </div>
              </div>
              <h3 className="text-[var(--grey-7)] font-semibold mb-2">
                Email Support
              </h3>
              <p className="text-[var(--grey-6)] text-sm mb-4">
                Send us your questions anytime
              </p>
              <Button
                type={ButtonType.SECONDARY}
                text="Send Email"
                onClick={handleEmailSupport}
                className="w-full border border-[var(--grey-3)]"
              />
            </div>

            <div className="bg-white border border-[var(--grey-3)] rounded-lg p-6 text-center hover:shadow-md transition-shadow duration-200">
              <div className="flex justify-center mb-4">
                <div className="w-12 h-12 bg-[var(--red-1)] rounded-full flex items-center justify-center">
                  <ChatIcon />
                </div>
              </div>
              <h3 className="text-[var(--grey-7)] font-semibold mb-2">
                Live Chat
              </h3>
              <p className="text-[var(--grey-6)] text-sm mb-4">
                Chat with us in real-time
              </p>
              <Button
                type={ButtonType.SECONDARY}
                text="Start Chat"
                onClick={handleLiveChat}
                className="w-full border border-[var(--grey-3)]"
              />
            </div>
          </div>

          {/* Frequently Asked Questions */}
          <div className="mb-12">
            <h2 className="text-[var(--grey-7)] text-2xl font-bold mb-6 text-center">
              Frequently Asked Questions
            </h2>
            <div className="space-y-4">
              {FAQ_DATA.map((faq) => (
                <div
                  key={faq.id}
                  className="bg-white border border-[var(--grey-3)] rounded-lg overflow-hidden"
                >
                  <button
                    onClick={() => toggleFAQ(faq.id)}
                    className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-[var(--grey-1)] transition-colors duration-200"
                  >
                    <span className="text-[var(--grey-7)] font-medium pr-4">
                      {faq.question}
                    </span>
                    <div className="text-[var(--grey-5)]">
                      {expandedFAQ === faq.id ? <MinusIcon /> : <PlusIcon />}
                    </div>
                  </button>
                  {expandedFAQ === faq.id && (
                    <div className="px-6 pb-4">
                      <p className="text-[var(--grey-6)] leading-relaxed">
                        {faq.answer}
                      </p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-[var(--grey-1)] rounded-lg p-8 text-center mb-8">
            <h3 className="text-[var(--grey-7)] text-xl font-semibold mb-4">
              Need Personalized Help?
            </h3>
            <p className="text-[var(--grey-6)] mb-6">
              Book a free consultation with our fertility specialists for
              personalized guidance
            </p>
            <Button
              type={ButtonType.PRIMARY}
              text="Book Free Consultation"
              onClick={handleBookConsultation}
              className="w-full md:w-auto"
            />
          </div>

          {/* Need Help Component */}
          <div className="flex justify-center">
            <NeedHelp phoneNumber="+91-9990044555" />
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default SupportPage;
