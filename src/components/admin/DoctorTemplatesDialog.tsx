"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ShadcnUI/dialog";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ShadcnUI/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ShadcnUI/card";
import { Badge } from "@/components/ShadcnUI/badge";
import { Clock, MapPin, Building, UserIcon } from "lucide-react";

interface AvailabilityTemplate {
  id: number;
  day_of_week: number;
  start_time: string;
  end_time: string;
  duration: number;
  is_available: boolean;
  fee?: number;
  currency?: string;
  clinic?: {
    id: number;
    clinic_name: string;
    address?: string;
    city?: {
      city_name: string;
      state_name?: string;
    };
  };
}

interface Doctor {
  id: number;
  profile: {
    display_name: string;
  };
  specialization_name?: string;
  years_of_experience?: number;
}

interface DoctorTemplatesDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  doctor: Doctor;
}

const DAYS_OF_WEEK = [
  { value: 6, label: 'SAT' },
  { value: 0, label: 'SUN' },
  { value: 1, label: 'MON' },
  { value: 2, label: 'TUE' },
  { value: 3, label: 'WED' },
  { value: 4, label: 'THU' },
  { value: 5, label: 'FRI' }
];

const generateTimeSlots = () => {
  const slots = [];
  for (let hour = 9; hour < 17; hour++) {
    for (let minute = 0; minute < 60; minute += 15) {
      const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
      slots.push(time);
    }
  }
  return slots;
};

export default function DoctorTemplatesDialog({ 
  open, 
  onOpenChange, 
  doctor 
}: DoctorTemplatesDialogProps) {
  const [templates, setTemplates] = useState<AvailabilityTemplate[]>([]);
  const [loading, setLoading] = useState(false);
  const [clinics, setClinics] = useState<Set<number>>(new Set());
  const [clinicTemplates, setClinicTemplates] = useState<Record<number, AvailabilityTemplate[]>>({});
  
  const timeSlots = generateTimeSlots();

  useEffect(() => {
    if (open && doctor) {
      fetchTemplates();
    }
  }, [open, doctor]);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/v1/admin/availability-templates?doctor_id=${doctor.id}`);
      const data = await response.json();

      if (data.success) {
        const templatesData = data.data;
        setTemplates(templatesData);
        
        // Extract unique clinic IDs and organize templates by clinic
        const clinicIds = new Set<number>();
        const templatesByClinic: Record<number, AvailabilityTemplate[]> = {};
        
        templatesData.forEach((template: AvailabilityTemplate) => {
          if (template.clinic?.id) {
            clinicIds.add(template.clinic.id);
            if (!templatesByClinic[template.clinic.id]) {
              templatesByClinic[template.clinic.id] = [];
            }
            templatesByClinic[template.clinic.id].push(template);
          }
        });
        
        setClinics(clinicIds);
        setClinicTemplates(templatesByClinic);
      }
    } catch (error) {
      console.error("Error fetching templates:", error);
    } finally {
      setLoading(false);
    }
  };

  const getClinicInfo = (clinicId: number) => {
    const template = templates.find(t => t.clinic?.id === clinicId);
    return template?.clinic;
  };

  const getTemplateForDayAndTime = (dayOfWeek: number, time: string, clinicId: number) => {
    return clinicTemplates[clinicId]?.find(template => 
      template.day_of_week === dayOfWeek && 
      template.start_time === time
    );
  };

  const renderAvailabilityGrid = (clinicId: number) => {
    const clinic = getClinicInfo(clinicId);
    const clinicTemplateCount = clinicTemplates[clinicId]?.length || 0;

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium">{clinic?.clinic_name}</h4>
            {clinic?.address && (
              <p className="text-sm text-muted-foreground">{clinic.address}</p>
            )}
          </div>
          <Badge variant="default">
            {clinicTemplateCount} slots configured
          </Badge>
        </div>
        
        <div className="border rounded-lg overflow-hidden">
          <div className="grid grid-cols-8 border-b bg-muted/50">
            <div className="p-2 text-sm font-medium">Time</div>
            {DAYS_OF_WEEK.map((day) => (
              <div key={day.value} className="p-2 text-center text-sm font-medium border-l">
                {day.label}
              </div>
            ))}
          </div>
          
          <div className="max-h-60 overflow-y-auto">
            {timeSlots.map((time) => (
              <div key={time} className="grid grid-cols-8 border-b">
                <div className="p-2 text-xs text-muted-foreground border-r">
                  {time}
                </div>
                {DAYS_OF_WEEK.map((day) => {
                  const template = getTemplateForDayAndTime(day.value, time, clinicId);
                  const isAvailable = template && template.is_available;

                  return (
                    <div
                      key={`${day.value}-${time}`}
                      className="p-1 border-l min-h-[40px] flex items-center justify-center"
                    >
                      <div
                        className={`w-full h-full rounded flex items-center justify-center text-xs ${
                          isAvailable
                            ? "bg-blue-100 text-blue-800"
                            : "bg-gray-100 text-gray-400"
                        }`}
                      >
                        {isAvailable ? "✓" : ""}
                      </div>
                    </div>
                  );
                })}
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-full !max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building className="h-5 w-5" />
            Doctor Information
          </DialogTitle>
        </DialogHeader>

        {/* Doctor Details Section */}
        <div className="mb-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
               <UserIcon className="h-4 w-4" />
               {doctor.profile.display_name}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground mb-1">Name</h4>
                  <p className="text-base font-semibold">{doctor.profile.display_name}</p>
                </div>
                {doctor.specialization_name && (
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground mb-1">Specialization</h4>
                    <p className="text-base">{doctor.specialization_name}</p>
                  </div>
                )}
                {doctor.years_of_experience && (
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground mb-1">Experience</h4>
                    <p className="text-base">{doctor.years_of_experience} years</p>
                  </div>
                )}
                <div>
                  <h4 className="font-medium text-sm text-muted-foreground mb-1">Total Clinics</h4>
                  <p className="text-base">{clinics.size} clinic{clinics.size !== 1 ? 's' : ''}</p>
                </div>
              </div>
              <div className="pt-2 border-t">
                <h4 className="font-medium text-sm text-muted-foreground mb-2">Summary</h4>
                <p className="text-sm text-muted-foreground">
                  This doctor has availability templates configured across {clinics.size} clinic{clinics.size !== 1 ? 's' : ''}. 
                  Below you can view and manage their weekly availability schedules.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : (
          <>
          {/* Availability Templates */}
          <div className="space-y-4">
            {clinics.size > 0 ? (
              <Tabs defaultValue={Array.from(clinics)[0]?.toString()} className="w-full">
                <TabsList className="grid w-full" style={{ gridTemplateColumns: `repeat(${clinics.size}, 1fr)` }}>
                  {Array.from(clinics).map((clinicId) => {
                    const clinic = getClinicInfo(clinicId);
                    const clinicTemplateCount = clinicTemplates[clinicId]?.length || 0;
                    return (
                      <TabsTrigger key={clinicId} value={clinicId.toString()}>
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4" />
                          <div className="truncate">
                            <div className="font-medium">{clinic?.clinic_name}</div>
                            {/* <div className="text-xs text-muted-foreground truncate">
                              {clinic?.city ? `${clinic.city.city_name}, ${clinic.city.state_name}` : clinic?.address || "N/A"}
                            </div> */}
                          </div>
                        </div>
                        <Badge variant="secondary" className="ml-1 text-xs">
                          {clinicTemplateCount} slots
                        </Badge>
                      </TabsTrigger>
                    );
                  })}
                </TabsList>

                {Array.from(clinics).map((clinicId) => (
                  <TabsContent key={clinicId} value={clinicId.toString()} className="space-y-4">
                    {renderAvailabilityGrid(clinicId)}
                  </TabsContent>
                ))}
              </Tabs>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Clock className="h-8 w-8 mx-auto mb-2" />
                <p>No availability templates found for this doctor.</p>
              </div>
            )}
          </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
