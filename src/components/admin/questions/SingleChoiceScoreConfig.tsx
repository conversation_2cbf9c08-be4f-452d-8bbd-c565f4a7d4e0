"use client";

import React, { useState, useEffect, useCallback } from "react";
import { FormBuilderOption } from "@/types";
import { Input } from "@/components/ShadcnUI/input";
import { Label } from "@/components/ShadcnUI/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ShadcnUI/card";
import { Badge } from "@/components/ShadcnUI/badge";
import { AlertCircle, Target } from "lucide-react";

interface SingleChoiceScoreConfigProps {
  options: FormBuilderOption[];
  onOptionsChange: (options: FormBuilderOption[]) => void;
  disabled?: boolean;
}

export function SingleChoiceScoreConfig({
  options,
  onOptionsChange,
  disabled = false
}: SingleChoiceScoreConfigProps) {
  const [errors, setErrors] = useState<Record<number, string>>({});

  const updateOptionScore = (index: number, scoreValue: string) => {
    if (scoreValue === "") {
      const updatedOptions = [...options];
      updatedOptions[index] = { ...updatedOptions[index], score: undefined };
      onOptionsChange(updatedOptions);
      return;
    }

    const numValue = parseFloat(scoreValue);
    // we are allowing user to type text, but only updating state if its a valid number
    if (!isNaN(numValue)) {
      const updatedOptions = [...options];
      updatedOptions[index] = {
        ...updatedOptions[index],
        score: numValue,
      };
      onOptionsChange(updatedOptions);

      if (errors[index]) {
        const newErrors = { ...errors };
        delete newErrors[index];
        setErrors(newErrors);
      }
    }
  };

  const validateScores = useCallback(() => {
    const newErrors: Record<number, string> = {};
    options.forEach((option, index) => {
      if (option.score === undefined || option.score === null) {
        newErrors[index] = "Score is required for all options";
      }
    });
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [options]);

  useEffect(() => {
    if (options.length > 0) {
      validateScores();
    }
  }, [options, validateScores]);

  if (options.length === 0) {
    return (
      <Card className="border-dashed">
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">No options available</p>
            <p className="text-xs text-muted-foreground">
              Add options to the question first, then configure their scores
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div>
        <Label className="text-base font-medium">Single Choice Score Configuration</Label>
        <p className="text-sm text-muted-foreground mt-1">
          Assign a score to each option. Users selecting an option will receive its corresponding score.
        </p>
      </div>

      <div className="space-y-3">
        {options.map((option, index) => (
          <Card key={option.id || index} className={errors[index] ? "border-red-500" : ""}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4 text-primary" />
                  <CardTitle className="text-sm">Option {index + 1}</CardTitle>
                  <Badge variant="outline" className="text-xs">
                    Order: {option.order}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2">
                <Label className="text-xs text-muted-foreground">Option Text</Label>
                <div className="p-2 bg-muted rounded-md text-sm">
                  {option.option_text}
                </div>
              </div>
              
              <div className="space-y-2">
                <Label className="text-xs">Score *</Label>
                <Input
                  type="text"
                  value={option.score ?? ""}
                  onChange={(e) => updateOptionScore(index, e.target.value)}
                  disabled={disabled}
                  placeholder="Enter score (e.g., 1.0, 0, -2)"
                  className={errors[index] ? "border-red-500" : ""}
                />
              </div>
              
              {errors[index] && (
                <div className="flex items-center gap-2 text-red-500 text-xs">
                  <AlertCircle className="h-3 w-3" />
                  <span>{errors[index]}</span>
                </div>
              )}
              
              <div className="text-xs text-muted-foreground">
                Users selecting &quot;{option.option_text}&quot; will receive a score of {option.score || 0}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {Object.keys(errors).length > 0 && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="py-3">
            <div className="flex items-center gap-2 text-red-600 text-sm">
              <AlertCircle className="h-4 w-4" />
              <span>Please assign scores to all options</span>
            </div>
          </CardContent>
        </Card>
      )}

      {options.length > 0 && Object.keys(errors).length === 0 && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="py-3">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-green-600 text-sm font-medium">
                <Target className="h-4 w-4" />
                <span>Score Summary</span>
              </div>
              <div className="grid grid-cols-2 gap-2 text-xs">
                {options.map((option, index) => (
                  <div key={index} className="flex justify-between">
                    <span className="text-muted-foreground truncate">
                      {option.option_text}:
                    </span>
                    <Badge variant="secondary" className="ml-2">
                      {option.score || 0}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
