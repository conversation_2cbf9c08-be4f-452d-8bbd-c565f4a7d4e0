"use client";

import React from "react";
import { ScoringType } from "@/types";
import { Label } from "@/components/ShadcnUI/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ShadcnUI/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ShadcnUI/card";
import { Badge } from "@/components/ShadcnUI/badge";
import { Calculator, List } from "lucide-react";

interface ScoringTypeSelectorProps {
  value?: ScoringType;
  onChange: (value: ScoringType) => void;
  disabled?: boolean;
}

const SCORING_TYPE_OPTIONS = [
  {
    value: ScoringType.range,
    label: "Range Scoring",
    description: "Numeric input mapped to score buckets (e.g., Age, BMI, Income)",
    icon: Calculator,
    examples: ["Age: 0-34 = 1.0, 35-37 = 0.9", "BMI ranges with different scores"]
  },
  {
    value: ScoringType.single_choice,
    label: "Single Choice Scoring", 
    description: "Predefined options, each mapped to a score (e.g., Yes/No, Frequency)",
    icon: List,
    examples: ["Yes = 1, No = 0", "Daily = 1.0, Weekly = 0.7, Never = 0.3"]
  }
];

export function ScoringTypeSelector({
  value,
  onChange,
  disabled = false
}: ScoringTypeSelectorProps) {
  const selectedOption = SCORING_TYPE_OPTIONS.find(opt => opt.value === value);

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="scoring_type">Scoring Type *</Label>
        <Select
          value={value}
          onValueChange={onChange}
          disabled={disabled}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select scoring type..." />
          </SelectTrigger>
          <SelectContent>
            {SCORING_TYPE_OPTIONS.map((option) => {
              const Icon = option.icon;
              return (
                <SelectItem key={option.value} value={option.value}>
                  <div className="flex items-center gap-2">
                    <Icon className="h-4 w-4" />
                    <span>{option.label}</span>
                  </div>
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>
      </div>

      {/* Show details for selected scoring type */}
      {selectedOption && (
        <Card className="bg-muted/50">
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <selectedOption.icon className="h-5 w-5 text-primary" />
              <CardTitle className="text-base">{selectedOption.label}</CardTitle>
              <Badge variant="secondary">Selected</Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-3">
            <p className="text-sm text-muted-foreground">
              {selectedOption.description}
            </p>
            <div className="space-y-1">
              <Label className="text-xs font-medium text-muted-foreground">Examples:</Label>
              <ul className="text-xs text-muted-foreground space-y-1">
                {selectedOption.examples.map((example, index) => (
                  <li key={index} className="flex items-start gap-1">
                    <span className="text-primary">•</span>
                    <span>{example}</span>
                  </li>
                ))}
              </ul>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
