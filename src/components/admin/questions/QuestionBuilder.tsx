"use client";

import React, { useState, useEffect } from "react";
import { FormFieldType, FormBuilderQuestion, FormBuilderOption, ScoringType, RangeScoreConfig, Track } from "@/types";
import { FIELD_CONFIGS, COMMON_UNITS } from "@/lib/constants/field-configs";
import { Button as ShadcnButton } from "@/components/ShadcnUI/button";
import { Input } from "@/components/ShadcnUI/input";
import { Label } from "@/components/ShadcnUI/label";
import { Textarea } from "@/components/ShadcnUI/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ShadcnUI/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ShadcnUI/card";
import { Badge } from "@/components/ShadcnUI/badge";

import { Checkbox } from "@/components/ShadcnUI/checkbox";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ShadcnUI/tabs";
import { Plus, Trash2, Save, X, Tag } from "lucide-react";
import { FormFieldPreview } from "./FormFieldRenderer";
import { ScoringTypeSelector } from "./ScoringTypeSelector";
import { RangeScoreConfigComponent } from "./RangeScoreConfig";
import { SingleChoiceScoreConfig } from "./SingleChoiceScoreConfig";
import { SubQuestionBuilder } from "./SubQuestionBuilder";
import Button, { ButtonType } from "@/components/shared/Button/Button";

interface QuestionBuilderProps {
  question?: FormBuilderQuestion;
  onSave: (question: FormBuilderQuestion) => void;
  onCancel: () => void;
  isEditing?: boolean;
  allQuestions: FormBuilderQuestion[];
}

export function QuestionBuilder({
  question,
  onSave,
  onCancel,
  isEditing = false,
  allQuestions
}: QuestionBuilderProps) {
  const [formData, setFormData] = useState<FormBuilderQuestion>({
    question_text: question?.question_text || "",
    help_text: question?.help_text || "",
    is_mandatory: question?.is_mandatory || false,
    field_type: question?.field_type || FormFieldType.INPUT,
    placeholder: question?.placeholder || "",
    min_value: question?.min_value,
    max_value: question?.max_value,
    step: question?.step,
    unit: question?.unit || "",
    order: question?.order || 0,
    options: question?.options || [],
    scoring_type: question?.scoring_type,
    scoring_config: question?.scoring_config || [],
    track_ids: question?.track_ids || [],
    sub_questions: question?.sub_questions || [],
    scoring_mode: question?.scoring_mode || 'separate',
    collective_formula: question?.collective_formula || '',
    depends_on_option_id: question?.depends_on_option_id || undefined,
    ...question
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [tracks, setTracks] = useState<Track[]>([]);
  const [isLoadingTracks, setIsLoadingTracks] = useState(true);
  const [showSubQuestionBuilder, setShowSubQuestionBuilder] = useState(false);
  const [editingSubQuestion, setEditingSubQuestion] = useState<FormBuilderQuestion | null>(null);
  const [activeTab, setActiveTab] = useState("question");

  // Check if sub-questions exist to determine what to hide
  const hasSubQuestions = (formData.sub_questions && formData.sub_questions.length > 0);

  // Fetch available tracks on component mount
  useEffect(() => {
    const fetchTracks = async () => {
      try {
        setIsLoadingTracks(true);
        const response = await fetch('/api/v1/tracks');
        if (response.ok) {
          const {data} = await response.json();
          setTracks(data.tracks || []);
        }
      } catch (error) {
        console.error('Failed to fetch tracks:', error);
      } finally {
        setIsLoadingTracks(false);
      }
    };

    fetchTracks();
  }, []);

  const fieldConfig = FIELD_CONFIGS[formData.field_type];

  const handleFieldChange = (field: keyof FormBuilderQuestion, value: string | number | boolean | FormBuilderOption[] | FormBuilderQuestion[] | string[] | undefined) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when field is updated
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  const handleTrackToggle = (trackId: string, checked: boolean) => {
    const currentTrackIds = formData.track_ids || [];
    let newTrackIds: string[];

    if (checked) {
      newTrackIds = [...currentTrackIds, trackId];
    } else {
      newTrackIds = currentTrackIds.filter(id => id !== trackId);
    }

    handleFieldChange('track_ids', newTrackIds);
  };

  const handleOptionChange = (index: number, field: keyof FormBuilderOption, value: string) => {
    const newOptions = [...(formData.options || [])];
    // Convert empty string to undefined for the value field
    const processedValue = field === 'value' && value === '' ? undefined : value;
    newOptions[index] = { ...newOptions[index], [field]: processedValue };
    handleFieldChange('options', newOptions);
  };

  const addOption = () => {
    const newOptions = [...(formData.options || [])];
    newOptions.push({
      option_text: "",
      value: undefined, // Use undefined instead of empty string
      order: newOptions.length
    });
    handleFieldChange('options', newOptions);
  };

  const removeOption = (index: number) => {
    const newOptions = [...(formData.options || [])];
    newOptions.splice(index, 1);
    // Reorder remaining options
    newOptions.forEach((option, idx) => {
      option.order = idx;
    });
    handleFieldChange('options', newOptions);
  };

  // Scoring handlers
  const handleScoringTypeChange = (scoringType: ScoringType) => {
    setFormData(prev => ({
      ...prev,
      scoring_type: scoringType,
      scoring_config: scoringType === ScoringType.range ? [] : []
    }));
  };

  const handleRangeScoreConfigChange = (config: RangeScoreConfig[]) => {
    setFormData(prev => ({
      ...prev,
      scoring_config: config
    }));
  };

  const handleOptionsWithScoreChange = (options: FormBuilderOption[]) => {
    setFormData(prev => ({
      ...prev,
      options: options
    }));
  };

  // Sub-question handlers
  const handleAddSubQuestion = () => {
    setEditingSubQuestion(null);
    setShowSubQuestionBuilder(true);
  };

  const handleEditSubQuestion = (subQuestion: FormBuilderQuestion) => {
    setEditingSubQuestion(subQuestion);
    setShowSubQuestionBuilder(true);
  };

  const handleSaveSubQuestion = (subQuestion: FormBuilderQuestion) => {
    const newSubQuestions = [...(formData.sub_questions || [])];
    
    if (editingSubQuestion) {
      // Update existing sub-question
      const index = newSubQuestions.findIndex(sq => sq.id === editingSubQuestion.id);
      if (index !== -1) {
        newSubQuestions[index] = { ...subQuestion, id: editingSubQuestion.id };
      }
    } else {
      // Add new sub-question
      const newSubQuestion = {
        ...subQuestion,
        id: `temp-${Date.now()}`,
        order: newSubQuestions.length
      };
      newSubQuestions.push(newSubQuestion);
    }
    
    handleFieldChange('sub_questions', newSubQuestions);
    setShowSubQuestionBuilder(false);
    setEditingSubQuestion(null);
  };

  const handleDeleteSubQuestion = (subQuestionId: string) => {
    const newSubQuestions = (formData.sub_questions || []).filter(sq => sq.id !== subQuestionId);
    handleFieldChange('sub_questions', newSubQuestions);
  };

  const handleCancelSubQuestion = () => {
    setShowSubQuestionBuilder(false);
    setEditingSubQuestion(null);
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.question_text.trim()) {
      newErrors.question_text = "Question text is required";
    }

    // Skip validation for field-specific settings when sub-questions exist
    if (!hasSubQuestions) {
      if (fieldConfig.supportsOptions && (!formData.options || formData.options.length < 2)) {
        newErrors.options = "At least 2 options are required";
      }

      if (fieldConfig.supportsOptions && formData.options) {
        formData.options.forEach((option, index) => {
          if (!option.option_text.trim()) {
            newErrors[`option_${index}`] = "Option text is required";
          }
        });
      }

      if (fieldConfig.supportsRange) {
        if (formData.min_value === undefined || formData.max_value === undefined) {
          newErrors.range = "Both minimum and maximum values are required";
        } else if (formData.min_value >= formData.max_value) {
          newErrors.range = "Minimum value must be less than maximum value";
        }
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (validateForm()) {
      onSave(formData);
    }
  };

  if (showSubQuestionBuilder) {
    return (
      <SubQuestionBuilder
        question={editingSubQuestion || undefined}
        parentQuestionId={question?.id || 'temp'}
        onSave={handleSaveSubQuestion}
        onCancel={handleCancelSubQuestion}
        isEditing={!!editingSubQuestion}
      />
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>
            {isEditing ? "Edit Question" : "Create New Question"}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="question">Question</TabsTrigger>
              <TabsTrigger value="tracks">Tracks</TabsTrigger>
              <TabsTrigger value="scoring">Scoring</TabsTrigger>
              {formData.field_type === FormFieldType.GROUP_QUESTION && <TabsTrigger value="sub-questions">Sub-questions</TabsTrigger>}
            </TabsList>

            {/* Question Tab */}
            <TabsContent value="question" className="space-y-6">
              <div className="space-y-4">
                {/* Info message when sub-questions exist */}
                {formData.field_type === FormFieldType.GROUP_QUESTION && (
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <p className="text-sm text-blue-800">
                      <strong>Note:</strong> Since this question has sub-questions, the field type, placeholder, help text, and scoring configuration are hidden. 
                      These settings are managed by the individual sub-questions instead.
                    </p>
                  </div>
                )}

                {/* Question Text */}
                <div className="space-y-2">
                  <Label htmlFor="question_text">Question Text *</Label>
                  <Textarea
                    id="question_text"
                    placeholder="Enter your question..."
                    value={formData.question_text}
                    onChange={(e) => handleFieldChange('question_text', e.target.value)}
                    className={errors.question_text ? "border-red-500" : ""}
                  />
                  {errors.question_text && (
                    <p className="text-sm text-red-500">{errors.question_text}</p>
                  )}
                </div>

                {/* Help Text - Hidden when sub-questions exist */}
                {formData.field_type !== FormFieldType.GROUP_QUESTION && (
                  <div className="space-y-2">
                    <Label htmlFor="help_text">Help Text</Label>
                    <Textarea
                      id="help_text"
                      placeholder="Enter help text for the user..."
                      value={formData.help_text || ""}
                      onChange={(e) => handleFieldChange('help_text', e.target.value)}
                    />
                  </div>
                )}

                {/* Is Mandatory */}
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="is_mandatory"
                    checked={formData.is_mandatory || false}
                    onCheckedChange={(checked) => handleFieldChange('is_mandatory', checked as boolean)}
                  />
                  <Label htmlFor="is_mandatory">This question is mandatory</Label>
                </div>

                {/* Field Type - Hidden when sub-questions exist */}
                <div className="space-y-2">
                    <Label htmlFor="field_type">Field Type *</Label>
                    <Select
                      value={formData.field_type}
                      onValueChange={(value) => handleFieldChange('field_type', value as FormFieldType)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.values(FormFieldType).map((type) => (
                          <SelectItem key={type} value={type}>
                            <div className="flex items-center gap-2">
                              <Badge variant="outline">{FIELD_CONFIGS[type].label}</Badge>
                              <span className="text-sm text-muted-foreground">
                                {FIELD_CONFIGS[type].description}
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                </div>
                

                {/* Placeholder (for INPUT and NUMBER_INPUT) - Hidden when sub-questions exist */}
                {formData.field_type !== FormFieldType.GROUP_QUESTION && fieldConfig.supportsPlaceholder && (
                  <div className="space-y-2">
                    <Label htmlFor="placeholder">Placeholder Text</Label>
                    <Input
                      id="placeholder"
                      placeholder="Enter placeholder text..."
                      value={formData.placeholder || ""}
                      onChange={(e) => handleFieldChange('placeholder', e.target.value)}
                    />
                  </div>
                )}

                {/* Range Values (for NUMBER_INPUT and RANGE_SLIDER) - Hidden when sub-questions exist */}
                {formData.field_type !== FormFieldType.GROUP_QUESTION && fieldConfig.supportsRange && (
                  <div className="space-y-2">
                    <Label>Value Range *</Label>
                    <div className="grid grid-cols-3 gap-2">
                      <div>
                        <Label htmlFor="min_value" className="text-sm">Minimum</Label>
                        <Input
                          id="min_value"
                          type="number"
                          placeholder="Min"
                          value={formData.min_value || ""}
                          onChange={(e) => handleFieldChange('min_value', parseInt(e.target.value) || undefined)}
                        />
                      </div>
                      <div>
                        <Label htmlFor="max_value" className="text-sm">Maximum</Label>
                        <Input
                          id="max_value"
                          type="number"
                          placeholder="Max"
                          value={formData.max_value || ""}
                          onChange={(e) => handleFieldChange('max_value', parseInt(e.target.value) || undefined)}
                        />
                      </div>
                      <div>
                        <Label htmlFor="step" className="text-sm">Step</Label>
                        <Input
                          id="step"
                          type="number"
                          placeholder="1"
                          value={formData.step || ""}
                          onChange={(e) => handleFieldChange('step', parseInt(e.target.value) || undefined)}
                        />
                      </div>
                    </div>
                    {errors.range && (
                      <p className="text-sm text-red-500">{errors.range}</p>
                    )}
                  </div>
                )}

                {/* Unit (for NUMBER_INPUT and RANGE_SLIDER) - Hidden when sub-questions exist */}
                {formData.field_type !== FormFieldType.GROUP_QUESTION && fieldConfig.supportsUnit && (
                  <div className="space-y-2">
                    <Label htmlFor="unit">Unit</Label>
                    <Select
                      value={formData.unit || "none"}
                      onValueChange={(value) => handleFieldChange('unit', value === "none" ? undefined : value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select unit (optional)" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">No unit</SelectItem>
                        {COMMON_UNITS.map((unit) => (
                          <SelectItem key={unit.value} value={unit.value}>
                            {unit.label} ({unit.value})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {/* Options (for RADIO_SELECT and DROPDOWN_SELECT) - Hidden when sub-questions exist */}
                {!hasSubQuestions && fieldConfig.supportsOptions && (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label>Options *</Label>
                      <Button
                        type={ButtonType.PRIMARY}
                        size="sm"
                        onClick={addOption}
                        className="gap-2 max-w-40"
                        icon={<Plus className="h-4 w-4" />}
                        text="Add Option"
                      />
                        
                        
                    </div>
                    
                    <div className="space-y-2">
                      {formData.options?.map((option, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <Input
                            placeholder="Option text"
                            value={option.option_text}
                            onChange={(e) => handleOptionChange(index, 'option_text', e.target.value)}
                            className={errors[`option_${index}`] ? "border-red-500" : ""}
                          />
                          <Input
                            placeholder="Value (optional)"
                            value={option.value || ""}
                            onChange={(e) => handleOptionChange(index, 'value', e.target.value)}
                          />
                          <ShadcnButton
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeOption(index)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </ShadcnButton>
                        </div>
                      ))}
                    </div>
                    
                    {errors.options && (
                      <p className="text-sm text-red-500">{errors.options}</p>
                    )}
                    
                    {formData.options?.some((_, index) => errors[`option_${index}`]) && (
                      <p className="text-sm text-red-500">All options must have text</p>
                    )}
                  </div>
                )}


                {/* Conditional Logic: Show if user selects option */}
                <div className="border-t pt-4">
                  <h3 className="font-semibold">Conditional Logic</h3>
                  <p className="text-sm text-muted-foreground">
                    Show this question only if the user selects a specific option from another question.
                  </p>
                  <Select
                    value={formData.depends_on_option_id || "__always__"}
                    onValueChange={(value) => handleFieldChange('depends_on_option_id', value === "__always__" ? undefined : value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Always show (not conditional)" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="__always__">Always show (not conditional)</SelectItem>
                      {allQuestions
                        .filter(q => q.id !== formData.id && q.options && q.options.length > 0)
                        .map(q =>
                          q.options!.map(opt => (
                            <SelectItem key={opt.id} value={opt.id!}>
                              {q.question_text} — {opt.option_text}
                            </SelectItem>
                          ))
                        )}
                    </SelectContent>
                  </Select>
                  {formData.depends_on_option_id && (
                    <ShadcnButton
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleFieldChange('depends_on_option_id', undefined)}
                      className="mt-1"
                    >
                      Clear condition
                    </ShadcnButton>
                  )}
                </div>
              </div>
            </TabsContent>

            {/* Tracks Tab */}
            <TabsContent value="tracks" className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Tag className="h-5 w-5 text-blue-600" />
                  <Label className="text-lg font-semibold">Track Assignment</Label>
                </div>
                <p className="text-sm text-muted-foreground">
                  Select which fertility tracks this question applies to. Questions will only be shown to users following the selected tracks.
                </p>
                
                {isLoadingTracks ? (
                  <div className="text-sm text-muted-foreground">Loading tracks...</div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {tracks.map((track) => (
                      <div key={track.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                        <Checkbox
                          id={`track-${track.id}`}
                          checked={formData.track_ids?.includes(track.id) || false}
                          onCheckedChange={(checked) => handleTrackToggle(track.id, checked as boolean)}
                        />
                        <div className="flex-1 min-w-0">
                          <Label htmlFor={`track-${track.id}`} className="font-medium cursor-pointer">
                            {track.code} - {track.name}
                          </Label>
                          <p className="text-xs text-muted-foreground mt-1">
                            {track.description}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
                
                {tracks.length === 0 && !isLoadingTracks && (
                  <div className="text-sm text-muted-foreground">
                    No tracks available. Make sure tracks are properly configured in the database.
                  </div>
                )}
              </div>
            </TabsContent>

            {/* Scoring Tab */}
            <TabsContent value="scoring" className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <Label className="text-lg font-semibold">IVF Fertility Meter Scoring</Label>
                    <p className="text-sm text-muted-foreground mt-1">
                      Configure how this question contributes to the overall fertility meter score.
                    </p>
                  </div>

                  {/* Scoring Mode Radio Group */}
                  {formData.field_type === FormFieldType.GROUP_QUESTION && (
                    <div className="space-y-2">
                    <Label className="font-medium">Scoring Mode</Label>
                    <div className="flex gap-6 mt-1">
                      <label className="flex items-center gap-2">
                        <input
                          type="radio"
                          name="scoring_mode"
                          value="separate"
                          checked={formData.scoring_mode !== 'collective'}
                          onChange={() => handleFieldChange('scoring_mode', 'separate')}
                        />
                        <span>Separate</span>
                      </label>
                      <label className="flex items-center gap-2">
                        <input
                          type="radio"
                          name="scoring_mode"
                          value="collective"
                          checked={formData.scoring_mode === 'collective'}
                          onChange={() => handleFieldChange('scoring_mode', 'collective')}
                        />
                        <span>Collective</span>
                      </label>
                    </div>
                  </div>)}

                  {/* Collective Formula Input */}
                  {formData.field_type === FormFieldType.GROUP_QUESTION && formData.scoring_mode === 'collective' && (
                    <div className="space-y-2">
                      <Label htmlFor="collective_formula">Collective Formula</Label>
                      <Input
                        id="collective_formula"
                        placeholder="e.g. (q1/q2)*2, q1+q2+q3, q1-q2"
                        value={formData.collective_formula || ''}
                        onChange={e => handleFieldChange('collective_formula', e.target.value)}
                      />
                      <p className="text-xs text-muted-foreground">
                        Enter a formula using q1, q2, etc. (e.g., (q1/q2)*2, q1+q2+q3, q1-q2). &apos;q&apos; represents the answer to a sub-question.
                      </p>
                    </div>
                  )}

                  {/* Show when scoring_mode is not 'seperate' */}
                      <ScoringTypeSelector
                        value={formData.scoring_type}
                        onChange={handleScoringTypeChange}
                      />

                      {formData.scoring_type === ScoringType.range && (
                        <RangeScoreConfigComponent
                          value={formData.scoring_config as RangeScoreConfig[] || []}
                          onChange={handleRangeScoreConfigChange}
                        />
                      )}

                      {formData.scoring_type === ScoringType.single_choice && (
                        <SingleChoiceScoreConfig
                          options={formData.options || []}
                          onOptionsChange={handleOptionsWithScoreChange}
                        />
                      )}
                </div>
            </TabsContent>

            {/* Sub-questions Tab */}
            {formData.field_type === FormFieldType.GROUP_QUESTION && (
              <TabsContent value="sub-questions" className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-lg font-semibold">Sub-Questions</Label>
                      <p className="text-sm text-muted-foreground mt-1">
                        Add conditional questions that appear based on the parent question&apos;s answer.
                      </p>
                    </div>
                    <Button
                      type={ButtonType.PRIMARY}
                      size="sm"
                      onClick={handleAddSubQuestion}
                      className="gap-2 max-w-50"
                      text="Add Sub-Question"
                      icon={<Plus className="h-4 w-4" />} />
                  </div>
                  
                  <div className="space-y-3">
                    {formData.sub_questions && formData.sub_questions.length > 0 ? (
                      formData.sub_questions.map((subQuestion, index) => (
                        <div key={subQuestion.id || index} className="p-3 border rounded-lg bg-muted/50">
                          <div className="flex items-center justify-between">
                            <div className="flex-1">
                              <h4 className="font-medium">{subQuestion.question_text}</h4>
                              <p className="text-sm text-muted-foreground">
                                {subQuestion.field_type} • {subQuestion.is_mandatory ? 'Mandatory' : 'Optional'}
                              </p>
                            </div>
                            <div className="flex items-center gap-2">
                              <ShadcnButton
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditSubQuestion(subQuestion)}
                              >
                                Edit
                              </ShadcnButton>
                              <ShadcnButton
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteSubQuestion(subQuestion.id!)}
                                className="text-red-500 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                              </ShadcnButton>
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-4 text-muted-foreground">
                        <p>No sub-questions added yet.</p>
                        <p className="text-sm">Click &quot;Add Sub-Question&quot; to create conditional questions.</p>
                      </div>
                    )}
                  </div>
                </div>
              </TabsContent>
            )}
          </Tabs>

          {/* Action Buttons */}
          <div className="flex items-center gap-2 mt-6 pt-6 border-t">
            <Button onClick={handleSave} 
            type={ButtonType.PRIMARY}
            size="sm"
            icon={<Save className="h-4 w-4" />}
            text={isEditing ? "Update Question" : "Create Question"}
            className="gap-2 max-w-50 whitespace-nowrap" />
            <Button 
            onClick={onCancel}
            size="sm"
            type={ButtonType.SECONDARY}
            icon={<X className="h-4 w-4" />}
            text="Cancel"
            className="gap-2 max-w-50" />
          </div>
        </CardContent>
      </Card>

      {/* Preview */}
      {formData.question_text && (
        <div>
          <Label className="text-base font-medium mb-2 block">Preview</Label>
          {hasSubQuestions ? (
            <div className="p-4 border rounded-lg bg-muted/50">
              <div className="flex items-center gap-2 mb-2">
                <Badge variant="outline">Parent Question</Badge>
                {formData.is_mandatory && <Badge variant="destructive">Mandatory</Badge>}
              </div>
              <p className="font-medium">{formData.question_text}</p>
              <p className="text-sm text-muted-foreground mt-2">
                This question has {formData.sub_questions?.length || 0} sub-question(s). 
                The actual form fields will be displayed by the sub-questions.
              </p>
            </div>
          ) : (
            <FormFieldPreview
              question={{
                id: 'preview',
                form_id: 'preview',
                parent_id: null,
                created_at: new Date(),
                updated_at: new Date(),
                question_text: formData.question_text,
                help_text: formData.help_text ?? null,
                is_mandatory: formData.is_mandatory ?? false,
                field_type: formData.field_type,
                placeholder: formData.placeholder ?? null,
                min_value: formData.min_value ?? null,
                max_value: formData.max_value ?? null,
                step: formData.step ?? null,
                unit: formData.unit ?? null,
                order: formData.order,
                scoring_type: formData.scoring_type ?? null,
                scoring_config: formData.scoring_config ? JSON.parse(JSON.stringify(formData.scoring_config)) : null,
                scoring_mode: formData.scoring_mode || 'separate',
                collective_formula: formData.collective_formula || null,
                options: formData.options?.map((opt, idx) => ({
                  ...opt,
                  id: `preview-${idx}`,
                  question_id: 'preview',
                  created_at: new Date(),
                  updated_at: new Date(),
                  value: opt.value ?? null,
                  score: opt.score ?? null,
                })) || [],
                depends_on_option_id: formData.depends_on_option_id ?? null,
              }}
            />
          )}
        </div>
      )}
    </div>
  );
}
