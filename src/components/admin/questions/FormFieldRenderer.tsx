"use client";

import React from "react";
import { FormFieldType, QuestionWithOptions } from "@/types";
import { Input } from "@/components/ShadcnUI/input";
import { Label } from "@/components/ShadcnUI/label";
import { RadioGroup, RadioGroupItem } from "@/components/ShadcnUI/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ShadcnUI/select";
import { Slider } from "@/components/ShadcnUI/slider";
import { Card, CardContent } from "@/components/ShadcnUI/card";

interface FormFieldRendererProps {
  question: QuestionWithOptions;
  value?: string | number;
  onChange: (value: string | number) => void;
  error?: string;
  disabled?: boolean;
}

export function FormFieldRenderer({
  question,
  value,
  onChange,
  error,
  disabled = false
}: FormFieldRendererProps) {
  const renderField = () => {
    switch (question.field_type) {
      case FormFieldType.INPUT:
        return (
          <Input
            type="text"
            placeholder={question.placeholder || "Enter your answer"}
            value={value as string || ""}
            onChange={(e) => onChange(e.target.value)}
            disabled={disabled}
            className={error ? "border-red-500" : ""}
          />
        );

      case FormFieldType.NUMBER_INPUT:
        return (
          <div className="flex items-center gap-2">
            <Input
              type="number"
              placeholder={question.placeholder || "Enter a number"}
              value={value as number || ""}
              onChange={(e) => onChange(parseFloat(e.target.value) || 0)}
              min={question.min_value || undefined}
              max={question.max_value || undefined}
              step={question.step || 1}
              disabled={disabled}
              className={error ? "border-red-500" : ""}
            />
            {question.unit && (
              <span className="text-sm text-muted-foreground">{question.unit}</span>
            )}
          </div>
        );

      case FormFieldType.RADIO_SELECT:
        return (
          <RadioGroup
            value={value as string || ""}
            onValueChange={onChange}
            disabled={disabled}
            className={error ? "border border-red-500 rounded-md p-2" : ""}
          >
            {question.options.map((option, index) => {
              const optionValue = option.value || option.option_text || `option-${index}`;
              return (
                <div key={option.id} className="flex items-center space-x-2">
                  <RadioGroupItem
                    value={optionValue}
                    id={option.id}
                  />
                  <Label htmlFor={option.id} className="cursor-pointer">
                    {option.option_text || `Option ${index + 1}`}
                  </Label>
                </div>
              );
            })}
          </RadioGroup>
        );

      case FormFieldType.MULTI_SELECT_RADIO:
        return (
          <RadioGroup
            value={value as string || ""}
            onValueChange={onChange}
            disabled={disabled}
          >
            {question.options.map((option, index) => {
              const optionValue = option.value || option.option_text || `option-${index}`;
              return (
                <div key={option.id} className="flex items-center space-x-2">
                  <RadioGroupItem
                    value={optionValue}
                    id={option.id}
                  />
                  <Label htmlFor={option.id} className="cursor-pointer">
                    {option.option_text || `Option ${index + 1}`}
                  </Label>
                </div>
              );
            })}
          </RadioGroup>
        );

      case FormFieldType.DROPDOWN_SELECT:
        return (
          <Select
            value={value as string || ""}
            onValueChange={onChange}
            disabled={disabled}
          >
            <SelectTrigger className={error ? "border-red-500" : ""}>
              <SelectValue placeholder="Select an option" />
            </SelectTrigger>
            <SelectContent>
              {question.options.map((option, index) => {
                const optionValue = option.value || option.option_text || `option-${index}`;
                return (
                  <SelectItem
                    key={option.id}
                    value={optionValue}
                  >
                    {option.option_text || `Option ${index + 1}`}
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        );

      case FormFieldType.RANGE_SLIDER:
        const numValue = typeof value === 'number' ? value : (question.min_value || 0);
        return (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">
                {question.min_value || 0}
                {question.unit && ` ${question.unit}`}
              </span>
              <span className="font-medium">
                {numValue}
                {question.unit && ` ${question.unit}`}
              </span>
              <span className="text-sm text-muted-foreground">
                {question.max_value || 100}
                {question.unit && ` ${question.unit}`}
              </span>
            </div>
            <Slider
              value={[numValue]}
              onValueChange={(values) => onChange(values[0])}
              min={question.min_value || 0}
              max={question.max_value || 100}
              step={question.step || 1}
              disabled={disabled}
              className={error ? "border-red-500" : ""}
            />
          </div>
        );
      case FormFieldType.GROUP_QUESTION:
        return (
          <div className="text-muted-foreground italic">
            This is a group question. Please configure sub-questions.
          </div>
        );
      default:
        return (
          <div className="text-red-500">
            Unsupported field type: {question.field_type}
          </div>
        );
    }
  };

  return (
    <Card className="w-full">
      <CardContent className="p-4">
        <div className="space-y-3">
          <Label className="text-base font-medium">
            {question.question_text}
          </Label>
          {renderField()}
          {error && (
            <p className="text-sm text-red-500">{error}</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Preview component for the form builder
interface FormFieldPreviewProps {
  question: QuestionWithOptions;
}

export function FormFieldPreview({ question }: FormFieldPreviewProps) {
  return (
    <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 bg-gray-50">
      <div className="space-y-2">
        <Label className="text-sm font-medium text-gray-700">
          Preview: {question.question_text}
        </Label>
        <div className="opacity-60 pointer-events-none">
          <FormFieldRenderer
            question={question}
            value=""
            onChange={() => {}}
            disabled={true}
          />
        </div>
      </div>
    </div>
  );
}
