"use client";

import React, { useState } from "react";
import { QuestionWithOptions } from "@/types";
import { FIELD_CONFIGS } from "@/lib/constants/field-configs";
import { Button } from "@/components/ShadcnUI/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ShadcnUI/card";
import { Badge } from "@/components/ShadcnUI/badge";
import { Separator } from "@/components/ShadcnUI/separator";
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ShadcnUI/alert-dialog";
import { Edit3, Trash2, GripVertical, Eye, EyeOff, Tag, ChevronDown, ChevronRight } from "lucide-react";
import { FormFieldPreview } from "./FormFieldRenderer";

interface QuestionListProps {
  questions: QuestionWithOptions[];
  onEditQuestion: (question: QuestionWithOptions) => void;
  onDeleteQuestion: (questionId: string) => void;
  onReorderQuestions?: (questionOrders: { id: string; order: number }[]) => void;
  isLoading?: boolean;
}

export function QuestionList({
  questions,
  onEditQuestion,
  onDeleteQuestion,
  onReorderQuestions,
  isLoading = false
}: QuestionListProps) {
  const [expandedQuestions, setExpandedQuestions] = useState<Set<string>>(new Set());
  const [expandedSubQuestions, setExpandedSubQuestions] = useState<Set<string>>(new Set());
  const [draggedItem, setDraggedItem] = useState<string | null>(null);

  const toggleExpanded = (questionId: string) => {
    const newExpanded = new Set(expandedQuestions);
    if (newExpanded.has(questionId)) {
      newExpanded.delete(questionId);
    } else {
      newExpanded.add(questionId);
    }
    setExpandedQuestions(newExpanded);
  };

  const toggleSubQuestionsExpanded = (questionId: string) => {
    const newExpanded = new Set(expandedSubQuestions);
    if (newExpanded.has(questionId)) {
      newExpanded.delete(questionId);
    } else {
      newExpanded.add(questionId);
    }
    setExpandedSubQuestions(newExpanded);
  };

  const handleDragStart = (e: React.DragEvent, questionId: string) => {
    setDraggedItem(questionId);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (e: React.DragEvent, targetQuestionId: string) => {
    e.preventDefault();
    
    if (!draggedItem || draggedItem === targetQuestionId || !onReorderQuestions) {
      setDraggedItem(null);
      return;
    }

    const draggedIndex = questions.findIndex(q => q.id === draggedItem);
    const targetIndex = questions.findIndex(q => q.id === targetQuestionId);

    if (draggedIndex === -1 || targetIndex === -1) {
      setDraggedItem(null);
      return;
    }

    // Create new order array
    const newQuestions = [...questions];
    const [draggedQuestion] = newQuestions.splice(draggedIndex, 1);
    newQuestions.splice(targetIndex, 0, draggedQuestion);

    // Generate new order values
    const questionOrders = newQuestions.map((question, index) => ({
      id: question.id,
      order: index
    }));

    onReorderQuestions(questionOrders);
    setDraggedItem(null);
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-4">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (questions.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <p className="text-muted-foreground">
            No questions added yet. Click &quot;Add Question&quot; to get started.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {questions.map((question, index) => {
        const isExpanded = expandedQuestions.has(question.id);
        const fieldConfig = FIELD_CONFIGS[question.field_type];

        return (
          <Card
            key={question.id}
            className={`transition-all duration-200 ${
              draggedItem === question.id ? 'opacity-50' : ''
            }`}
            draggable={!!onReorderQuestions}
            onDragStart={(e) => handleDragStart(e, question.id)}
            onDragOver={handleDragOver}
            onDrop={(e) => handleDrop(e, question.id)}
          >
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3 flex-1">
                  {onReorderQuestions && (
                    <div className="mt-1 cursor-grab active:cursor-grabbing">
                      <GripVertical className="h-4 w-4 text-muted-foreground" />
                    </div>
                  )}
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-sm font-medium text-muted-foreground">
                        Question {index + 1}
                      </span>
                      <Badge variant="secondary">
                        {fieldConfig.label}
                      </Badge>
                    </div>
                    
                    <CardTitle className="text-base leading-relaxed">
                      {question.question_text}
                      {question.is_mandatory && (
                        <Badge variant="destructive" className="ml-2">
                          Mandatory
                        </Badge>
                      )}
                    </CardTitle>

                    {question.help_text && (
                      <p className="text-sm text-muted-foreground mt-1">
                        {question.help_text}
                      </p>
                    )}
                    
                    {/* Question metadata */}
                    <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                      {question.placeholder && (
                        <span>Placeholder: &quot;{question.placeholder}&quot;</span>
                      )}
                      {question.min_value !== null && question.max_value !== null && (
                        <span>
                          Range: {question.min_value} - {question.max_value}
                          {question.unit && ` ${question.unit}`}
                        </span>
                      )}
                      {question.options.length > 0 && (
                        <span>{question.options.length} options</span>
                      )}
                    </div>

                    {/* Track assignments */}
                    {question.question_tracks && question.question_tracks.length > 0 && (
                      <div className="flex items-center gap-2 mt-3">
                        <Tag className="h-3 w-3 text-blue-600" />
                        <span className="text-xs text-muted-foreground">Tracks:</span>
                        <div className="flex gap-1">
                          {question.question_tracks.map((qt) => (
                            <Badge
                              key={qt.id}
                              variant="outline"
                              className="text-xs px-2 py-0 bg-blue-50 text-blue-700 border-blue-200"
                            >
                              {qt.track.code}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Sub-questions indicator */}
                    {question.sub_questions && question.sub_questions.length > 0 && (
                      <div className="flex items-center gap-2 mt-3">
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleSubQuestionsExpanded(question.id)}
                          className="h-6 px-2 text-xs"
                        >
                          {expandedSubQuestions.has(question.id) ? (
                            <ChevronDown className="h-3 w-3 mr-1" />
                          ) : (
                            <ChevronRight className="h-3 w-3 mr-1" />
                          )}
                          {question.sub_questions.length} sub-question{question.sub_questions.length !== 1 ? 's' : ''}
                        </Button>
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleExpanded(question.id)}
                    className="h-8 w-8 p-0"
                    title={isExpanded ? "Hide preview" : "Show preview"}
                  >
                    {isExpanded ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onEditQuestion(question)}
                    className="h-8 w-8 p-0"
                    title="Edit question"
                  >
                    <Edit3 className="h-4 w-4" />
                  </Button>

                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                        title="Delete question"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Delete Question</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to delete this question? This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => onDeleteQuestion(question.id)}
                          className="bg-red-500 hover:bg-red-600"
                        >
                          Delete
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
            </CardHeader>

            {/* Options display */}
            {question.options.length > 0 && (
              <CardContent className="pt-0 pb-3">
                <div className="flex flex-wrap gap-1">
                  {question.options.slice(0, 5).map((option) => (
                    <Badge key={option.id} variant="outline" className="text-xs">
                      {option.option_text}
                    </Badge>
                  ))}
                  {question.options.length > 5 && (
                    <Badge variant="outline" className="text-xs">
                      +{question.options.length - 5} more
                    </Badge>
                  )}
                </div>
              </CardContent>
            )}

            {/* Preview */}
            {isExpanded && (
              <>
                <Separator />
                <CardContent className="pt-4">
                  <FormFieldPreview question={question} />
                </CardContent>
              </>
            )}

            {/* Sub-Questions */}
            {expandedSubQuestions.has(question.id) && question.sub_questions && question.sub_questions.length > 0 && (
              <>
                <Separator />
                <CardContent className="pt-4">
                  <div className="space-y-3">
                    <h4 className="text-sm font-medium text-muted-foreground">Sub-Questions</h4>
                    {question.sub_questions.map((subQuestion, subIndex) => (
                      <div key={subQuestion.id} className="p-3 border rounded-lg bg-muted/30">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="text-xs font-medium text-muted-foreground">
                                Sub-Question {subIndex + 1}
                              </span>
                              <Badge variant="outline" className="text-xs">
                                {FIELD_CONFIGS[subQuestion.field_type].label}
                              </Badge>
                            </div>
                            <h5 className="text-sm font-medium">
                              {subQuestion.question_text}
                              {subQuestion.is_mandatory && (
                                <Badge variant="destructive" className="ml-2 text-xs">
                                  Mandatory
                                </Badge>
                              )}
                            </h5>
                            {subQuestion.help_text && (
                              <p className="text-xs text-muted-foreground mt-1">
                                {subQuestion.help_text}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </>
            )}
          </Card>
        );
      })}
    </div>
  );
}
