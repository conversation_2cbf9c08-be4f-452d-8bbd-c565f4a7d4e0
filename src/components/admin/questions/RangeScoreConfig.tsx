"use client";

import React, { useState, useCallback, useMemo } from "react";
import { RangeScoreConfig } from "@/types";
import Button, { ButtonType } from "@/components/shared/Button/Button";
import { Button as ShadcnButton } from "@/components/ShadcnUI/button";
import { Input } from "@/components/ShadcnUI/input";
import { Label } from "@/components/ShadcnUI/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ShadcnUI/card";
import { Plus, Trash2, AlertCircle } from "lucide-react";

interface RangeScoreConfigProps {
  value: RangeScoreConfig[];
  onChange: (value: RangeScoreConfig[]) => void;
  disabled?: boolean;
}

export function RangeScoreConfigComponent({
  value,
  onChange,
  disabled = false
}: RangeScoreConfigProps) {
  // Ensure value is always an array, memoized for hook deps
  const safeValue = useMemo(() => Array.isArray(value) ? value : [], [value]);
  const [errors, setErrors] = useState<Record<number, string>>({});

  const addRange = () => {
    const newRange: RangeScoreConfig = {
      min: 0,
      max: 10,
      score: 1.0
    };
    onChange([...safeValue, newRange]);
  };

  const updateRange = (index: number, field: keyof RangeScoreConfig, newValue: string) => {
    const numValue = parseFloat(newValue);
    if (isNaN(numValue)) return;

    const updatedRanges = [...safeValue];
    updatedRanges[index] = {
      ...updatedRanges[index],
      [field]: numValue
    };
    onChange(updatedRanges);
    
    // Clear error for this range
    if (errors[index]) {
      const newErrors = { ...errors };
      delete newErrors[index];
      setErrors(newErrors);
    }
  };

  const removeRange = (index: number) => {
    const updatedRanges = safeValue.filter((_, i) => i !== index);
    onChange(updatedRanges);
    
    // Clear error for this range
    if (errors[index]) {
      const newErrors = { ...errors };
      delete newErrors[index];
      setErrors(newErrors);
    }
  };

  const validateRanges = useCallback(() => {
    const newErrors: Record<number, string> = {};
    
    safeValue.forEach((range, index) => {
      if (range.min >= range.max) {
        newErrors[index] = "Min value must be less than max value";
      }
    });

    // Check for overlapping ranges
    for (let i = 0; i < safeValue.length; i++) {
      for (let j = i + 1; j < safeValue.length; j++) {
        const range1 = safeValue[i];
        const range2 = safeValue[j];
        
        if ((range1.min <= range2.max && range1.max >= range2.min)) {
          newErrors[i] = "Ranges should not overlap";
          newErrors[j] = "Ranges should not overlap";
        }
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [safeValue]);

  React.useEffect(() => {
    if (safeValue.length > 0) {
      validateRanges();
    }
  }, [safeValue, validateRanges]);

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <Label className="text-base font-medium">Range Score Configuration</Label>
          <p className="text-sm text-muted-foreground mt-1">
            Define score ranges for numeric inputs. Each range should have min/max values and a corresponding score.
          </p>
        </div>
        <Button
          type={ButtonType.PRIMARY}
          onClick={addRange}
          disabled={disabled}
          size="sm"
          icon={<Plus className="h-4 w-4" />}
          text="Add Range"
          className="gap-2 max-w-40"
        />
      </div>

      {safeValue.length === 0 && (
        <Card className="border-dashed">
          <CardContent className="flex items-center justify-center py-8">
            <div className="text-center">
              <AlertCircle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">No ranges configured</p>
              <p className="text-xs text-muted-foreground">Click &quot;Add Range&quot; to get started</p>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="space-y-3">
        {safeValue.map((range, index) => (
          <Card key={index} className={errors[index] ? "border-red-500" : ""}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm">Range {index + 1}</CardTitle>
                <ShadcnButton
                  onClick={() => removeRange(index)}
                  disabled={disabled}
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                </ShadcnButton>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-3 gap-3">
                <div className="space-y-1">
                  <Label className="text-xs">Min Value</Label>
                  <Input
                    type="number"
                    value={range.min}
                    onChange={(e) => updateRange(index, 'min', e.target.value)}
                    disabled={disabled}
                    step="any"
                  />
                </div>
                <div className="space-y-1">
                  <Label className="text-xs">Max Value</Label>
                  <Input
                    type="number"
                    value={range.max}
                    onChange={(e) => updateRange(index, 'max', e.target.value)}
                    disabled={disabled}
                    step="any"
                  />
                </div>
                <div className="space-y-1">
                  <Label className="text-xs">Score</Label>
                  <Input
                    type="number"
                    value={range.score}
                    onChange={(e) => updateRange(index, 'score', e.target.value)}
                    disabled={disabled}
                    step="any"
                  />
                </div>
              </div>
              
              {errors[index] && (
                <div className="flex items-center gap-2 text-red-500 text-xs">
                  <AlertCircle className="h-3 w-3" />
                  <span>{errors[index]}</span>
                </div>
              )}
              
              <div className="text-xs text-muted-foreground">
                Values from {range.min} to {range.max} will receive a score of {range.score}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {Object.keys(errors).length > 0 && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="py-3">
            <div className="flex items-center gap-2 text-red-600 text-sm">
              <AlertCircle className="h-4 w-4" />
              <span>Please fix the validation errors above</span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
