"use client";

import React, { useState } from "react";
import {
  FormWithQuestions,
  FormBuilderQuestion,
  FormCategory,
  QuestionWithOptions,
} from "@/types";
import { FORM_CATEGORIES } from "@/lib/constants/field-configs";
import { Button as ShadcnButton } from "@/components/ShadcnUI/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ShadcnUI/card";
import {
  Ta<PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ShadcnUI/tabs";
import { Badge } from "@/components/ShadcnUI/badge";
import { Separator } from "@/components/ShadcnUI/separator";
import { Plus, RefreshCw, Heart, Brain, Globe } from "lucide-react";
import Button, { ButtonType } from "@/components/shared/Button/Button";
import { QuestionBuilder } from "./QuestionBuilder";
import { QuestionList } from "./QuestionList";
import { useToast } from "@/contexts/ToastContext";

interface FormBuilderProps {
  forms: Record<FormCategory, FormWithQuestions | null>;
  onSaveQuestion: (
    formId: string,
    question: FormBuilderQuestion
  ) => Promise<void>;
  onUpdateQuestion: (
    questionId: string,
    question: Partial<FormBuilderQuestion>
  ) => Promise<void>;
  onDeleteQuestion: (questionId: string) => Promise<void>;
  onReorderQuestions: (
    formId: string,
    questionOrders: { id: string; order: number }[]
  ) => Promise<void>;
  onRefresh: () => Promise<void>;
  isLoading?: boolean;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const CATEGORY_ICONS: Record<FormCategory, React.ComponentType<any>> = {
  biological: Heart,
  lifestyle: Brain,
  environmental: Globe,
};

export function FormBuilder({
  forms,
  onSaveQuestion,
  onUpdateQuestion,
  onDeleteQuestion,
  onReorderQuestions,
  onRefresh,
  isLoading = false,
}: FormBuilderProps) {
  const [activeTab, setActiveTab] = useState<FormCategory>("biological");
  const [showQuestionBuilder, setShowQuestionBuilder] = useState(false);
  const [editingQuestion, setEditingQuestion] =
    useState<QuestionWithOptions | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  const currentForm = forms[activeTab];
  const currentQuestions = currentForm?.questions || [];
  const toast = useToast();

  const handleAddQuestion = () => {
    setEditingQuestion(null);
    setShowQuestionBuilder(true);
  };

  const handleEditQuestion = (question: QuestionWithOptions) => {
    setEditingQuestion(question);
    setShowQuestionBuilder(true);
  };

  // Helper function to map options
  const mapOptions = (
    options: QuestionWithOptions["options"]
  ): FormBuilderQuestion["options"] => {
    return options.map((opt) => ({
      id: opt.id,
      option_text: opt.option_text,
      value: opt.value || undefined,
      order: opt.order,
      score: typeof opt.score === 'number' ? opt.score : undefined,
    }));
  };

  // Helper function to parse scoring config
  const parseScoringConfig = (
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    config: any
  ): FormBuilderQuestion["scoring_config"] => {
    if (!config) return undefined;
    return typeof config === "string" ? JSON.parse(config) : config;
  };

  const mapQuestionData = (
    question: QuestionWithOptions
  ): FormBuilderQuestion => {
    return {
      id: question.id,
      question_text: question.question_text,
      help_text: question.help_text || undefined,
      is_mandatory: question.is_mandatory || false,
      field_type: question.field_type,
      placeholder: question.placeholder || undefined,
      min_value: question.min_value || undefined,
      max_value: question.max_value || undefined,
      step: question.step || undefined,
      unit: question.unit || undefined,
      order: question.order,
      track_ids: question.question_tracks?.map((qt) => qt.track_id) || [],
      options: mapOptions(question.options),
      scoring_mode: question.scoring_mode || "separate",
      collective_formula: question.collective_formula || undefined,
      parent_id: question.parent_id || undefined,
      scoring_type: question.scoring_type || undefined,
      scoring_config: parseScoringConfig(question.scoring_config),
      sub_questions:
        question.sub_questions?.map((sq) =>
          mapQuestionData(sq as QuestionWithOptions)
        ) || [],
      depends_on_option_id: question.depends_on_option_id || undefined,
    };
  };

  const handleSaveQuestion = async (question: FormBuilderQuestion) => {
    if (!currentForm) {
      toast.error("No form selected");
      return;
    }

    setIsSaving(true);
    try {
      if (editingQuestion) {
        // Handle sub-questions for updates
        const subQuestions = question.sub_questions || [];
        delete question.sub_questions; // Remove sub_questions from the main question data

        await onUpdateQuestion(editingQuestion.id, question);

        // Handle sub-questions separately
        for (const subQuestion of subQuestions) {
          if (subQuestion.id && subQuestion.id.startsWith("temp-")) {
            // New sub-question
            await onSaveQuestion(currentForm.id, {
              ...subQuestion,
              parent_id: editingQuestion.id,
            });
          } else if (subQuestion.id) {
            // Existing sub-question
            await onUpdateQuestion(subQuestion.id, subQuestion);
          }
        }

        toast.success("Question updated successfully");
      } else {
        // Handle sub-questions for new questions
        delete question.sub_questions; // Remove sub_questions from the main question data

        await onSaveQuestion(currentForm.id, question);

        toast.success("Question added successfully");
      }
      setShowQuestionBuilder(false);
      setEditingQuestion(null);
    } catch (error) {
      toast.error(
        editingQuestion ? "Failed to update question" : "Failed to add question"
      );
      console.error("Error saving question:", error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleDeleteQuestion = async (questionId: string) => {
    setIsSaving(true);
    try {
      await onDeleteQuestion(questionId);
      toast.success("Question deleted successfully");
    } catch (error) {
      toast.error("Failed to delete question");
      console.error("Error deleting question:", error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleReorderQuestions = async (
    questionOrders: { id: string; order: number }[]
  ) => {
    if (!currentForm) return;

    try {
      await onReorderQuestions(currentForm.id, questionOrders);
      toast.success("Questions reordered successfully");
    } catch (error) {
      toast.error("Failed to reorder questions");
      console.error("Error reordering questions:", error);
    }
  };

  const handleCancel = () => {
    setShowQuestionBuilder(false);
    setEditingQuestion(null);
  };

  const handleRefresh = async () => {
    try {
      await onRefresh();
      toast.success("Forms refreshed successfully");
    } catch (error) {
      toast.error("Failed to refresh forms");
      console.error("Error refreshing forms:", error);
    }
  };

  if (showQuestionBuilder) {
    return (
      <QuestionBuilder
        question={
          editingQuestion ? mapQuestionData(editingQuestion) : undefined
        }
        onSave={handleSaveQuestion}
        onCancel={handleCancel}
        isEditing={!!editingQuestion}
        allQuestions={currentQuestions.map(mapQuestionData)}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Fertility Meter Questions</h1>
          <p className="text-muted-foreground mt-1">
            Configure and manage questions for the fertility assessment
          </p>
        </div>
        <div className="flex items-center gap-2">
          <ShadcnButton
            variant="ghost"
            onClick={handleRefresh}
            disabled={isLoading}
            className="gap-2"
          >
            <RefreshCw
              className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`}
            />
            Refresh
          </ShadcnButton>
        </div>
      </div>

      {/* Form Tabs */}
      <Tabs
        value={activeTab}
        onValueChange={(value) => setActiveTab(value as FormCategory)}
      >
        <TabsList className="grid w-full grid-cols-3">
          {Object.entries(FORM_CATEGORIES).map(([key, config]) => {
            const categoryKey = key as FormCategory;
            const Icon = CATEGORY_ICONS[categoryKey];
            const form = forms[categoryKey];
            const questionCount = form?.questions.length || 0;

            return (
              <TabsTrigger key={key} value={key} className="gap-2">
                <Icon className="h-4 w-4" />
                <span className="hidden sm:inline">{config.name}</span>
                <Badge variant="secondary" className="ml-1">
                  {questionCount}
                </Badge>
              </TabsTrigger>
            );
          })}
        </TabsList>

        {Object.entries(FORM_CATEGORIES).map(([key, config]) => {
          const categoryKey = key as FormCategory;
          const form = forms[categoryKey];
          const Icon = CATEGORY_ICONS[categoryKey];

          return (
            <TabsContent key={key} value={key} className="space-y-4">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-primary/10 rounded-lg">
                        <Icon className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <CardTitle>{config.name}</CardTitle>
                        <p className="text-sm text-muted-foreground mt-1">
                          {config.description}
                        </p>
                      </div>
                    </div>
                    <Button
                      type={ButtonType.PRIMARY}
                      onClick={handleAddQuestion}
                      disabled={!form || isSaving}
                      className="gap-2 max-w-40"
                      size="sm"
                      text=" Add Question"
                      icon={<Plus className="h-4 w-4" />}
                    />
                  </div>
                </CardHeader>

                {form && (
                  <>
                    <Separator />
                    <CardContent className="pt-6">
                      <QuestionList
                        questions={currentQuestions}
                        onEditQuestion={handleEditQuestion}
                        onDeleteQuestion={handleDeleteQuestion}
                        onReorderQuestions={handleReorderQuestions}
                        isLoading={isLoading || isSaving}
                      />
                    </CardContent>
                  </>
                )}
              </Card>

              {!form && (
                <Card>
                  <CardContent className="p-8 text-center">
                    <p className="text-muted-foreground">
                      Form not found. Please refresh the page or contact
                      support.
                    </p>
                  </CardContent>
                </Card>
              )}
            </TabsContent>
          );
        })}
      </Tabs>
    </div>
  );
}
