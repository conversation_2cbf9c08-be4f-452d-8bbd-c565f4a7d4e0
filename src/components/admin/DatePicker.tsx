"use client";
import React from "react";
import { Input } from "@/components/ShadcnUI/input";
import { Label } from "@/components/ShadcnUI/label";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

interface DatePickerProps {
  date: Date | undefined;
  onDateChange: (date: Date | undefined) => void;
  className?: string;
}

export default function DatePicker({
  date,
  onDateChange,
  className,
}: DatePickerProps) {
  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value) {
      onDateChange(new Date(value));
    } else {
      onDateChange(undefined);
    }
  };

  const getDateInputValue = (date: Date | undefined) => {
    if (!date) return "";
    return format(date, "yyyy-MM-dd");
  };

  return (
    <div className={cn("space-y-2", className)}>
      <Label htmlFor="date">Date *</Label>
      <Input
        id="date"
        type="date"
        value={getDateInputValue(date)}
        onChange={handleDateChange}
        min={format(new Date(), "yyyy-MM-dd")}
        className="w-full"
      />
      {date && (
        <div className="text-sm text-muted-foreground">
          {format(date, "PPP")}
        </div>
      )}
    </div>
  );
}
