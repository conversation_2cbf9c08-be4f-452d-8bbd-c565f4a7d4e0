"use client";

import React from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { cn } from "@/lib/utils";

interface SortableHeaderClientProps {
  children: React.ReactNode;
  sortKey: string;
  currentSort: string;
  currentDirection: 'asc' | 'desc';
}

export function SortableHeaderClient({
  children,
  sortKey,
  currentSort,
  currentDirection
}: SortableHeaderClientProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const handleSort = () => {
    const newDirection = currentSort === sortKey && currentDirection === 'asc' ? 'desc' : 'asc';
    const params = new URLSearchParams(searchParams.toString());
    
    params.set('sort_by', sortKey);
    params.set('sort_direction', newDirection);
    
    router.push(`?${params.toString()}`);
  };

  const isActive = currentSort === sortKey;
  
  return (
    <button
      onClick={handleSort}
      className={cn(
        "flex items-center gap-1 hover:text-foreground transition-colors",
        isActive ? "text-foreground" : "text-muted-foreground"
      )}
    >
      {children}
      <div className="flex flex-col">
        <div className={cn(
          "h-0 w-0 border-l-[3px] border-r-[3px] border-b-[3px] border-transparent",
          isActive && currentDirection === 'asc' ? "border-b-current" : "border-b-muted-foreground"
        )} />
        <div className={cn(
          "h-0 w-0 border-l-[3px] border-r-[3px] border-t-[3px] border-transparent",
          isActive && currentDirection === 'desc' ? "border-t-current" : "border-t-muted-foreground"
        )} />
      </div>
    </button>
  );
}
