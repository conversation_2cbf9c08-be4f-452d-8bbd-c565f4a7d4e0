import React from "react";
import { Button } from "@/components/ShadcnUI/button";
import type { RoleWithPermissions } from "@/types/roles-permissions";
import { togglePermission } from "@/lib/services/roles-permissions.service";

interface PermissionToggleProps {
  role: RoleWithPermissions;
  resource: string;
  action: string;
  hasPermission: boolean;
}

export default function PermissionToggle({ 
  role, 
  resource, 
  action, 
  hasPermission 
}: PermissionToggleProps) {
  return (
    <form action={togglePermission} style={{ display: 'inline' }}>
      <input type="hidden" name="roleId" value={role.id.toString()} />
      <input type="hidden" name="resource" value={resource} />
      <input type="hidden" name="action" value={action} />
      <input type="hidden" name="currentValue" value={hasPermission.toString()} />
      <Button
        type="submit"
        variant={hasPermission ? "default" : "outline"}
        size="sm"
        className="h-6 w-6 p-0 text-xs"
        title={`${hasPermission ? 'Remove' : 'Grant'} ${action} permission for ${resource}`}
      >
        {action.charAt(0).toUpperCase()}
      </Button>
    </form>
  );
} 