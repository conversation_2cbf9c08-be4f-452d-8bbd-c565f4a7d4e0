import React from "react";
import {
  Table,
  TableBody,
  Table<PERSON>ell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ShadcnUI/table";
import { Checkbox } from "@/components/ShadcnUI/checkbox";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ShadcnUI/card";
import { AVAILABLE_RESOURCES, type PermissionFormData } from "@/types/roles-permissions";

interface PermissionsMatrixProps {
  permissions: PermissionFormData[];
  onChange: (permissions: PermissionFormData[]) => void;
}

export function PermissionsMatrix({ permissions, onChange }: PermissionsMatrixProps) {
  const getPermissionForResource = (resource: string): PermissionFormData => {
    return permissions.find(p => p.resource === resource) || {
      resource,
      can_create: false,
      can_read: false,
      can_update: false,
      can_delete: false,
    };
  };

  const updatePermission = (resource: string, field: keyof PermissionFormData, value: boolean) => {
    const updatedPermissions = [...permissions];
    const existingIndex = updatedPermissions.findIndex(p => p.resource === resource);
    
    if (existingIndex >= 0) {
      updatedPermissions[existingIndex] = {
        ...updatedPermissions[existingIndex],
        [field]: value,
      };
    } else {
      updatedPermissions.push({
        resource,
        can_create: field === 'can_create' ? value : false,
        can_read: field === 'can_read' ? value : false,
        can_update: field === 'can_update' ? value : false,
        can_delete: field === 'can_delete' ? value : false,
      });
    }

    // Remove permissions with no actions selected
    const filteredPermissions = updatedPermissions.filter(p => 
      p.can_create || p.can_read || p.can_update || p.can_delete
    );

    onChange(filteredPermissions);
  };

  const toggleAllForResource = (resource: string, checked: boolean) => {
    updatePermission(resource, 'can_create', checked);
    updatePermission(resource, 'can_read', checked);
    updatePermission(resource, 'can_update', checked);
    updatePermission(resource, 'can_delete', checked);
  };

  const isAllSelectedForResource = (resource: string): boolean => {
    const permission = getPermissionForResource(resource);
    return permission.can_create && permission.can_read && permission.can_update && permission.can_delete;
  };

  const isSomeSelectedForResource = (resource: string): boolean => {
    const permission = getPermissionForResource(resource);
    return permission.can_create || permission.can_read || permission.can_update || permission.can_delete;
  };

  const getResourceDisplayName = (resource: string): string => {
    return resource.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const getResourceDescription = (resource: string): string => {
    const descriptions: Record<string, string> = {
      profiles: "User profile information and settings",
      ivf_scores: "IVF scoring data and assessments",
      roles: "System roles and role management",
      permissions: "Permission definitions and assignments",
      users: "User accounts and authentication",
      guest_sessions: "Guest user sessions and temporary data",
      otps: "One-time passwords and verification codes",
    };
    return descriptions[resource] || "System resource";
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Resource Permissions</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[200px]">Resource</TableHead>
                <TableHead className="text-center w-[80px]">All</TableHead>
                <TableHead className="text-center w-[80px]">Create</TableHead>
                <TableHead className="text-center w-[80px]">Read</TableHead>
                <TableHead className="text-center w-[80px]">Update</TableHead>
                <TableHead className="text-center w-[80px]">Delete</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {AVAILABLE_RESOURCES.map((resource) => {
                const permission = getPermissionForResource(resource);
                const allSelected = isAllSelectedForResource(resource);
                const someSelected = isSomeSelectedForResource(resource);

                return (
                  <TableRow key={resource}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{getResourceDisplayName(resource)}</div>
                        <div className="text-sm text-muted-foreground">
                          {getResourceDescription(resource)}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="text-center">
                      <Checkbox
                        checked={allSelected}
                        onCheckedChange={(checked) => 
                          toggleAllForResource(resource, checked as boolean)
                        }
                        className={someSelected && !allSelected ? "data-[state=checked]:bg-orange-500" : ""}
                      />
                    </TableCell>
                    <TableCell className="text-center">
                      <Checkbox
                        checked={permission.can_create}
                        onCheckedChange={(checked) => 
                          updatePermission(resource, 'can_create', checked as boolean)
                        }
                      />
                    </TableCell>
                    <TableCell className="text-center">
                      <Checkbox
                        checked={permission.can_read}
                        onCheckedChange={(checked) => 
                          updatePermission(resource, 'can_read', checked as boolean)
                        }
                      />
                    </TableCell>
                    <TableCell className="text-center">
                      <Checkbox
                        checked={permission.can_update}
                        onCheckedChange={(checked) => 
                          updatePermission(resource, 'can_update', checked as boolean)
                        }
                      />
                    </TableCell>
                    <TableCell className="text-center">
                      <Checkbox
                        checked={permission.can_delete}
                        onCheckedChange={(checked) => 
                          updatePermission(resource, 'can_delete', checked as boolean)
                        }
                      />
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
        
        <div className="mt-4 text-sm text-muted-foreground">
          <p><strong>Create:</strong> Can create new records</p>
          <p><strong>Read:</strong> Can view and list records</p>
          <p><strong>Update:</strong> Can modify existing records</p>
          <p><strong>Delete:</strong> Can remove records</p>
        </div>
      </CardContent>
    </Card>
  );
}
