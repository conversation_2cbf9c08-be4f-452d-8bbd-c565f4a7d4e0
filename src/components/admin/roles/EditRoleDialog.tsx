import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON>alogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ShadcnUI/dialog";
import { <PERSON><PERSON> } from "@/components/ShadcnUI/button";
import { Input } from "@/components/ShadcnUI/input";
import { Label } from "@/components/ShadcnUI/label";
import { Textarea } from "@/components/ShadcnUI/textarea";
import { PermissionsMatrix } from "./PermissionsMatrix";
import type { RoleWithPermissions, UpdateRoleRequest, PermissionFormData } from "@/types/roles-permissions";

interface EditRoleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  role: RoleWithPermissions | null;
  onSubmit: (roleData: UpdateRoleRequest) => Promise<void>;
}

export function EditRoleDialog({ open, onOpenChange, role, onSubmit }: EditRoleDialogProps) {
  const [formData, setFormData] = useState<UpdateRoleRequest>({
    id: "",
    name: "",
    description: "",
    permissions: [],
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Update form data when role changes
  useEffect(() => {
    if (role) {
      const permissionsData: PermissionFormData[] = role.permissions.map(p => ({
        resource: p.resource!,
        can_create: p.can_create,
        can_read: p.can_read,
        can_update: p.can_update,
        can_delete: p.can_delete,
        condition: (p.condition || undefined) as string | undefined,
      }));

      setFormData({
        id: role.id.toString(),
        name: role.name,
        description: role.description || "",
        permissions: permissionsData,
      } as UpdateRoleRequest);
    }
  }, [role]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrors({});

    // Validation
    const newErrors: Record<string, string> = {};
    if (!formData.name?.trim()) {
      newErrors.name = "Role name is required";
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error("Failed to update role:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePermissionsChange = (permissions: PermissionFormData[]) => {
    setFormData(prev => ({ ...prev, permissions }));
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setErrors({});
      onOpenChange(false);
    }
  };

  if (!role) return null;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="!max-w-[1100px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Role: {role.name}</DialogTitle>
          <DialogDescription>
            Update the role details and permissions.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid gap-4">
            <div className="space-y-2">
              <Label htmlFor="edit-name">Role Name *</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter role name (e.g., Admin, Editor, Viewer)"
                className={errors.name ? "border-destructive" : ""}
              />
              {errors.name && (
                <p className="text-sm text-destructive">{errors.name}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-description">Description</Label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe the role and its purpose"
                rows={3}
              />
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <Label>Permissions</Label>
              <p className="text-sm text-muted-foreground">
                Configure what actions this role can perform on each resource.
              </p>
            </div>
            <PermissionsMatrix
              permissions={formData.permissions || []}
              onChange={handlePermissionsChange}
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Updating..." : "Update Role"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
