import React from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDes<PERSON>,
  Di<PERSON><PERSON>ooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ShadcnUI/dialog";
import { Button } from "@/components/ShadcnUI/button";
import { Badge } from "@/components/ShadcnUI/badge";
import { AlertTriangle } from "lucide-react";
import type { RoleWithPermissions } from "@/types/roles-permissions";

interface DeleteRoleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  role: RoleWithPermissions | null;
  onConfirm: () => Promise<void>;
}

export function DeleteRoleDialog({ open, onOpenChange, role, onConfirm }: DeleteRoleDialogProps) {
  const [isDeleting, setIsDeleting] = React.useState(false);

  const handleConfirm = async () => {
    setIsDeleting(true);
    try {
      await onConfirm();
    } catch (error) {
      console.error("Failed to delete role:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  if (!role) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="!max-w-[1100px]">
        <DialogHeader>
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            <DialogTitle>Delete Role</DialogTitle>
          </div>
          <DialogDescription>
            This action cannot be undone. This will permanently delete the role and remove all associated permissions.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="rounded-lg border p-4 bg-muted/50">
            <h4 className="font-medium mb-2">Role Details</h4>
            <div className="space-y-2 text-sm">
              <div>
                <span className="font-medium">Name:</span> {role.name}
              </div>
              {role.description && (
                <div>
                  <span className="font-medium">Description:</span> {role.description}
                </div>
              )}
              <div>
                <span className="font-medium">Permissions:</span> {role.permissions.length} configured
              </div>
            </div>
          </div>

          {role.permissions.length > 0 && (
            <div>
              <h5 className="font-medium mb-2 text-sm">Permissions to be deleted:</h5>
              <div className="flex flex-wrap gap-1">
                {role.permissions.map((permission, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {permission.resource}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          <div className="rounded-lg border border-destructive/20 bg-destructive/5 p-3">
            <p className="text-sm text-destructive">
              <strong>Warning:</strong> If this role is assigned to any users, you will not be able to delete it. 
              Please reassign users to different roles first.
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isDeleting}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleConfirm}
            disabled={isDeleting}
          >
            {isDeleting ? "Deleting..." : "Delete Role"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
