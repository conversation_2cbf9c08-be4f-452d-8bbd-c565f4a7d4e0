import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ShadcnUI/table";
import { Button } from "@/components/ShadcnUI/button";
import { Badge } from "@/components/ShadcnUI/badge";
import { Edit, Trash2, Shield } from "lucide-react";
import type { RoleWithPermissions, Permission } from "@/types/roles-permissions";

interface RolesTableProps {
  roles: RoleWithPermissions[];
  loading: boolean;
  onEdit: (role: RoleWithPermissions) => void;
  onDelete: (role: RoleWithPermissions) => void;
}

export function RolesTable({ roles, loading, onEdit, onDelete }: RolesTableProps) {
  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (roles.length === 0) {
    return (
      <div className="text-center py-8">
        <Shield className="mx-auto h-12 w-12 text-muted-foreground" />
        <h3 className="mt-2 text-sm font-semibold text-muted-foreground">No roles found</h3>
        <p className="mt-1 text-sm text-muted-foreground">
          Get started by creating a new role.
        </p>
      </div>
    );
  }

  const formatPermissions = (permissions: Permission[]) => {
    const resourceGroups = permissions.reduce((acc, permission) => {
      // Skip permissions with null resource
      if (!permission.resource) return acc;
      
      if (!acc[permission.resource]) {
        acc[permission.resource] = [];
      }
      
      if (permission.can_create) acc[permission.resource].push('C');
      if (permission.can_read) acc[permission.resource].push('R');
      if (permission.can_update) acc[permission.resource].push('U');
      if (permission.can_delete) acc[permission.resource].push('D');
      
      return acc;
    }, {} as Record<string, string[]>);

    return Object.entries(resourceGroups).map(([resource, actions]) => (
      <Badge key={resource} variant="secondary" className="mr-1 mb-1">
        {resource}: {actions.join('')}
      </Badge>
    ));
  };

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Role Name</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Permissions</TableHead>
            <TableHead>Created</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {roles.map((role) => (
            <TableRow key={role.id.toString()}>
              <TableCell className="font-medium">
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4 text-muted-foreground" />
                  {role.name}
                </div>
              </TableCell>
              <TableCell className="max-w-xs">
                <div className="truncate" title={role.description || ""}>
                  {role.description || "No description"}
                </div>
              </TableCell>
              <TableCell>
                <div className="flex flex-wrap gap-1 max-w-md">
                  {role.permissions.length > 0 ? (
                    formatPermissions(role.permissions)
                  ) : (
                    <Badge variant="outline">No permissions</Badge>
                  )}
                </div>
              </TableCell>
              <TableCell className="text-muted-foreground">
                {new Date(role.created_at).toLocaleDateString()}
              </TableCell>
              <TableCell className="text-right">
                <div className="flex items-center justify-end gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onEdit(role)}
                    className="h-8 w-8 p-0"
                  >
                    <Edit className="h-4 w-4" />
                    <span className="sr-only">Edit role</span>
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onDelete(role)}
                    className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                  >
                    <Trash2 className="h-4 w-4" />
                    <span className="sr-only">Delete role</span>
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
