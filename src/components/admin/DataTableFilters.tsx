"use client";

import React, { useState } from "react";
import { Button } from "@/components/ShadcnUI/button";
import { Input } from "@/components/ShadcnUI/input";
import { Label } from "@/components/ShadcnUI/label";
import { Filter, ChevronDown, ChevronUp } from "lucide-react";
import { cn } from "@/lib/utils";
import { FilterForm } from "./FilterForm";
import { ClearFiltersButton } from "./ClearFiltersButton";

export interface FilterState {
  search?: string;
  dateFrom?: string;
  dateTo?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}

interface DataTableFiltersProps {
  filters: FilterState;
  onFiltersChange?: (filters: FilterState) => void;
  onClearFilters?: () => void;
  additionalFilters?: React.ReactNode;
  searchPlaceholder?: string;
}

export function DataTableFilters({
  filters,
  onFiltersChange,
  additionalFilters,
  searchPlaceholder = "Search..."
}: DataTableFiltersProps) {
  const [isOpen, setIsOpen] = useState(false);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleFilterChange = (key: string, value: any) => {
    if (onFiltersChange) {
      onFiltersChange({
        ...filters,
        [key]: value
      });
    }
  };

  const hasActiveFilters = Object.values(filters).some(value => 
    value !== undefined && value !== "" && value !== null
  );

  return (
    <div className="relative">
      <Button 
        variant="outline" 
        className="gap-2"
        onClick={() => setIsOpen(!isOpen)}
      >
        <Filter className="h-4 w-4" />
        Filters
        {hasActiveFilters && (
          <div className="h-2 w-2 bg-primary rounded-full" />
        )}
        {isOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
      </Button>

      {isOpen && (
        <div className="absolute top-full right-0 mt-2 w-80 bg-background border rounded-lg shadow-lg p-4 z-50">
          <FilterForm>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">Filters</h4>
                {hasActiveFilters && (
                  <ClearFiltersButton className="h-auto p-1 text-muted-foreground hover:text-foreground" />
                )}
              </div>

              {/* Search */}
              <div className="space-y-2">
                <Label htmlFor="search">Search</Label>
                <Input
                  id="search"
                  name="search"
                  placeholder={searchPlaceholder}
                  defaultValue={filters.search || ""}
                  onChange={(e) => handleFilterChange("search", e.target.value)}
                />
              </div>

              {/* Date Range */}
              <div className="space-y-2">
                <Label>Date Range</Label>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <Label htmlFor="dateFrom" className="text-xs text-muted-foreground">
                      From
                    </Label>
                    <Input
                      id="dateFrom"
                      name="date_from"
                      type="date"
                      defaultValue={filters.dateFrom || ""}
                      onChange={(e) => handleFilterChange("dateFrom", e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="dateTo" className="text-xs text-muted-foreground">
                      To
                    </Label>
                    <Input
                      id="dateTo"
                      name="date_to"
                      type="date"
                      defaultValue={filters.dateTo || ""}
                      onChange={(e) => handleFilterChange("dateTo", e.target.value)}
                    />
                  </div>
                </div>
              </div>

              {/* Additional Filters */}
              {additionalFilters}

              <div className="flex gap-2 pt-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setIsOpen(false)}
                  className="flex-1"
                >
                  Close
                </Button>
                <Button
                  type="submit"
                  size="sm"
                  className="flex-1"
                >
                  Apply Filters
                </Button>
              </div>
            </div>
          </FilterForm>
        </div>
      )}
    </div>
  );
}

export function SortableHeader({
  children,
  sortKey,
  currentSort,
  currentDirection,
  onSort
}: {
  children: React.ReactNode;
  sortKey: string;
  currentSort: string;
  currentDirection: 'asc' | 'desc';
  onSort: (key: string) => void;
}) {
  const isActive = currentSort === sortKey;
  
  return (
    <button
      onClick={() => onSort(sortKey)}
      className={cn(
        "flex items-center gap-1 hover:text-foreground transition-colors",
        isActive ? "text-foreground" : "text-muted-foreground"
      )}
    >
      {children}
      <div className="flex flex-col">
        <div className={cn(
          "h-0 w-0 border-l-[3px] border-r-[3px] border-b-[3px] border-transparent",
          isActive && currentDirection === 'asc' ? "border-b-current" : "border-b-muted-foreground"
        )} />
        <div className={cn(
          "h-0 w-0 border-l-[3px] border-r-[3px] border-t-[3px] border-transparent",
          isActive && currentDirection === 'desc' ? "border-t-current" : "border-t-muted-foreground"
        )} />
      </div>
    </button>
  );
}
