"use client";
import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ShadcnUI/dialog";
import { Button } from "@/components/ShadcnUI/button";
import { Input } from "@/components/ShadcnUI/input";
import { Label } from "@/components/ShadcnUI/label";
import { Calendar } from "lucide-react";
import DatePicker from "@/components/admin/DatePicker";
import TimeSlotPicker from "@/components/admin/TimeSlotPicker";

interface TimeSlot {
  id: string;
  startTime: string;
  endTime: string;
}

interface Doctor {
  id: number;
  profile: {
    id?: number;
    display_name: string;
    email: string;
    phone?: string;
    auth_id?: string;
  };
  specialization_name?: string;
  auth_id?: string; // For CurrentDoctor compatibility
}

interface CreateUnavailabilityDialogProps {
  doctor: Doctor | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUnavailabilityCreated: () => void;
}

export default function CreateUnavailabilityDialog({
  doctor,
  open,
  onOpenChange,
  onUnavailabilityCreated,
}: CreateUnavailabilityDialogProps) {
  const [loading, setLoading] = useState(false);
  const [date, setDate] = useState<Date | undefined>(undefined);
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [reasonForUnavailability, setReasonForUnavailability] = useState("");
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);

  const resetForm = () => {
    setDate(undefined);
    setTimeSlots([]);
    setReasonForUnavailability("");
    setMessage(null);
  };

  const createUnavailabilities = async () => {
    if (!doctor || !date) {
      setMessage({ type: 'error', text: 'Please select a date' });
      return;
    }

    if (timeSlots.length === 0) {
      setMessage({ type: 'error', text: 'Please add at least one time slot' });
      return;
    }

    try {
      setLoading(true);
      setMessage(null);

      const unavailabilityData = timeSlots.map(slot => ({
        date: date.toISOString().split('T')[0],
        start_time: slot.startTime,
        end_time: slot.endTime,
        reason: reasonForUnavailability || null,
        notes: null,
      }));

      const requestBody = {
        doctorId: doctor.id,
        unavailabilities: unavailabilityData
      };

      const response = await fetch('/api/v1/admin/unavailabilities', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      const data = await response.json();
      
      if (data.success) {
        setMessage({ type: 'success', text: 'Unavailabilities created successfully' });
        resetForm();
        onUnavailabilityCreated();
        // Close dialog after a short delay to show success message
        setTimeout(() => {
          onOpenChange(false);
        }, 1500);
      } else {
        setMessage({ type: 'error', text: data.error || 'Failed to create unavailabilities' });
      }
    } catch (error) {
      console.error('Error creating unavailabilities:', error);
      setMessage({ type: 'error', text: 'Failed to create unavailabilities' });
    } finally {
      setLoading(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      resetForm();
    }
    onOpenChange(newOpen);
  };

  if (!doctor) return null;

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="!max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Create New Unavailability - {doctor.profile.display_name}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Message Display */}
          {message && (
            <div className={`p-3 rounded-md ${
              message.type === 'success' 
                ? 'bg-green-50 text-green-800 border border-green-200' 
                : 'bg-red-50 text-red-800 border border-red-200'
            }`}>
              {message.text}
            </div>
          )}

          {/* Date Range Picker */}
          <div className="space-y-2">
            <Label>Select Date</Label>
            <DatePicker
              date={date}
              onDateChange={setDate}
            />
          </div>

          {/* Reason for Unavailability */}
          <div className="space-y-2">
            <Label>Reason for Unavailability (Optional)</Label>
            <Input
              type="text"
              placeholder="e.g., Vacation, Conference, Personal leave..."
              value={reasonForUnavailability}
              onChange={(e) => setReasonForUnavailability(e.target.value)}
              className="w-full"
            />
          </div>

          {/* Time Slots */}
          <div className="space-y-2">
            <Label>Time Slots</Label>
            <TimeSlotPicker
              timeSlots={timeSlots}
              onTimeSlotsChange={setTimeSlots}
              selectedDate={date}
            />
          </div>

          {/* Instructions */}
          <div className="bg-gray-50 p-3 rounded-md">
            <div className="text-sm text-gray-600">
              <h4 className="font-medium mb-2">Instructions:</h4>
              <ul className="list-disc list-inside space-y-1 text-xs">
                <li>Choose a date for the unavailability</li>
                <li>Add a reason for the unavailability (optional)</li>
                <li>Add time slots when the doctor is unavailable or mark as unavailable for the entire day</li>
                <li>Click Create Unavailabilities to save</li>
              </ul>
            </div>
          </div>
        </div>
        
        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={() => handleOpenChange(false)}>
            Cancel
          </Button>
          <Button 
            onClick={createUnavailabilities} 
            disabled={loading || !date}
          >
            {loading ? "Creating..." : "Create Unavailabilities"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
