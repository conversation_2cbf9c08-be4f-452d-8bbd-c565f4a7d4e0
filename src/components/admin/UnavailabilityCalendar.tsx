"use client";
import React, { useState } from "react";
import { format, isSameDay, addMonths, subMonths } from "date-fns";
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon, Clock, MapPin, FileText, Edit } from "lucide-react";
import ReactCalendar from "react-calendar";
import { But<PERSON> } from "@/components/ShadcnUI/button";
import { Card, CardContent } from "@/components/ShadcnUI/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ShadcnUI/dialog";
import { Input } from "@/components/ShadcnUI/input";
import { Label } from "@/components/ShadcnUI/label";
import DatePicker from "@/components/admin/DatePicker";
import TimeSlotPicker from "@/components/admin/TimeSlotPicker";

interface TimeSlot {
  id: string;
  startTime: string;
  endTime: string;
}

interface Unavailability {
  id: number;
  doctor_id: number;
  clinic_id?: number;
  date: string;
  start_time: string;
  end_time: string;
  duration: number;
  reason?: string;
  notes?: string;
  clinic?: {
    id: number;
    clinic_name: string;
    address?: string;
  };
}

interface UnavailabilityCalendarProps {
  unavailabilities: Unavailability[];
  onMonthChange?: (date: Date) => void;
}

export default function UnavailabilityCalendar({
  unavailabilities,
  onMonthChange
}: UnavailabilityCalendarProps) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [selectedUnavailability, setSelectedUnavailability] = useState<Unavailability | null>(null);
  const [editDate, setEditDate] = useState<Date | undefined>(undefined);
  const [editTimeSlots, setEditTimeSlots] = useState<TimeSlot[]>([]);
  const [editReason, setEditReason] = useState("");
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = direction === 'prev' ? subMonths(currentDate, 1) : addMonths(currentDate, 1);
    setCurrentDate(newDate);
    onMonthChange?.(newDate);
  };

  const getUnavailabilitiesForDate = (date: Date) => {
    return unavailabilities.filter(unavailability =>
      isSameDay(new Date(unavailability.date), date)
    );
  };



  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  const isFullDayUnavailable = (unavailabilities: Unavailability[]) => {
    return unavailabilities.some(u => u.start_time === "00:00" && u.end_time === "23:59");
  };

  const selectedDateUnavailabilities = selectedDate ? getUnavailabilitiesForDate(selectedDate) : [];

  const handleEditClick = (unavailability: Unavailability) => {
    setSelectedUnavailability(unavailability);
    setEditDate(new Date(unavailability.date));
    
    // Convert all unavailabilities for this date to time slots
    const dateUnavailabilities = getUnavailabilitiesForDate(new Date(unavailability.date));
    const timeSlots = dateUnavailabilities.map(u => ({
      id: `edit-${u.id}`,
      startTime: u.start_time,
      endTime: u.end_time
    }));
    setEditTimeSlots(timeSlots);
    
    // Use the reason from the first unavailability (assuming they're all the same)
    setEditReason(unavailability.reason || '');
    setShowEditDialog(true);
    setMessage(null);
  };

  const handleEditSubmit = async () => {
    if (!selectedUnavailability || !editDate) {
      setMessage({ type: 'error', text: 'Please select a date' });
      return;
    }

    if (editTimeSlots.length === 0) {
      setMessage({ type: 'error', text: 'Please add at least one time slot' });
      return;
    }

    try {
      setLoading(true);
      setMessage(null);

      // Get all existing unavailabilities for this date
      const dateUnavailabilities = getUnavailabilitiesForDate(editDate);
      
      // Delete all existing unavailabilities for this date
      for (const unavailability of dateUnavailabilities) {
        await fetch(`/api/v1/admin/unavailabilities/${unavailability.id}`, {
          method: 'DELETE',
        });
      }

      // Create new unavailabilities with the updated time slots
      const unavailabilityData = editTimeSlots.map(slot => ({
        date: editDate.toISOString().split('T')[0],
        start_time: slot.startTime,
        end_time: slot.endTime,
        reason: editReason || null,
        notes: null,
      }));

      const requestBody = {
        doctorId: selectedUnavailability.doctor_id,
        unavailabilities: unavailabilityData
      };

      const response = await fetch('/api/v1/admin/unavailabilities', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      const data = await response.json();
      
      if (data.success) {
        setMessage({ type: 'success', text: 'Unavailabilities updated successfully' });
        // Close dialog after a short delay to show success message
        setTimeout(() => {
          setShowEditDialog(false);
          setSelectedUnavailability(null);
          // Refresh the page or trigger a re-fetch of unavailabilities
          window.location.reload();
        }, 1500);
      } else {
        setMessage({ type: 'error', text: data.error || 'Failed to update unavailabilities' });
      }
    } catch (error) {
      console.error('Error updating unavailabilities:', error);
      setMessage({ type: 'error', text: 'Failed to update unavailabilities' });
    } finally {
      setLoading(false);
    }
  };

  const handleEditDialogClose = () => {
    setShowEditDialog(false);
    setSelectedUnavailability(null);
    setEditDate(undefined);
    setEditTimeSlots([]);
    setEditReason("");
    setMessage(null);
  };

  return (
    <div className="space-y-4 p-8">
      {/* Calendar Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <CalendarIcon className="h-5 w-5 text-blue-600" />
          <h3 className="text-lg font-semibold">
            {format(currentDate, 'MMMM yyyy')}
          </h3>
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigateMonth('prev')}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigateMonth('next')}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Calendar */}
      <ReactCalendar
        className="w-full border-0 [&_.react-calendar__navigation]:hidden [&_.react-calendar__viewContainer]:w-full [&_.react-calendar__month-view]:w-full [&_.react-calendar__month-view__days]:w-full [&_.react-calendar__tile]:w-full [&_.react-calendar__tile]:h-12 [&_.react-calendar__tile]:flex [&_.react-calendar__tile]:items-center [&_.react-calendar__tile]:justify-center [&_.react-calendar__tile]:relative"
        view="month"
        activeStartDate={currentDate}
        onActiveStartDateChange={({ activeStartDate }) => {
          if (activeStartDate) {
            setCurrentDate(activeStartDate);
            onMonthChange?.(activeStartDate);
          }
        }}
        onClickDay={(value) => {
          const date = value as Date;
          const dayUnavailabilities = getUnavailabilitiesForDate(date);
          if (dayUnavailabilities.length > 0) {
            setSelectedDate(date);
            setShowDetails(true);
          }
        }}
        tileClassName={({ date, view }) => {
          if (view !== 'month') return undefined;
          const dayUnavailabilities = getUnavailabilitiesForDate(date);
          if (dayUnavailabilities.length === 0) return undefined;
          const full = isFullDayUnavailable(dayUnavailabilities);
          return full ? '!bg-red-500 text-white hover:!bg-red-600' : '!bg-red-200 text-red-800 hover:!bg-red-300';
        }}
        tileContent={({ date, view }) => {
          if (view !== 'month') return null;
          const dayUnavailabilities = getUnavailabilitiesForDate(date);
          if (dayUnavailabilities.length === 0) return null;
          const full = isFullDayUnavailable(dayUnavailabilities);
          return (
            <div className="absolute bottom-1 right-1">
              <div className={`w-2 h-2 rounded-full ${full ? 'bg-white' : 'bg-red-600'}`} />
            </div>
          );
        }}
      />

      {/* Legend */}
      <div className="flex items-center justify-center gap-6 text-sm">
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-red-200 rounded border" />
          <span>Partial Day Unavailable</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-red-500 rounded border" />
          <span>Full Day Unavailable</span>
        </div>
      </div>

      {/* Details Dialog */}
      <Dialog open={showDetails} onOpenChange={setShowDetails}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <CalendarIcon className="h-5 w-5" />
              Unavailabilities for {selectedDate && format(selectedDate, 'EEEE, MMMM d, yyyy')}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {selectedDateUnavailabilities.map((unavailability) => (
              <Card key={unavailability.id} className="border-l-4 border-l-red-500">
                <CardContent className="p-4 space-y-3">
                  {/* Time */}
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-gray-500" />
                    <span className="font-medium">
                      {unavailability.start_time === "00:00" && unavailability.end_time === "23:59"
                        ? "Full Day"
                        : `${formatTime(unavailability.start_time)} - ${formatTime(unavailability.end_time)}`
                      }
                    </span>
                  </div>

                  {/* Clinic */}
                  {unavailability.clinic && (
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-gray-500" />
                      <span className="text-sm">{unavailability.clinic.clinic_name}</span>
                    </div>
                  )}

                  {/* Reason */}
                  {unavailability.reason && (
                    <div className="flex items-start gap-2">
                      <FileText className="h-4 w-4 text-gray-500 mt-0.5" />
                      <div>
                        <div className="text-sm font-medium text-gray-700">Reason:</div>
                        <div className="text-sm text-gray-600">{unavailability.reason}</div>
                      </div>
                    </div>
                  )}

                  {/* Notes */}
                  {unavailability.notes && (
                    <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                      <strong>Notes:</strong> {unavailability.notes}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}

            {/* Edit Button - Outside the loop, once per date */}
            {selectedDateUnavailabilities.length > 0 && (
              <div className="flex justify-end pt-4 border-t">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEditClick(selectedDateUnavailabilities[0])}
                  className="flex items-center gap-2"
                >
                  <Edit className="h-4 w-4" />
                  Edit Unavailabilities for this Date
                </Button>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Unavailability Dialog */}
      <Dialog open={showEditDialog} onOpenChange={handleEditDialogClose}>
        <DialogContent className="!max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Edit className="h-5 w-5" />
              Edit Unavailabilities for {editDate && format(editDate, 'EEEE, MMMM d, yyyy')}
            </DialogTitle>
          </DialogHeader>
          
          <div className="space-y-6">
            {/* Message Display */}
            {message && (
              <div className={`p-3 rounded-md ${
                message.type === 'success' 
                  ? 'bg-green-50 text-green-800 border border-green-200' 
                  : 'bg-red-50 text-red-800 border border-red-200'
              }`}>
                {message.text}
              </div>
            )}

            {/* Date Picker */}
            <div className="space-y-2">
              <Label>Select Date</Label>
              <DatePicker
                date={editDate}
                onDateChange={setEditDate}
              />
            </div>

            {/* Reason for Unavailability */}
            <div className="space-y-2">
              <Label>Reason for Unavailability (Optional)</Label>
              <Input
                type="text"
                placeholder="e.g., Vacation, Conference, Personal leave..."
                value={editReason}
                onChange={(e) => setEditReason(e.target.value)}
                className="w-full"
              />
            </div>

            {/* Time Slots */}
            <div className="space-y-2">
              <Label>Time Slots</Label>
              <TimeSlotPicker
                timeSlots={editTimeSlots}
                onTimeSlotsChange={setEditTimeSlots}
                selectedDate={editDate}
              />
            </div>

            {/* Instructions */}
            <div className="bg-gray-50 p-3 rounded-md">
              <div className="text-sm text-gray-600">
                <h4 className="font-medium mb-2">Instructions:</h4>
                <ul className="list-disc list-inside space-y-1 text-xs">
                  <li>Choose a date for the unavailability</li>
                  <li>Add a reason for the unavailability (optional)</li>
                  <li>Add time slots when the doctor is unavailable or mark as unavailable for the entire day</li>
                  <li>Click Update Unavailability to save changes</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button variant="outline" onClick={handleEditDialogClose}>
              Cancel
            </Button>
            <Button 
              onClick={handleEditSubmit} 
              disabled={loading || !editDate}
            >
              {loading ? "Updating..." : "Update Unavailabilities"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
