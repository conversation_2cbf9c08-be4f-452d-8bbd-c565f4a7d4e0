"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ShadcnUI/button";
import { X } from "lucide-react";

interface ClearFiltersButtonProps {
  className?: string;
}

export function ClearFiltersButton({ className }: ClearFiltersButtonProps) {
  const router = useRouter();

  const handleClearFilters = () => {
    router.push(window.location.pathname);
  };

  return (
    <Button
      type="button"
      variant="ghost"
      size="sm"
      onClick={handleClearFilters}
      className={className}
    >
      <X className="h-3 w-3" />
    </Button>
  );
}
