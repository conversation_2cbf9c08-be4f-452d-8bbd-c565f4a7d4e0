"use client";

import React from "react";
import { useRouter } from "next/navigation";

interface FilterFormProps {
  children: React.ReactNode;
  className?: string;
}

export function FilterForm({ children, className }: FilterFormProps) {
  const router = useRouter();

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const params = new URLSearchParams();

    // Add all form data to params
    for (const [key, value] of formData.entries()) {
      if (value && value.toString() !== 'all') {
        params.set(key, value.toString());
      }
    }

    // Reset page to 1 when filtering
    params.set('page', '1');

    // Navigate to the new URL
    router.push(`?${params.toString()}`);
  };

  return (
    <form onSubmit={handleSubmit} className={className}>
      {children}
    </form>
  );
}
