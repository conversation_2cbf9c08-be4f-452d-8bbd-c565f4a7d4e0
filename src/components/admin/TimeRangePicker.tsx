"use client";
import React from "react";
import { Label } from "@/components/ShadcnUI/label";
import { Clock } from "lucide-react";
import { validateTimeRange, formatTimeForDisplay } from "@/lib/utils/time-utils";

interface TimeRangePickerProps {
  startTime: string;
  endTime: string;
  onStartTimeChange: (time: string) => void;
  onEndTimeChange: (time: string) => void;
  label?: string;
  required?: boolean;
  error?: string;
  className?: string;
}

export default function TimeRangePicker({
  startTime,
  endTime,
  onStartTimeChange,
  onEndTimeChange,
  label = "Clinic Hours",
  required = false,
  error,
  className = ""
}: TimeRangePickerProps) {
  // Generate time options for the dropdowns (15-minute intervals)
  const generateTimeOptions = () => {
    const options = [];
    for (let hour = 0; hour < 24; hour++) {
      for (let minute = 0; minute < 60; minute += 15) {
        const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        options.push(time);
      }
    }
    return options;
  };

  const timeOptions = generateTimeOptions();
  const validation = validateTimeRange(startTime, endTime);
  const hasError = error || (!validation.isValid && startTime && endTime);

  return (
    <div className={`space-y-3 ${className}`}>
      <div className="flex items-center gap-2">
        <Clock className="h-4 w-4 text-muted-foreground" />
        <Label className="text-sm font-medium">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="startTime" className="text-xs text-muted-foreground">
            Start Time
          </Label>
          <select
            id="startTime"
            value={startTime}
            onChange={(e) => onStartTimeChange(e.target.value)}
            className={`w-full rounded-md border px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              hasError ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:ring-blue-500'
            }`}
          >
            <option value="">Select start time</option>
            {timeOptions.map((time) => (
              <option key={time} value={time}>
                {formatTimeForDisplay(time)}
              </option>
            ))}
          </select>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="endTime" className="text-xs text-muted-foreground">
            End Time
          </Label>
          <select
            id="endTime"
            value={endTime}
            onChange={(e) => onEndTimeChange(e.target.value)}
            className={`w-full rounded-md border px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              hasError ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:ring-blue-500'
            }`}
          >
            <option value="">Select end time</option>
            {timeOptions.map((time) => (
              <option key={time} value={time}>
                {formatTimeForDisplay(time)}
              </option>
            ))}
          </select>
        </div>
      </div>
      
      {/* Error message */}
      {hasError && (
        <div className="text-sm text-red-600 bg-red-50 border border-red-200 rounded-md px-3 py-2">
          {error || validation.error}
        </div>
      )}
      
      {/* Preview of selected time range */}
      {startTime && endTime && validation.isValid && (
        <div className="text-sm text-green-600 bg-green-50 border border-green-200 rounded-md px-3 py-2">
          <span className="font-medium">Clinic hours:</span> {formatTimeForDisplay(startTime)} - {formatTimeForDisplay(endTime)}
        </div>
      )}
    </div>
  );
}
