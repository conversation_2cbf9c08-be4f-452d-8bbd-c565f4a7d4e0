"use client";
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ShadcnUI/button";
import { Label } from "@/components/ShadcnUI/label";
import { Clock, Plus, X, CalendarX } from "lucide-react";
import { Card, CardContent } from "@/components/ShadcnUI/card";
import { format } from "date-fns";
import { generateTimeSlotsWithDefaults } from "@/lib/utils/time-utils";

interface TimeSlot {
  id: string;
  startTime: string;
  endTime: string;
}

interface TimeSlotPickerProps {
  timeSlots: TimeSlot[];
  onTimeSlotsChange: (slots: TimeSlot[]) => void;
  selectedDate?: Date;
  className?: string;
}

const generateTimeSlots = () => {
  return generateTimeSlotsWithDefaults(undefined, undefined, 15);
};

export default function TimeSlotPicker({
  timeSlots,
  onTimeSlotsChange,
  selectedDate,
  className,
}: TimeSlotPickerProps) {
  const timeSlotsList = generateTimeSlots();

  const createEmptySlot = (): TimeSlot => ({
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    id: (typeof crypto !== 'undefined' && (crypto as any).randomUUID) ? (crypto as any).randomUUID() : Math.random().toString(36).slice(2),
    startTime: '',
    endTime: ''
  });

  const [slots, setSlots] = useState<TimeSlot[]>(() =>
    timeSlots && timeSlots.length > 0 ? timeSlots : [createEmptySlot()]
  );
  const [isUnavailableFullDay, setIsUnavailableFullDay] = useState<boolean>(() => {
    return (
      timeSlots?.length === 1 &&
      timeSlots[0]?.startTime === '00:00' &&
      timeSlots[0]?.endTime === '23:59'
    );
  });

  // Hydrate once from parent-provided slots if any (avoid ongoing sync to prevent loops)
  const hasHydratedFromPropsRef = React.useRef(false);
  React.useEffect(() => {
    if (!hasHydratedFromPropsRef.current && timeSlots && timeSlots.length > 0) {
      const isFullDay = timeSlots.length === 1 && timeSlots[0].startTime === '00:00' && timeSlots[0].endTime === '23:59';
      setIsUnavailableFullDay(isFullDay);
      setSlots(isFullDay ? [createEmptySlot()] : timeSlots);
      hasHydratedFromPropsRef.current = true;
    }
  }, [timeSlots]);

  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  const isValidSlot = (slot: TimeSlot) =>
    Boolean(slot.startTime && slot.endTime && slot.startTime < slot.endTime);

  const areTimeSlotsEqual = (a: TimeSlot[] = [], b: TimeSlot[] = []) => {
    if (a.length !== b.length) return false;
    for (let i = 0; i < a.length; i++) {
      if (a[i].startTime !== b[i].startTime || a[i].endTime !== b[i].endTime) {
        return false;
      }
    }
    return true;
  };

  const addSlot = () => {
    setSlots(prev => [...prev, createEmptySlot()]);
  };

  const removeSlot = (id: string) => {
    setSlots(prev => {
      const next = prev.filter(s => s.id !== id);
      return next.length > 0 ? next : [createEmptySlot()];
    });
  };

  const updateSlot = (id: string, field: 'startTime' | 'endTime', value: string) => {
    setSlots(prev => prev.map(s => (s.id === id ? { ...s, [field]: value } : s)));
  };

  // Notify parent on change (only valid, complete ranges)
  React.useEffect(() => {
    const fullDaySlot: TimeSlot[] = [{ id: 'full-day', startTime: '00:00', endTime: '23:59' }];
    const valid = slots.filter(isValidSlot);
    const outgoing = isUnavailableFullDay ? fullDaySlot : valid;
    if (!areTimeSlotsEqual(outgoing, timeSlots)) {
      onTimeSlotsChange(outgoing);
    }
  }, [slots, timeSlots, onTimeSlotsChange, isUnavailableFullDay]);

  // Reset rows on date change to avoid carrying over previous inputs
  React.useEffect(() => {
    setSlots([createEmptySlot()]);
    setIsUnavailableFullDay(false);
    hasHydratedFromPropsRef.current = false;
  }, [selectedDate]);

  const handleFullDayToggle = (checked: boolean) => {
    setIsUnavailableFullDay(checked);
  };

  // If no date is selected, show a message
  if (!selectedDate) {
    return (
      <div className={className}>
        <Card>
          <CardContent className="p-6 text-center text-muted-foreground">
            <CalendarX className="h-8 w-8 mx-auto mb-2" />
            <p>Please select a date first to manage time slots</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={className}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2 text-blue-700">
          <Clock className="h-5 w-5" />
          <Label className="text-base font-medium">Time slots for {format(selectedDate, "EEEE, MMMM d, yyyy")}</Label>
        </div>
        <div className="flex items-center gap-4">
          <label className="inline-flex items-center gap-2 text-sm">
            <input
              type="checkbox"
              checked={isUnavailableFullDay}
              onChange={(e) => handleFullDayToggle(e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <span className="text-gray-700">Mark as unavailable for full day</span>
          </label>
          <div className="text-xs text-muted-foreground">Changes are saved automatically</div>
        </div>
      </div>

      {!isUnavailableFullDay && (
        <Card>
          <CardContent className="p-4 space-y-3">
          {slots.map((slot) => {
            const isComplete = Boolean(slot.startTime && slot.endTime);
            const invalid = isComplete && slot.startTime >= slot.endTime;
            const rowBg = invalid
              ? 'bg-red-50 border-red-200'
              : isComplete
              ? 'bg-emerald-50 border-emerald-200'
              : 'bg-amber-50 border-amber-200';
            const textColor = invalid
              ? 'text-red-700'
              : isComplete
              ? 'text-emerald-700'
              : 'text-amber-700';
            return (
              <div key={slot.id} className={`border ${rowBg} rounded-md p-3`}> 
                <div className="grid grid-cols-12 gap-3 items-center">
                  <div className="col-span-5">
                    <Label className="text-xs text-gray-600">Start time</Label>
                    <select
                      className="mt-1 w-full rounded-md border border-gray-300 bg-white px-2 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                      value={slot.startTime}
                      onChange={(e) => updateSlot(slot.id, 'startTime', e.target.value)}
                    >
                      <option value="">Select start</option>
                      {timeSlotsList.map(t => (
                        <option key={t} value={t}>{formatTime(t)}</option>
                      ))}
                    </select>
                  </div>
                  <div className="col-span-5">
                    <Label className="text-xs text-gray-600">End time</Label>
                    <select
                      className="mt-1 w-full rounded-md border border-gray-300 bg-white px-2 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                      value={slot.endTime}
                      onChange={(e) => updateSlot(slot.id, 'endTime', e.target.value)}
                    >
                      <option value="">Select end</option>
                      {timeSlotsList.map(t => (
                        <option key={t} value={t}>{formatTime(t)}</option>
                      ))}
                    </select>
                  </div>
                  <div className="col-span-2 flex items-end justify-end">
                    <Button type="button" variant="destructive" size="sm" onClick={() => removeSlot(slot.id)} className="w-full">
                      <X className="h-4 w-4 mr-1" /> Remove
                    </Button>
                  </div>
                </div>
                <div className={`mt-2 text-xs ${textColor}`}>
                  {invalid && 'End time must be after start time'}
                  {!invalid && isComplete && 'Slot looks good'}
                  {!isComplete && 'Choose both start and end'}
                </div>
              </div>
            );
          })}

          <div className="flex justify-between items-center pt-2">
            <Button type="button" variant="outline" size="sm" onClick={addSlot}>
              <Plus className="h-4 w-4 mr-1" /> Add another slot
            </Button>
            <div className="text-xs text-muted-foreground">Tip: Use multiple rows for separate breaks</div>
          </div>
          </CardContent>
        </Card>
      )}
      {isUnavailableFullDay && (
        <Card>
          <CardContent className="p-6 text-center text-emerald-700 bg-emerald-50 border border-emerald-200">
            Doctor will be unavailable all day • 12:00 AM - 11:59 PM
          </CardContent>
        </Card>
      )}
    </div>
  );
}
