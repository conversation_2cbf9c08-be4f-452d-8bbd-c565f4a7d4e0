"use client";
import React from "react";
import { Input } from "@/components/ShadcnUI/input";
import { Label } from "@/components/ShadcnUI/label";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

interface DateRangePickerProps {
  startDate: Date | undefined;
  endDate: Date | undefined;
  onStartDateChange: (date: Date | undefined) => void;
  onEndDateChange: (date: Date | undefined) => void;
  className?: string;
}

export default function DateRangePicker({
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange,
  className,
}: DateRangePickerProps) {
  const handleStartDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value) {
      onStartDateChange(new Date(value));
    } else {
      onStartDateChange(undefined);
    }
  };

  const handleEndDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value) {
      onEndDateChange(new Date(value));
    } else {
      onEndDateChange(undefined);
    }
  };

  const getDateInputValue = (date: Date | undefined) => {
    if (!date) return "";
    return format(date, "yyyy-MM-dd");
  };

  return (
    <div className={cn("space-y-4", className)}>
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="start-date">Start Date *</Label>
          <Input
            id="start-date"
            type="date"
            value={getDateInputValue(startDate)}
            onChange={handleStartDateChange}
            min={format(new Date(), "yyyy-MM-dd")}
            className="w-full"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="end-date">End Date *</Label>
          <Input
            id="end-date"
            type="date"
            value={getDateInputValue(endDate)}
            onChange={handleEndDateChange}
            min={startDate ? format(startDate, "yyyy-MM-dd") : format(new Date(), "yyyy-MM-dd")}
            className="w-full"
          />
        </div>
      </div>
      
      {startDate && endDate && (
        <div className="text-sm text-muted-foreground">
          {format(startDate, "PPP")} - {format(endDate, "PPP")}
        </div>
      )}
    </div>
  );
}
