import React from "react";
import { PageHeaderProvider } from "@/contexts/PageHeaderContext";
import { ToastProvider } from "@/contexts/ToastContext";

// Create a mock component that looks like FertilityDietPlan but doesn't have Prisma dependencies
const MockFertilityDietPlan = () => {
  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="bg-white rounded-lg shadow-lg p-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-6">
          Fertility Diet Plan Assessment
        </h1>

        <div className="space-y-6">
          <div className="border rounded-lg p-4">
            <h2 className="text-xl font-semibold mb-4">Basic Information</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  What is your age?
                </label>
                <input
                  type="number"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter your age"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  What is your height (cm)?
                </label>
                <input
                  type="number"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter your height"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  What is your weight (kg)?
                </label>
                <input
                  type="number"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter your weight"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Activity Level
                </label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                  <option>Sedentary</option>
                  <option>Lightly Active</option>
                  <option>Moderately Active</option>
                  <option>Very Active</option>
                </select>
              </div>
            </div>
          </div>

          <div className="flex justify-between">
            <button className="px-6 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
              Previous
            </button>
            <button className="px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default {
  title: "Components/FertilityDietPlan",
  component: MockFertilityDietPlan,
  decorators: [
    (Story: React.ComponentType) => (
      <ToastProvider>
        <PageHeaderProvider>
          <Story />
        </PageHeaderProvider>
      </ToastProvider>
    ),
  ],
};

export const Default = () => (
  <div style={{ padding: "20px", background: "#f5f5f5", minHeight: "600px" }}>
    <MockFertilityDietPlan />
  </div>
);
