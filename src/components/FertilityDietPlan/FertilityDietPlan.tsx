import React, { useState, useEffect } from "react";
import { useDietPlanForm } from "@/hooks/useDietPlanForm";
import { usePageHeader } from "@/contexts/PageHeaderContext";
import { Card, CardContent } from "../ShadcnUI/card";
import Button, { ButtonType } from "../shared/Button/Button";
import DynamicFormField from "../Steps/Common/DynamicFormField";
import PageLoader from "../shared/PageLoader";
import ErrorMessage from "../shared/ErrorMessage/ErrorMessage";
import Image from "next/image";
import { useDynamicForm } from "@/hooks/useDynamicForm";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import { useToast } from "@/contexts/ToastContext";
import { convertNumericValues } from "@/utils/formDataUtils";
import { useBreadcrumb } from "@/contexts/BreadcrumbContext";

const FertilityDietPlan: React.FC = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submissionError, setSubmissionError] = useState<string | null>(null);
  const { user, user_token } = useAuth();
  const router = useRouter();
  const toast = useToast();

  const { setTitle, setSubtitle } = usePageHeader();
  const { setBreadcrumbs } = useBreadcrumb();
  const { questions, isLoading, error } = useDietPlanForm();

  useEffect(() => {
    setTitle("Fertility Diet Plan");
    setSubtitle("Let's Personalize Your Diet Plan");
    setBreadcrumbs(null);
  }, [setTitle, setSubtitle, setBreadcrumbs]);

  const handleFormSubmit = async (submissionData: Record<string, string>) => {
    setIsSubmitting(true);
    setSubmissionError(null);

    try {
      const formData = {
        "diet-plan": submissionData,
      };

      const payload = convertNumericValues(formData, questions);
      // Submit data to the fertility diet plan API
      const response = await fetch("/api/v1/fertility-diet-plan/form", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${user_token}`,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || `HTTP error! status: ${response.status}`
        );
      }

      const result = await response.json();
      console.log("Diet plan form submitted successfully:", result);

      // Handle successful submission
      if (result?.allStepsCompleted) {
        // Show success message
        toast.success(
          "Diet plan assessment completed successfully! Redirecting to BMR calculation..."
        );

        // Redirect to BMR calculation page after a short delay
        setTimeout(() => {
          router.push("/user/fertility-diet-plan/bmr-calories-calculation");
        }, 1500);
      } else if (result?.nextAction) {
        // More steps to complete (shouldn't happen for diet plan)
        console.log("Next step:", result.nextAction);
      }
    } catch (error) {
      console.log(error);
      setSubmissionError("Failed to submit diet plan form. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const {
    formData,
    errors,
    showValidation,
    renderList,
    handleInputChange,
    handleSubmit,
    isFormValid,
    isQuestionVisible,
  } = useDynamicForm(
    questions,
    handleFormSubmit,
    "Diet Plan",
    user?.id,
    "diet-plan"
  );

  if (isLoading) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <PageLoader />
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <ErrorMessage message="Failed to load diet plan questions. Please try again." />
      </div>
    );
  }

  return (
    <div className="w-full h-full flex items-center justify-center">
      <Card className="w-[90%] lg:w-[75%] flex flex-col xl:flex-row items-center justify-center shadow-none border-none p-6 rounded-none bg-white">
        {/* Left: Fruit Image */}
        <div className="flex-[0_0_45%] flex items-center justify-center h-full">
          <Image
            src="/assets/diet-plan/diet.svg"
            alt="Fruits"
            className="max-w-[120%]"
            width={439}
            height={351}
          />
        </div>

        {/* Right: Form */}
        <CardContent className="flex-[0_0_55%] w-full flex flex-col items-start justify-center px-0 xl:pr-4 h-full">
          <h2 className="w-full text-2xl font-bold text-[var(--grey-7)] mb-6 text-center">
            Diet Assessment
          </h2>

          {questions.length > 0 ? (
            <div className="w-full space-y-4 mb-6">
              {renderList.map((item) => {
                if ("isGroup" in item) {
                  return (
                    <div key={item.id} className="pt-4">
                      <h3 className="text-lg font-semibold text-[var(--grey-8)]">
                        {item.text}
                      </h3>
                    </div>
                  );
                }

                const question = item;

                if (!isQuestionVisible(question, formData)) {
                  return null;
                }

                return (
                  <DynamicFormField
                    key={question.id}
                    question={question}
                    value={formData[question.id] || ""}
                    onChange={(value) => handleInputChange(question.id, value)}
                    error={showValidation && !!errors[question.id]}
                    errorMessage={
                      showValidation ? errors[question.id] : undefined
                    }
                    required={question.is_mandatory}

                    // An Example of Multi Select Radio ************
                  />
                  // <DynamicFormField
                  //       question={{
                  //         ...question,
                  //         id: `${question.id}_multi`,
                  //         question_text: "Select multiple options that apply:",
                  //         field_type: {
                  //           ...question.field_type,
                  //           value: "MULTI_SELECT_RADIO",
                  //         },
                  //       }}
                  //       value={formData[`${question.id}_multi`] || ""}
                  //       onChange={(value) =>
                  //         handleInputChange(`${question.id}_multi`, value)
                  //       }
                  //       error={
                  //         showValidation && !!errors[`${question.id}_multi`]
                  //       }
                  //       errorMessage={
                  //         showValidation ? errors[`${question.id}_multi`]
                  //           : undefined
                  //       }
                  //       required={false}
                  //     />
                );
              })}
            </div>
          ) : (
            <div className="w-full text-center py-8">
              <div className="text-[var(--grey-6)] text-base">
                No diet assessment questions available
              </div>
            </div>
          )}

          {/* Submission Error */}
          {submissionError && (
            <ErrorMessage message={submissionError} className="mb-4" />
          )}

          {/* Validation Error Message */}
          {showValidation && !isFormValid && (
            <ErrorMessage
              message="Please fill in all required fields to continue."
              className="mb-4 mx-auto"
            />
          )}

          {/* Submit Button */}
          <Button
            type={ButtonType.PRIMARY}
            text={isSubmitting ? "Processing..." : "Calculate BMR & Continue"}
            onClick={handleSubmit}
            className="rounded px-4 py-3 text-base font-semibold w-full"
            disabled={isSubmitting || !isFormValid}
            icon={
              isSubmitting ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : undefined
            }
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default FertilityDietPlan;
