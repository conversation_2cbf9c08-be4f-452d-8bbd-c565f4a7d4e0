import React from "react";
import DynamicFormPage from "../Common/DynamicFormPage";
import { Question } from "@/types/questions";

export interface EnvironmentalData {
  incomeRange: string;
  livingArea: string;
  workStressLevel: string;
  pollutionExposure: string;
  occupationType: string;
}

export interface EnvironmentalProps {
  onNext?: (data: Record<string, string>) => void;
  onCancel?: () => void;
  questions?: Question[];
  loading?: boolean;
  error?: string | null;
  isSubmitting?: boolean;
  errorMessage?: string;
}

const Environmental: React.FC<EnvironmentalProps> = ({ 
  onNext, 
  questions = [],
  loading = false,
  error = null,
  isSubmitting = false,
  errorMessage = "",
  ...props 
}) => {
  return (
    <DynamicFormPage
      title="Environmental & Socioeconomic Factors"
      submitButtonText="Continue to Verify Details"
      onNext={onNext}
      currentStep={3}
      totalSteps={5}
      questions={questions}
      loading={loading}
      error={error}
      isSubmitting={isSubmitting}
      errorMessage={errorMessage}
      {...props}
    />
  );
};

export default Environmental;
