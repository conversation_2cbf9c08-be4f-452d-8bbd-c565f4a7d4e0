/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";

// Mock DynamicFormPage since we're testing the wrapper
jest.mock("../Common/DynamicFormPage", () => {
  return function MockDynamicFormPage(props: any) {
    return (
      <div data-testid="dynamic-form-page">
        <div data-testid="title">{props.title}</div>
        <div data-testid="current-step">{props.currentStep}</div>
        <div data-testid="total-steps">{props.totalSteps}</div>
        <div data-testid="submit-button-text">{props.submitButtonText}</div>
        <div data-testid="loading">{String(props.loading)}</div>
        <div data-testid="error">{props.error || "null"}</div>
        <div data-testid="questions-count">{props.questions?.length || 0}</div>
      </div>
    );
  };
});

// Import after mocks
import Environmental from "./Environmental";

const mockOnNext = jest.fn();

describe("Environmental Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders correctly with questions", () => {
    const mockQuestions = [
      {} as any,
      {} as any,
      {} as any,
      {} as any,
      {} as any,
    ]; // Mock 5 questions

    render(<Environmental onNext={mockOnNext} questions={mockQuestions} />);

    expect(screen.getByTestId("dynamic-form-page")).toBeInTheDocument();
    expect(screen.getByTestId("title")).toHaveTextContent(
      "Environmental & Socioeconomic Factors"
    );
    expect(screen.getByTestId("current-step")).toHaveTextContent("3");
    expect(screen.getByTestId("total-steps")).toHaveTextContent("5");
    expect(screen.getByTestId("submit-button-text")).toHaveTextContent(
      "Continue to Verify Details"
    );
    expect(screen.getByTestId("questions-count")).toHaveTextContent("5");
  });

  it("handles loading state", () => {
    render(<Environmental onNext={mockOnNext} loading={true} />);

    expect(screen.getByTestId("loading")).toHaveTextContent("true");
    expect(screen.getByTestId("questions-count")).toHaveTextContent("0");
  });

  it("handles error state", () => {
    const errorMessage = "Failed to load environmental questions";

    render(<Environmental onNext={mockOnNext} error={errorMessage} />);

    expect(screen.getByTestId("error")).toHaveTextContent(errorMessage);
    expect(screen.getByTestId("loading")).toHaveTextContent("false");
  });

  it("renders without onNext callback", () => {
    render(<Environmental />);

    expect(screen.getByTestId("dynamic-form-page")).toBeInTheDocument();
  });

  it("passes questions prop to DynamicFormPage", () => {
    const mockQuestions = [{} as any, {} as any, {} as any]; // 3 mock questions

    render(<Environmental onNext={mockOnNext} questions={mockQuestions} />);

    expect(screen.getByTestId("questions-count")).toHaveTextContent("3");
  });

  it("maintains correct step information for environmental form", () => {
    render(<Environmental onNext={mockOnNext} />);

    // Verify this is step 3 of 5 for the environmental factors
    expect(screen.getByTestId("current-step")).toHaveTextContent("3");
    expect(screen.getByTestId("total-steps")).toHaveTextContent("5");
    expect(screen.getByTestId("title")).toHaveTextContent(
      "Environmental & Socioeconomic Factors"
    );
  });

  it("has correct submit button text for verification step", () => {
    render(<Environmental onNext={mockOnNext} />);

    expect(screen.getByTestId("submit-button-text")).toHaveTextContent(
      "Continue to Verify Details"
    );
  });

  it("handles empty questions array gracefully", () => {
    render(<Environmental onNext={mockOnNext} questions={[]} />);

    expect(screen.getByTestId("questions-count")).toHaveTextContent("0");
    expect(screen.getByTestId("loading")).toHaveTextContent("false");
    expect(screen.getByTestId("error")).toHaveTextContent("null");
  });

  it("passes through additional props to DynamicFormPage", () => {
    render(
      <Environmental
        onNext={mockOnNext}
        isSubmitting={true}
        errorMessage="Test error"
      />
    );

    // The component should render without errors when additional props are passed
    expect(screen.getByTestId("dynamic-form-page")).toBeInTheDocument();
  });

  it("handles network errors in question loading", () => {
    const networkError = "Network error occurred";

    render(<Environmental onNext={mockOnNext} error={networkError} />);

    expect(screen.getByTestId("error")).toHaveTextContent(networkError);
    expect(screen.getByTestId("questions-count")).toHaveTextContent("0");
  });

  it("displays correct form title for environmental assessment", () => {
    const mockQuestions = [{} as any];

    render(<Environmental onNext={mockOnNext} questions={mockQuestions} />);

    // Verify the specific title for environmental assessment
    expect(screen.getByTestId("title")).toHaveTextContent(
      "Environmental & Socioeconomic Factors"
    );
    expect(screen.getByTestId("questions-count")).toHaveTextContent("1");
  });
});
