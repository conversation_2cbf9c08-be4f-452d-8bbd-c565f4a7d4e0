/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import PhysiologicalPage from "./PhysiologicalPage";

// Mock DynamicFormPage since we're testing the wrapper
jest.mock("../Common/DynamicFormPage", () => {
  return function MockDynamicFormPage(props: any) {
    return (
      <div data-testid="dynamic-form-page">
        <div data-testid="title">{props.title}</div>
        <div data-testid="current-step">{props.currentStep}</div>
        <div data-testid="total-steps">{props.totalSteps}</div>
        <div data-testid="submit-button-text">{props.submitButtonText}</div>
        <div data-testid="loading">{props.loading ? "true" : "false"}</div>
        <div data-testid="error">{props.error || "null"}</div>
        <div data-testid="questions-count">{props.questions.length}</div>
      </div>
    );
  };
});

describe("PhysiologicalPage", () => {
  it("renders with default props", () => {
    render(<PhysiologicalPage />);
    expect(screen.getByTestId("dynamic-form-page")).toBeInTheDocument();
    expect(screen.getByTestId("title")).toHaveTextContent("Biological Factors");
    expect(screen.getByTestId("current-step")).toHaveTextContent("1");
    expect(screen.getByTestId("total-steps")).toHaveTextContent("5");
    expect(screen.getByTestId("submit-button-text")).toHaveTextContent(
      "Next Step (Lifestyle)"
    );
    expect(screen.getByTestId("loading")).toHaveTextContent("false");
    expect(screen.getByTestId("error")).toHaveTextContent("null");
    expect(screen.getByTestId("questions-count")).toHaveTextContent("0");
  });

  it("renders with custom questions and loading state", () => {
    render(<PhysiologicalPage loading={true} />);
    expect(screen.getByTestId("loading")).toHaveTextContent("true");
  });

  it("renders with error state", () => {
    render(<PhysiologicalPage error="Some error" />);
    expect(screen.getByTestId("error")).toHaveTextContent("Some error");
  });

  it("renders with isSubmitting and errorMessage", () => {
    render(<PhysiologicalPage isSubmitting={true} errorMessage="Error!" />);
    expect(screen.getByTestId("dynamic-form-page")).toBeInTheDocument();
  });

  it("renders without onNext callback", () => {
    render(<PhysiologicalPage questions={[]} />);
    expect(screen.getByTestId("dynamic-form-page")).toBeInTheDocument();
  });
});
