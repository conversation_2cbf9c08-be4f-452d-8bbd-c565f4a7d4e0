import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import PhysiologicalPage from "./PhysiologicalPage";
import { AuthProvider } from "@/contexts/AuthContext";
import { ToastProvider } from "@/contexts/ToastContext";

const meta: Meta<typeof PhysiologicalPage> = {
  title: "Steps/PhysiologicalPage",
  component: PhysiologicalPage,
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component:
          "A comprehensive form component for collecting biological factors data as part of a multi-step IVF assessment process.",
      },
    },
  },
  argTypes: {
    onNext: {
      description:
        "Callback function called when the Next Step button is clicked",
      action: "next-step-clicked",
    },
    className: {
      description: "Additional CSS classes to apply to the component",
      control: "text",
    },
  },
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <ToastProvider duration={4000} maxToasts={5}>
        <AuthProvider>
          <Story />
        </AuthProvider>
      </ToastProvider>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story:
          "The default physiological page with all form fields. Shows the biological factors form with interactive elements.",
      },
    },
  },
};

export const WithHandlers: Story = {
  args: {
    onNext: (data) => {
      console.log("Form data submitted:", data);
      alert(`Next step clicked with data: ${JSON.stringify(data, null, 2)}`);
    },
  },
  parameters: {
    docs: {
      description: {
        story:
          "PhysiologicalPage with console logging and alert to demonstrate data handling.",
      },
    },
  },
};

export const CustomClassName: Story = {
  args: {
    onNext: () => alert("Next step clicked"),
    className: "custom-physiological-page",
  },
  parameters: {
    docs: {
      description: {
        story: "PhysiologicalPage with custom CSS classes applied.",
      },
    },
  },
};

export const Mobile: Story = {
  args: {},
  parameters: {
    viewport: {
      defaultViewport: "mobile1",
    },
    docs: {
      description: {
        story: "PhysiologicalPage optimized for mobile viewing.",
      },
    },
  },
};

export const Tablet: Story = {
  args: {},
  parameters: {
    viewport: {
      defaultViewport: "tablet",
    },
    docs: {
      description: {
        story: "PhysiologicalPage optimized for tablet viewing.",
      },
    },
  },
};

export const WithoutHandler: Story = {
  args: {
    // No onNext handler provided
  },
  parameters: {
    docs: {
      description: {
        story:
          "PhysiologicalPage without a next step handler to test default behavior.",
      },
    },
  },
};
