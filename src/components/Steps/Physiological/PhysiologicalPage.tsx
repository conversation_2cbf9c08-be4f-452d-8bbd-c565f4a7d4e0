import React from "react";
import DynamicFormPage from "../Common/DynamicFormPage";
import { Question } from "@/types/questions";

export interface PhysiologicalPageProps {
  onNext?: (data: Record<string, string>) => void;
  className?: string;
  questions?: Question[];
  loading?: boolean;
  error?: string | null;
  isSubmitting?: boolean;
  errorMessage?: string;
}

export interface BiologicalFactorsData {
  age: string;
  height: string;
  weight: string;
  menstrualRegularity: "regular" | "irregular" | "";
  infertilityDuration: "<6 months" | "6-12 months" | ">12 months" | "";
  ivfAttempts: string;
  knownCondition: string;
}

const PhysiologicalPage: React.FC<PhysiologicalPageProps> = ({
  onNext,
  questions = [],
  loading = false,
  error = null,
  isSubmitting = false,
  errorMessage = "",
  ...props
}) => {
  return (
    <DynamicFormPage
      title="Biological Factors"
      onNext={onNext}
      currentStep={1}
      totalSteps={5}
      questions={questions}
      loading={loading}
      error={error}
      isSubmitting={isSubmitting}
      errorMessage={errorMessage}
      submitButtonText="Next Step (Lifestyle)"
      {...props}
    />
  );
};

export default PhysiologicalPage;
