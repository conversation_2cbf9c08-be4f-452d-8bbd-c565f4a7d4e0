import React, { useState, useEffect } from "react";
import Header, { HeaderState } from "../../shared/Header/Header";
import Footer from "../../shared/Footer/Footer";
import Button, { ButtonType } from "../../shared/Button/Button";
import StepHeader from "../../shared/StepHeader/StepHeader";
import <PERSON>Help from "../../shared/NeedHelp/NeedHelp";
import { ArrowRightIcon } from "@phosphor-icons/react";

export interface TypeSelectionProps {
  onTypeSelect?: (type: number) => void;
  onNext?: () => void;
  className?: string;
}

const TRACKS = [
  {
    id: 1,
    title: "Track 1",
    description: "I have not gotten any testing done",
  },
  {
    id: 2,
    title: "Track 2",
    description: "I have gotten fertility tests done",
  },
  {
    id: 3,
    title: "Track 3",
    description: "I have done IVF before",
  },
];

const TypeSelection: React.FC<TypeSelectionProps> = ({
  onTypeSelect,
  onNext,
  className = "",
}) => {
  const [selectedTrack, setSelectedTrack] = useState<number | null>(null);

  // Auto-populate selected track from sessionStorage on component mount
  useEffect(() => {
    const storedType = sessionStorage.getItem("ivf_selected_assessment_type");
    if (storedType) {
      const typeNumber = parseInt(storedType, 10);
      if (!isNaN(typeNumber) && typeNumber >= 1 && typeNumber <= 3) {
        setSelectedTrack(typeNumber);
        if (onTypeSelect) onTypeSelect(typeNumber);
      }
    }
  }, [onTypeSelect]);

  const handleTrackSelect = (id: number) => {
    setSelectedTrack(id);
    if (onTypeSelect) onTypeSelect(id);
  };

  const handleNext = () => {
    if (onNext && selectedTrack !== null) onNext();
  };

  return (
    <div className={`min-h-screen flex flex-col ${className}`}>
      <Header state={HeaderState.LOGIN} />
      <main className="flex-1 flex flex-col items-center justify-center px-2 md:px-0">
        <div className="w-[22.063rem] md:w-[26.75rem] max-w-xl mt-8 mb-4">
          <StepHeader
            currentStep={1}
            totalSteps={5}
            title="Select Track"
            className="mb-7.5 md:mb-12"
          />
          <div className="text-left mb-4">
            <p className="text-[var(--grey-7)] text-lg font-medium mb-2">
              Where are you currently in your fertility journey?
            </p>
          </div>
          <div className="flex flex-col gap-6 mb-10 md:mb-15">
            {TRACKS.map((track) => (
              <button
                key={track.id}
                type="button"
                className={`w-full h-[7.438rem] md:h-[8.125rem] flex items-center gap-4 border-2 px-6 py-5 md:py-7.5 md:px-10 transition-all duration-200 text-left focus:outline-none cursor-pointer
                  ${
                    selectedTrack === track.id
                      ? "bg-[var(--violet-11)]"
                      : "bg-white hover:bg-[var(--violet-1)] border-[var(--grey-3)] border-1"
                  }
                `}
                onClick={() => handleTrackSelect(track.id)}
              >
                <div
                  className={`flex items-center justify-center w-17.5 h-17.5 rounded-full text-[2rem] font-bold mr-4
                    ${
                      selectedTrack === track.id
                        ? "bg-[var(--violet-6)] text-white"
                        : "bg-[var(--violet-1)] text-[var(--violet-11)]"
                    }
                  `}
                  style={{ fontFamily: "Scheherazade New, serif" }}
                >
                  <h1 className="flex items-baseline justify-center">
                    T<span className="text-[1.25rem]">{track.id}</span>
                  </h1>
                </div>
                <div>
                  <div
                    className={`text-lg font-semibold text-[var(--grey-10)] mb-1
                      ${
                        selectedTrack === track.id
                          ? "text-white"
                          : "text-[var(--grey-10)]"
                      }
                      `}
                  >
                    {track.title}
                  </div>
                  <div
                    className={`text-[var(--grey-6)] text-base leading-tight text-left md:text-center
                    ${
                      selectedTrack === track.id
                        ? "text-white"
                        : "text-[var(--grey-10)]"
                    }
                    `}
                  >
                    {track.description}
                  </div>
                </div>
              </button>
            ))}
          </div>
          <Button
            type={ButtonType.PRIMARY}
            text="Next Step (Biological)"
            icon={<ArrowRightIcon size={20} />}
            onClick={handleNext}
            disabled={selectedTrack === null}
            className="w-full max-w-md mx-auto text-base font-semibold"
          />
        </div>
      </main>
      <NeedHelp className="ml-6 md:ml-37.5 md:my-10 my-6" />
      <Footer />
    </div>
  );
};

export default TypeSelection;
