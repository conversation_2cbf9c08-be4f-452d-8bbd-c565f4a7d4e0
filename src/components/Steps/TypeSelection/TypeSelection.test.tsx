/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import TypeSelection from "./TypeSelection";
import Image from "next/image";

// Mock <PERSON>er, <PERSON>er, StepHeader, NeedHelp to avoid Auth/Supabase
jest.mock("../../shared/Header/Header", () => {
  const MockHeader = () => <header role="banner">Header</header>;
  MockHeader.displayName = "MockHeader";
  return {
    __esModule: true,
    default: MockHeader,
    HeaderState: { LOGIN: "LOGIN" },
  };
});
jest.mock("../../shared/Footer/Footer", () => {
  const MockFooter = () => <footer role="contentinfo">Footer</footer>;
  MockFooter.displayName = "MockFooter";
  return {
    __esModule: true,
    default: MockFooter,
  };
});
jest.mock("../../shared/StepHeader/StepHeader", () => {
  const MockStepHeader = () => <div>StepHeader</div>;
  MockStepHeader.displayName = "MockStepHeader";
  return MockStepHeader;
});
jest.mock("../../shared/NeedHelp/NeedHelp", () => {
  const MockNeedHelp = () => <div>NeedHelp</div>;
  MockNeedHelp.displayName = "MockNeedHelp";
  return MockNeedHelp;
});

// Mock Next.js router
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

// Mock Next.js Image component
jest.mock("next/image", () => ({
  __esModule: true,
  default: (props: any) => <Image {...props} alt="Type Selection" />,
}));

describe("TypeSelection", () => {
  it("renders the component and all tracks", () => {
    render(<TypeSelection />);
    expect(
      screen.getByText("Where are you currently in your fertility journey?")
    ).toBeInTheDocument();
    expect(screen.getByText("Track 1")).toBeInTheDocument();
    expect(screen.getByText("Track 2")).toBeInTheDocument();
    expect(screen.getByText("Track 3")).toBeInTheDocument();
  });

  it("enables continue button when track is selected", () => {
    render(<TypeSelection />);
    const track2Card = screen.getByText("Track 2").closest("button");
    if (track2Card) fireEvent.click(track2Card);
    const continueButton = screen.getByText("Next Step (Biological)");
    expect(continueButton).not.toBeDisabled();
  });

  it("calls onTypeSelect when a track is selected", () => {
    const mockOnTypeSelect = jest.fn();
    render(<TypeSelection onTypeSelect={mockOnTypeSelect} />);
    const track1Card = screen.getByText("Track 1").closest("button");
    if (track1Card) fireEvent.click(track1Card);
    expect(mockOnTypeSelect).toHaveBeenCalledWith(1);
  });

  it("calls onNext when continue button is clicked with selected track", () => {
    const mockOnNext = jest.fn();
    render(<TypeSelection onNext={mockOnNext} />);
    const track2Card = screen.getByText("Track 2").closest("button");
    if (track2Card) fireEvent.click(track2Card);
    const continueButton = screen.getByText("Next Step (Biological)");
    fireEvent.click(continueButton);
    expect(mockOnNext).toHaveBeenCalled();
  });

  it("renders Header and Footer", () => {
    render(<TypeSelection />);
    expect(screen.getByRole("banner")).toBeInTheDocument();
    expect(screen.getByRole("contentinfo")).toBeInTheDocument();
  });
});
