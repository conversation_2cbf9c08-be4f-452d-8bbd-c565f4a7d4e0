import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import TypeSelection from "./TypeSelection";
import { AuthProvider } from "@/contexts/AuthContext";
import { ToastProvider } from "@/contexts/ToastContext";

// Mock action function for stories
const action =
  (name: string) =>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (...args: any[]) => {
    console.log(`${name}:`, ...args);
  };

const meta: Meta<typeof TypeSelection> = {
  title: "Steps/TypeSelection",
  component: TypeSelection,
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component:
          "A type selection page that allows users to choose between Type 1, 2, or 3 for their fertility assessment. Features interactive cards with visual feedback and a Continue button that enables once a selection is made.",
      },
    },
  },
  argTypes: {
    onTypeSelect: {
      description: "Callback function called when a type is selected",
      control: false,
    },
    onNext: {
      description: "Callback function called when Continue button is clicked",
      control: false,
    },
    className: {
      description: "Additional CSS classes to apply to the component",
      control: "text",
    },
  },
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <ToastProvider duration={4000} maxToasts={5}>
        <AuthProvider>
          <Story />
        </AuthProvider>
      </ToastProvider>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Default story
export const Default: Story = {
  args: {
    onTypeSelect: action("type-selected"),
    onNext: action("next-clicked"),
  },
  parameters: {
    docs: {
      description: {
        story:
          "The default state of the TypeSelection component with no type selected initially. Users can click on any type card to select it, which will enable the Continue button.",
      },
    },
  },
};

// Interactive story with enhanced actions
export const Interactive: Story = {
  args: {
    onTypeSelect: (type: number) => {
      action("type-selected")(type);
      console.log(`Type ${type} selected`);
    },
    onNext: () => {
      action("next-clicked")();
      console.log("Continue button clicked");
    },
  },
  parameters: {
    docs: {
      description: {
        story:
          "Interactive version of the component with enhanced logging. Open the browser console to see the selection events.",
      },
    },
  },
};

// Story with custom styling
export const CustomStyling: Story = {
  args: {
    onTypeSelect: action("type-selected"),
    onNext: action("next-clicked"),
    className: "bg-gray-50 p-4",
  },
  parameters: {
    docs: {
      description: {
        story:
          "TypeSelection component with custom styling applied via className prop.",
      },
    },
  },
};

// Story for testing type descriptions
export const TypeDescriptions: Story = {
  args: {
    onTypeSelect: action("type-selected"),
    onNext: action("next-clicked"),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Focus on the type descriptions to verify content:\n\n" +
          "• **Type 1**: For individuals looking to assess their fertility potential\n" +
          "• **Type 2**: For couples planning to start a family together\n" +
          "• **Type 3**: For those seeking comprehensive fertility evaluation",
      },
    },
  },
};

// Story without callbacks (for testing UI only)
export const UIOnly: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story:
          "TypeSelection component without any callback functions. Useful for testing UI interactions without side effects.",
      },
    },
  },
};

// Story for accessibility testing
export const AccessibilityFocus: Story = {
  args: {
    onTypeSelect: action("type-selected"),
    onNext: action("next-clicked"),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Use this story to test keyboard navigation and screen reader compatibility. All interactive elements should be focusable and have proper ARIA attributes.",
      },
    },
  },
  play: async ({ canvasElement }) => {
    // Focus the first type card for accessibility testing
    const firstTypeCard = canvasElement.querySelector('[class*="border-2"]');
    if (firstTypeCard instanceof HTMLElement) {
      firstTypeCard.focus();
    }
  },
};

// Story showing different viewport sizes
export const ResponsiveDesign: Story = {
  args: {
    onTypeSelect: action("type-selected"),
    onNext: action("next-clicked"),
  },
  parameters: {
    viewport: {
      defaultViewport: "mobile1",
    },
    docs: {
      description: {
        story:
          "Test the responsive design on different screen sizes. Use the viewport addon to switch between desktop, tablet, and mobile views.",
      },
    },
  },
};

// Story demonstrating the selection state
export const SelectionStates: Story = {
  render: (args) => (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-semibold mb-4 text-center">
          Default State (No Selection)
        </h3>
        <TypeSelection {...args} />
      </div>
    </div>
  ),
  args: {
    onTypeSelect: action("type-selected"),
    onNext: action("next-clicked"),
  },
  parameters: {
    docs: {
      description: {
        story:
          "Demonstrates the different visual states of the component. Click on type cards to see the selection feedback.",
      },
    },
  },
};
