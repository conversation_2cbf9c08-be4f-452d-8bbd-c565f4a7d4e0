import type { <PERSON><PERSON>, StoryObj } from "@storybook/nextjs";
import Signup from "./Signup";
import { AuthProvider } from "@/contexts/AuthContext";
import { ToastProvider } from "@/contexts/ToastContext";

const meta: Meta<typeof Signup> = {
  title: "Steps/Signup",
  component: Signup,
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component:
          "A signup form component that handles user registration with email verification. Includes form validation, consent checkbox, and error handling.",
      },
    },
  },
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <ToastProvider duration={4000} maxToasts={5}>
        <AuthProvider>
          <Story />
        </AuthProvider>
      </ToastProvider>
    ),
  ],
  argTypes: {
    onVerifyEmail: {
      description:
        "Callback function called when email verification is triggered",
      action: "verifyEmail",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story:
          "Default signup form with all fields empty and ready for user input.",
      },
    },
  },
};

export const WithPrefilledData: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story:
          "Signup form with some pre-filled data to demonstrate the form state.",
      },
    },
  },
  play: async () => {
    // This would be used to interact with the form in Storybook
    // For now, it's a placeholder for future interactions
  },
};

export const MobileView: Story = {
  args: {},
  parameters: {
    viewport: {
      defaultViewport: "mobile1",
    },
    docs: {
      description: {
        story:
          "Signup form optimized for mobile devices with responsive design.",
      },
    },
  },
};

export const TabletView: Story = {
  args: {},
  parameters: {
    viewport: {
      defaultViewport: "tablet",
    },
    docs: {
      description: {
        story: "Signup form displayed on tablet-sized screens.",
      },
    },
  },
};

export const DesktopView: Story = {
  args: {},
  parameters: {
    viewport: {
      defaultViewport: "desktop",
    },
    docs: {
      description: {
        story: "Signup form displayed on desktop screens with full layout.",
      },
    },
  },
};

export const LoadingState: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story: "Signup form in loading state during form submission.",
      },
    },
  },
  play: async () => {
    // This would simulate the loading state
    // For now, it's a placeholder for future interactions
  },
};

export const ErrorState: Story = {
  args: {},
  parameters: {
    docs: {
      description: {
        story:
          "Signup form displaying error messages for validation or API errors.",
      },
    },
  },
  play: async () => {
    // This would simulate error states
    // For now, it's a placeholder for future interactions
  },
};
