import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";

// Mock Supabase client first
const mockSignInWithOtp = jest.fn();
jest.mock("@/utils/supabase/client", () => ({
  createClient: () => ({
    auth: {
      signInWithOtp: mockSignInWithOtp,
    },
  }),
}));

// Mock sessionStorage
Object.defineProperty(window, "sessionStorage", {
  value: {
    setItem: jest.fn(),
    getItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  },
});

// Mock components
jest.mock("../../shared/Header/Header", () => {
  const MockHeader = () => <div data-testid="header">Header</div>;
  MockHeader.displayName = "MockHeader";
  return {
    __esModule: true,
    default: MockHeader,
    HeaderState: { LOGIN: "LOGIN" },
  };
});

jest.mock("../../shared/Footer/Footer", () => {
  const MockFooter = () => <div data-testid="footer">Footer</div>;
  MockFooter.displayName = "MockFooter";
  return MockFooter;
});

jest.mock("../../shared/NeedHelp/NeedHelp", () => {
  const MockNeedHelp = () => <div data-testid="need-help">Need Help</div>;
  MockNeedHelp.displayName = "MockNeedHelp";
  return MockNeedHelp;
});

jest.mock("../../shared/StepHeader/StepHeader", () => {
  const MockStepHeader = () => <div data-testid="step-header">Step Header</div>;
  MockStepHeader.displayName = "MockStepHeader";
  return MockStepHeader;
});

jest.mock("../../shared/Button/Button", () => {
  interface ButtonProps {
    text: string;
    onClick: () => void;
    disabled: boolean;
  }
  const MockButton = ({ text, onClick, disabled }: ButtonProps) => (
    <button onClick={onClick} disabled={disabled} data-testid="signup-button">
      {text}
    </button>
  );
  MockButton.displayName = "MockButton";
  return {
    __esModule: true,
    default: MockButton,
    ButtonType: { PRIMARY: "primary" },
  };
});

jest.mock("../../shared/Input/Input", () => {
  interface InputProps {
    value: string;
    onChange: (value: string) => void;
    label: string;
    disabled: boolean;
  }
  const MockInput = ({ value, onChange, label, disabled }: InputProps) => (
    <input
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={label}
      disabled={disabled}
      data-testid={`input-${label?.toLowerCase().replace(/[^a-z0-9]/g, "-")}`}
    />
  );
  MockInput.displayName = "MockInput";
  return MockInput;
});

import Signup from "./Signup";

describe("Signup Component", () => {
  const mockProps = {
    onVerifyEmail: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockSignInWithOtp.mockReset();
    (window.sessionStorage.setItem as jest.Mock).mockClear();
  });

  test("renders the signup form", () => {
    render(<Signup {...mockProps} />);

    expect(screen.getByTestId("header")).toBeInTheDocument();
    expect(screen.getByTestId("footer")).toBeInTheDocument();
    expect(screen.getByTestId("signup-button")).toBeInTheDocument();
  });

  test("updates input values when typing", () => {
    render(<Signup {...mockProps} />);

    const nameInput = screen.getByTestId("input-name");
    fireEvent.change(nameInput, { target: { value: "John Doe" } });
    expect(nameInput).toHaveValue("John Doe");
  });

  test("enables submit button when form is valid", () => {
    render(<Signup {...mockProps} />);

    const nameInput = screen.getByTestId("input-name");
    const emailInput = screen.getByTestId("input-email");
    const consentCheckbox = screen.getByRole("checkbox");
    const submitButton = screen.getByTestId("signup-button");

    expect(submitButton).toBeDisabled();

    fireEvent.change(nameInput, { target: { value: "John Doe" } });
    fireEvent.change(emailInput, { target: { value: "<EMAIL>" } });
    fireEvent.click(consentCheckbox);

    expect(submitButton).not.toBeDisabled();
  });

  test("disables submit button when form is incomplete", () => {
    render(<Signup {...mockProps} />);

    const submitButton = screen.getByTestId("signup-button");
    expect(submitButton).toBeDisabled();
  });

  test("calls Supabase on valid form submission", async () => {
    mockSignInWithOtp.mockResolvedValue({ error: null });

    render(<Signup {...mockProps} />);

    fireEvent.change(screen.getByTestId("input-name"), {
      target: { value: "John Doe" },
    });
    fireEvent.change(screen.getByTestId("input-email"), {
      target: { value: "<EMAIL>" },
    });
    fireEvent.click(screen.getByRole("checkbox"));

    fireEvent.click(screen.getByTestId("signup-button"));

    await waitFor(() => {
      expect(mockSignInWithOtp).toHaveBeenCalledWith({
        email: "<EMAIL>",
        options: {
          shouldCreateUser: true,
          data: {
            first_name: "John",
            last_name: "Doe",
            phone_number: null,
            consent_given: true,
          },
        },
      });
    });
  });

  test("handles signup success", async () => {
    mockSignInWithOtp.mockResolvedValue({ error: null });

    render(<Signup {...mockProps} />);

    fireEvent.change(screen.getByTestId("input-name"), {
      target: { value: "John Doe" },
    });
    fireEvent.change(screen.getByTestId("input-email"), {
      target: { value: "<EMAIL>" },
    });
    fireEvent.click(screen.getByRole("checkbox"));

    fireEvent.click(screen.getByTestId("signup-button"));

    await waitFor(() => {
      expect(window.sessionStorage.setItem).toHaveBeenCalledWith(
        "ivf_signup_data",
        JSON.stringify({
          name: "John Doe",
          email: "<EMAIL>",
          phone: "",
          consent: true,
        })
      );
      expect(mockProps.onVerifyEmail).toHaveBeenCalledTimes(1);
    });
  });

  test("handles signup error", async () => {
    mockSignInWithOtp.mockResolvedValue({
      error: { message: "Invalid email" },
    });

    render(<Signup {...mockProps} />);

    fireEvent.change(screen.getByTestId("input-name"), {
      target: { value: "John Doe" },
    });
    fireEvent.change(screen.getByTestId("input-email"), {
      target: { value: "<EMAIL>" },
    });
    fireEvent.click(screen.getByRole("checkbox"));

    fireEvent.click(screen.getByTestId("signup-button"));

    await waitFor(() => {
      expect(screen.getByText("Invalid email")).toBeInTheDocument();
    });

    expect(mockProps.onVerifyEmail).not.toHaveBeenCalled();
    expect(window.sessionStorage.setItem).not.toHaveBeenCalled();
  });

  test("shows loading state during signup", async () => {
    mockSignInWithOtp.mockImplementation(
      () =>
        new Promise((resolve) =>
          setTimeout(() => resolve({ error: null }), 100)
        )
    );

    render(<Signup {...mockProps} />);

    fireEvent.change(screen.getByTestId("input-name"), {
      target: { value: "John Doe" },
    });
    fireEvent.change(screen.getByTestId("input-email"), {
      target: { value: "<EMAIL>" },
    });
    fireEvent.click(screen.getByRole("checkbox"));

    fireEvent.click(screen.getByTestId("signup-button"));

    expect(screen.getByText("Creating Account...")).toBeInTheDocument();

    await waitFor(() => {
      expect(screen.getByText("Verify Email")).toBeInTheDocument();
    });
  });

  test("handles existing user error", async () => {
    mockSignInWithOtp.mockResolvedValue({
      error: { message: "User already registered" },
    });

    render(<Signup {...mockProps} />);

    fireEvent.change(screen.getByTestId("input-name"), {
      target: { value: "John Doe" },
    });
    fireEvent.change(screen.getByTestId("input-email"), {
      target: { value: "<EMAIL>" },
    });
    fireEvent.click(screen.getByRole("checkbox"));

    fireEvent.click(screen.getByTestId("signup-button"));

    await waitFor(() => {
      expect(
        screen.getByText(/An account with this email already exists/)
      ).toBeInTheDocument();
    });
  });
});
