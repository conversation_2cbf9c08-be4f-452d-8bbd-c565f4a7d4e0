"use client";

import React, { useState } from "react";
import Image from "next/image";
import Header, { HeaderState } from "../../shared/Header/Header";
import Footer from "../../shared/Footer/Footer";
import Button, { ButtonType } from "../../shared/Button/Button";
import PasswordInput from "@/components/shared/PasswordInput";
import StepHeader from "../../shared/StepHeader/StepHeader";
import NeedHelp from "../../shared/NeedHelp/NeedHelp";
import { createClient } from "@/utils/supabase/client";
import { getGuestSessionToken } from "@/utils/guestSessionUtils";

// Password Strength Indicator Component
const PasswordStrengthIndicator: React.FC<{ newPassword: string }> = ({
  newPassword,
}) => (
  <div className="flex flex-col gap-2">
    {/* Strength Bar */}
    <div className="w-full h-2 rounded bg-gray-200 overflow-hidden mb-2">
      <div
        style={{
          width: (() => {
            let score = 0;
            if (newPassword.length >= 8) score++;
            if (/[a-z]/.test(newPassword)) score++;
            if (/[A-Z]/.test(newPassword)) score++;
            if (/[@&%$!#^*()]/.test(newPassword)) score++;
            return `${(score / 4) * 100}%`;
          })(),
          background: (() => {
            let score = 0;
            if (newPassword.length >= 8) score++;
            if (/[a-z]/.test(newPassword)) score++;
            if (/[A-Z]/.test(newPassword)) score++;
            if (/[@&%$!#^*()]/.test(newPassword)) score++;
            if (score === 0) return "#e5e7eb"; // gray-200
            if (score === 1) return "#f87171"; // red-400
            if (score === 2) return "#fbbf24"; // yellow-400
            if (score === 3) return "#60a5fa"; // blue-400
            return "#22c55e"; // green-500
          })(),
          height: "100%",
          transition: "width 0.3s, background 0.3s",
        }}
      />
    </div>
    {/* Password Rules */}
    <div className="grid md:grid-cols-2 gap-2">
      <label className="flex items-center gap-2 text-sm">
        <span
          className={`inline-flex items-center justify-center w-5 h-5 border-2 ${
            newPassword.length >= 8
              ? "border-green-600 bg-green-600"
              : "border-gray-300 bg-white"
          } rounded-full transition-colors`}
          aria-hidden="true"
        >
          {newPassword.length >= 8 && (
            <svg
              className="w-3 h-3 text-white"
              fill="none"
              stroke="currentColor"
              strokeWidth="3"
              viewBox="0 0 16 16"
            >
              <path
                d="M4 8l3 3 5-5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          )}
        </span>
        <span
          className={
            newPassword.length >= 8 ? "text-green-600" : "text-gray-500"
          }
        >
          At least 8 characters
        </span>
      </label>
      <label className="flex items-center gap-2 text-sm">
        <span
          className={`inline-flex items-center justify-center w-5 h-5 border-2 ${
            /[a-z]/.test(newPassword)
              ? "border-green-600 bg-green-600"
              : "border-gray-300 bg-white"
          } rounded-full transition-colors`}
          aria-hidden="true"
        >
          {/[a-z]/.test(newPassword) && (
            <svg
              className="w-3 h-3 text-white"
              fill="none"
              stroke="currentColor"
              strokeWidth="3"
              viewBox="0 0 16 16"
            >
              <path
                d="M4 8l3 3 5-5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          )}
        </span>
        <span
          className={
            /[a-z]/.test(newPassword) ? "text-green-600" : "text-gray-500"
          }
        >
          1 lowercase letter
        </span>
      </label>
      <label className="flex items-center gap-2 text-sm">
        <span
          className={`inline-flex items-center justify-center w-5 h-5 border-2 ${
            /[A-Z]/.test(newPassword)
              ? "border-green-600 bg-green-600"
              : "border-gray-300 bg-white"
          } rounded-full transition-colors`}
          aria-hidden="true"
        >
          {/[A-Z]/.test(newPassword) && (
            <svg
              className="w-3 h-3 text-white"
              fill="none"
              stroke="currentColor"
              strokeWidth="3"
              viewBox="0 0 16 16"
            >
              <path
                d="M4 8l3 3 5-5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          )}
        </span>
        <span
          className={
            /[A-Z]/.test(newPassword) ? "text-green-600" : "text-gray-500"
          }
        >
          1 uppercase letter
        </span>
      </label>
      <label className="flex items-center gap-2 text-sm">
        <span
          className={`inline-flex items-center justify-center w-5 h-5 border-2 ${
            /[@&%$!#^*()]/.test(newPassword)
              ? "border-green-600 bg-green-600"
              : "border-gray-300 bg-white"
          } rounded-full transition-colors`}
          aria-hidden="true"
        >
          {/[@&%$!#^*()]/.test(newPassword) && (
            <svg
              className="w-3 h-3 text-white"
              fill="none"
              stroke="currentColor"
              strokeWidth="3"
              viewBox="0 0 16 16"
            >
              <path
                d="M4 8l3 3 5-5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          )}
        </span>
        <span
          className={
            /[@&%$!#^*()]/.test(newPassword)
              ? "text-green-600"
              : "text-gray-500"
          }
        >
          1 symbol (@ & % $)
        </span>
      </label>
    </div>
  </div>
);

const CreatePassword: React.FC = () => {
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newPassword || !confirmPassword) {
      setError("Please fill in all fields");
      return;
    }

    if (newPassword !== confirmPassword) {
      setError("Passwords do not match");
      return;
    }

    if (newPassword.length < 8) {
      setError("Password must be at least 8 characters long");
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      const supabase = createClient();

      // Update the user's password
      const { error: updateError } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (updateError) {
        setError(updateError.message);
        console.error("Password update error:", updateError);
        return;
      }

      // Get guest session token from storage
      const guestSessionToken = getGuestSessionToken();
      if (!guestSessionToken) {
        console.error("Guest session token not found");
        return;
      }

      // Get the access token directly after updating the password
      // This should be available since we just updated the user's password
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        setError("Authentication error: User not found");
        return;
      }

      // Create a new session if needed
      const {
        data: { session },
      } = await supabase.auth.refreshSession();
      const accessToken = session?.access_token;

      if (!accessToken) {
        setError("Authentication error: No access token available");
        return;
      }
    } catch (err) {
      setError("An unexpected error occurred. Please try again.");
      console.error("Create password error:", err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSetPassword = async () => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    await handlePasswordSubmit(new Event("submit") as any);
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header state={HeaderState.LOGIN} />
      <main className="flex-1 flex justify-center py-4 px-1 md:py-6 md:px-[9.375rem]">
        <div className="max-w-[40.125rem] w-full">
          <StepHeader currentStep={6} totalSteps={6} title="Set Password" />

          <div className="bg-white rounded-lg">
            <div className="max-w-md mx-auto space-y-8">
              <div className="text-center flex flex-col">
                <h1 className="text-center pt-[0.563rem] text-2xl font-semibold text-[var(--grey-7)] mb-2">
                  Set New Password
                  <div className="flex justify-center py-1">
                    <Image
                      src="/assets/loginPage/Line.png"
                      alt="Decorative line"
                      className="h-1 w-16"
                      width={100}
                      height={9}
                    />
                  </div>
                </h1>
                <p className="text-[var(--grey-6)] text-base font-medium">
                  Create a secure password to complete your GIVF account setup.
                </p>
              </div>

              <form onSubmit={handlePasswordSubmit}>
                <div className="space-y-6">
                  <div className="flex flex-col gap-2">
                    <label
                      htmlFor="newPassword"
                      className="block text-base font-medium text-[var(--grey-6)]"
                    >
                      New Password
                    </label>
                    <PasswordInput
                      id="newPassword"
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                      placeholder="Enter new password"
                      disabled={isLoading}
                      minLength={8}
                    />
                  </div>

                  <div className="flex flex-col gap-2">
                    <label
                      htmlFor="confirmPassword"
                      className="block text-base font-medium text-[var(--grey-6)]"
                    >
                      Confirm Password
                    </label>
                    <PasswordInput
                      id="confirmPassword"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      placeholder="Confirm new password"
                      disabled={isLoading}
                      minLength={8}
                    />
                  </div>

                  {/* Password Strength Indicator */}
                  {newPassword && (
                    <PasswordStrengthIndicator newPassword={newPassword} />
                  )}

                  {newPassword &&
                    confirmPassword &&
                    newPassword !== confirmPassword && (
                      <div className="text-[var(--red-6)] text-sm text-center bg-red-50 border border-red-200 rounded-sm p-3">
                        Passwords do not match
                      </div>
                    )}

                  {error && (
                    <div className="text-[var(--red-6)] text-sm text-center bg-red-50 border border-red-200 rounded-sm p-3">
                      {error}
                    </div>
                  )}

                  <div className="pt-2">
                    <Button
                      type={ButtonType.PRIMARY}
                      text={isLoading ? "Setting Password..." : "Set Password"}
                      onClick={handleSetPassword}
                      disabled={
                        isLoading ||
                        !newPassword ||
                        !confirmPassword ||
                        newPassword !== confirmPassword ||
                        newPassword.length < 8
                      }
                      className="w-full"
                    />
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </main>
      <NeedHelp className="ml-6 md:ml-37.5 md:my-10 my-6" />
      <Footer />
    </div>
  );
};

export default CreatePassword;
