import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import Verify from "./Verify";

// Mock Next.js router
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  }),
}));

// Mock AuthProvider to avoid Supabase dependency
jest.mock("@/contexts/AuthContext", () => ({
  useAuth: () => ({ user: { id: "test" }, loading: false }),
  AuthProvider: ({ children }: { children: React.ReactNode }) => (
    <>{children}</>
  ),
}));

// Set dummy env vars for Supabase if needed
process.env.NEXT_PUBLIC_SUPABASE_URL = "http://localhost:54321";
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = "test_anon_key";

describe("Verify Details", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the component", () => {
    render(<Verify />);
    expect(screen.getByText("Verify Details")).toBeInTheDocument();
  });

  it("renders the step header", () => {
    render(<Verify />);
    expect(screen.getByText("Verify Details")).toBeInTheDocument();
    expect(
      screen.getByText((content, element) => {
        return element?.textContent === "Step 4 of 5";
      })
    ).toBeInTheDocument();
  });

  it("renders Continue with Google button", () => {
    render(<Verify />);
    expect(screen.getByText("Continue with Google")).toBeInTheDocument();
  });

  it("renders Continue with Email button", () => {
    render(<Verify />);
    expect(screen.getByText("Continue with Email")).toBeInTheDocument();
  });

  it("renders Sign in here button", () => {
    render(<Verify />);
    expect(screen.getByText("Sign in here")).toBeInTheDocument();
  });

  it("renders Back button", () => {
    render(<Verify />);
    expect(screen.getByText("Back")).toBeInTheDocument();
  });

  it("renders 'Already have an Account?' text", () => {
    render(<Verify />);
    expect(screen.getByText("Already have an Account?")).toBeInTheDocument();
  });

  it("Continue with Google button is clickable", () => {
    render(<Verify />);

    const googleButton = screen.getByText("Continue with Google");
    expect(googleButton).not.toBeDisabled();
    expect(() => fireEvent.click(googleButton)).not.toThrow();
  });

  it("Continue with Email button is clickable", () => {
    render(<Verify />);

    const emailButton = screen.getByText("Continue with Email");
    expect(emailButton).not.toBeDisabled();
    expect(() => fireEvent.click(emailButton)).not.toThrow();
  });

  it("Sign in here button is clickable", () => {
    render(<Verify />);

    const signInButton = screen.getByText("Sign in here");
    expect(signInButton).not.toBeDisabled();
    expect(() => fireEvent.click(signInButton)).not.toThrow();
  });

  it("Back button is clickable", () => {
    render(<Verify />);

    const backButton = screen.getByText("Back");
    expect(backButton).not.toBeDisabled();
    expect(() => fireEvent.click(backButton)).not.toThrow();
  });

  it("renders Google icon in Continue with Google button", () => {
    render(<Verify />);

    const googleButton = screen
      .getByText("Continue with Google")
      .closest("button");
    const googleIcon = googleButton?.querySelector("svg");

    expect(googleIcon).toBeInTheDocument();
    expect(googleIcon).toHaveAttribute("width", "20");
    expect(googleIcon).toHaveAttribute("height", "20");
  });

  it("renders Email icon in Continue with Email button", () => {
    render(<Verify />);

    const emailButton = screen
      .getByText("Continue with Email")
      .closest("button");
    const emailIcon = emailButton?.querySelector("svg");

    expect(emailIcon).toBeInTheDocument();
    expect(emailIcon).toHaveAttribute("width", "20");
    expect(emailIcon).toHaveAttribute("height", "20");
  });

  it("renders Back icon in Back button", () => {
    render(<Verify />);

    const backButton = screen.getByText("Back").closest("button");
    const backIcon = backButton?.querySelector("svg");

    expect(backIcon).toBeInTheDocument();
    expect(backIcon).toHaveAttribute("width", "16");
    expect(backIcon).toHaveAttribute("height", "16");
  });

  it("renders Header component with login state", () => {
    render(<Verify />);
    expect(screen.getByRole("banner")).toBeInTheDocument();
    // Should show "Have an account? Login" instead of "Having Trouble? Get Help"
    expect(screen.queryByText("Having Trouble?")).not.toBeInTheDocument();
  });

  it("renders Footer component", () => {
    render(<Verify />);
    expect(screen.getByRole("contentinfo")).toBeInTheDocument();
  });

  it("renders NeedHelp component", () => {
    render(<Verify />);
    expect(screen.getByText(/Need Help/)).toBeInTheDocument();
  });

  it("has proper layout structure", () => {
    const { container } = render(<Verify />);

    expect(container.firstChild).toHaveClass("min-h-screen");
    expect(container.firstChild).toHaveClass("flex");
    expect(container.firstChild).toHaveClass("flex-col");
  });

  it("has proper main content layout", () => {
    render(<Verify />);

    const main = screen.getByRole("main");
    expect(main).toHaveClass("flex-1");
    expect(main).toHaveClass("flex");
    expect(main).toHaveClass("justify-center");
  });

  it("has proper button styling", () => {
    render(<Verify />);

    const googleButton = screen.getByText("Continue with Google");
    const emailButton = screen.getByText("Continue with Email");
    const backButton = screen.getByText("Back");

    expect(googleButton).toHaveAttribute("type", "button");
    expect(emailButton).toHaveAttribute("type", "button");
    expect(backButton).toHaveAttribute("type", "button");
  });

  it("has proper spacing and layout structure", () => {
    const { container } = render(<Verify />);

    const mainContent = container.querySelector(".space-y-6");
    expect(mainContent).toBeInTheDocument();

    const buttonContainer = container.querySelector(".flex.flex-col.gap-6");
    expect(buttonContainer).toBeInTheDocument();
  });

  it("displays correct step information", () => {
    render(<Verify />);

    expect(
      screen.getByText((content, element) => {
        return element?.textContent === "Step 4 of 5";
      })
    ).toBeInTheDocument();
  });

  it("has centered layout for sign in section", () => {
    render(<Verify />);

    const signInSection = screen
      .getByText("Already have an Account?")
      .closest(".text-center");
    expect(signInSection).toBeInTheDocument();
  });

  it("has proper text styling", () => {
    render(<Verify />);

    const accountText = screen.getByText("Already have an Account?");
    expect(accountText).toHaveClass("text-[var(--grey-6)]");
    expect(accountText).toHaveClass("text-base");
    expect(accountText).toHaveClass("mb-4");
  });

  it("has proper Sign in here button styling", () => {
    render(<Verify />);

    const signInButton = screen.getByText("Sign in here");
    expect(signInButton).toHaveClass("text-[var(--red-6)]");
    expect(signInButton).toHaveClass("underline");
    expect(signInButton).toHaveClass("font-medium");
  });

  it("renders without step 5 indicator", () => {
    render(<Verify />);

    // Should show step 4 of 5, not step 5 of 5
    expect(
      screen.getByText((content, element) => {
        return element?.textContent === "Step 4 of 5";
      })
    ).toBeInTheDocument();

    expect(
      screen.queryByText((content, element) => {
        return element?.textContent === "Step 5 of 5";
      })
    ).not.toBeInTheDocument();
  });

  it("has proper accessibility attributes", () => {
    render(<Verify />);

    const googleButton = screen.getByText("Continue with Google");
    const emailButton = screen.getByText("Continue with Email");
    const signInButton = screen.getByText("Sign in here");
    const backButton = screen.getByText("Back");

    expect(googleButton).toHaveAttribute("type", "button");
    expect(emailButton).toHaveAttribute("type", "button");
    expect(signInButton).toHaveAttribute("type", "button");
    expect(backButton).toHaveAttribute("type", "button");
  });

  it("handles multiple button clicks without errors", () => {
    render(<Verify />);

    const googleButton = screen.getByText("Continue with Google");
    const emailButton = screen.getByText("Continue with Email");

    expect(() => {
      fireEvent.click(googleButton);
      fireEvent.click(emailButton);
    }).not.toThrow();
  });

  it("has proper Google icon colors", () => {
    render(<Verify />);

    const googleButton = screen
      .getByText("Continue with Google")
      .closest("button");
    const googleIcon = googleButton?.querySelector("svg");
    const paths = googleIcon?.querySelectorAll("path");

    expect(paths).toHaveLength(4);
    expect(paths?.[0]).toHaveAttribute("fill", "#4285F4");
    expect(paths?.[1]).toHaveAttribute("fill", "#34A853");
    expect(paths?.[2]).toHaveAttribute("fill", "#FBBC05");
    expect(paths?.[3]).toHaveAttribute("fill", "#EA4335");
  });

  it("has proper email icon attributes", () => {
    render(<Verify />);

    const emailButton = screen
      .getByText("Continue with Email")
      .closest("button");
    const emailIcon = emailButton?.querySelector("svg");

    expect(emailIcon).toHaveAttribute("stroke", "currentColor");
    expect(emailIcon).toHaveAttribute("stroke-width", "2");
    expect(emailIcon).toHaveAttribute("fill", "none");
  });

  it("has proper back icon attributes", () => {
    render(<Verify />);

    const backButton = screen.getByText("Back").closest("button");
    const backIcon = backButton?.querySelector("svg");

    expect(backIcon).toHaveAttribute("stroke", "currentColor");
    expect(backIcon).toHaveAttribute("stroke-width", "2");
    expect(backIcon).toHaveAttribute("fill", "none");
  });

  it("handles component mounting and unmounting", () => {
    const { unmount } = render(<Verify />);

    expect(screen.getByText("Verify Details")).toBeInTheDocument();

    unmount();

    expect(screen.queryByText("Verify Details")).not.toBeInTheDocument();
  });

  it("has proper gap spacing between sections", () => {
    const { container } = render(<Verify />);

    const mainContainer = container.querySelector(".gap-20");
    expect(mainContainer).toBeInTheDocument();

    const buttonContainer = container.querySelector(".gap-6");
    expect(buttonContainer).toBeInTheDocument();
  });

  it("renders in correct order", () => {
    render(<Verify />);

    const buttons = screen.getAllByRole("button");
    const buttonTexts = buttons.map((button) => button.textContent);

    // Should include these buttons in the component (plus header/footer buttons)
    expect(buttonTexts).toContain("Continue with Google");
    expect(buttonTexts).toContain("Continue with Email");
    expect(buttonTexts).toContain("Sign in here");
    expect(buttonTexts).toContain("Back");
  });
});
