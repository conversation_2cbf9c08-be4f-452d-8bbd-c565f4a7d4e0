import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import Verify from "./Verify";
import { AuthProvider } from "@/contexts/AuthContext";
import { ToastProvider } from "@/contexts/ToastContext";

const meta: Meta<typeof Verify> = {
  title: "Steps/VerifyDetails",
  component: Verify,
  parameters: {
    layout: "fullscreen",
  },
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <ToastProvider duration={4000} maxToasts={5}>
        <AuthProvider>
          <Story />
        </AuthProvider>
      </ToastProvider>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const WithCustomHandlers: Story = {
  args: {},
  render: () => {
    // This would show how the component works with custom handlers
    // In a real implementation, you'd pass these as props
    return <Verify />;
  },
};

export const MobileView: Story = {
  args: {},
  parameters: {
    viewport: {
      defaultViewport: "mobile1",
    },
  },
};

export const TabletView: Story = {
  args: {},
  parameters: {
    viewport: {
      defaultViewport: "tablet",
    },
  },
};
