import type { <PERSON>a, StoryObj } from "@storybook/nextjs";
import VerifyEmail from "./Verify";
import { AuthProvider } from "@/contexts/AuthContext";
import { ToastProvider } from "@/contexts/ToastContext";

const meta: Meta<typeof VerifyEmail> = {
  title: "Steps/VerifyEmail",
  component: VerifyEmail,
  parameters: {
    layout: "fullscreen",
  },
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <ToastProvider duration={4000} maxToasts={5}>
        <AuthProvider>
          <Story />
        </AuthProvider>
      </ToastProvider>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {},
};

export const MobileView: Story = {
  args: {},
  parameters: {
    viewport: {
      defaultViewport: "mobile1",
    },
  },
};

export const TabletView: Story = {
  args: {},
  parameters: {
    viewport: {
      defaultViewport: "tablet",
    },
  },
};

export const LoadingState: Story = {
  args: {},
  play: async ({ canvasElement }) => {
    const canvas = canvasElement;
    const resendButton = canvas.querySelector("button") as HTMLButtonElement;
    if (resendButton && resendButton.textContent?.includes("Resend Code")) {
      resendButton.click();
    }
  },
};
