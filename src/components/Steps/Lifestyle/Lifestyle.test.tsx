/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";

// Mock DynamicFormPage since we're testing the wrapper
jest.mock("../Common/DynamicFormPage", () => {
  return function MockDynamicFormPage(props: any) {
    return (
      <div data-testid="dynamic-form-page">
        <div data-testid="title">{props.title}</div>
        <div data-testid="current-step">{props.currentStep}</div>
        <div data-testid="total-steps">{props.totalSteps}</div>
        <div data-testid="submit-button-text">{props.submitButtonText}</div>
        <div data-testid="loading">{String(props.loading)}</div>
        <div data-testid="error">{props.error || "null"}</div>
        <div data-testid="questions-count">{props.questions?.length || 0}</div>
      </div>
    );
  };
});

// Import after mocks
import Lifestyle from "./Lifestyle";

const mockOnNext = jest.fn();

describe("Lifestyle Component", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders correctly with questions", () => {
    const mockQuestions = [{} as any, {} as any, {} as any];

    render(<Lifestyle onNext={mockOnNext} questions={mockQuestions} />);

    expect(screen.getByTestId("dynamic-form-page")).toBeInTheDocument();
    expect(screen.getByTestId("title")).toHaveTextContent(
      "Lifestyle & Psychosocial"
    );
    expect(screen.getByTestId("current-step")).toHaveTextContent("2");
    expect(screen.getByTestId("total-steps")).toHaveTextContent("5");
    expect(screen.getByTestId("submit-button-text")).toHaveTextContent(
      "Next Step (Environmental)"
    );
    expect(screen.getByTestId("questions-count")).toHaveTextContent("3");
  });

  it("handles loading state", () => {
    render(<Lifestyle onNext={mockOnNext} loading={true} />);

    expect(screen.getByTestId("loading")).toHaveTextContent("true");
    expect(screen.getByTestId("questions-count")).toHaveTextContent("0");
  });

  it("handles error state", () => {
    const errorMessage = "Failed to load lifestyle questions";

    render(<Lifestyle onNext={mockOnNext} error={errorMessage} />);

    expect(screen.getByTestId("error")).toHaveTextContent(errorMessage);
    expect(screen.getByTestId("loading")).toHaveTextContent("false");
  });

  it("renders without onNext callback", () => {
    render(<Lifestyle />);

    expect(screen.getByTestId("dynamic-form-page")).toBeInTheDocument();
  });

  it("passes questions prop to DynamicFormPage", () => {
    const mockQuestions = [{} as any, {} as any, {} as any, {} as any]; // 4 mock questions

    render(<Lifestyle onNext={mockOnNext} questions={mockQuestions} />);

    expect(screen.getByTestId("questions-count")).toHaveTextContent("4");
  });

  it("maintains correct step information for lifestyle form", () => {
    render(<Lifestyle onNext={mockOnNext} />);

    // Verify this is step 2 of 5 for the lifestyle factors
    expect(screen.getByTestId("current-step")).toHaveTextContent("2");
    expect(screen.getByTestId("total-steps")).toHaveTextContent("5");
    expect(screen.getByTestId("title")).toHaveTextContent(
      "Lifestyle & Psychosocial"
    );
  });

  it("has correct submit button text for next step", () => {
    render(<Lifestyle onNext={mockOnNext} />);

    expect(screen.getByTestId("submit-button-text")).toHaveTextContent(
      "Next Step (Environmental)"
    );
  });

  it("handles empty questions array gracefully", () => {
    render(<Lifestyle onNext={mockOnNext} questions={[]} />);

    expect(screen.getByTestId("questions-count")).toHaveTextContent("0");
    expect(screen.getByTestId("loading")).toHaveTextContent("false");
    expect(screen.getByTestId("error")).toHaveTextContent("null");
  });

  it("passes through additional props to DynamicFormPage", () => {
    render(
      <Lifestyle
        onNext={mockOnNext}
        isSubmitting={true}
        errorMessage="Test error"
      />
    );

    // The component should render without errors when additional props are passed
    expect(screen.getByTestId("dynamic-form-page")).toBeInTheDocument();
  });
});
