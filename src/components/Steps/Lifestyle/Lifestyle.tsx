import React from "react";
import DynamicFormPage from "../Common/DynamicFormPage";
import { Question } from "@/types/questions";

export interface LifestyleData {
  stressLevel: number;
  height: string;
  dietType: string;
  exercise: string;
  sleepQuality: number;
  emotionalSupport: string;
  smoking: string;
}

export interface LifestyleProps {
  onNext?: (data: Record<string, string>) => void;
  onCancel?: () => void;
  questions?: Question[];
  loading?: boolean;
  error?: string | null;
  isSubmitting?: boolean;
  errorMessage?: string;
}

const Lifestyle: React.FC<LifestyleProps> = ({ 
  onNext, 
  questions = [],
  loading = false,
  error = null,
  isSubmitting = false,
  errorMessage = "",
  ...props 
}) => {
  return (
    <DynamicFormPage
      onNext={onNext}
      title="Lifestyle & Psychosocial"
      submitButtonText="Next Step (Environmental)"
      currentStep={2}
      totalSteps={5}
      questions={questions}
      loading={loading}
      error={error}
      isSubmitting={isSubmitting}
      errorMessage={errorMessage}
      {...props}
    />
  );
};

export default Lifestyle;
