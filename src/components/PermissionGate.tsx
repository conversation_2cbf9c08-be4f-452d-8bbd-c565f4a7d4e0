"use client";

import React from "react";
import { useUserCan } from "@/hooks/useUserCan";

interface PermissionGateProps {
  children: React.ReactNode;
  resource_action: string;
  fallback?: React.ReactNode;
  showLoadingState?: boolean;
}

export default function PermissionGate({ 
  children, 
  resource_action, 
  fallback = null,
  showLoadingState = false 
}: PermissionGateProps) {
  const { data: isAuthorized, isLoading, error } = useUserCan(resource_action);

  if (isLoading && showLoadingState) {
    return (
      <div className="animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    );
  }

  if (error) {
    console.error('Permission check failed:', error);
    return fallback;
  }

  if (!isAuthorized) {
    return fallback;
  }

  return <>{children}</>;
} 