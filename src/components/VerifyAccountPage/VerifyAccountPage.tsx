/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Header, { HeaderState } from "../shared/Header/Header";
import Footer from "../shared/Footer/Footer";
import Button, { ButtonType } from "../shared/Button/Button";
import OTPInput from "../shared/OTPInput/OTPInput";
import PageHeader from "../shared/PageHeader/PageHeader";
import { createClient } from "@/utils/supabase/client";
import {
  getGuestSessionToken,
  clearGuestSessionToken,
} from "@/utils/guestSessionUtils";
import { useCountdown } from "@/hooks/useCountdown";

const VerifyAccountPage = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [otp, setOtp] = useState("");
  const [isResending, setIsResending] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [error, setError] = useState("");
  const [userEmail, setUserEmail] = useState("");
  const [otpKey, setOtpKey] = useState(0); // Key to force OTP component re-render
  const countdown = useCountdown(120);

  React.useEffect(() => {
    // Get email from sessionStorage
    const signupData = sessionStorage.getItem("ivf_signup_data");
    if (signupData) {
      try {
        const parsed = JSON.parse(signupData);
        setUserEmail(parsed.email || "");
      } catch (error) {
        console.error("Error parsing signup data:", error);
        setUserEmail("");
      }
    }
  }, []);

  const clearOTPInput = () => {
    setOtp("");
    // Force re-render of OTPInput component by changing key
    setOtpKey((prev) => prev + 1);
  };

  const handleOTPChange = (otpValue: string) => {
    setOtp(otpValue);
  };

  const handleOTPComplete = async (otpValue: string) => {
    if (!userEmail) {
      setError("Email not found. Please try signing up again.");
      return;
    }
    setIsVerifying(true);
    setError("");
    try {
      const supabase = createClient();
      const { data, error: verifyError } = await supabase.auth.verifyOtp({
        email: userEmail,
        token: otpValue,
        type: "email",
      });
      if (verifyError) {
        setError(
          "Invalid verification code. Please check your code and try again."
        );
        return;
      }
      if (data.user) {
        // Determine redirect target
        const redirectUrl = searchParams.get("redirect");
        const from = searchParams.get("from");
        console.log("from", from);

        if (redirectUrl) {
          try {
            // Attempt to convert guest data if available
            const guestSessionToken = getGuestSessionToken();
            if (guestSessionToken) {
              const { data: sessionData } = await supabase.auth.getSession();
              const accessToken = sessionData.session?.access_token;
              if (accessToken) {
                await fetch("/api/v1/ivf-assessment/convert-guest", {
                  method: "POST",
                  headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${accessToken}`,
                  },
                  body: JSON.stringify({
                    guestSessionToken,
                    userId: data.user.id,
                  }),
                }).catch(() => {});
                // Clear guest session token regardless of result
                clearGuestSessionToken();
              }

              if (from === "verify-details") {
                sessionStorage.removeItem("Test_status");
                sessionStorage.setItem("Result_status", "completed");
              }
            }
          } catch (e) {
            // Ignore conversion errors; proceed with redirect
          }

          router.push(decodeURIComponent(redirectUrl));
        } else {
          // Default behavior for normal registration
          router.push("/user/dashboard?newUser=true");
        }
      }
    } catch (err) {
      console.log("err", err);
      setError("An unexpected error occurred. Please try again.");
      clearOTPInput();
    } finally {
      setIsVerifying(false);
    }
  };

  const handleResendCode = () => {
    setIsResending(true);
    // Simulate resend API call
    setTimeout(() => {
      setIsResending(false);
      console.log("Code resent");
      clearOTPInput();
      countdown.reset(120);
    }, 2000);
  };

  const handleContinueToDashboard = async () => {
    if (otp.length === 6) {
      await handleOTPComplete(otp);
    }
  };

  const maskedEmail = userEmail
    ? userEmail.replace(/(.{1}).*(@.*)/, (m, a, b) => a + "*****" + b)
    : "p*****@g***.com";

  return (
    <div className="min-h-screen flex flex-col">
      <Header state={HeaderState.HELP} />
      <main className="flex-1 flex justify-center items-center py-4 px-4 md:py-6 md:px-[9.375rem]">
        <div className="max-w-[40.125rem] w-full">
          <div className="bg-white rounded-lg">
            <div className="max-w-md mx-auto space-y-8 text-center">
              {/* Title */}
              <div className="mb-12">
                <PageHeader title="Verify Your Account" />
              </div>

              {/* Instructions */}
              <div className="space-y-2">
                <p className="text-[var(--grey-6)] text-base">
                  We've sent you a passcode.
                </p>
                <p className="text-[var(--grey-6)] text-base">
                  Please check your inbox at {maskedEmail}.
                </p>
              </div>

              {/* Error Message */}
              {error && (
                <div className="bg-red-50 border border-red-200 rounded-md p-4 text-left">
                  <div className="text-red-800 text-sm">{error}</div>
                </div>
              )}

              {/* OTP Input */}
              <div className="flex justify-center mb-6">
                <OTPInput
                  length={6}
                  onChange={handleOTPChange}
                  onComplete={handleOTPComplete}
                  autoFocus={true}
                />
              </div>

              {/* Resend Code Link with Countdown Timer */}
              <div className="flex items-center justify-center gap-1">
                <button
                  type="button"
                  onClick={handleResendCode}
                  disabled={isResending || !countdown.isExpired}
                  className="text-[var(--grey-5)] text-base hover:text-[var(--grey-6)] font-bold transition-colors duration-200 underline disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isResending ? "Resending..." : "Resend Code"}
                </button>

                {/* Countdown Timer Display */}
                {!countdown.isExpired && (
                  <p className="text-[var(--grey-5)] text-base">
                    in{" "}
                    <span className="text-[var(--violet-11)] text-base font-bold">
                      ({countdown.formattedTime})
                    </span>
                  </p>
                )}
              </div>

              {/* Additional info when timer is active */}
              {!countdown.isExpired && (
                <p className="text-[var(--red-6)] text-base">
                  Please do not refresh or close this page.
                </p>
              )}

              {/* Continue to Dashboard Button */}
              <div className="pt-4">
                <Button
                  type={ButtonType.PRIMARY}
                  text={isVerifying ? "Verifying..." : "Verify & Continue"}
                  onClick={handleContinueToDashboard}
                  disabled={otp.length !== 6 || isVerifying}
                  className="w-full"
                />
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default VerifyAccountPage;
