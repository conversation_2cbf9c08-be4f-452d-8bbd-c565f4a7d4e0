/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import VerifyAccountPage from "./VerifyAccountPage";

// Mock Next.js router
jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

// Mock AuthProvider to avoid Supabase dependency
jest.mock("@/contexts/AuthContext", () => ({
  useAuth: () => ({ user: null, loading: false }),
  AuthProvider: ({ children }: { children: React.ReactNode }) => (
    <>{children}</>
  ),
}));

// Mock Supabase client
jest.mock("@/utils/supabase/client", () => ({
  createClient: () => ({
    auth: {
      verifyOtp: jest
        .fn()
        .mockResolvedValue({ data: { user: true }, error: null }),
    },
  }),
}));

// Mock OTPInput
jest.mock("../shared/OTPInput/OTPInput", () => {
  return function MockOTPInput({ onChange, onComplete }: any) {
    return (
      <input
        data-testid="otp-field"
        onChange={(e) => {
          onChange(e.target.value);
          if (e.target.value.length === 5 && onComplete)
            onComplete(e.target.value);
        }}
      />
    );
  };
});

describe("VerifyAccountPage", () => {
  it("renders the title", () => {
    render(<VerifyAccountPage />);
    expect(screen.getByText(/verify your account/i)).toBeInTheDocument();
  });

  it("handles OTP input change and completion", async () => {
    render(<VerifyAccountPage />);
    const otpField = screen.getByTestId("otp-field");
    fireEvent.change(otpField, { target: { value: "12345" } });
    expect(otpField).toHaveValue("12345");
    // onComplete is called automatically in the mock above for 5 digits
    // You can add more assertions here if needed
  });

  it("handles resend code click", async () => {
    render(<VerifyAccountPage />);
    const resendButton = screen.getByText(/resend code/i);
    fireEvent.click(resendButton);
    expect(resendButton).toBeDisabled();
    await waitFor(() => expect(resendButton).not.toBeDisabled(), {
      timeout: 3000,
    });
  });

  it("disables continue button initially", () => {
    render(<VerifyAccountPage />);
    const button = screen.getByText(/verify & continue/i);
    expect(button).toBeDisabled();
  });

  // Add more simple tests as needed...
});
