# Authorization Components and Hooks

This folder contains components and hooks for implementing role-based access control (RBAC) using Supabase's `authorize` RPC function.

## Components

### AdminAuthWrapper
Protects admin routes by checking if the user has admin permissions. Automatically redirects unauthorized users to the login page.

```tsx
import AdminAuthWrapper from "@/components/AdminAuthWrapper";

export default function AdminLayout({ children }) {
  return (
    <AdminAuthWrapper>
      {/* Admin content */}
    </AdminAuthWrapper>
  );
}
```

### PermissionGate
Conditionally renders content based on user permissions. Useful for hiding/showing UI elements based on user access levels.

```tsx
import PermissionGate from "@/components/PermissionGate";

export default function MyComponent() {
  return (
    <div>
      <PermissionGate 
        resource_action="users.delete"
        fallback={<p>You don't have permission to delete users</p>}
      >
        <button>Delete User</button>
      </PermissionGate>
    </div>
  );
}
```

## Hooks

### useAdminAuth
Hook specifically for admin route protection. Returns authorization status and loading state.

```tsx
import { useAdminAuth } from "@/hooks/useAdminAuth";

export default function AdminPage() {
  const { isAuthorized, loading, user } = useAdminAuth();
  
  if (loading) return <div>Loading...</div>;
  if (!isAuthorized) return null; // Will redirect to login
  
  return <div>Admin content</div>;
}
```

### useUserCan
Generic hook for checking any permission. Uses React Query for caching and efficient data fetching.

```tsx
import { useUserCan } from "@/hooks/useUserCan";

export default function UserList() {
  const { data: canCreate, isLoading } = useUserCan('users.create');
  const { data: canDelete } = useUserCan('users.delete');
  
  return (
    <div>
      {canCreate && <button>Create User</button>}
      {canDelete && <button>Delete User</button>}
    </div>
  );
}
```

## Permission Format

Permissions follow the format `resource.action`, where:
- `resource` is the entity (e.g., 'users', 'profiles', 'admin')
- `action` is the operation ('create', 'read', 'update', 'delete', 'access')

Examples:
- `users.read` - Can view users
- `users.create` - Can create users  
- `profiles.update` - Can update profiles
- `admin.access` - Can access admin area

## Error Handling

All hooks and components include error handling:
- Network errors are logged to the console
- Failed permission checks default to `false` (no access)
- Loading states are provided for better UX 