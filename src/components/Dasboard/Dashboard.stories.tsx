import type { Meta, StoryObj } from "@storybook/nextjs";
import Dashboard from "./Dashboard";
import { AuthProvider } from "@/contexts/AuthContext";
import { ToastProvider } from "@/contexts/ToastContext";

const meta = {
  title: "Pages/Dashboard",
  component: Dashboard,
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component: `
# Dashboard Component

A comprehensive dashboard page for the IVF assessment application that provides users with an overview of their fertility journey.

## Features
- Personalized welcome message
- Key statistics cards (assessments, scores, fertility band)
- Recent assessments history
- Quick action buttons for common tasks
- Progress tracking with visual indicators
- Responsive design for all devices

## Usage
This page serves as the main hub after user login, providing:
- Overview of fertility assessment history
- Easy access to take new assessments
- Quick navigation to key features
- Progress tracking and insights
        `,
      },
    },
  },
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <ToastProvider duration={4000} maxToasts={5}>
        <AuthProvider>
          <Story />
        </AuthProvider>
      </ToastProvider>
    ),
  ],
} satisfies Meta<typeof Dashboard>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default dashboard with sample user data
 */
export const Default: Story = {};

/**
 * Dashboard for a user with multiple assessments
 */
export const ExperiencedUser: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Dashboard view for a user who has completed multiple assessments over time.",
      },
    },
  },
};

/**
 * Dashboard for a new user with minimal data
 */
export const NewUser: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Dashboard view for a new user who hasn't completed many assessments yet.",
      },
    },
  },
};

/**
 * Dashboard showing high fertility results
 */
export const HighFertilityUser: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Dashboard displaying high fertility scores and positive progress indicators.",
      },
    },
  },
};

/**
 * Dashboard showing moderate fertility results
 */
export const ModerateFertilityUser: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Dashboard showing moderate fertility results with room for improvement.",
      },
    },
  },
};

/**
 * Dashboard showing improvement over time
 */
export const ProgressTracking: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Dashboard highlighting progress tracking features and score improvements over time.",
      },
    },
  },
};

/**
 * Mobile view of the dashboard
 */
export const MobileView: Story = {
  parameters: {
    viewport: {
      defaultViewport: "mobile1",
    },
    docs: {
      description: {
        story:
          "Mobile-responsive version of the dashboard optimized for smaller screens.",
      },
    },
  },
};

/**
 * Dashboard with focus on quick actions
 */
export const QuickActions: Story = {
  parameters: {
    docs: {
      description: {
        story: "Focuses on the quick action buttons and their functionality.",
      },
    },
  },
};

/**
 * Dashboard showing assessment history interaction
 */
export const AssessmentHistory: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Highlights the assessment history section and viewing past results.",
      },
    },
  },
};

/**
 * Dashboard demonstrating the complete user experience
 */
export const UserJourney: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Shows the complete dashboard experience for fertility tracking and assessment management.",
      },
    },
  },
};
