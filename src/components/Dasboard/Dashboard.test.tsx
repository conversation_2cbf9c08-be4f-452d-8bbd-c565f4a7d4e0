/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import { useRouter } from "next/navigation";
import Dashboard from "./Dashboard";

// Mock Next.js router
jest.mock("next/navigation", () => ({
  useRouter: jest.fn(),
}));

// Mock AuthProvider to avoid Supabase dependency
jest.mock("@/contexts/AuthContext", () => ({
  useAuth: () => ({ user: { id: "test" }, loading: false }),
  AuthProvider: ({ children }: { children: React.ReactNode }) => (
    <>{children}</>
  ),
}));

// Set dummy env vars for Supabase if needed
process.env.NEXT_PUBLIC_SUPABASE_URL = "http://localhost:54321";
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = "test_anon_key";

const mockPush = jest.fn();
const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;

describe("Dashboard", () => {
  beforeEach(() => {
    mockUseRouter.mockReturnValue({
      push: mockPush,
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
    } as any);
    mockPush.mockClear();
  });

  it("renders the welcome message", () => {
    render(<Dashboard />);
    expect(screen.getByText(/Welcome back/i)).toBeInTheDocument();
  });

  it("navigates to new assessment when 'Take New Assessment' is clicked", () => {
    render(<Dashboard />);
    fireEvent.click(screen.getByText("Take New Assessment"));
    expect(mockPush).toHaveBeenCalledWith("/ivf-assessment/user-type");
  });

  it("renders the quick actions section", () => {
    render(<Dashboard />);
    expect(screen.getByText("Quick Actions")).toBeInTheDocument();
  });

  // Add more simple tests as needed...
});
