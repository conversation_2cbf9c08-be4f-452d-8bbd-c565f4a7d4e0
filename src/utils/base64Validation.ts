/**
 * Validates a base64 string for security purposes
 */
export function validateBase64(base64String: string): { isValid: boolean; error?: string } {
  // Check if string is not empty
  if (!base64String || typeof base64String !== 'string') {
    return { isValid: false, error: 'Invalid base64 string: must be a non-empty string' };
  }

  // Check if string starts with data URL prefix for images
  if (!base64String.startsWith('data:image/')) {
    return { isValid: false, error: 'Invalid base64 string: must be a data URL for an image' };
  }

  // Extract the actual base64 part (after the comma)
  const base64Part = base64String.split(',')[1];
  if (!base64Part) {
    return { isValid: false, error: 'Invalid base64 string: missing data part' };
  }

  // Check if the base64 part is valid
  try {
    // Test if it can be decoded
    atob(base64Part);
  } catch (error) {
    console.log(error)
    return { isValid: false, error: 'Invalid base64 string: cannot be decoded' };
  }

  // Check length limits (reasonable size for an image)
  const maxSize = 10 * 1024 * 1024; // 10MB limit
  const decodedSize = Math.ceil((base64Part.length * 3) / 4);
  if (decodedSize > maxSize) {
    return { isValid: false, error: 'Invalid base64 string: file size too large' };
  }

  // Check for suspicious patterns (basic security check)
  const suspiciousPatterns = [
    /<script/i,
    /javascript:/i,
    /vbscript:/i,
    /onload/i,
    /onerror/i,
    /onclick/i,
  ];

  for (const pattern of suspiciousPatterns) {
    if (pattern.test(base64Part)) {
      return { isValid: false, error: 'Invalid base64 string: contains suspicious content' };
    }
  }

  return { isValid: true };
}

/**
 * Converts a base64 string to a Buffer for email attachment
 */
export function base64ToBuffer(base64String: string): Buffer | null {
  const validation = validateBase64(base64String);
  if (!validation.isValid) {
    return null;
  }

  try {
    const base64Part = base64String.split(',')[1];
    return Buffer.from(base64Part, 'base64');
  } catch (error) {
    console.error('Error converting base64 to buffer:', error);
    return null;
  }
}

/**
 * Extracts MIME type from base64 data URL
 */
export function getMimeTypeFromBase64(base64String: string): string | null {
  if (!base64String.startsWith('data:image/')) {
    return null;
  }

  const match = base64String.match(/^data:([^;]+);/);
  return match ? match[1] : null;
} 