/**
 * Get guest session token from storage
 * Checks sessionStorage for the token
 */
export function getGuestSessionToken(): string | null {
  // Check sessionStorage first (where it's set)
  const sessionToken = sessionStorage.getItem("ivf_guest_session_token");
  if (sessionToken) {
    return sessionToken;
  }
  
  return null;
}

/**
 * Set guest session token in storage
 */
export function setGuestSessionToken(token: string): void {
  sessionStorage.setItem("ivf_guest_session_token", token);
}

/**
 * Clear guest session token from storage
 */
export function clearGuestSessionToken(): void {
  sessionStorage.removeItem("ivf_guest_session_token");
} 

/**
 * Get selected track type from storage
 */
export function getSelectedTrackType(): string | null {
  return sessionStorage.getItem("ivf_selected_assessment_type");
}

/**
 * Set selected track type in storage
 */
export function setSelectedTrackType(trackType: string): void {
  sessionStorage.setItem("ivf_selected_assessment_type", trackType);
}

/**
 * Clear selected track type from storage
 */
export function clearSelectedTrackType(): void {
  sessionStorage.removeItem("ivf_selected_assessment_type");
} 