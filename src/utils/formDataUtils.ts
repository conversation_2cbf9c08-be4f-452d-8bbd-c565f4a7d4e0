import { Question } from '@/types/questions';

// Function to convert string values back to numbers for numeric field types
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const convertNumericValues = (allFormData: Record<string, any>, allQuestions: Question[]): Record<string, any> => {
  // Create a map of question IDs to their field types
  const questionFieldTypeMap: Record<string, string> = {};
  
  // Recursively flatten all questions (including sub-questions)
  const flattenQuestions = (qs: Question[]): Question[] => {
    return qs.flatMap(q => [q, ...(q.sub_questions ? flattenQuestions(q.sub_questions) : [])]);
  };
  
  const flattenedQuestions = flattenQuestions(allQuestions);
  
  // Build the field type mapping
  flattenedQuestions.forEach(q => {
    questionFieldTypeMap[q.id] = q.field_type.value;
  });

  // Create a new payload with converted numeric values
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const convertedPayload: Record<string, any> = {};
  
  for (const formName in allFormData) {
    const form = allFormData[formName];
    for (const questionId in form) {
      const value = form[questionId];
      const fieldType = questionFieldTypeMap[questionId];
      
      if (value !== '') {
        // Convert to number for numeric field types
        if (fieldType === 'NUMBER_INPUT' || fieldType === 'RANGE_SLIDER') {
          convertedPayload[questionId] = Number(value);
        } else {
          // Keep as string for other field types
          convertedPayload[questionId] = value;
        }
      }
    }
  }
  
  return convertedPayload;
};
