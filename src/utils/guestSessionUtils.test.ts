import { getGuestSessionToken, setGuestSessionToken, clearGuestSessionToken } from './guestSessionUtils';

// Mock storage
const mockSessionStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, 'sessionStorage', {
  value: mockSessionStorage,
});

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

describe('guestSessionUtils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getGuestSessionToken', () => {
    it('should return token from sessionStorage when available', () => {
      const token = 'test-session-token';
      mockSessionStorage.getItem.mockReturnValue(token);
      mockLocalStorage.getItem.mockReturnValue(null);

      const result = getGuestSessionToken();

      expect(result).toBe(token);
      expect(mockSessionStorage.getItem).toHaveBeenCalledWith('ivf_guest_session_token');
    });

    it('should return token from localStorage when sessionStorage is empty', () => {
      const token = 'test-session-token';
      mockSessionStorage.getItem.mockReturnValue(null);
      mockLocalStorage.getItem.mockReturnValue(token);

      const result = getGuestSessionToken();

      expect(result).toBe(token);
      expect(mockSessionStorage.getItem).toHaveBeenCalledWith('ivf_guest_session_token');
      expect(mockLocalStorage.getItem).toHaveBeenCalledWith('ivf_guest_session_token');
      expect(mockSessionStorage.setItem).toHaveBeenCalledWith('ivf_guest_session_token', token);
    });

    it('should return null when no token is found', () => {
      mockSessionStorage.getItem.mockReturnValue(null);
      mockLocalStorage.getItem.mockReturnValue(null);

      const result = getGuestSessionToken();

      expect(result).toBeNull();
    });
  });

  describe('setGuestSessionToken', () => {
    it('should set token in both sessionStorage and localStorage', () => {
      const token = 'new-session-token';

      setGuestSessionToken(token);

      expect(mockSessionStorage.setItem).toHaveBeenCalledWith('ivf_guest_session_token', token);
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('ivf_guest_session_token', token);
    });
  });

  describe('clearGuestSessionToken', () => {
    it('should remove token from both sessionStorage and localStorage', () => {
      clearGuestSessionToken();

      expect(mockSessionStorage.removeItem).toHaveBeenCalledWith('ivf_guest_session_token');
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('ivf_guest_session_token');
    });
  });
}); 