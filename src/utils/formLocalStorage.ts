/* eslint-disable @typescript-eslint/no-explicit-any */
const getStorageKey = (userId?: string) => {
  return userId ? `ivf_form_data_${userId}` : 'ivf_form_data_guest';
};

export const getStoredFormData = (userId?: string) => {
  const key = getStorageKey(userId);
  const storedData = localStorage.getItem(key);
  return storedData ? JSON.parse(storedData) : {};
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const storeFormData = (data: any, userId?: string | undefined) => {
  const key = getStorageKey(userId);
  const existingData = getStoredFormData(userId);
  const newData = { ...existingData, ...data };
  localStorage.setItem(key, JSON.stringify(newData));
};

export const clearStoredFormData = () => {
  // remove all keys start with `ivf_form_data_`
  const allKeys = Object.keys(localStorage);
  allKeys.forEach((k) => {
    if (k.startsWith('ivf_form_data_')) {
      localStorage.removeItem(k);
    }
  });
};

/**
 * Convert guest form data to authenticated user data when conditions are met
 * @param userId - The authenticated user's ID
 * @returns boolean - True if conversion was performed, false otherwise
 */
export const convertGuestFormDataToUser = (userId: string): boolean => {
  // Check if guest form data exists
  const guestFormData = localStorage.getItem('ivf_form_data_guest');
  if (!guestFormData) {
    return false;
  }

  // Check if test status is in-progress
  const testStatus = sessionStorage.getItem('Test_status');
  if (testStatus !== 'in-progress') {
    return false;
  }

  // Check if user is authenticated (userId is provided)
  if (!userId) {
    return false;
  }

  try {
    // Parse the guest form data
    const parsedGuestData = JSON.parse(guestFormData);
    
    // Store the data with the user's ID
    storeFormData(parsedGuestData, userId);
    
    // Remove the guest form data
    localStorage.removeItem('ivf_form_data_guest');
    
    console.log('Successfully converted guest form data to user data for user:', userId);
    return true;
  } catch (error) {
    console.error('Error converting guest form data:', error);
    return false;
  }
};


export const clearFormData = (form_slug: string, userId: string) =>{
  const key = getStorageKey(userId);
  const storedData = localStorage.getItem(key);
  if (storedData) {
    const parsedData = JSON.parse(storedData);
    delete parsedData[form_slug];
    localStorage.setItem(key, JSON.stringify(parsedData));
  }
}


// Transform form data from question ID format to API format
export const transformFormDataForAPI = (
  formData: Record<string, string>,
  questions: any[]
): Record<string, any> => {
  // Recursively flatten all questions (including sub-questions)
  function flattenQuestions(qs: any[]): any[] {
    return qs.flatMap(q => [q, ...(q.sub_questions ? flattenQuestions(q.sub_questions) : [])]);
  }
  const allQuestions = flattenQuestions(questions);

  // Create a mapping of question ID to question text and field type
  const questionMap: Record<string, { text: string; fieldType: string }> = {};
  allQuestions.forEach(q => {
    questionMap[q.id] = {
      text: q.question_text,
      fieldType: q.field_type.value
    };
  });

  // Transform the data to use question text as keys with normalized data types
  const transformedData: Record<string, any> = {};

  Object.keys(formData).forEach(questionId => {
    const questionInfo = questionMap[questionId];
    if (questionInfo) {
      const value = formData[questionId];
      const fieldType = questionInfo.fieldType;

      // Normalize data types based on field type
      if (fieldType === 'NUMBER_INPUT' || fieldType === 'RANGE_SLIDER') {
        // Convert to number for numeric fields, null for empty values
        transformedData[questionInfo.text] = value === '' ? null : Number(value);
      } else {
        // Keep as string for other field types
        transformedData[questionInfo.text] = value;
      }
    }
  });

  return transformedData;
};

// Test function to verify data transformation
export const testFormDataTransformation = () => {
  const mockQuestions = [
    {
      id: 'q1',
      question_text: 'What is your age?',
      field_type: { value: 'NUMBER_INPUT' }
    },
    {
      id: 'q2', 
      question_text: 'What is your name?',
      field_type: { value: 'INPUT' }
    }
  ];
  
  const mockFormData = {
    'q1': '25',
    'q2': 'John Doe'
  };
  
  const result = transformFormDataForAPI(mockFormData, mockQuestions);
  console.log('Test transformation result:', result);
  return result;
};
