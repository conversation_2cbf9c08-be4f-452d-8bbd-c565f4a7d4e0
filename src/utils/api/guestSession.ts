import { NextRequest } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { randomUUID } from "crypto";
import { GuestSessionRow } from "@/types/guest-session";
import { FieldValue } from "@/types/ivf-score/ivf-score";
import { determineIVFAssessmentStatus } from '@/lib/services/ivf-scores.service';

const prisma = new PrismaClient();


/**
 * Get or create a guest session token from request headers
 */
export function getGuestSessionToken(req: NextRequest): string {
  // Check for existing guest session token in headers
  const existingToken = req.headers.get("x-guest-session") || req.headers.get("X-Guest-Session");
  
  if (existingToken) {
    return existingToken;
  }
  
  // Generate new guest session token
  return `guest_${randomUUID()}`;
}

/**
 * Create or update guest session data
 */
export async function saveGuestSession(
  sessionToken: string,
  ivfData: Record<string, FieldValue>,
  currentStep: number,
  email?: string,
  selectedTrack?: string
): Promise<GuestSessionRow> {
  const expiresAt = new Date();
  expiresAt.setHours(expiresAt.getHours() + 24); // 24 hour expiry

  try {
    // Try to update existing session first
    const existingSession = await prisma.guest_sessions.findUnique({
      where: { session_token: sessionToken },
    });

    if (existingSession) {
      const status = await determineIVFAssessmentStatus(currentStep);
      const updated = await prisma.guest_sessions.update({
        where: { session_token: sessionToken },
        data: {
          ivf_data: ivfData,
          current_step: currentStep,
          email: email || null,
          selected_track: selectedTrack || null,
          status,
          expires_at: expiresAt,
        },
      });
      return updated as unknown as GuestSessionRow;
    }

    // Create new session
    const status = await determineIVFAssessmentStatus(currentStep);
    const newSession = await prisma.guest_sessions.create({
      data: {
        session_token: sessionToken,
        ivf_data: ivfData,
        current_step: currentStep,
        email: email || null,
        selected_track: selectedTrack || null,
        status,
        expires_at: expiresAt,
      },
    });

    return newSession as unknown as GuestSessionRow;
  } catch (error) {
    console.error("Database error, falling back to cache:", error);

    // Fallback to in-memory cache if database is not available
    const status = await determineIVFAssessmentStatus(currentStep);
    const guestSession: GuestSessionRow = {
      id: randomUUID(),
      session_token: sessionToken,
      ivf_data: ivfData,
      current_step: currentStep,
      email,
      selected_track: selectedTrack,
      is_verified: false,
      status,
      expires_at: expiresAt,
      created_at: new Date(),
      updated_at: new Date(),
    };

    guestSessionCache.set(sessionToken, guestSession);
    return guestSession;
  }
}

/**
 * Get guest session data
 */
export async function getGuestSession(sessionToken: string): Promise<GuestSessionRow | null> {
  try {
    // Try database first
    const session = await prisma.guest_sessions.findFirst({
      where: {
        session_token: sessionToken,
        expires_at: {
          gt: new Date(), // Greater than current time (not expired)
        },
      },
    });

    if (session) {
      return session as unknown as GuestSessionRow;
    }
  } catch (error) {
    console.error("Database error, falling back to cache:", error);
  }

  // Check cache as fallback
  const cachedSession = guestSessionCache.get(sessionToken);
  if (cachedSession && cachedSession.expires_at > new Date()) {
    return cachedSession;
  }

  return null;
}

/**
 * Convert guest session to authenticated user data
 */
export async function convertGuestToUser(sessionToken: string, userId: string, overwrite: boolean = false): Promise<boolean> {
  try {
    const guestSession = await getGuestSession(sessionToken);
    if (!guestSession) {
      console.error("Guest session not found:", sessionToken);
      return false;
    }

    // Check if the user profile exists
    const userProfile = await prisma.profiles.findUnique({
      where: { auth_id: userId },
    });

    if (!userProfile) {
      console.error("User profile not found for auth_id:", userId);
      return false;
    }

    // Check if user already has IVF scores
    const existingIVFScores = await prisma.ivf_scores.findUnique({
      where: { user_id: userId },
    });

    if (existingIVFScores && !overwrite) {
      console.error("User already has IVF scores and overwrite not allowed:", userId);
      return false;
    }

    if (existingIVFScores && overwrite) {
      // Update existing IVF scores
      await prisma.ivf_scores.update({
        where: { user_id: userId },
        data: {
          form_data: guestSession.ivf_data,
          current_step: guestSession.current_step,
          selected_track: guestSession.selected_track,
        },
      });
    } else {
      // Create new IVF scores for the authenticated user
      await prisma.ivf_scores.create({
        data: {
          user_id: userId,
          form_data: guestSession.ivf_data,
          current_step: guestSession.current_step,
          selected_track: guestSession.selected_track,
        },
      });
    }

    // Clean up guest session
    try {
      await prisma.guest_sessions.delete({
        where: { session_token: sessionToken },
      });
    } catch (error) {
      console.error("Error deleting guest session from database:", error);
      // Remove from cache if database cleanup fails
      guestSessionCache.delete(sessionToken);
    }

    return true;
  } catch (error) {
    console.error("Error converting guest to user:", error);
    return false;
  }
}

/**
 * Verify guest email and mark session as verified
 */
export async function verifyGuestEmail(sessionToken: string, email: string): Promise<boolean> {
  try {
    const guestSession = await getGuestSession(sessionToken);
    if (!guestSession || guestSession.email !== email) {
      return false;
    }

    // Mark as verified
    try {
      await prisma.guest_sessions.update({
        where: { session_token: sessionToken },
        data: { is_verified: true },
      });
    } catch (error) {
      console.error("Error updating guest session in database:", error);
      // Update cache if database update fails
      const cachedSession = guestSessionCache.get(sessionToken);
      if (cachedSession) {
        cachedSession.is_verified = true;
        cachedSession.updated_at = new Date();
        guestSessionCache.set(sessionToken, cachedSession);
      }
    }

    return true;
  } catch (error) {
    console.error("Error verifying guest email:", error);
    return false;
  }
}

// Simple in-memory cache for guest sessions (use Redis in production)
const guestSessionCache = new Map<string, GuestSessionRow>();

// Clean up expired sessions every hour
setInterval(() => {
  const now = new Date();
  for (const [token, session] of guestSessionCache.entries()) {
    if (session.expires_at <= now) {
      guestSessionCache.delete(token);
    }
  }
}, 60 * 60 * 1000); // 1 hour
