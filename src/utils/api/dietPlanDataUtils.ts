import { NextRequest } from "next/server";
import { authenticate } from "@/utils/api/authenticate";
import { PrismaClient } from "@/generated/prisma";

const prisma = new PrismaClient();

export interface DietPlanContext {
  isAuthenticated: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  user?: any;
  token?: string;
}

/**
 * Get authenticated user context for diet plan
 * Diet plan is only for authenticated users
 */
export async function getDietPlanContext(req: NextRequest): Promise<{
  context: DietPlanContext | null;
  error?: Response;
}> {
  // Try to authenticate the user
  const authResult = await authenticate(req);
  
  if (!authResult.error) {
    // User is authenticated
    return {
      context: {
        isAuthenticated: true,
        user: authResult.user,
        token: authResult.token,
      }
    };
  }

  // User is not authenticated - return error
  return {
    context: null,
    error: new Response(JSON.stringify({ error: "Authentication required" }), {
      status: 401,
      headers: { "Content-Type": "application/json" },
    })
  };
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
async function _saveDietPlanDataForAuthenticatedUser(userId: string, dietPlanData: any, currentStep: number): Promise<any> {
  const existingData = await prisma.diet_plan_scores.findUnique({
    where: { user_id: userId },
  });

  if (existingData) {
    // Update existing data
    const formDataToSave = {
      ...(existingData.form_data && typeof existingData.form_data === 'object' && !Array.isArray(existingData.form_data)
        ? existingData.form_data
        : {}),
      ...dietPlanData
    };
    
    const status = await determineDietPlanStatus(currentStep);
    
    return prisma.diet_plan_scores.update({
      where: { user_id: userId },
      data: {
        form_data: formDataToSave,
        current_step: currentStep,
        status,
      },
    });
  } else {
    // Create new data
    const status = await determineDietPlanStatus(currentStep);

    return prisma.diet_plan_scores.create({
      data: {
        user_id: userId,
        form_data: dietPlanData,
        current_step: currentStep,
        status,
      },
    });
  }
}

/**
 * Save diet plan data for authenticated user
 */
export async function saveDietPlanData(
  context: DietPlanContext,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  dietPlanData: any,
  currentStep: number
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    if (context.isAuthenticated && context.user) {
      const result = await _saveDietPlanDataForAuthenticatedUser(context.user.id, dietPlanData, currentStep);
      return { success: true, data: result };
    } else {
      return { success: false, error: "Authentication required" };
    }
  } catch (error) {
    console.error("Error saving diet plan data:", error);
    return { success: false, error: "Failed to save data" };
  }
}

/**
 * Get diet plan data for authenticated user
 */
export async function getDietPlanData(context: DietPlanContext): Promise<{
  success: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data?: any;
  error?: string;
}> {
  try {
    if (context.isAuthenticated && context.user) {
      // Get from authenticated user's record
      const dietPlanData = await prisma.diet_plan_scores.findUnique({
        where: { user_id: context.user.id },
      });

      return { success: true, data: dietPlanData };
    } else {
      return { success: false, error: "Authentication required" };
    }
  } catch (error) {
    console.error("Error getting diet plan data:", error);
    return { success: false, error: "Failed to retrieve data" };
  }
}

/**
 * Determine diet plan status based on current step
 */
export async function determineDietPlanStatus(currentStep: number): Promise<'pending' | 'completed' | 'failed'> {
  // For diet plan, we only have step 1, so if we have any data, it's completed
  if (currentStep >= 1) {
    return 'completed';
  }
  return 'pending';
}

/**
 * Get step from category (for diet plan, always returns 1)
 */
export function getStepFromCategory(category: string): number {
  if (category === 'diet-plan') {
    return 1;
  }
  return 1; // Default to step 1
} 