import { PrismaClient } from "@/generated/prisma";
import { fetchQuestionsForValidation } from "@/lib/services/questions.service";

const prisma = new PrismaClient();

/**
 * Fetches questions for the three specific categories (biological, lifestyle, environmental)
 */
export async function getFormQuestions(categories: string[], tracks?: string) {
  try {
    // Use the shared fetchQuestionsForValidation for consistent filtering
    const result = await fetchQuestionsForValidation(categories, tracks);
    return result;
  } catch (error) {
    console.error("Error fetching form questions:", error);
    return { success: false, error: "Failed to fetch form questions" };
  }
}

/**
 * Maps question IDs to their field names for data storage
 */
export async function getQuestionFieldMap() {
  try {
    const questions = await prisma.questions.findMany({
      select: {
        id: true,
        question_text: true,
        form: {
          select: {
            name: true
          }
        }
      }
    });
    
    // Create a map of question IDs to field names
    const fieldMap = new Map();
    questions.forEach(question => {
      // Generate a field name based on question text (lowercase, underscores)
      const fieldName = question.question_text
        .toLowerCase()
        .replace(/[^a-z0-9\s]/g, '')
        .replace(/\s+/g, '_');
      
      fieldMap.set(question.id, fieldName);
    });
    
    return { success: true, fieldMap };
  } catch (error) {
    console.error("Error creating question field map:", error);
    return { success: false, error: "Failed to create question field map" };
  }
}

/**
 * Validates form data against questions from the three specific categories
 * Optionally filters questions by provided tracks (e.g., "T1", "T2", "T3").
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function validateFormData(categories: string[], formData: any, tracks?: string) {
  try {
    // Get questions from all three categories in a single API call
    const result = await fetchQuestionsForValidation(categories, tracks);
    
    if (!result.success || !result.questions) {
      return { success: false, error: "Failed to fetch questions for validation" };
    }
    
    const allQuestions = result.questions;
    
    if (allQuestions.length === 0) {
      return { success: false, error: "No questions found for validation" };
    }
    
    const errors = [];

    // Validate each question based on its field type
    for (const question of allQuestions) {
      const value = formData[question.id];

      // Skip `GROUP_QUESTION` type
      if (question.field_type === 'GROUP_QUESTION') {
        continue;
      }

      // Check if required field is missing or empty
      if (question.is_mandatory) {
        if (value === undefined || value === "" || value === null) {
          errors.push(`${question.question_text} is required`);
          continue;
        }
      } else {
        // For optional fields, skip validation if value is empty
        if (value === undefined || value === "" || value === null) {
          continue;
        }
      }
      
      // Validate based on field type
      switch (question.field_type) {
        case 'NUMBER_INPUT':
          if (typeof value !== 'number') {
            errors.push(`${question.question_text}: Expected a number`);
          } else if (
            question.min_value !== null && value < question.min_value ||
            question.max_value !== null && value > question.max_value
          ) {
            errors.push(`${question.question_text}: Value out of range`);
          }
          break;
          
        case 'RADIO_SELECT':
        case 'DROPDOWN_SELECT':
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const options = (question as any).options || [];
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const validOptions = options.map((opt: any) => opt.value ?? opt.option_text);
          if (!validOptions.includes(value)) {
            errors.push(`${question.question_text}: Invalid option selected`);
          }
          break;
          
        case 'RANGE_SLIDER':
          if (typeof value !== 'number') {
            errors.push(`${question.question_text}: Expected a number`);
          } else if (
            question.min_value !== null && value < question.min_value ||
            question.max_value !== null && value > question.max_value
          ) {
            errors.push(`${question.question_text}: Value out of range`);
          }
          break;
      }
    }
    
    if (errors.length > 0) {
      return { success: false, errors };
    }
    
    return { success: true };
  } catch (error) {
    console.error("Error validating form data:", error);
    return { success: false, error: "Failed to validate form data" };
  }
}

/**
 * Gets the category from step number
 */
export function getCategoryFromStep(step: number): string {
  switch (step) {
    case 1: return 'biological';
    case 2: return 'lifestyle';
    case 3: return 'environmental';
    default: return 'all';
  }
}

/**
 * Gets the step number from category
 */
export function getStepFromCategory(category: string): number {
  switch (category.toLowerCase()) {
    case 'biological': return 1;
    case 'lifestyle': return 2;
    case 'environmental': return 3;
    default: return 0;
  }
}