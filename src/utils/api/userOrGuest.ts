import { NextRequest } from "next/server";
import { authenticate } from "@/utils/api/authenticate";
import { getGuestSessionToken, getGuestSession, saveGuestSession } from "@/utils/api/guestSession";
import { PrismaClient } from "@/generated/prisma";
import type { IVFScoreRow, IVFScoreUpdate } from "@/types/ivf-score";
import type { GuestSessionRow } from "@/types/guest-session";
import { determineIVFAssessmentStatus } from '@/lib/services/ivf-scores.service';

const prisma = new PrismaClient();

export interface UserContext {
  isAuthenticated: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  user?: any;
  token?: string;
  guestSessionToken?: string;
  guestSession?: GuestSessionRow;
}

/**
 * Get user context - either authenticated user or guest session
 * This allows endpoints to work with both authenticated and guest users
 */
export async function getUserOrGuestContext(req: NextRequest): Promise<{
  context: UserContext | null;
  error?: Response;
}> {
  // First try to authenticate the user
  const authResult = await authenticate(req);
  
  if (!authResult.error) {
    // User is authenticated
    return {
      context: {
        isAuthenticated: true,
        user: authResult.user,
        token: authResult.token,
      }
    };
  }

  // User is not authenticated, try guest session
  const guestSessionToken = getGuestSessionToken(req);
  const guestSession = await getGuestSession(guestSessionToken);

  return {
    context: {
      isAuthenticated: false,
      guestSessionToken,
      guestSession: guestSession || undefined,
    }
  };
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
async function _saveIVFDataForAuthenticatedUser(userId: string, ivfData: any, currentStep: number, selectedTrack?: string): Promise<IVFScoreRow> {
  const existingScores = await prisma.ivf_scores.findUnique({
    where: { user_id: userId },
  });

  if (existingScores) {
    // Overwrite form_data completely every time
    const status = await determineIVFAssessmentStatus(currentStep);
    
    const dataToUpdate: IVFScoreUpdate = {
      form_data: ivfData,
      current_step: currentStep,
      selected_track: selectedTrack,
      status,
    };
    
    return prisma.ivf_scores.update({
      where: { user_id: userId },
      data: dataToUpdate,
    }) as Promise<IVFScoreRow>;
  } else {
    const status = await determineIVFAssessmentStatus(currentStep);
    
    return prisma.ivf_scores.create({
      data: {
        user_id: userId,
        form_data: ivfData,
        current_step: currentStep,
        selected_track: selectedTrack,
        status,
      },
    }) as Promise<IVFScoreRow>;
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
async function _saveIVFDataForGuest(guestSessionToken: string, guestSession: GuestSessionRow | undefined, ivfData: any, currentStep: number, selectedTrack?: string): Promise<GuestSessionRow> {
  // Overwrite ivf_data completely every time
  
  return saveGuestSession(
    guestSessionToken,
    ivfData,
    currentStep,
    undefined, // email
    selectedTrack
  );
}

/**
 * Save IVF data for either authenticated user or guest session
 */
export async function saveIVFData(
  context: UserContext,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ivfData: any,
  currentStep: number,
  selectedTrack?: string
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
): Promise<{ success: boolean; data?: any; error?: string; guestSessionToken?: string }> {
  try {
    if (context.isAuthenticated && context.user) {
      const result = await _saveIVFDataForAuthenticatedUser(context.user.id, ivfData, currentStep, selectedTrack);
      return { success: true, data: result };
    } else {
      const sessionToken = context.guestSessionToken!;
      const guestSession = await _saveIVFDataForGuest(sessionToken, context.guestSession, ivfData, currentStep, selectedTrack);

      return { 
        success: true, 
        data: guestSession, 
        guestSessionToken: sessionToken 
      };
    }
  } catch (error) {
    console.error("Error saving IVF data:", error);
    return { success: false, error: "Failed to save data" };
  }
}

/**
 * Get IVF data for either authenticated user or guest session
 */
export async function getIVFData(context: UserContext): Promise<{
  success: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data?: any;
  error?: string;
}> {
  try {
    if (context.isAuthenticated && context.user) {
      // Get from authenticated user's record
      const { PrismaClient } = await import("@/generated/prisma");
      const prisma = new PrismaClient();

      const ivfScores = await prisma.ivf_scores.findUnique({
        where: { user_id: context.user.id },
      });

      return { success: true, data: ivfScores };
    } else {
      // Get from guest session
      if (context.guestSession) {
        return { 
          success: true, 
          data: {
            ...context.guestSession.ivf_data,
            current_step: context.guestSession.current_step,
            id: context.guestSession.id,
            created_at: context.guestSession.created_at,
            updated_at: context.guestSession.updated_at,
          }
        };
      }

      return { success: true, data: null };
    }
  } catch (error) {
    console.error("Error getting IVF data:", error);
    return { success: false, error: "Failed to retrieve data" };
  }
}
