
import fs from 'fs';
import path from 'path';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function cp_log(logObj: Record<string, any>, category: string = 'questions') {
  // if production, don't log
  if (process.env.NODE_ENV === 'production') {
    return;
  }

  logObj.timestamp = new Date().toISOString();

  try {
    const dateStr = new Date().toISOString().slice(0, 10); // YYYY-MM-DD
    const logsDir = path.resolve(process.cwd(), 'logs', dateStr, category);
    const logFile = path.join(logsDir, 'log.json');
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let logs: any[] = [];
    if (fs.existsSync(logFile)) {
      const content = fs.readFileSync(logFile, 'utf-8');
      if (content.trim()) {
        logs = JSON.parse(content);
      }
    }
    logs.push(logObj);
    fs.writeFileSync(logFile, JSON.stringify(logs, null, 2));
  } catch (err) {
    console.error('Error writing log:', err);
  }
}