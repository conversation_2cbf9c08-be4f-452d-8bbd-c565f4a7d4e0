export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim());
};

// Name validation function
export const isValidName = (name: string): boolean => {
  if (!name.trim()) return false; // Required field

  const trimmed = name.trim();

  // Length check (2-100 characters)
  if (trimmed.length < 2 || trimmed.length > 100) return false;

  // Check if contains only alphabets and spaces
  if (!/^[A-Za-z\s]+$/.test(trimmed)) return false;

  return true;
};

// Phone number validation function
export const isValidPhoneNumber = (phone: string): boolean => {
  if (!phone.trim()) return true; // Optional field, so empty is valid

  const cleaned = phone.replace(/\s+/g, ""); // Remove spaces
  const withoutPlus = cleaned.startsWith("+") ? cleaned.slice(1) : cleaned;

  // Check if contains only digits
  if (!/^\d+$/.test(withoutPlus)) return false;

  // Length check (10-15 digits)
  if (withoutPlus.length < 10 || withoutPlus.length > 15) return false;

  // Reject trivial patterns (all same digits)
  if (/^(.)\1+$/.test(withoutPlus)) return false;

  return true;
};
