// Utility function to serialize Prisma responses (handle BigInt, Date objects, and Decimal)
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function serializePrismaResponse(data: any): any {
  if (data === null || data === undefined) {
    return data;
  }
  
  if (typeof data === 'bigint') {
    return data.toString();
  }
  
  if (data instanceof Date) {
    return data.toISOString();
  }
  
  // Handle Prisma Decimal type
  if (typeof data === 'object' && data.constructor && data.constructor.name === 'Decimal') {
    return data.toNumber();
  }
  
  // Handle Decimal-like objects (fallback for different Decimal implementations)
  if (typeof data === 'object' && data.d && Array.isArray(data.d) && typeof data.e === 'number' && typeof data.s === 'number') {
    return parseFloat(data.toString());
  }
  
  if (Array.isArray(data)) {
    return data.map(serializePrismaResponse);
  }
  
  if (typeof data === 'object') {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const serialized: any = {};
    for (const [key, value] of Object.entries(data)) {
      serialized[key] = serializePrismaResponse(value);
    }
    return serialized;
  }
  
  return data;
}
