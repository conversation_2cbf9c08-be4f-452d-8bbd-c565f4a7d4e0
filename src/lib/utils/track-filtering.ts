import type { QuestionWithOptions, TrackFilter } from '@/types';

/**
 * Filters questions based on selected tracks
 * @param questions - Array of questions with track associations
 * @param filter - Track filter configuration
 * @returns Filtered array of questions
 */
export function filterQuestionsByTracks(
  questions: QuestionWithOptions[],
  filter: TrackFilter
): QuestionWithOptions[] {
  // If no tracks are selected, return all questions
  if (filter.selectedTrackIds.length === 0) {
    return questions;
  }

  return questions.filter(question => {
    const questionTrackIds = question.question_tracks?.map(qt => qt.track_id) || [];
    
    if (questionTrackIds.length === 0) {
      // Question has no tracks assigned - include it for now
      // You might want to change this behavior based on your requirements
      return true;
    }

    if (filter.mode === 'any') {
      // OR logic: question must have at least one of the selected tracks
      return filter.selectedTrackIds.some(trackId => questionTrackIds.includes(trackId));
    } else {
      // AND logic: question must have all selected tracks
      return filter.selectedTrackIds.every(trackId => questionTrackIds.includes(trackId));
    }
  });
}

/**
 * Gets all unique track IDs from a list of questions
 * @param questions - Array of questions with track associations
 * @returns Array of unique track IDs
 */
export function getAllTrackIdsFromQuestions(questions: QuestionWithOptions[]): string[] {
  const trackIds = new Set<string>();
  
  questions.forEach(question => {
    question.question_tracks?.forEach(qt => {
      trackIds.add(qt.track_id);
    });
  });
  
  return Array.from(trackIds);
}

/**
 * Checks if a question belongs to specific tracks
 * @param question - Question to check
 * @param trackIds - Array of track IDs to check against
 * @param mode - 'any' for OR logic, 'all' for AND logic
 * @returns Boolean indicating if question matches the track criteria
 */
export function questionMatchesTracks(
  question: QuestionWithOptions,
  trackIds: string[],
  mode: 'any' | 'all' = 'any'
): boolean {
  const questionTrackIds = question.question_tracks?.map(qt => qt.track_id) || [];
  
  if (trackIds.length === 0) {
    return true; // No filter applied
  }
  
  if (questionTrackIds.length === 0) {
    return true; // Question has no tracks - include by default
  }

  if (mode === 'any') {
    return trackIds.some(trackId => questionTrackIds.includes(trackId));
  } else {
    return trackIds.every(trackId => questionTrackIds.includes(trackId));
  }
}

/**
 * Groups questions by their track assignments
 * @param questions - Array of questions with track associations
 * @returns Object with track codes as keys and arrays of questions as values
 */
export function groupQuestionsByTracks(
  questions: QuestionWithOptions[]
): Record<string, QuestionWithOptions[]> {
  const grouped: Record<string, QuestionWithOptions[]> = {};
  
  questions.forEach(question => {
    if (question.question_tracks && question.question_tracks.length > 0) {
      question.question_tracks.forEach(qt => {
        const trackCode = qt.track.code;
        if (!grouped[trackCode]) {
          grouped[trackCode] = [];
        }
        grouped[trackCode].push(question);
      });
    } else {
      // Questions without tracks go to 'unassigned' group
      if (!grouped['unassigned']) {
        grouped['unassigned'] = [];
      }
      grouped['unassigned'].push(question);
    }
  });
  
  return grouped;
} 