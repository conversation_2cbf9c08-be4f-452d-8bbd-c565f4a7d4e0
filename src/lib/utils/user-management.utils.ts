import type { User } from "@/lib/services/user-management.service";

// Constants
export const PER_PAGE = 10;

// Utility functions
export function getUserDisplayName(user: User): string {
  if (user.user_metadata?.full_name) {
    return user.user_metadata.full_name;
  }
  if (user.user_metadata?.first_name || user.user_metadata?.last_name) {
    return `${user.user_metadata?.first_name || ''} ${user.user_metadata?.last_name || ''}`.trim();
  }
  return user.email?.split('@')[0] || 'Unknown User';
}

export function getProviderBadge(user: User): string {
  const provider = user.app_metadata?.provider || 'email';
  const colors = {
    email: 'bg-blue-100 text-blue-800',
    google: 'bg-red-100 text-red-800',
    github: 'bg-gray-100 text-gray-800',
    facebook: 'bg-blue-100 text-blue-800',
  };
  return colors[provider as keyof typeof colors] || colors.email;
}

export function formatDate(dateString?: string): string {
  if (!dateString) return 'Never';
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

// Statistics calculation functions
export function getVerifiedUsersCount(users: User[]): number {
  return users.filter(u => u.email_confirmed_at).length;
}

export function getActiveTodayUsersCount(users: User[]): number {
  return users.filter(u => u.last_sign_in_at && 
    new Date(u.last_sign_in_at).toDateString() === new Date().toDateString()
  ).length;
}

export function getAdminUsersCount(users: User[]): number {
  return users.filter(u => u.role === 'admin').length;
} 