/**
 * Utility functions for time-related operations
 */

export interface TimeRange {
  startTime: string; // HH:mm format
  endTime: string;   // HH:mm format
}

/**
 * Validates if a time range is valid
 * @param startTime - Start time in HH:mm format
 * @param endTime - End time in HH:mm format
 * @returns Object with isValid boolean and error message if invalid
 */
export function validateTimeRange(startTime: string, endTime: string): { isValid: boolean; error?: string } {
  if (!startTime || !endTime) {
    return { isValid: false, error: 'Start time and end time are required' };
  }

  const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
  if (!timeRegex.test(startTime) || !timeRegex.test(endTime)) {
    return { isValid: false, error: 'Time must be in HH:mm format' };
  }

  const [startHour, startMinute] = startTime.split(':').map(Number);
  const [endHour, endMinute] = endTime.split(':').map(Number);

  const startMinutes = startHour * 60 + startMinute;
  const endMinutes = endHour * 60 + endMinute;

  if (startMinutes === endMinutes) {
    return { isValid: false, error: 'Start time and end time cannot be the same' };
  }

  if (startMinutes > endMinutes) {
    return { isValid: false, error: 'End time must be after start time' };
  }

  return { isValid: true };
}

/**
 * Generates time slots within a given time range
 * @param startTime - Start time in HH:mm format
 * @param endTime - End time in HH:mm format
 * @param slotDuration - Duration of each slot in minutes (default: 15)
 * @returns Array of time slots in HH:mm format
 */
export function generateTimeSlots(
  startTime: string,
  endTime: string,
  slotDuration: number = 15
): string[] {
  const validation = validateTimeRange(startTime, endTime);
  if (!validation.isValid) {
    throw new Error(validation.error);
  }

  const slots: string[] = [];
  const [startHour, startMinute] = startTime.split(':').map(Number);
  const [endHour, endMinute] = endTime.split(':').map(Number);

  let currentHour = startHour;
  let currentMinute = startMinute;

  while (true) {
    const timeString = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`;
    
    // Don't add the end time itself, only times before it
    if (currentHour > endHour || (currentHour === endHour && currentMinute >= endMinute)) {
      break;
    }

    slots.push(timeString);

    // Move to next slot
    currentMinute += slotDuration;
    if (currentMinute >= 60) {
      currentHour += Math.floor(currentMinute / 60);
      currentMinute = currentMinute % 60;
    }
  }

  return slots;
}

/**
 * Generates time slots with default clinic hours (09:00-17:00) if no clinic hours provided
 * @param clinicStartTime - Optional clinic start time in HH:mm format
 * @param clinicEndTime - Optional clinic end time in HH:mm format
 * @param slotDuration - Duration of each slot in minutes (default: 15)
 * @returns Array of time slots in HH:mm format
 */
export function generateTimeSlotsWithDefaults(
  clinicStartTime?: string,
  clinicEndTime?: string,
  slotDuration: number = 15
): string[] {
  const startTime = clinicStartTime || '09:00';
  const endTime = clinicEndTime || '17:00';
  
  return generateTimeSlots(startTime, endTime, slotDuration);
}

/**
 * Formats time for display (e.g., "09:00" -> "9:00 AM")
 * @param time - Time in HH:mm format
 * @returns Formatted time string
 */
export function formatTimeForDisplay(time: string): string {
  const [hours, minutes] = time.split(':');
  const hour = parseInt(hours);
  const ampm = hour >= 12 ? 'PM' : 'AM';
  const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
  return `${displayHour}:${minutes} ${ampm}`;
}

/**
 * Converts time to minutes since midnight for easy comparison
 * @param time - Time in HH:mm format
 * @returns Minutes since midnight
 */
export function timeToMinutes(time: string): number {
  const [hours, minutes] = time.split(':').map(Number);
  return hours * 60 + minutes;
}

/**
 * Checks if a time falls within a given range
 * @param time - Time to check in HH:mm format
 * @param startTime - Range start time in HH:mm format
 * @param endTime - Range end time in HH:mm format
 * @returns True if time is within range
 */
export function isTimeInRange(time: string, startTime: string, endTime: string): boolean {
  const timeMinutes = timeToMinutes(time);
  const startMinutes = timeToMinutes(startTime);
  const endMinutes = timeToMinutes(endTime);
  
  return timeMinutes >= startMinutes && timeMinutes < endMinutes;
}
