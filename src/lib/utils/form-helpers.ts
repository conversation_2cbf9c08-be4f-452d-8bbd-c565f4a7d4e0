/* eslint-disable @typescript-eslint/no-explicit-any */
// Shared helper functions for form-related APIs

export function getFieldTypeLabel(fieldType: string): string {
  const labels: Record<string, string> = {
    INPUT: 'Text Input',
    NUMBER_INPUT: 'Number Input',
    RADIO_SELECT: 'Radio Selection',
    DROPDOWN_SELECT: 'Dropdown Selection',
    RANGE_SLIDER: 'Range Slider'
  };
  return labels[fieldType] || fieldType;
}

export function getFieldTypeDescription(fieldType: string): string {
  const descriptions: Record<string, string> = {
    INPUT: 'Free text input field',
    NUMBER_INPUT: 'Numerical input with optional range',
    RADIO_SELECT: 'Single selection from radio buttons',
    DROPDOWN_SELECT: 'Single selection from dropdown menu',
    RANGE_SLIDER: 'Value selection using a slider'
  };
  return descriptions[fieldType] || 'Unknown field type';
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function getFieldTypeCapabilities(fieldType: string): any {
  const capabilities = {
    INPUT: {
      supportsOptions: false,
      supportsRange: false,
      supportsPlaceholder: true,
      supportsUnit: false,
      supportsScoring: false
    },
    NUMBER_INPUT: {
      supportsOptions: false,
      supportsRange: true,
      supportsPlaceholder: true,
      supportsUnit: true,
      supportsScoring: true
    },
    RADIO_SELECT: {
      supportsOptions: true,
      supportsRange: false,
      supportsPlaceholder: false,
      supportsUnit: false,
      supportsScoring: true
    },
    DROPDOWN_SELECT: {
      supportsOptions: true,
      supportsRange: false,
      supportsPlaceholder: false,
      supportsUnit: false,
      supportsScoring: true
    },
    RANGE_SLIDER: {
      supportsOptions: false,
      supportsRange: true,
      supportsPlaceholder: false,
      supportsUnit: true,
      supportsScoring: true
    }
  };

  return capabilities[fieldType as keyof typeof capabilities] || {
    supportsOptions: false,
    supportsRange: false,
    supportsPlaceholder: false,
    supportsUnit: false,
    supportsScoring: false
  };
}

export function getCategoryFromFormName(formName: string): string {
  const lowerName = formName.toLowerCase();
  if (lowerName.includes('biological')) return 'biological';
  if (lowerName.includes('lifestyle')) return 'lifestyle';
  if (lowerName.includes('environmental')) return 'environmental';
  return 'other';
}

export function getAllFieldTypesWithCapabilities() {
  const fieldTypes = ['INPUT', 'NUMBER_INPUT', 'RADIO_SELECT', 'DROPDOWN_SELECT', 'RANGE_SLIDER'];
  
  return fieldTypes.map(fieldType => ({
    value: fieldType,
    label: getFieldTypeLabel(fieldType),
    description: getFieldTypeDescription(fieldType),
    capabilities: getFieldTypeCapabilities(fieldType)
  }));
}

// Summary and statistics helpers
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function getTrackDistribution(questions: any[]): Record<string, number> {
  const distribution: Record<string, number> = {};
  
  questions.forEach(question => {
    if (question && question.tracks) {
      question.tracks.forEach((track: any) => {
        if (track && track.code) {
          distribution[track.code] = (distribution[track.code] || 0) + 1;
        }
      });
    }
  });
  
  return distribution;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function getFieldTypeDistribution(questions: any[]): Record<string, number> {
  const distribution: Record<string, number> = {};
  
  questions.forEach(question => {
    if (question && question.field_type) {
      const fieldType = typeof question.field_type === 'string' 
        ? question.field_type 
        : question.field_type.value;
      if (fieldType) {
        distribution[fieldType] = (distribution[fieldType] || 0) + 1;
      }
    }
  });
  
  return distribution;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function getTrackSummaryForForm(questions: any[]): Record<string, number> {
  const distribution: Record<string, number> = {};
  
  questions.forEach(question => {
    if (question.question_tracks) {
      question.question_tracks.forEach((qt: any) => {
        if (qt.track && qt.track.code) {
          distribution[qt.track.code] = (distribution[qt.track.code] || 0) + 1;
        }
      });
    }
  });
  
  return distribution;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function getFieldTypeSummaryForForm(questions: any[]): Record<string, number> {
  const distribution: Record<string, number> = {};
  
  questions.forEach(question => {
    if (question.field_type) {
      distribution[question.field_type] = (distribution[question.field_type] || 0) + 1;
    }
  });
  
  return distribution;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function getCategorySummary(forms: any[]): Record<string, number> {
  const distribution: Record<string, number> = {};
  
  forms.forEach(form => {
    if (form && form.category) {
      distribution[form.category] = (distribution[form.category] || 0) + 1;
    }
  });
  
  return distribution;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function getGlobalTrackDistribution(forms: any[]): Record<string, number> {
  const distribution: Record<string, number> = {};
  
  forms.forEach(form => {
    if (form && form.questions) {
      form.questions.forEach((question: any) => {
        if (question && question.tracks) {
          question.tracks.forEach((track: any) => {
            if (track && track.code) {
              distribution[track.code] = (distribution[track.code] || 0) + 1;
            }
          });
        }
      });
    }
  });
  
  return distribution;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function getGlobalFieldTypeDistribution(forms: any[]): Record<string, number> {
  const distribution: Record<string, number> = {};
  
  forms.forEach(form => {
    if (form && form.questions) {
      form.questions.forEach((question: any) => {
        if (question && question.field_type && question.field_type.value) {
          const fieldType = question.field_type.value;
          distribution[fieldType] = (distribution[fieldType] || 0) + 1;
        }
      });
    }
  });
  
  return distribution;
}