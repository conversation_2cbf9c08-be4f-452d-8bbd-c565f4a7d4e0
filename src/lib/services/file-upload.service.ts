import { supabase } from "@/utils/supabase/client";

export interface UploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

export class FileUploadService {
  private static BUCKET_NAME = 'meal-plan-pdfs';
  private static AVATARS_BUCKET_NAME = 'avatars';
  private static MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
  private static MAX_IMAGE_SIZE = 5 * 1024 * 1024; // 5MB
  private static ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

  /**
   * Check if user has permission for storage operation
   */
  private static async checkPermission(action: 'create' | 'read' | 'update' | 'delete'): Promise<boolean> {
    try {
      const { data, error } = await supabase.rpc('authorize', {
        resource_action: `storage.${action}`
      });
      
      if (error) {
        console.error(`Error checking storage.${action} permission:`, error);
        return false;
      }
      
      return !!data;
    } catch (error) {
      console.error(`Error checking storage.${action} permission:`, error);
      return false;
    }
  }

  /**
   * Upload a PDF file to Supabase storage
   */
  static async uploadPDF(file: File): Promise<UploadResult> {
    try {
      // Check if user has permission to create files
      const hasPermission = await this.checkPermission('create');
      if (!hasPermission) {
        return {
          success: false,
          error: 'You do not have permission to upload files'
        };
      }

      // Validate file type
      if (file.type !== 'application/pdf') {
        return {
          success: false,
          error: 'Only PDF files are allowed'
        };
      }

      // Validate file size
      if (file.size > this.MAX_FILE_SIZE) {
        return {
          success: false,
          error: `File size must be less than ${this.MAX_FILE_SIZE / (1024 * 1024)}MB`
        };
      }

      // Generate unique filename
      const timestamp = Date.now();
      const randomString = Math.random().toString(36).substring(2, 15);
      const fileExtension = file.name.split('.').pop();
      const fileName = `meal-plan-${timestamp}-${randomString}.${fileExtension}`;

      // Upload file to Supabase storage
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) {
        console.error('Supabase upload error:', error);
        return {
          success: false,
          error: error.message || 'Failed to upload file'
        };
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from(this.BUCKET_NAME)
        .getPublicUrl(fileName);

      return {
        success: true,
        url: urlData.publicUrl
      };
    } catch (error) {
      console.error('File upload error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Upload an image file to Supabase storage
   */
  static async uploadImage(file: File): Promise<UploadResult> {
    try {
      // Check if user has permission to create files
      const hasPermission = await this.checkPermission('create');
      if (!hasPermission) {
        return {
          success: false,
          error: 'You do not have permission to upload files'
        };
      }

      // Validate file type
      if (!this.ALLOWED_IMAGE_TYPES.includes(file.type)) {
        return {
          success: false,
          error: 'Only JPEG, PNG, and WebP images are allowed'
        };
      }

      // Validate file size
      if (file.size > this.MAX_IMAGE_SIZE) {
        return {
          success: false,
          error: `File size must be less than ${this.MAX_IMAGE_SIZE / (1024 * 1024)}MB`
        };
      }

      // Generate unique filename
      const timestamp = Date.now();
      const randomString = Math.random().toString(36).substring(2, 15);
      const fileExtension = file.name.split('.').pop()?.toLowerCase() || 'jpg';
      const fileName = `doctor-avatar-${timestamp}-${randomString}.${fileExtension}`;

      // Upload file to Supabase storage
      const { data, error } = await supabase.storage
        .from(this.AVATARS_BUCKET_NAME)
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) {
        console.error('Supabase upload error:', error);
        return {
          success: false,
          error: error.message || 'Failed to upload image'
        };
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from(this.AVATARS_BUCKET_NAME)
        .getPublicUrl(fileName);

      return {
        success: true,
        url: urlData.publicUrl
      };
    } catch (error) {
      console.error('Image upload error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Delete a file from Supabase storage
   */
  static async deleteFile(fileUrl: string): Promise<UploadResult> {
    try {
      // Check if user has permission to delete files
      const hasPermission = await this.checkPermission('delete');
      if (!hasPermission) {
        return {
          success: false,
          error: 'You do not have permission to delete files'
        };
      }

      // Extract filename from URL
      const urlParts = fileUrl.split('/');
      const fileName = urlParts[urlParts.length - 1];

      const { error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .remove([fileName]);

      if (error) {
        console.error('Supabase delete error:', error);
        return {
          success: false,
          error: error.message || 'Failed to delete file'
        };
      }

      return {
        success: true
      };
    } catch (error) {
      console.error('File deletion error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Delete an image file from Supabase storage
   */
  static async deleteImage(fileUrl: string): Promise<UploadResult> {
    try {
      // Check if user has permission to delete files
      const hasPermission = await this.checkPermission('delete');
      if (!hasPermission) {
        return {
          success: false,
          error: 'You do not have permission to delete files'
        };
      }

      // Extract filename from URL
      const urlParts = fileUrl.split('/');
      const fileName = urlParts[urlParts.length - 1];

      const { error } = await supabase.storage
        .from(this.AVATARS_BUCKET_NAME)
        .remove([fileName]);

      if (error) {
        console.error('Supabase delete error:', error);
        return {
          success: false,
          error: error.message || 'Failed to delete image'
        };
      }

      return {
        success: true
      };
    } catch (error) {
      console.error('Image deletion error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Check if storage bucket exists, create if it doesn't
   */
  static async ensureBucketExists(): Promise<boolean> {
    try {
      // Check if user has permission to create files (implies bucket access)
      const hasPermission = await this.checkPermission('create');
      if (!hasPermission) {
        console.error('User does not have storage.create permission');
        return false;
      }

      const { data: buckets, error } = await supabase.storage.listBuckets();
      
      if (error) {
        console.error('Error listing buckets:', error);
        return false;
      }

      const bucketExists = buckets.some(bucket => bucket.name === this.BUCKET_NAME);
      
      if (!bucketExists) {
        const { error: createError } = await supabase.storage.createBucket(this.BUCKET_NAME, {
          public: true,
          allowedMimeTypes: ['application/pdf'],
          fileSizeLimit: this.MAX_FILE_SIZE
        });

        if (createError) {
          console.error('Error creating bucket:', createError);
          // If bucket creation fails due to RLS, provide helpful error message
          if (createError.message.includes('row-level security policy')) {
            console.error('RLS policies not set up. Please run the storage policies in Supabase SQL editor.');
          }
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('Error ensuring bucket exists:', error);
      return false;
    }
  }

  /**
   * Check if avatars bucket exists, create if it doesn't
   */
  static async ensureAvatarsBucketExists(): Promise<boolean> {
    try {
      // Check if user has permission to create files (implies bucket access)
      const hasPermission = await this.checkPermission('create');
      if (!hasPermission) {
        console.error('User does not have storage.create permission');
        return false;
      }

      const { data: buckets, error } = await supabase.storage.listBuckets();
      
      if (error) {
        console.error('Error listing buckets:', error);
        return false;
      }

      const bucketExists = buckets.some(bucket => bucket.name === this.AVATARS_BUCKET_NAME);
      
      if (!bucketExists) {
        console.error(`Bucket '${this.AVATARS_BUCKET_NAME}' does not exist`);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error ensuring avatars bucket exists:', error);
      return false;
    }
  }
}
