"use client";

import {
  FormWithQuestions,
  FormBuilderQuestion,
} from "@/types";
import {
  addQuestion as serverAddQuestion,
  updateQuestion as serverUpdateQuestion,
  deleteQuestion as serverDeleteQuestion,
  reorderQuestions as serverReorderQuestions,
  addSubQuestion as serverAddSubQuestion,
  getDietPlanForm as serverGetDietPlanForm,
  initializeDietPlanForm as serverInitializeDietPlanForm,
} from "@/lib/services/diet-plan.service";

export async function getDietPlanForm(): Promise<FormWithQuestions | null> {
  return await serverGetDietPlanForm();
}

export async function initializeDietPlanForm(): Promise<void> {
  await serverInitializeDietPlanForm();
}

export async function addQuestion(
  formId: string,
  question: FormBuilderQuestion
): Promise<string> {
  // Handle sub-questions for new questions
  const subQuestions = question.sub_questions || [];
  delete question.sub_questions; // Remove sub_questions from the main question data

  const questionId = await serverAddQuestion(formId, question);

  // Handle sub-questions separately after parent is created
  for (const subQuestion of subQuestions) {
    await serverAddSubQuestion(questionId, subQuestion);
  }

  return questionId;
}

export async function updateQuestion(
  questionId: string,
  questionData: Partial<FormBuilderQuestion>
): Promise<void> {
  await serverUpdateQuestion(questionId, questionData);
}

export async function deleteQuestion(questionId: string): Promise<void> {
  await serverDeleteQuestion(questionId);
}

export async function reorderQuestions(
  formId: string,
  questionOrders: { id: string; order: number }[]
): Promise<void> {
  await serverReorderQuestions(formId, questionOrders);
}

export async function addSubQuestion(
  parentQuestionId: string,
  subQuestionData: FormBuilderQuestion
): Promise<void> {
  await serverAddSubQuestion(parentQuestionId, subQuestionData);
} 