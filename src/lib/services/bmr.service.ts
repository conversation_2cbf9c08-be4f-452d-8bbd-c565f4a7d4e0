
"use server";

import { FieldValue } from "@/types/ivf-score/ivf-score";
import { calculateDietPlanScore } from "@/lib/services/diet-plan-scoring";
import { PrismaClient } from "@/generated/prisma";

const prisma = new PrismaClient();

export const getIVFScoreByUserId = async (userId: string) => {
  const data = await prisma.diet_plan_scores.findUnique({
    where: { user_id: userId },
  });
  return data;
};

export async function calculateBMRForUser(userId: string) {
  const dietPlansData = await getIVFScoreByUserId(userId);

  if (!dietPlansData) {
    throw new Error("Diet plan data not found for this user");
  }

  const scoreData = await calculateDietPlanScore(
    dietPlansData.form_data as Record<string, FieldValue>
  );

  return scoreData;
}
