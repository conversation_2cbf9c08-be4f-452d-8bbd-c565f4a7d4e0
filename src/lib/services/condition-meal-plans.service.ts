"use server";

import { PrismaClient , diet_condition} from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";

const prisma = new PrismaClient();

export interface ConditionMealPlan {
  id: string;
  doctor_id: string; // Serialized from BigInt
  condition: diet_condition;
  target_calories_min: number;
  target_calories_max: number;
  recommended_target_calories: number;
  meal_plan_doc_url?: string;
  nutritional_advice?: string;
  is_active: boolean;
  created_at: string; // Serialized as ISO string
  updated_at: string; // Serialized as ISO string
  doctor?: {
    display_name?: string;
    email?: string;
  };
}

export async function getConditionMealPlans(
  page: number = 1, 
  condition?: diet_condition,
  perPage: number = 10
) {
  try {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const where: any = { is_active: true };
    if (condition) {
      where.condition = condition;
    }

    const mealPlans = await prisma.condition_meal_plans.findMany({
      where,
      take: perPage,
      skip: (page - 1) * perPage,
      include: {
        doctor: {
          select: {
            display_name: true,
            email: true
          }
        }
      },
      orderBy: { created_at: 'desc' }
    });

    const total = await prisma.condition_meal_plans.count({ where });

    return {
      mealPlans: serializePrismaResponse(mealPlans) as ConditionMealPlan[],
      total,
      error: null,
    };
  } catch (error) {
    console.error('Error in getConditionMealPlans:', error);
    return {
      mealPlans: [],
      total: 0,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

export async function deleteConditionMealPlan(formData: FormData) {
  const mealPlanId = formData.get("mealPlanId") as string;
  
  try {
    await prisma.condition_meal_plans.delete({
      where: { id: mealPlanId }
    });
  } catch (error) {
    console.error('Error deleting condition meal plan:', error);
    throw new Error('Failed to delete condition meal plan');
  }
}

export async function getMealPlanForUser(
  condition: diet_condition,
  targetCalories: number
): Promise<ConditionMealPlan | null> {
  try {
    const mealPlan = await prisma.condition_meal_plans.findFirst({
      where: {
        condition,
        target_calories_min: { lte: targetCalories },
        target_calories_max: { gte: targetCalories },
        is_active: true
      },
      orderBy: { created_at: 'desc' }
    });

    return serializePrismaResponse(mealPlan) as ConditionMealPlan;
  } catch (error) {
    console.error('Error in getMealPlanForUser:', error);
    return null;
  }
}
