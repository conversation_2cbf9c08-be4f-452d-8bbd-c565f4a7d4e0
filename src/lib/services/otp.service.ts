import { PrismaClient } from "@/generated/prisma";

const prisma = new PrismaClient();

export interface OTPData {
  code: string;
  expires: Date;
  attempts: number;
  is_verified: boolean;
}

class OTPService {
  private readonly EXPIRY_TIME = 10 * 60 * 1000; // 10 minutes
  private readonly MAX_ATTEMPTS = 5;

  generateOTP(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  async storeOTP(sessionToken: string, email: string, code: string): Promise<void> {
    const expiresAt = new Date(Date.now() + this.EXPIRY_TIME);

    // Delete any existing OTP for this session and email
    await prisma.otps.deleteMany({
      where: { 
        phone_number: `${sessionToken}:${email}` // Using phone_number field for session:email combination
      },
    });

    // Create new OTP record
    await prisma.otps.create({
      data: {
        phone_number: `${sessionToken}:${email}`,
        code,
        expires_at: expiresAt,
        attempts: 0,
        is_verified: false,
      },
    });
  }

  async verifyOTP(sessionToken: string, email: string, inputCode: string): Promise<{
    success: boolean;
    error?: string;
    remainingAttempts?: number;
  }> {
    try {
      // Find the OTP record
      const otpRecord = await prisma.otps.findUnique({
        where: { phone_number: `${sessionToken}:${email}` },
      });

      if (!otpRecord) {
        return {
          success: false,
          error: "No OTP found for this session. Please request a new one.",
        };
      }

      // Check if already verified
      if (otpRecord.is_verified) {
        return {
          success: false,
          error: "This OTP has already been used. Please request a new one.",
        };
      }

      // Check if OTP has expired
      if (new Date() > otpRecord.expires_at) {
        await this.deleteOTP(sessionToken, email);
        return {
          success: false,
          error: "OTP has expired. Please request a new one.",
        };
      }

      // Check if max attempts reached
      if (otpRecord.attempts >= this.MAX_ATTEMPTS) {
        await this.deleteOTP(sessionToken, email);
        return {
          success: false,
          error: "Maximum attempts exceeded. Please request a new OTP.",
        };
      }

      // Increment attempts
      const updatedRecord = await prisma.otps.update({
        where: { phone_number: `${sessionToken}:${email}` },
        data: { attempts: otpRecord.attempts + 1 },
      });

      // Verify the code
      if (otpRecord.code !== inputCode) {
        const remainingAttempts = this.MAX_ATTEMPTS - updatedRecord.attempts;

        if (remainingAttempts === 0) {
          await this.deleteOTP(sessionToken, email);
          return {
            success: false,
            error: "Invalid OTP. Maximum attempts exceeded. Please request a new OTP.",
          };
        }

        return {
          success: false,
          error: "Invalid OTP. Please check your code and try again.",
          remainingAttempts,
        };
      }

      // Success - mark as verified and delete
      await prisma.otps.update({
        where: { phone_number: `${sessionToken}:${email}` },
        data: { is_verified: true },
      });

      // Delete the record after successful verification
      await this.deleteOTP(sessionToken, email);

      return { success: true };
    } catch (error) {
      console.error("Database error during OTP verification:", error);
      return {
        success: false,
        error: "An error occurred during verification. Please try again.",
      };
    }
  }

  async deleteOTP(sessionToken: string, email: string): Promise<void> {
    try {
      await prisma.otps.deleteMany({
        where: { phone_number: `${sessionToken}:${email}` },
      });
    } catch (error) {
      console.error("Error deleting OTP:", error);
    }
  }

  async cleanupExpiredOTPs(): Promise<void> {
    try {
      await prisma.otps.deleteMany({
        where: {
          expires_at: {
            lt: new Date(),
          },
        },
      });
    } catch (error) {
      console.error("Error cleaning up expired OTPs:", error);
    }
  }
}

// Export singleton instance
export const otpService = new OTPService();

// Clean up expired OTPs every 5 minutes
setInterval(() => {
  otpService.cleanupExpiredOTPs();
}, 5 * 60 * 1000); 