import { ScoringType, ScoringMode } from "@/generated/prisma";
import type { RangeScoreConfig, SingleChoiceScoreConfig } from "@/types";
import { ProcessedQuestion, CategoryScores } from './types';

/**
 * Step 1: Calculate raw score for a question based on user's answer
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function calculateRawScore(question: ProcessedQuestion, answer: any): number {
  if (!question.scoring_type || answer === undefined || answer === null) {
    return 0;
  }

  try {
    if (question.scoring_type === ScoringType.range) {
      const numericAnswer = Number(answer);
      if (isNaN(numericAnswer)) return 0;
      
      const rangeConfig = question.scoring_config as RangeScoreConfig[];
      if (Array.isArray(rangeConfig) && rangeConfig.length > 0) {
        for (const range of rangeConfig) {
          if (numericAnswer >= range.min && numericAnswer <= range.max) {
            return range.score;
          }
        }
      }
    } else if (question.scoring_type === ScoringType.single_choice) {
      const choiceConfig = question.scoring_config as SingleChoiceScoreConfig[];
      
      if (Array.isArray(choiceConfig) && choiceConfig.length > 0) {
        const matchingOption = choiceConfig.find(option => 
          option.option.toString() === answer.toString()
        );
        if (matchingOption) {
          return matchingOption.score;
        }
      }
      
      // Fallback to options table - this is the main path for your questions
      const matchingTableOption = question.options.find(option => 
        (option.value || option.option_text) === answer
      );
      
      if (matchingTableOption && matchingTableOption.score !== null) {
        return matchingTableOption.score;
      }
      
      console.log('No matching option found in options table');
    }
  } catch (error) {
    console.error(`Error calculating raw score for question ${question.id}:`, error);
  }
  
  console.log('No raw score found, returning 0');
  return 0;
}

/**
 * Step 2: Find maximum possible score for a question
 */
function calculateMinMaxScore(question: ProcessedQuestion): { minScore: number, maxScore: number } {
  if (question.scoring_type === ScoringType.range) {
    const rangeConfig = question.scoring_config as RangeScoreConfig[];
    if (Array.isArray(rangeConfig) && rangeConfig.length > 0) {
      const scores = rangeConfig.map(range => range.score);
      return { minScore: Math.min(...scores), maxScore: Math.max(...scores) };
    }
  } else if (question.scoring_type === ScoringType.single_choice) {
    const choiceConfig = question.scoring_config as SingleChoiceScoreConfig[];
    if (Array.isArray(choiceConfig) && choiceConfig.length > 0) {
      const scores = choiceConfig.map(option => option.score);
      return { minScore: Math.min(...scores), maxScore: Math.max(...scores) };
    }
    if (question.options.length > 0) {
      const scores = question.options.map(option => option.score).filter(score => score !== null) as number[];
      if (scores.length > 0) {
        return { minScore: Math.min(...scores), maxScore: Math.max(...scores) };
      }
    }
  }
  return { minScore: 0, maxScore: 0 };
}

/**
 * Step 3: Normalize score to 0-1 range
 */
function normalizeScore(rawScore: number, minScore: number, maxScore: number): number {
  if (maxScore === minScore) return 1; // Avoid division by zero, treat as full score
  return Number(((rawScore - minScore) / (maxScore - minScore)).toFixed(2));
}

/**
 * Main function: Calculate normalized score (0-1) for a question
 * Follows the step-by-step process: Raw Score → Max Score → Normalize
 * Returns object with all scores for debugging
 */
export function calculateQuestionScoreOptimized(
  question: ProcessedQuestion,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  answer: any
): { normalizedScore: number; rawScore: number; maxScore: number } {
  // Step 1: Calculate raw score
  const rawScore = calculateRawScore(question, answer);
  
  // Step 2: Find maximum possible score
  const { minScore, maxScore } = calculateMinMaxScore(question);
  
  // Step 3: Normalize to 0-1 range
  const normalizedScore = normalizeScore(rawScore, minScore, maxScore);
  
  return { normalizedScore, rawScore, maxScore };
}

/**
 * Calculate score for questions with sub-questions
 * Handles both separate and collective scoring modes
 */
function calculateSubQuestionScore(
  parentQuestion: ProcessedQuestion,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  formData: any,
  categoryScores: CategoryScores
): { normalizedScore: number; rawScore?: number; maxScore?: number } {
  if (!parentQuestion.sub_questions || parentQuestion.sub_questions.length === 0) {
    return { normalizedScore: 0 };
  }

  let finalScore = 0;
  let rawScore: number | undefined = undefined;
  let maxScore: number | undefined = undefined;

  if (parentQuestion.scoring_mode === ScoringMode.separate) {
    // Step 4a: Calculate normalized scores for each sub-question
    const subQuestionScores: number[] = [];
    for (const subQuestion of parentQuestion.sub_questions) {
      const userAnswer = formData[subQuestion.id];
      if (userAnswer !== undefined) {
        const scoreResult = calculateQuestionScoreOptimized(subQuestion, userAnswer);
        subQuestionScores.push(scoreResult.normalizedScore);
      }
    }
    // Step 4b: Average the normalized sub-question scores
    if (subQuestionScores.length > 0) {
      finalScore = subQuestionScores.reduce((sum, score) => sum + score, 0) / subQuestionScores.length;
    }
    // For separate mode, rawScore and maxScore are not meaningful for the group as a whole
    rawScore = undefined;
    maxScore = undefined;

  } else if (parentQuestion.scoring_mode === ScoringMode.collective) {

    // Step 4c: Combine sub-question values using formula, then score parent
    const subQuestionValues: { [key: string]: number } = {};
    parentQuestion.sub_questions.forEach((subQuestion, index) => {
      const userAnswer = formData[subQuestion.id];
      if (userAnswer !== undefined) {
        const numericValue = Number(userAnswer);
        if (!isNaN(numericValue)) {
          subQuestionValues[`q${index + 1}`] = numericValue;
        }
      }
    });

    if (Object.keys(subQuestionValues).length > 0 && parentQuestion.collective_formula) {
      try {
        let formula = parentQuestion.collective_formula;
        for (const key in subQuestionValues) {
          formula = formula.replace(new RegExp(key, 'g'), subQuestionValues[key].toString());
        }
        const collectiveValue = new Function(`return ${formula}`)();
        // Get raw score from parent question's scoring config
        rawScore = calculateRawScore(parentQuestion, collectiveValue);
        const minMax = calculateMinMaxScore(parentQuestion);
        maxScore = minMax.maxScore;
        finalScore = normalizeScore(rawScore, minMax.minScore, minMax.maxScore);
      } catch (error) {
        console.error(`Error evaluating collective formula for question ${parentQuestion.id}:`, error);
        finalScore = 0;
        rawScore = undefined;
        maxScore = undefined;
      }
    }
  }

  // Add normalized score to category
  categoryScores[parentQuestion.category] += finalScore;
  return { normalizedScore: Number(finalScore.toFixed(2)), rawScore, maxScore };
}

export { calculateSubQuestionScore };
