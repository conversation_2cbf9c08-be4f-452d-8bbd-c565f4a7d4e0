import { IVFScoreRow } from "@/types/ivf-score";
import { getQuestionsWithScoring } from './questions';
import { calculateQuestionScoreOptimized } from './scoring';
import { CategoryScores } from "./types";
import { 
  initializeCategoryTracking, 
  calculateCategoryAverages, 
  applyIVFFormula, 
  determineCategory 
} from './utils';
import { 
  processGroupQuestion, 
  processIndividualQuestion 
} from './processing';

/**
 * Represents the final result of the IVF score calculation.
 * Contains the total score, percentage, category, and detailed breakdown of contributing factors.
 */
export interface IVFScoreResult {
  totalScore: number;
  maxScore: number;
  percentage: number;
  category: string;
  factors: CategoryScores;
  avgFactors: {
    biological: [number, number];
    lifestyle: [number, number];
    environmental: [number, number];
  };
  questions?: {
    question: string;
    score: number;
    rawScore?: number;
    maxScore?: number;
    answer?: string;
    scoring_mode: string;
    scoring_type: string;
  }[];
}

/**
 * Calculates a dynamic IVF score based on user-provided form data and a comprehensive set of fertility questions.
 * The process involves several steps:
 * 1. Fetching questions with their scoring configurations.
 * 2. Processing each question to calculate a raw score.
 * 3. Normalizing scores to a common scale.
 * 4. Aggregating scores into categories (biological, lifestyle, environmental).
 * 5. Calculating weighted averages for each category.
 * 6. Applying a final formula to determine the IVF score.
 *
 * This function also supports a debug mode to provide detailed scoring information for each question.
 *
 * @param data - The user's form data, containing answers to the fertility questions.
 * @param debug_score - A boolean flag to enable or disable debug mode, which includes detailed question scores in the result.
 * @param trackCode - An optional code to fetch a specific track of questions.
 * @returns A promise that resolves to an `IVFScoreResult` object with the calculated score and related details.
 */
export async function calculateDynamicIVFScore(
  data: IVFScoreRow,
  debug_score = false,
  trackCode?: string,
  formSlugs?: string[]
): Promise<IVFScoreResult> {
  try {
    // Step 1: Get questions with their scoring configurations.
    const questions = await getQuestionsWithScoring(trackCode, formSlugs);
    const formData = data.form_data || {};

    // Step 2: Initialize tracking for scores and other metrics.
    const categoryTracking = initializeCategoryTracking();
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const questionScores: any[] = [];

    // Step 3: Process each question to calculate its score.
    for (const question of questions) {
      let normalizedScore = 0;
      let debugInfo: { rawScore?: number; maxScore?: number } = {};

      // Process group questions, which contain sub-questions.
      if (question.field_type === 'GROUP_QUESTION' && question.sub_questions?.length > 0) {
        const result = processGroupQuestion(question, formData, categoryTracking);
        normalizedScore = result.normalizedScore;
        debugInfo = result.debugInfo;

        // If in debug mode, add the group question and its sub-questions to the scores array
        if (debug_score) {
          // Add the parent group question
          questionScores.push({
            question: question.question_text,
            answer: 'GROUP_QUESTION', // Group questions don't have direct answers
            score: normalizedScore,
            ...debugInfo,
            scoring_mode: question.scoring_mode,
            scoring_type: question.scoring_type
          });

          // Add each sub-question with its individual score
          for (const subQuestion of question.sub_questions) {
            const subAnswer = formData[subQuestion.id];
            if (subAnswer !== undefined) {
              const subScoreResult = calculateQuestionScoreOptimized(subQuestion, subAnswer);
              questionScores.push({
                question: `  ${subQuestion.question_text}`, // Indent to show it's a sub-question
                answer: subAnswer,
                score: subScoreResult.normalizedScore,
                rawScore: subScoreResult.rawScore,
                maxScore: subScoreResult.maxScore,
                scoring_mode: subQuestion.scoring_mode,
                scoring_type: subQuestion.scoring_type
              });
            }
          }
        }
      }

      // Process individual questions that are not part of a group.
      if (question.parent_id === null) {
        const result = processIndividualQuestion(question, formData, questions, categoryTracking);
        if (result.shouldSkip) continue;
        
        normalizedScore = result.normalizedScore;
        debugInfo = result.debugInfo;

        // If in debug mode, store detailed score information for the current question.
        if (debug_score) {
          questionScores.push({
            question: question.question_text,
            answer: formData[question.id],
            score: normalizedScore,
            ...debugInfo,
            scoring_mode: question.scoring_mode,
            scoring_type: question.scoring_type
          });
        }
      }
    }

    // Step 4: Calculate the average score for each category.
    const averages = calculateCategoryAverages(categoryTracking);

    // Step 5: Apply the final IVF formula to get the total score.
    const totalScore = applyIVFFormula(averages.biological, averages.lifestyle, averages.environmental);

    // Step 6: Ensure the score is within a valid range and determine the final category.
    const finalScore = Math.max(totalScore, 60);
    const percentage = Math.round(finalScore);
    const category = determineCategory(percentage);

    // Construct the final result object.
    const result: IVFScoreResult = {
      totalScore: Number(finalScore.toFixed(2)),
      maxScore: 100,
      percentage,
      category,
      factors: {
        biological: Number((0.5 * averages.biological).toFixed(2)),
        lifestyle: Number((0.25 * averages.lifestyle).toFixed(2)),
        environmental: Number((0.25 * averages.environmental).toFixed(2)),
      },
      avgFactors: {
        biological: [Number(averages.biological.toFixed(2)), categoryTracking.counts.biological],
        lifestyle: [Number(averages.lifestyle.toFixed(2)), categoryTracking.counts.lifestyle],
        environmental: [Number(averages.environmental.toFixed(2)), categoryTracking.counts.environmental]
      }
    };

    // Include detailed question scores in the result if debug mode is enabled.
    if (debug_score) {
      result.questions = questionScores;
    }

    return result;
  } catch (error) {
    console.error("Error calculating dynamic IVF score:", error);
    // Return a default "Fair" score in case of an error.
    return {
      totalScore: 60,
      maxScore: 100,
      percentage: 60,
      category: "Fair",
      factors: { biological: 0, lifestyle: 0, environmental: 0 },
      avgFactors: { biological: [0, 0], lifestyle: [0, 0], environmental: [0, 0] }
    };
  }
}