# Dynamic Scoring Module

This module is responsible for calculating the IVF score based on a user's answers to the fertility assessment. It's designed to be flexible and handle complex scoring logic, including sub-questions and different scoring modes.

## Key Features

- **Normalized Scoring**: All question and sub-question scores are normalized to a 0-1 range for consistent comparison
- **Sub-question Support**: Handles complex questions with multiple sub-questions
- **Multiple Scoring Modes**: Supports separate and collective scoring modes for grouped questions
- **Dynamic Configuration**: Scoring rules are configurable through the database

## File Structure

The module is broken down into the following files:

-   `index.ts`: The main entry point for the module. It exports the public functions that can be used by other parts of the application.
-   `questions.ts`: This file contains the logic for fetching and processing the fertility questions from the database.
-   `scoring.ts`: This file contains the core scoring logic, including how to calculate the score for individual questions and how to handle sub-questions with different scoring modes.
-   `helpers.ts`: This file contains helper functions that are used across the module.
-   `types.ts`: This file defines the data structures and types that are used throughout the module.

## How it Works

The main function in this module is `calculateDynamicIVFScore`. It takes the user's answers as input and returns a comprehensive score, including a total score, a percentage, a category, and a breakdown of the different factors that contributed to the score.

### Normalization Process

1. **Individual Question Scoring**: Each question's raw score is normalized to 0-1 by dividing by the maximum possible score for that question
2. **Sub-question Scoring**: Sub-questions are scored individually and normalized, then combined according to the scoring mode
3. **Category Aggregation**: Normalized scores are averaged within each category (biological, lifestyle, environmental)
4. **Final Calculation**: The IVF formula is applied using the normalized category averages

### Scoring Modes

- **Separate Mode**: Sub-questions are scored individually and averaged
- **Collective Mode**: Sub-question values are combined using a formula, then the parent question's scoring rules are applied

The scoring logic is designed to be flexible and can be configured in the database. Each question can have its own scoring configuration, and questions can be grouped together with sub-questions that are scored collectively.

## Usage

To calculate the IVF score for a user, you can import the `calculateDynamicIVFScore` function from this module and pass in the user's answers.

```typescript
import { calculateDynamicIVFScore } from '@/lib/services/ivf-scoring';

const userAnswers = {
  // ...
};

const score = await calculateDynamicIVFScore(userAnswers);
```

The returned score includes:
- `totalScore`: The final IVF score (60-100)
- `percentage`: The score as a percentage
- `category`: The fertility category (Excellent, Good, Fair, Moderate, Low)
- `factors`: Breakdown of scores by category (biological, lifestyle, environmental)
- `questions`: Optional detailed breakdown of individual question scores (when `with_questions=true`)
