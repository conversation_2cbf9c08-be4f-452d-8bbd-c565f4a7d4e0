import { ScoringType } from "@/generated/prisma";
import { ProcessedQuestion, QuestionWithScoring } from './types';
import { getCategoryFromFormName } from './helpers';
import { createClient } from "@/utils/supabase/server";

/**
 * Fetches all questions with scoring configurations
 * Now includes sub-questions and handles GROUP_QUESTION field type
 * Optionally filters by track code (T1, T2, T3)
 */
export async function getQuestionsWithScoring(trackCode?: string, formSlugs?: string[]): Promise<ProcessedQuestion[]> {
  try {

    // Previous Version with Prisma
    // const questions = await prisma.questions.findMany({
    //   where: {
    //     parent_id: null, // Only fetch top-level questions
    //     OR: [
    //       // Questions with scoring_type (individual questions)
    //       {
    //         scoring_type: {
    //           not: null
    //         }
    //       },
    //       // GROUP_QUESTION type questions (parent questions with sub-questions)
    //       {
    //         field_type: 'GROUP_QUESTION'
    //       }
    //     ],
    //     ...(trackCode
    //       ? {
    //           OR: [
    //             // Questions with no tracks (global)
    //             { question_tracks: { none: {} } },
    //             // Questions with the selected track
    //             { question_tracks: { some: { track: { code: trackCode } } } }
    //           ]
    //         }
    //       : {})
    //   },
    //   select: {
    //     id: true,
    //     question_text: true,
    //     field_type: true,
    //     scoring_type: true,
    //     scoring_config: true,
    //     scoring_mode: true,
    //     collective_formula: true,
    //     parent_id: true,
    //     depends_on_option_id: true,
    //     form: {
    //       select: {
    //         name: true
    //       }
    //     },
    //     options: {
    //       select: {
    //         id: true,
    //         option_text: true,
    //         value: true,
    //         score: true
    //       }
    //     },
    //     question_tracks: {
    //       select: {
    //         track: { select: { code: true } }
    //       }
    //     },
    //     sub_questions: {
    //       select: {
    //         id: true,
    //         question_text: true,
    //         field_type: true,
    //         scoring_type: true,
    //         scoring_config: true,
    //         scoring_mode: true,
    //         collective_formula: true,
    //         parent_id: true,
    //         form: {
    //           select: {
    //             name: true
    //           }
    //         },
    //         options: {
    //           select: {
    //             id: true,
    //             option_text: true,
    //             value: true,
    //             score: true
    //           }
    //         },
    //         question_tracks: {
    //           select: {
    //             track: { select: { code: true } }
    //           }
    //         }
    //       }
    //     }
    //   }
    // });

    // Refactor with RPC calling
    const supabase = await createClient();
    const { data: questions, error } = await supabase.rpc('get_questions_with_scoring', { track_code: trackCode, form_slugs: formSlugs });
    
    if (error) {
      console.error("Error fetching questions with scoring:", error);
      return [];
    }

    if (!questions) {
      return [];
    }

    // Pre-process questions
    return questions.map((question: QuestionWithScoring) => {
      // Pre-parse scoring config once
      let parsedScoringConfig = null;
      if (question.scoring_config) {
        try {
          parsedScoringConfig = typeof question.scoring_config === 'string'
            ? JSON.parse(question.scoring_config)
            : question.scoring_config;

          // Validate that the parsed config is an array for scoring types that require it
          if (question.scoring_type === ScoringType.range || question.scoring_type === ScoringType.single_choice) {
            if (!Array.isArray(parsedScoringConfig)) {
              console.error(`Invalid scoring config for question ${question.id}: expected array, got ${typeof parsedScoringConfig}`, parsedScoringConfig);
              parsedScoringConfig = null;
            }
          }
        } catch (error) {
          console.error(`Error parsing scoring config for question ${question.id}:`, error);
        }
      }

      // Process sub-questions
      const processedSubQuestions: ProcessedQuestion[] = (question.sub_questions || []).map((subQuestion: QuestionWithScoring) => {
        let parsedSubScoringConfig = null;
        if (subQuestion.scoring_config) {
          try {
            parsedSubScoringConfig = typeof subQuestion.scoring_config === 'string'
              ? JSON.parse(subQuestion.scoring_config)
              : subQuestion.scoring_config;

            if (subQuestion.scoring_type === ScoringType.range || subQuestion.scoring_type === ScoringType.single_choice) {
              if (!Array.isArray(parsedSubScoringConfig)) {
                console.error(`Invalid scoring config for sub-question ${subQuestion.id}: expected array, got ${typeof parsedSubScoringConfig}`, parsedSubScoringConfig);
                parsedSubScoringConfig = null;
              }
            }
          } catch (error) {
            console.error(`Error parsing scoring config for sub-question ${subQuestion.id}:`, error);
          }
        }

        return {
          ...subQuestion,
          scoring_config: parsedSubScoringConfig,
          category: getCategoryFromFormName(subQuestion.form.name),
          sub_questions: [] // Sub-questions don't have their own sub-questions (one level only)
        };
      });

      return {
        ...question,
        scoring_config: parsedScoringConfig,
        category: getCategoryFromFormName(question.form.name),
        sub_questions: processedSubQuestions
      };
    });
  } catch (error) {
    console.error("Error fetching questions with scoring:", error);
    return [];
  }
}
