
import { calculateQuestionScoreOptimized, calculateSubQuestionScore } from './scoring';
import { ProcessedQuestion } from "./types";
import { shouldExcludeParentQuestion, shouldIncludeConditionalQuestion, initializeCategoryTracking } from './utils';

/**
 * Processes a group question, which contains sub-questions.
 * The score for a group question is an aggregation of its sub-questions' scores.
 *
 * @param question - The group question to process.
 * @param formData - The user's submitted form data.
 * @param categoryTracking - The tracking object for category scores and counts.
 * @returns An object containing the normalized score and debugging information (raw and max scores).
 */
export function processGroupQuestion(
  question: ProcessedQuestion,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  formData: Record<string, any>,
  categoryTracking: ReturnType<typeof initializeCategoryTracking>
): { normalizedScore: number; debugInfo: { rawScore?: number; maxScore?: number } } {
  // Calculate the score based on its sub-questions.
  const subScoreResult = calculateSubQuestionScore(question, formData, categoryTracking.scores);
  
  // Increment the count for the question's category.
  if (question.category && categoryTracking.counts[question.category] !== undefined) {
    categoryTracking.counts[question.category] += 1;
  }
  
  return {
    normalizedScore: subScoreResult.normalizedScore,
    debugInfo: {
      rawScore: subScoreResult.rawScore,
      maxScore: subScoreResult.maxScore
    }
  };
}

/**
 * Processes an individual question, calculating its score and updating category tracking.
 * This function handles various conditions, such as question dependencies and exclusions.
 *
 * @param question - The individual question to process.
 * @param formData - The user's submitted form data.
 * @param questions - An array of all processed questions.
 * @param categoryTracking - The tracking object for category scores and counts.
 * @returns An object with the normalized score, debug info, and a flag indicating if the question was skipped.
 */
export function processIndividualQuestion(
  question: ProcessedQuestion,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  formData: Record<string, any>,
  questions: ProcessedQuestion[],
  categoryTracking: ReturnType<typeof initializeCategoryTracking>
): { normalizedScore: number; debugInfo: { rawScore?: number; maxScore?: number }; shouldSkip: boolean } {
  const answer = formData[question.id];
  
  // Skip question if there is no answer.
  if (!answer) {
    return { normalizedScore: 0, debugInfo: {}, shouldSkip: true };
  }
  
  // Skip parent questions when a dependent child question is included.
  if (shouldExcludeParentQuestion(question, formData, questions)) {
    return { normalizedScore: 0, debugInfo: {}, shouldSkip: true };
  }
  
  // Skip conditional questions if their dependencies are not met.
  if (!shouldIncludeConditionalQuestion(question, formData, questions)) {
    return { normalizedScore: 0, debugInfo: {}, shouldSkip: true };
  }
  
  // Calculate the score for the question based on the user's answer.
  const scoreResult = calculateQuestionScoreOptimized(question, answer);
  
  // Add the score to the corresponding category.
  categoryTracking.scores[question.category] += scoreResult.normalizedScore;
  
  // Increment the count for the question's category.
  if (question.category && categoryTracking.counts[question.category] !== undefined) {
      categoryTracking.counts[question.category] += 1;
  }
  
  return {
    normalizedScore: scoreResult.normalizedScore,
    debugInfo: {
      rawScore: scoreResult.rawScore,
      maxScore: scoreResult.maxScore
    },
    shouldSkip: false
  };
}
