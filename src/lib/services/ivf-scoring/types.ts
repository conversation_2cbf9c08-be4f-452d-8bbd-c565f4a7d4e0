import { ScoringType, ScoringMode } from "@/generated/prisma";
import type { RangeScoreConfig, SingleChoiceScoreConfig } from "@/types";

export interface QuestionWithScoring {
  id: string;
  question_text: string;
  field_type: string;
  scoring_type: ScoringType | null;
  scoring_config: RangeScoreConfig[] | SingleChoiceScoreConfig[] | string | null;
  scoring_mode: ScoringMode;
  collective_formula: string | null;
  parent_id: string | null;
  form: {
    name: string;
  };
  options: Array<{
    id: string;
    option_text: string;
    value: string | null;
    score: number | null;
  }>;
  sub_questions: QuestionWithScoring[];
  depends_on_option_id?: string | null;
}

export interface CategoryScores {
  biological: number;
  lifestyle: number;
  environmental: number;
}

export interface ProcessedQuestion extends Omit<QuestionWithScoring, 'scoring_config'> {
  scoring_config: RangeScoreConfig[] | SingleChoiceScoreConfig[] | null;
  category: keyof CategoryScores;
  sub_questions: ProcessedQuestion[];
  depends_on_option_id?: string | null;
}
