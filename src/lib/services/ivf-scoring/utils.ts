
import { ProcessedQuestion } from "./types";

/**
 * Finds the parent question and the controlling option for a given `depends_on_option_id`.
 * This is used to establish the relationship between a conditional question and the option that triggers it.
 *
 * @param dependsOnOptionId - The ID of the option that the conditional question depends on.
 * @param allQuestions - An array of all processed questions available in the assessment.
 * @returns An object containing the parent question and the controlling option, or null if not found.
 */
export function findParentQuestionAndOption(dependsOnOptionId: string, allQuestions: ProcessedQuestion[]) {
  // Find the question that contains the option with the given ID.
  const parentQuestion = allQuestions.find(q => 
    q.options.some(opt => opt.id === dependsOnOptionId)
  );
  
  if (!parentQuestion) return null;
  
  // Find the specific option within the parent question.
  const controllingOption = parentQuestion.options.find(opt => 
    opt.id === dependsOnOptionId
  );
  
  if (!controllingOption) return null;
  
  return { parentQuestion, controllingOption };
}

/**
 * Checks if the user's answer to a parent question matches the value of the controlling option.
 * This is a key part of determining if a conditional question should be displayed.
 *
 * @param question - The conditional question to check.
 * @param formData - The user's submitted form data.
 * @param allQuestions - An array of all processed questions.
 * @returns `true` if the answer matches the controlling option's value, `false` otherwise.
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function answerIsMatched(question: ProcessedQuestion, formData: Record<string, any>, allQuestions: ProcessedQuestion[]): boolean {
  if (!question.depends_on_option_id) return false;
  
  const result = findParentQuestionAndOption(question.depends_on_option_id, allQuestions);
  if (!result) return false;
  
  const { parentQuestion, controllingOption } = result;
  
  // The value to match against can be the option's `value` field or fallback to `option_text`.
  const matchValue = controllingOption.value ?? controllingOption.option_text;
  
  // The user's answer for the parent question.
  const answerString = formData[parentQuestion.id];
  
  return answerString === matchValue;
}

/**
 * Determines whether a conditional question should be included in the scoring process.
 * A question is included if its `depends_on_option_id` is met by the user's answers.
 *
 * @param question - The conditional question to evaluate.
 * @param formData - The user's submitted form data.
 * @param allQuestions - An array of all processed questions.
 * @returns `true` if the question should be included, `false` otherwise.
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function shouldIncludeConditionalQuestion(question: ProcessedQuestion, formData: Record<string, any>, allQuestions: ProcessedQuestion[]): boolean {
  // If the question has no dependency, it should always be included.
  if (!question.depends_on_option_id) return true;
  
  const result = findParentQuestionAndOption(question.depends_on_option_id, allQuestions);
  if (!result) return false;
  
  const { parentQuestion, controllingOption } = result;
  
  const matchValue = controllingOption.value ?? controllingOption.option_text;
  const parentAnswer = formData[parentQuestion.id];
  
  // Include the question only if the parent's answer matches the controlling option.
  return parentAnswer === matchValue;
}

/**
 * Initializes tracking objects for category-based scores and counts.
 * This helps in aggregating scores for 'biological', 'lifestyle', and 'environmental' factors.
 *
 * @returns An object with `scores` and `counts` for each category, initialized to zero.
 */
export function initializeCategoryTracking() {
  return {
    scores: {
      biological: 0,
      lifestyle: 0,
      environmental: 0
    },
    counts: {
      biological: 0,
      lifestyle: 0,
      environmental: 0
    }
  };
}

/**
 * Calculates the average score for each category based on the total scores and counts.
 *
 * @param categoryTracking - The object containing the aggregated scores and counts for each category.
 * @returns An object with the average scores for 'biological', 'lifestyle', and 'environmental' categories.
 */
export function calculateCategoryAverages(
  categoryTracking: ReturnType<typeof initializeCategoryTracking>
): { biological: number; lifestyle: number; environmental: number } {
  const { scores, counts } = categoryTracking;
  
  return {
    biological: counts.biological > 0 ? scores.biological / counts.biological : 0,
    lifestyle: counts.lifestyle > 0 ? scores.lifestyle / counts.lifestyle : 0,
    environmental: counts.environmental > 0 ? scores.environmental / counts.environmental : 0
  };
}

/**
 * Applies the IVF formula to the average category scores to calculate the final IVF score.
 * The formula weights each category differently.
 *
 * @param avgBiological - The average score for the biological category.
 * @param avgLifestyle - The average score for the lifestyle category.
 * @param avgEnvironmental - The average score for the environmental category.
 * @returns The final calculated IVF score, as a percentage.
 */
export function applyIVFFormula(avgBiological: number, avgLifestyle: number, avgEnvironmental: number): number {
  const biological = 0.5 * avgBiological;
  const lifestyle = 0.25 * avgLifestyle;
  const environmental = 0.25 * avgEnvironmental;
  
  return 100 * (biological + lifestyle + environmental);
}

/**
 * Determines the descriptive category (e.g., "Excellent", "Good") based on the final percentage score.
 *
 * @param percentage - The final IVF score percentage.
 * @returns A string representing the score category.
 */
export function determineCategory(percentage: number): string {
  if (percentage >= 80) return "Excellent";
  if (percentage >= 70) return "Good";
  if (percentage >= 60) return "Fair";
  if (percentage >= 50) return "Moderate";
  return "Low";
}

/**
 * Checks if a parent question should be excluded from scoring.
 * This occurs when a dependent (child) question is included, to avoid double-counting.
 *
 * @param question - The parent question to check.
 * @param formData - The user's submitted form data.
 * @param allQuestions - An array of all processed questions.
 * @returns `true` if the parent question should be excluded, `false` otherwise.
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function shouldExcludeParentQuestion(question: ProcessedQuestion, formData: Record<string, any>, allQuestions: ProcessedQuestion[]): boolean {
  // Check if any other question depends on an option from this question.
  const hasChildQuestion = allQuestions.some(q => 
    q.depends_on_option_id && 
    question.options.some(opt => opt.id === q.depends_on_option_id)
  );
  
  if (!hasChildQuestion) return false;
  
  // Find the specific child question that has this dependency.
  const childQuestion = allQuestions.find(q => 
    q.depends_on_option_id && 
    question.options.some(opt => opt.id === q.depends_on_option_id)
  );
  
  if (!childQuestion) return false;
  
  // If the child question is included, the parent should be excluded.
  return shouldIncludeConditionalQuestion(childQuestion, formData, allQuestions);
}
