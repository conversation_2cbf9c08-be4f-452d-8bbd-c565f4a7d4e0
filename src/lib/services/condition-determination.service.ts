import { diet_condition } from "@/generated/prisma";

/**
 * Determine the user's condition based on their diet plan assessment data
 * This function analyzes the form data to determine if the user has PCOS, Low AMH, or both
 * Handles the 4 standard labels: PCOS, Low AMH, PCOS and Low AMH, No Condition
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function determineUserCondition(formData: Record<string, any>): diet_condition {
  
  // First check if there's a direct condition field with standard labels
  const directCondition = checkDirectConditionField(formData);
  if (directCondition) {
    return directCondition;
  }
  
  // Extract relevant fields from form data - handle various field name patterns
  const hasPCOS = checkPCOSCondition(formData);
  const hasLowAMH = checkLowAMHCondition(formData);
  
  console.log('Condition check results:', { hasPCOS, hasLowAMH });
  
  // Determine condition based on the data
  if (hasPCOS && hasLowAMH) {
    console.log('User has both PCOS and Low AMH');
    return 'PCOS_AND_LOW_AMH';
  } else if (hasPC<PERSON>) {
    console.log('User has PCOS');
    return 'PCOS';
  } else if (hasLowAMH) {
    console.log('User has Low AMH');
    return 'LOW_AMH';
  } else {
    console.log('User has no specific condition');
    return 'NO_CONDITION';
  }
}

/**
 * Check for direct condition field with standard labels
 * Handles case sensitivity and various formats
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function checkDirectConditionField(formData: Record<string, any>): diet_condition | null {
  const conditionFields = [
    'condition', 'diagnosis', 'medical_condition', 'fertility_condition',
    'user_condition', 'health_condition', 'primary_condition'
  ];
  
  for (const field of conditionFields) {
    const value = formData[field];
    if (!value) continue;
    
    const normalizedValue = String(value).trim().toLowerCase();
    
    // Check for exact matches (case insensitive)
    if (normalizedValue === 'pcos') {
      return 'PCOS';
    }
    if (normalizedValue === 'low amh' || normalizedValue === 'low_amh') {
      return 'LOW_AMH';
    }
    if (normalizedValue === 'pcos and low amh' || normalizedValue === 'pcos_and_low_amh') {
      return 'PCOS_AND_LOW_AMH';
    }
    if (normalizedValue === 'no condition' || normalizedValue === 'none' || normalizedValue === 'normal') {
      return 'NO_CONDITION';
    }
    
    // Check for partial matches
    if (normalizedValue.includes('pcos') && normalizedValue.includes('low amh')) {
      return 'PCOS_AND_LOW_AMH';
    }
    if (normalizedValue.includes('pcos')) {
      return 'PCOS';
    }
    if (normalizedValue.includes('low amh') || normalizedValue.includes('amh')) {
      return 'LOW_AMH';
    }
  }
  
  return null;
}

/**
 * Check if user has PCOS based on form data
 * Handles various field name patterns that might be used
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function checkPCOSCondition(formData: Record<string, any>): boolean {
  // Check various possible field names for PCOS
  const pcosFields = [
    'pcos', 'PCOS', 'polycystic_ovary_syndrome', 'polycystic_ovary',
    'condition_pcos', 'has_pcos', 'pcos_diagnosis'
  ];
  
  for (const field of pcosFields) {
    const value = formData[field];
    if (value === 'yes' || value === true || value === 'true' || value === 'Yes') {
      console.log(`PCOS detected from field '${field}' with value:`, value);
      return true;
    }
  }
  
  // Check if any field contains PCOS-related text (case insensitive)
  for (const [key, value] of Object.entries(formData)) {
    if (typeof value === 'string') {
      const normalizedValue = value.toLowerCase();
      if (normalizedValue.includes('pcos') || 
          normalizedValue.includes('polycystic')) {
        console.log(`PCOS detected from field '${key}' with text:`, value);
        return true;
      }
    }
  }
  
  return false;
}

/**
 * Check if user has Low AMH based on form data
 * Handles various field name patterns that might be used
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function checkLowAMHCondition(formData: Record<string, any>): boolean {
  // Check various possible field names for Low AMH
  const amhFields = [
    'amh', 'AMH', 'amh_level', 'amh_value', 'anti_mullerian_hormone',
    'low_amh', 'has_low_amh', 'amh_diagnosis', 'condition_amh'
  ];
  
  // Check boolean fields
  for (const field of amhFields) {
    const value = formData[field];
    if (value === 'yes' || value === true || value === 'true' || value === 'Yes') {
      console.log(`Low AMH detected from field '${field}' with value:`, value);
      return true;
    }
  }
  
  // Check AMH level if provided (AMH < 1.0 is considered low)
  for (const field of amhFields) {
    const value = formData[field];
    if (typeof value === 'string' || typeof value === 'number') {
      const amhLevel = parseFloat(String(value));
      if (!isNaN(amhLevel) && amhLevel < 1.0) {
        console.log(`Low AMH detected from field '${field}' with level:`, amhLevel);
        return true;
      }
    }
  }
  
  // Check if any field contains AMH-related text (case insensitive)
  for (const [key, value] of Object.entries(formData)) {
    if (typeof value === 'string') {
      const normalizedValue = value.toLowerCase();
      if (normalizedValue.includes('amh') || 
          normalizedValue.includes('low amh') ||
          normalizedValue.includes('anti-müllerian') ||
          normalizedValue.includes('anti-mullerian')) {
        console.log(`Low AMH detected from field '${key}' with text:`, value);
        return true;
      }
    }
  }
  
  return false;
}

/**
 * Get the appropriate meal plan condition for the user
 * This function maps the user's condition to the available meal plan conditions
 */
export function getMealPlanCondition(userCondition: diet_condition): diet_condition {
  console.log('Mapping user condition to meal plan condition:', userCondition);
  
  // Map user conditions to available meal plan conditions
  switch (userCondition) {
    case 'PCOS_AND_LOW_AMH':
    case 'PCOS':
    case 'LOW_AMH':
    case 'NO_CONDITION':
      console.log('Using meal plan for condition:', userCondition);
      return userCondition;
    default:
      console.log('Using default NO_CONDITION meal plan');
      return 'NO_CONDITION';
  }
}
