/* eslint-disable @typescript-eslint/no-explicit-any */
"use server";
import { PrismaClient } from "@/generated/prisma";
import type { 
  FormWithQuestions, 
  FormBuilderForm, 
  FormBuilderQuestion, 
  FormBuilderOption 
} from "@/types";
import { revalidatePath } from "next/cache";


const prisma = new PrismaClient();

// Get all forms with their questions and options
export async function getAllForms(): Promise<FormWithQuestions[]> {
  try {
    const forms = await prisma.forms.findMany({
      include: {
        questions: {
          include: {
            options: {
              orderBy: { order: 'asc' }
            },
            question_tracks: {
              include: {
                track: true
              }
            }
          },
          orderBy: { order: 'asc' }
        }
      },
      orderBy: { created_at: 'asc' }
    });

    return forms;
  } catch (error) {
    console.error("Error fetching forms:", error);
    throw new Error("Failed to fetch forms");
  }
}

// Get a specific form by ID
export async function getFormById(id: string): Promise<FormWithQuestions | null> {
  try {
    const form = await prisma.forms.findUnique({
      where: { id },
      include: {
        questions: {
          include: {
            options: {
              orderBy: { order: 'asc' }
            },
            question_tracks: {
              include: {
                track: true
              }
            },
            sub_questions: {
              include: {
                options: {
                  orderBy: { order: 'asc' }
                }
              },
              orderBy: { order: 'asc' }
            }
          },
          orderBy: { order: 'asc' }
        }
      }
    });

    return form;
  } catch (error) {
    console.error("Error fetching form:", error);
    throw new Error("Failed to fetch form");
  }
}

// Get form by name (for category-based forms)
export async function getFormByName(name: string): Promise<FormWithQuestions | null> {
  try {
    const form = await prisma.forms.findUnique({
      where: { name },
      include: {
        questions: {
          include: {
            options: {
              orderBy: { order: 'asc' }
            },
            question_tracks: {
              include: {
                track: true
              }
            },
            sub_questions: {
              include: {
                options: {
                  orderBy: { order: 'asc' }
                }
              },
              orderBy: { order: 'asc' }
            }
          },
          where: {
            parent_id: null // Only get parent questions
          },
          orderBy: { order: 'asc' }
        }
      }
    });

    return form;
  } catch (error) {
    console.error("Error fetching form by name:", error);
    throw new Error("Failed to fetch form");
  }
}

// Create a new form
export async function createForm(formData: FormBuilderForm): Promise<FormWithQuestions> {
  try {
    const form = await prisma.forms.create({
      data: {
        name: formData.name,
        description: formData.description,
        questions: {
          create: formData.questions.map((question, index) => ({
            question_text: question.question_text,
            field_type: question.field_type,
            placeholder: question.placeholder,
            min_value: question.min_value,
            max_value: question.max_value,
            step: question.step,
            unit: question.unit,
            order: question.order || index,
            options: question.options ? {
              create: question.options.map((option, optIndex) => ({
                option_text: option.option_text,
                value: option.value,
                order: option.order || optIndex
              }))
            } : undefined
          }))
        }
      },
      include: {
        questions: {
          include: {
            options: {
              orderBy: { order: 'asc' }
            }
          },
          orderBy: { order: 'asc' }
        }
      }
    });

    revalidatePath('/admin/fertility-meter-questions');
    return form;
  } catch (error) {
    console.error("Error creating form:", error);
    throw new Error("Failed to create form");
  }
}

// Update an existing form
export async function updateForm(id: string, formData: Partial<FormBuilderForm>): Promise<FormWithQuestions> {
  try {
    const form = await prisma.forms.update({
      where: { id },
      data: {
        name: formData.name,
        description: formData.description,
      },
      include: {
        questions: {
          include: {
            options: {
              orderBy: { order: 'asc' }
            }
          },
          orderBy: { order: 'asc' }
        }
      }
    });

    revalidatePath('/admin/fertility-meter-questions');
    return form;
  } catch (error) {
    console.error("Error updating form:", error);
    throw new Error("Failed to update form");
  }
}

// Delete a form
export async function deleteForm(id: string): Promise<void> {
  try {
    await prisma.forms.delete({
      where: { id }
    });

    revalidatePath('/admin/fertility-meter-questions');
  } catch (error) {
    console.error("Error deleting form:", error);
    throw new Error("Failed to delete form");
  }
}

// Initialize default forms for the three categories if they don't exist
export async function initializeDefaultForms(): Promise<void> {
  try {
    // Get all existing form slugs to avoid duplicates
    const existingForms = await prisma.forms.findMany({
      select: { slug: true }
    });
    const existingSlugs = new Set(existingForms.map(f => f.slug).filter(Boolean));

    // Define default categories to initialize
    const defaultCategories = ['biological', 'lifestyle', 'environmental'];

    for (const category of defaultCategories) {
      if (!existingSlugs.has(category)) {
        await prisma.forms.create({
          data: {
            name: `${category.charAt(0).toUpperCase() + category.slice(1)} Factors Form`,
            slug: category,
            description: `Default form for ${category} factors`,
            questions: {
              create: []
            }
          }
        });
      }
    }

    revalidatePath('/admin/fertility-meter-questions');
  } catch (error) {
    console.error("Error initializing default forms:", error);
    throw new Error("Failed to initialize default forms");
  }
}

// Question management functions

// Add a new question to a form
export async function addQuestion(formId: string, questionData: FormBuilderQuestion): Promise<string> {
  try {
    // Get the current max order for questions in this form
    const maxOrderResult = await prisma.questions.aggregate({
      where: { form_id: formId },
      _max: { order: true }
    });

    const nextOrder = (maxOrderResult._max.order || 0) + 1;

    let questionId: string = '';
    
    await prisma.$transaction(async (tx) => {
      // Create the question
      const question = await tx.questions.create({
        data: {
          form_id: formId,
          parent_id: questionData.parent_id,
          question_text: questionData.question_text,
          help_text: questionData.help_text,
          is_mandatory: questionData.is_mandatory,
          field_type: questionData.field_type,
          placeholder: questionData.placeholder,
          min_value: questionData.min_value,
          max_value: questionData.max_value,
          step: questionData.step,
          unit: questionData.unit,
          order: questionData.order || nextOrder,

          // Scoring fields
          scoring_type: questionData.scoring_type,
          scoring_config: questionData.scoring_config ? 
            (typeof questionData.scoring_config === 'string' ? 
              questionData.scoring_config : 
              JSON.stringify(questionData.scoring_config)) : undefined,

          // Conditional logic
          depends_on_option_id: questionData.depends_on_option_id || undefined,

          options: questionData.options ? {
            create: questionData.options.map((option, index) => ({
              option_text: option.option_text,
              value: option.value,
              order: option.order || index,
              score: option.score
            }))
          } : undefined
        }
      });

      questionId = question.id;

      // Create track associations if provided (only for parent questions)
      if (!questionData.parent_id && questionData.track_ids && questionData.track_ids.length > 0) {
        await tx.question_tracks.createMany({
          data: questionData.track_ids.map(trackId => ({
            question_id: question.id,
            track_id: trackId
          }))
        });
      }
    });

    revalidatePath('/admin/fertility-meter-questions');
    
    return questionId;
  } catch (error) {
    console.error("Error adding question:", error);
    throw new Error("Failed to add question");
  }
}

// Update an existing question
export async function updateQuestion(questionId: string, questionData: Partial<FormBuilderQuestion>): Promise<void> {
  try {
    await prisma.$transaction(async (tx) => {
      // Update the question
      await tx.questions.update({
        where: { id: questionId },
        data: {
          question_text: questionData.question_text,
          help_text: questionData.help_text,
          is_mandatory: questionData.is_mandatory,
          field_type: questionData.field_type,
          placeholder: questionData.placeholder,
          min_value: questionData.min_value,
          max_value: questionData.max_value,
          step: questionData.step,
          unit: questionData.unit,
          order: questionData.order,

          // Scoring fields
          scoring_type: questionData.scoring_type,
          scoring_config: questionData.scoring_config ? 
            (typeof questionData.scoring_config === 'string' ? 
              questionData.scoring_config : 
              JSON.stringify(questionData.scoring_config)) : undefined,

          // Conditional logic
          depends_on_option_id: questionData.depends_on_option_id || undefined,
        }
      });

      // Update track associations if provided
      if (questionData.track_ids !== undefined) {
        // Delete existing track associations
        await tx.question_tracks.deleteMany({
          where: { question_id: questionId }
        });

        // Create new track associations
        if (questionData.track_ids.length > 0) {
          await tx.question_tracks.createMany({
            data: questionData.track_ids.map(trackId => ({
              question_id: questionId,
              track_id: trackId
            }))
          });
        }
      }

              // Update options if provided
        if (questionData.options !== undefined) {
          // Delete existing options
          await tx.options.deleteMany({
            where: { question_id: questionId }
          });

          // Create new options
          if (questionData.options.length > 0) {
            await tx.options.createMany({
            data: questionData.options.map((option, index) => ({
              question_id: questionId,
              option_text: option.option_text,
              value: option.value,
              order: option.order || index,
              score: option.score
            }))
          });
        }
      }
    });


    revalidatePath('/admin/fertility-meter-questions');
  } catch (error) {
    console.error("Error updating question:", error);
    throw new Error("Failed to update question");
  }
}

// Delete a question
export async function deleteQuestion(questionId: string): Promise<void> {
  try {
    await prisma.questions.delete({
      where: { id: questionId }
    });

    revalidatePath('/admin/fertility-meter-questions');
  } catch (error) {
    console.error("Error deleting question:", error);
    throw new Error("Failed to delete question");
  }
}

// Add sub-question to a parent question
export async function addSubQuestion(parentQuestionId: string, subQuestionData: FormBuilderQuestion): Promise<void> {
  try {
    // Get the parent question to get the form_id
    const parentQuestion = await prisma.questions.findUnique({
      where: { id: parentQuestionId },
      select: { form_id: true }
    });

    if (!parentQuestion) {
      throw new Error("Parent question not found");
    }

    // Get the current max order for sub-questions of this parent
    const maxOrderResult = await prisma.questions.aggregate({
      where: { parent_id: parentQuestionId },
      _max: { order: true }
    });

    const nextOrder = (maxOrderResult._max.order || 0) + 1;

    await prisma.$transaction(async (tx) => {
      // Create the sub-question
      await tx.questions.create({
        data: {
          form_id: parentQuestion.form_id,
          parent_id: parentQuestionId,
          question_text: subQuestionData.question_text,
          help_text: subQuestionData.help_text,
          is_mandatory: subQuestionData.is_mandatory,
          field_type: subQuestionData.field_type,
          placeholder: subQuestionData.placeholder,
          min_value: subQuestionData.min_value,
          max_value: subQuestionData.max_value,
          step: subQuestionData.step,
          unit: subQuestionData.unit,
          order: subQuestionData.order || nextOrder,

          // Scoring fields
          scoring_type: subQuestionData.scoring_type,
          scoring_config: subQuestionData.scoring_config ? 
            (typeof subQuestionData.scoring_config === 'string' ? 
              subQuestionData.scoring_config : 
              JSON.stringify(subQuestionData.scoring_config)) : undefined,

          options: subQuestionData.options ? {
            create: subQuestionData.options.map((option, index) => ({
              option_text: option.option_text,
              value: option.value,
              order: option.order || index,
              score: option.score
            }))
          } : undefined
        }
      });
    });

    revalidatePath('/admin/fertility-meter-questions');
  } catch (error) {
    console.error("Error adding sub-question:", error);
    throw new Error("Failed to add sub-question");
  }
}

// Reorder questions within a form
export async function reorderQuestions(formId: string, questionOrders: { id: string; order: number }[]): Promise<void> {
  try {
    await prisma.$transaction(
      questionOrders.map(({ id, order }) =>
        prisma.questions.update({
          where: { id },
          data: { order }
        })
      )
    );

    revalidatePath('/admin/fertility-meter-questions');
  } catch (error) {
    console.error("Error reordering questions:", error);
    throw new Error("Failed to reorder questions");
  }
}

// Option management functions

// Add options to a question
export async function addOptionsToQuestion(questionId: string, options: FormBuilderOption[]): Promise<void> {
  try {
    await prisma.options.createMany({
      data: options.map((option, index) => ({
        question_id: questionId,
        option_text: option.option_text,
        value: option.value,
        order: option.order || index,
        score: option.score
      }))
    });

    revalidatePath('/admin/fertility-meter-questions');
  } catch (error) {
    console.error("Error adding options:", error);
    throw new Error("Failed to add options");
  }
}

// Update question options (replace all options)
export async function updateQuestionOptions(questionId: string, options: FormBuilderOption[]): Promise<void> {
  try {
    await prisma.$transaction(async (tx) => {
      // Delete existing options
      await tx.options.deleteMany({
        where: { question_id: questionId }
      });

      // Create new options
      if (options.length > 0) {
        await tx.options.createMany({
          data: options.map((option, index) => ({
            question_id: questionId,
            option_text: option.option_text,
            value: option.value,
            order: option.order || index,
            score: option.score
          }))
        });
      }
    });

    revalidatePath('/admin/fertility-meter-questions');
  } catch (error) {
    console.error("Error updating options:", error);
    throw new Error("Failed to update options");
  }
}

// Delete an option
export async function deleteOption(optionId: string): Promise<void> {
  try {
    await prisma.options.delete({
      where: { id: optionId }
    });

    revalidatePath('/admin/fertility-meter-questions');
  } catch (error) {
    console.error("Error deleting option:", error);
    throw new Error("Failed to delete option");
  }
}

// Utility function to get forms by category
export async function getFormsByCategory(): Promise<Record<string, FormWithQuestions | null>> {
  try {
    // Get all forms with their questions
    const forms = await prisma.forms.findMany({
      include: {
        questions: {
          include: {
            options: {
              orderBy: { order: 'asc' },
            },
            question_tracks: {
              include: {
                track: true,
              },
            },
            sub_questions: {
              include: {
                options: {
                  orderBy: { order: 'asc' },
                },
              },
            },
          },
          where: {
            parent_id: null, // Only get parent questions
          },
          orderBy: { order: 'asc' },
        },
      },
    });

    // Create a dynamic result object based on available slugs
    const result: Record<string, FormWithQuestions | null> = {};

    for (const form of forms) {
      if (form.slug) {
        result[form.slug] = form;
      }
    }

    return result;
  } catch (error) {
    console.error("Error fetching forms by category:", error);
    throw new Error("Failed to fetch forms by category");
  }
}

/**
 * Fetch questions with optional category and tracks filtering.
 * Returns raw Prisma objects (not transformed) for flexibility.
 */
export async function fetchQuestionsWithFilters({
  categories,
  tracks,
  flat = false
}: {
  categories?: string[];
  tracks?: string[];
  flat?: boolean;
}) {
  // Fetch forms based on categories using slug field
  const forms = await prisma.forms.findMany({
    where: categories && categories.length > 0 ? {
      slug: {
        in: categories
      }
    } : {},
    include: {
      questions: {
        include: {
          options: { orderBy: { order: 'asc' } },
          question_tracks: { include: { track: true } },
          sub_questions: {
            include: {
              options: { orderBy: { order: 'asc' } },
              question_tracks: { include: { track: true } }
            },
            orderBy: { order: 'asc' }
          }
        },
        orderBy: { order: 'asc' }
      }
    },
    orderBy: { created_at: 'asc' }
  });

  // Helper to flatten nested questions
  // TODO: Add proper types for questions and sub-questions
  function flattenQuestions(questions: any[], arr: any[] = []): any[] {
    for (const q of questions) {
      arr.push(q);
      if (q.sub_questions && q.sub_questions.length > 0) {
        flattenQuestions(q.sub_questions, arr);
      }
    }
    return arr;
  }

  // Helper to filter questions by tracks
  function filterByTracks(questions: any[]): any[] {
    if (!tracks || tracks.length === 0) return questions;
    return questions.filter((q: any) => {
      const qTracks = (q.question_tracks || []).map((qt: any) => qt.track.code);
      // If no tracks assigned, include (global question)
      if (qTracks.length === 0) return true;
      // Otherwise, must match at least one track
      return tracks.some((t: string) => qTracks.includes(t));
    });
  }

  // Build nested or flat question list
  const result: any[] = [];
  for (const form of forms) {
    // Only top-level questions
    let topQuestions = form.questions.filter(q => q.parent_id == null);
    // Filter by tracks at the top level
    topQuestions = filterByTracks(topQuestions);
    // Optionally flatten
    if (flat) {
      result.push(...flattenQuestions(topQuestions));
    } else {
      // Recursively filter sub-questions by tracks
      function filterNested(qs: any[]): any[] {
        return qs.map((q: any) => ({
          ...q,
          sub_questions: q.sub_questions ? filterByTracks(q.sub_questions).map(filterNested) : []
        }));
      }
      result.push(...filterNested(topQuestions));
    }
  }
  return result;
}

/**
 * Fetch questions for validation (flat list with proper filtering)
 */
export async function fetchQuestionsForValidation(categories: string[], tracks?: string) {
  // Support both single category and comma-separated categories
  const questions = await fetchQuestionsWithFilters({
    categories,
    tracks: tracks ? tracks.split(',').map(t => t.trim()) : undefined,
    flat: true
  });
  return { success: true, questions };
}
