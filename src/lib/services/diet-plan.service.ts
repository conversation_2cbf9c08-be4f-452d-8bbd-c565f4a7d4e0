/* eslint-disable @typescript-eslint/no-explicit-any */
"use server";
import { PrismaClient } from "@/generated/prisma";
import type { 
  FormWithQuestions, 
  FormBuilderQuestion
} from "@/types";
import { revalidatePath } from "next/cache";

const prisma = new PrismaClient();

const DIET_PLAN_FORM_SLUG = "diet-plan";

// Get the diet plan form with questions and options
export async function getDietPlanForm(): Promise<FormWithQuestions | null> {
  try {
    const form = await prisma.forms.findUnique({
      where: { slug: DIET_PLAN_FORM_SLUG },
      include: {
        questions: {
          include: {
            options: {
              orderBy: { order: 'asc' }
            },
            question_tracks: {
              include: {
                track: true
              }
            },
            sub_questions: {
              include: {
                options: {
                  orderBy: { order: 'asc' }
                }
              },
              orderBy: { order: 'asc' }
            }
          },
          where: {
            parent_id: null, // Only get parent questions
          },
          orderBy: { order: 'asc' }
        }
      }
    });

    return form;
  } catch (error) {
    console.error("Error fetching diet plan form:", error);
    throw new Error("Failed to fetch diet plan form");
  }
}

// Initialize the diet plan form if it doesn't exist
export async function initializeDietPlanForm(): Promise<void> {
  try {
    const existingForm = await prisma.forms.findUnique({
      where: { slug: DIET_PLAN_FORM_SLUG }
    });

    if (!existingForm) {
      await prisma.forms.create({
        data: {
          name: "Diet Plan",
          slug: DIET_PLAN_FORM_SLUG,
          description: "Diet plan assessment form for fertility optimization",
          questions: {
            create: []
          }
        }
      });
    }

    revalidatePath('/admin/diet-plan-questions');
  } catch (error) {
    console.error("Error initializing diet plan form:", error);
    throw new Error("Failed to initialize diet plan form");
  }
}

// Add a new question to the diet plan form
export async function addQuestion(formId: string, questionData: FormBuilderQuestion): Promise<string> {
  try {
    // Get the current max order for questions in this form
    const maxOrderResult = await prisma.questions.aggregate({
      where: { form_id: formId },
      _max: { order: true }
    });

    const nextOrder = (maxOrderResult._max.order || 0) + 1;

    let questionId: string = '';
    
    await prisma.$transaction(async (tx) => {
      // Create the question
      const question = await tx.questions.create({
        data: {
          form_id: formId,
          parent_id: questionData.parent_id,
          question_text: questionData.question_text,
          help_text: questionData.help_text,
          is_mandatory: questionData.is_mandatory,
          field_type: questionData.field_type,
          placeholder: questionData.placeholder,
          min_value: questionData.min_value,
          max_value: questionData.max_value,
          step: questionData.step,
          unit: questionData.unit,
          order: questionData.order || nextOrder,

          // Scoring fields
          scoring_type: questionData.scoring_type,
          scoring_config: questionData.scoring_config ? 
            (typeof questionData.scoring_config === 'string' ? 
              questionData.scoring_config : 
              JSON.stringify(questionData.scoring_config)) : undefined,

          // Conditional logic
          depends_on_option_id: questionData.depends_on_option_id || undefined,

          options: questionData.options ? {
            create: questionData.options.map((option, index) => ({
              option_text: option.option_text,
              value: option.value,
              order: option.order || index,
              score: option.score
            }))
          } : undefined
        }
      });

      questionId = question.id;

      // Create track associations if provided (only for parent questions)
      if (questionData.track_ids && questionData.track_ids.length > 0) {
        await tx.question_tracks.createMany({
          data: questionData.track_ids.map(trackId => ({
            question_id: question.id,
            track_id: trackId
          }))
        });
      }
    });

    revalidatePath('/admin/diet-plan-questions');
    return questionId;
  } catch (error) {
    console.error("Error adding question:", error);
    throw new Error("Failed to add question");
  }
}

// Update an existing question
export async function updateQuestion(questionId: string, questionData: Partial<FormBuilderQuestion>): Promise<void> {
  try {
    await prisma.$transaction(async (tx) => {
      const updateData: any = {};

      // Update basic fields
      if (questionData.question_text !== undefined) updateData.question_text = questionData.question_text;
      if (questionData.help_text !== undefined) updateData.help_text = questionData.help_text;
      if (questionData.is_mandatory !== undefined) updateData.is_mandatory = questionData.is_mandatory;
      if (questionData.field_type !== undefined) updateData.field_type = questionData.field_type;
      if (questionData.placeholder !== undefined) updateData.placeholder = questionData.placeholder;
      if (questionData.min_value !== undefined) updateData.min_value = questionData.min_value;
      if (questionData.max_value !== undefined) updateData.max_value = questionData.max_value;
      if (questionData.step !== undefined) updateData.step = questionData.step;
      if (questionData.unit !== undefined) updateData.unit = questionData.unit;
      if (questionData.order !== undefined) updateData.order = questionData.order;
      if (questionData.scoring_type !== undefined) updateData.scoring_type = questionData.scoring_type;
      if (questionData.depends_on_option_id !== undefined) updateData.depends_on_option_id = questionData.depends_on_option_id;

      // Handle scoring config
      if (questionData.scoring_config !== undefined) {
        updateData.scoring_config = typeof questionData.scoring_config === 'string' 
          ? questionData.scoring_config 
          : JSON.stringify(questionData.scoring_config);
      }

      // Update the question
      await tx.questions.update({
        where: { id: questionId },
        data: updateData
      });

      // Update track associations if provided
      if (questionData.track_ids !== undefined) {
        // Delete existing track associations
        await tx.question_tracks.deleteMany({
          where: { question_id: questionId }
        });

        // Create new track associations
        if (questionData.track_ids.length > 0) {
          await tx.question_tracks.createMany({
            data: questionData.track_ids.map(trackId => ({
              question_id: questionId,
              track_id: trackId
            }))
          });
        }
      }

      // Update options if provided
      if (questionData.options !== undefined) {
        // Delete existing options
        await tx.options.deleteMany({
          where: { question_id: questionId }
        });

        // Create new options
        if (questionData.options.length > 0) {
          await tx.options.createMany({
            data: questionData.options.map((option, index) => ({
              question_id: questionId,
              option_text: option.option_text,
              value: option.value,
              order: option.order || index,
              score: option.score
            }))
          });
        }
      }
    });

    revalidatePath('/admin/diet-plan-questions');
  } catch (error) {
    console.error("Error updating question:", error);
    throw new Error("Failed to update question");
  }
}

// Delete a question
export async function deleteQuestion(questionId: string): Promise<void> {
  try {
    await prisma.questions.delete({
      where: { id: questionId }
    });

    revalidatePath('/admin/diet-plan-questions');
  } catch (error) {
    console.error("Error deleting question:", error);
    throw new Error("Failed to delete question");
  }
}

// Reorder questions
export async function reorderQuestions(formId: string, questionOrders: { id: string; order: number }[]): Promise<void> {
  try {
    await prisma.$transaction(
      questionOrders.map(({ id, order }) =>
        prisma.questions.update({
          where: { id },
          data: { order }
        })
      )
    );

    revalidatePath('/admin/diet-plan-questions');
  } catch (error) {
    console.error("Error reordering questions:", error);
    throw new Error("Failed to reorder questions");
  }
}

// Add a sub-question
export async function addSubQuestion(parentQuestionId: string, subQuestionData: FormBuilderQuestion): Promise<void> {
  try {
    // Get the parent question to get the form_id
    const parentQuestion = await prisma.questions.findUnique({
      where: { id: parentQuestionId },
      select: { form_id: true }
    });

    if (!parentQuestion) {
      throw new Error("Parent question not found");
    }

    // Get the current max order for sub-questions of this parent
    const maxOrderResult = await prisma.questions.aggregate({
      where: { parent_id: parentQuestionId },
      _max: { order: true }
    });

    const nextOrder = (maxOrderResult._max.order || 0) + 1;

    await prisma.questions.create({
      data: {
        form_id: parentQuestion.form_id,
        parent_id: parentQuestionId,
        question_text: subQuestionData.question_text,
        help_text: subQuestionData.help_text,
        is_mandatory: subQuestionData.is_mandatory,
        field_type: subQuestionData.field_type,
        placeholder: subQuestionData.placeholder,
        min_value: subQuestionData.min_value,
        max_value: subQuestionData.max_value,
        step: subQuestionData.step,
        unit: subQuestionData.unit,
        order: subQuestionData.order || nextOrder,
        scoring_type: subQuestionData.scoring_type,
        scoring_config: subQuestionData.scoring_config ? 
          (typeof subQuestionData.scoring_config === 'string' ? 
            subQuestionData.scoring_config : 
            JSON.stringify(subQuestionData.scoring_config)) : undefined,
        depends_on_option_id: subQuestionData.depends_on_option_id || undefined,
        options: subQuestionData.options ? {
          create: subQuestionData.options.map((option, index) => ({
            option_text: option.option_text,
            value: option.value,
            order: option.order || index,
            score: option.score
          }))
        } : undefined
      }
    });

    revalidatePath('/admin/diet-plan-questions');
  } catch (error) {
    console.error("Error adding sub-question:", error);
    throw new Error("Failed to add sub-question");
  }
} 