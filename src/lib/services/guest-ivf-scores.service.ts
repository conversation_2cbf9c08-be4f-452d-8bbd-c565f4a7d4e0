"use server";

import { PrismaClient } from "@/generated/prisma";
import { redirect } from "next/navigation";
import { calculateDynamicIVFScore, IVFScoreResult } from "@/lib/services/ivf-scoring";
import { FieldValue } from "@/types/ivf-score/ivf-score";

const prisma = new PrismaClient();

export interface GuestIvfScore {
  id: string;
  session_token: string;
  display_name?: string | null;
  email?: string | null;
  is_verified: boolean;
  created_at: Date;
  updated_at: Date;
  selected_track?: string | null;
}

export interface GetGuestIvfScoresResult {
  scores: GuestIvfScore[];
  total: number;
  error: string | null;
}

export interface FilterOptions {
  dateFrom?: string;
  dateTo?: string;
  track?: string;
  isVerified?: boolean;
}

export async function getGuestIvfScores(
  page: number = 1, 
  search?: string, 
  perPage: number = 10,
  sortBy: string = 'created_at',
  sortDirection: 'asc' | 'desc' = 'desc',
  filters?: FilterOptions
): Promise<GetGuestIvfScoresResult> {
  try {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const where: any = {};

    // Search filter
    if (search) {
      where.OR = [
        {
          email: {
            contains: search,
            mode: 'insensitive' as const
          }
        },
        {
          display_name: {
            contains: search,
            mode: 'insensitive' as const
          }
        }
      ];
    }

    // Date range filter
    if (filters?.dateFrom || filters?.dateTo) {
      where.created_at = {};
      if (filters.dateFrom) {
        where.created_at.gte = new Date(filters.dateFrom);
      }
      if (filters.dateTo) {
        where.created_at.lte = new Date(filters.dateTo + 'T23:59:59.999Z');
      }
    }

    // Track filter
    if (filters?.track) {
      where.selected_track = filters.track;
    }

    // Verification status filter
    if (filters?.isVerified !== undefined) {
      where.is_verified = filters.isVerified;
    }

    // Build orderBy clause
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const orderBy: any = {};
    orderBy[sortBy] = sortDirection;

    const scores = await prisma.guest_sessions.findMany({
      where,
      take: perPage,
      skip: (page - 1) * perPage,
      orderBy,
      select: {
        id: true,
        session_token: true,
        display_name: true,
        email: true,
        is_verified: true,
        created_at: true,
        updated_at: true,
        selected_track: true,
      }
    });

    const total = await prisma.guest_sessions.count({ where });

    return {
      scores: scores as unknown as GuestIvfScore[],
      total,
      error: null,
    };
  } catch (error) {
    console.error('Error in getGuestIvfScores:', error);
    return {
      scores: [],
      total: 0,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

export async function deleteGuestIvfScore(formData: FormData) {
  const scoreId = formData.get("scoreId") as string;
  
  try {
    await prisma.guest_sessions.delete({
      where: {
        id: scoreId
      }
    });
    
    redirect("/admin/guest-ivf-scores");
  } catch (error) {
    console.error("Error deleting guest IVF score:", error);
    throw error;
  }
}

/**
 * Shared function to fetch guest IVF scores and calculate results.
 * Returns { error } if not found or incomplete, else { scoreData, guestSession }.
 */
export async function getGuestIVFScoresAndCalculate({
  sessionToken,
  debug = false,
  minStep = 3,
}: {
  sessionToken: string;
  debug?: boolean;
  minStep?: number;
}): Promise<
  | { error: { status: number; message: string } }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  | { scoreData: IVFScoreResult; guestSession: any }
> {
  const guestSession = await prisma.guest_sessions.findUnique({
    where: { session_token: sessionToken },
  });

  if (!guestSession) {
    return { error: { status: 404, message: "Guest session not found" } };
  }
  
  if (guestSession.current_step < minStep) {
    return { error: { status: 400, message: "All steps must be completed to view results" } };
  }

  const scoreData = await calculateDynamicIVFScore(
    {
      id: guestSession.id,
      user_id: guestSession.session_token, // Using session_token as user_id for calculation
      form_data: guestSession.ivf_data as Record<string, FieldValue>,
      current_step: guestSession.current_step,
      created_at: guestSession.created_at,
      updated_at: guestSession.updated_at,
      selected_track: guestSession.selected_track ?? undefined
    },
    debug,
    guestSession.selected_track ?? undefined,
    ['biological','lifestyle','environmental']
  );

  return { scoreData, guestSession } as {
    scoreData: IVFScoreResult;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    guestSession: any;
  };
}
