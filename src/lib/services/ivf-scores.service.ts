/* eslint-disable @typescript-eslint/no-explicit-any */
"use server";

import { PrismaClient } from "@/generated/prisma";
import { redirect } from "next/navigation";
import { calculateDynamicIVFScore, IVFScoreResult } from "@/lib/services/ivf-scoring";
import { FieldValue } from "@/types/ivf-score/ivf-score";

const prisma = new PrismaClient();

export interface IvfScore {
  id: string;
  user_id: string;
  score: number | null;
  created_at: Date;
  updated_at: Date;
  selected_track?: string | null;
  user: {
    email: string | null;
    user_metadata: {
      first_name?: string;
      last_name?: string;
    }
  } | null;
}

export interface GetIvfScoresResult {
  scores: IvfScore[];
  total: number;
  error: string | null;
}

export interface FilterOptions {
  dateFrom?: string;
  dateTo?: string;
  scoreMin?: number;
  scoreMax?: number;
  track?: string;
}

export async function getIvfScores(
  page: number = 1, 
  search?: string, 
  perPage: number = 10,
  sortBy: string = 'created_at',
  sortDirection: 'asc' | 'desc' = 'desc',
  filters?: FilterOptions
): Promise<GetIvfScoresResult> {
  try {
    const where: any = {};

    // Search filter
    if (search) {
      where.user = {
        email: {
          contains: search,
          mode: 'insensitive' as const
        }
      };
    }

    // Date range filter
    if (filters?.dateFrom || filters?.dateTo) {
      where.created_at = {};
      if (filters.dateFrom) {
        where.created_at.gte = new Date(filters.dateFrom);
      }
      if (filters.dateTo) {
        where.created_at.lte = new Date(filters.dateTo + 'T23:59:59.999Z');
      }
    }

    // Score range filter
    if (filters?.scoreMin !== undefined || filters?.scoreMax !== undefined) {
      where.score = {};
      if (filters.scoreMin !== undefined) {
        where.score.gte = filters.scoreMin;
      }
      if (filters.scoreMax !== undefined) {
        where.score.lte = filters.scoreMax;
      }
    }

    // Track filter
    if (filters?.track) {
      where.selected_track = filters.track;
    }

    // Build orderBy clause
    const orderBy: any = {};
    if (sortBy === 'user.email') {
      orderBy.user = { email: sortDirection };
    } else {
      orderBy[sortBy] = sortDirection;
    }

    const scores = await prisma.ivf_scores.findMany({
      where,
      take: perPage,
      skip: (page - 1) * perPage,
      orderBy,
      select: {
        id: true,
        user_id: true,
        created_at: true,
        updated_at: true,
        selected_track: true,
        form_data: true,
        current_step: true,
        user: {
          select: {
            email: true
          }
        }
      }
    });

    const total = await prisma.ivf_scores.count({ where });

    // Calculate scores for each record
    const scoresWithCalculatedScores = await Promise.all(
      scores.map(async (score) => {
        try {
          // Only calculate score if the assessment is completed
          if (score.current_step >= 3) {
            const scoreData = await calculateDynamicIVFScore(
              {
                id: score.id,
                user_id: score.user_id,
                form_data: score.form_data as Record<string, any>,
                current_step: score.current_step,
                created_at: score.created_at,
                updated_at: score.updated_at,
                selected_track: score.selected_track ?? undefined
              },
              false, // debug = false
              score.selected_track ?? undefined,
              ['biological','lifestyle','environmental']
            );
            
            return {
              ...score,
              score: scoreData.totalScore
            };
          } else {
            return {
              ...score,
              score: null // No score calculated yet
            };
          }
        } catch (error) {
          console.error(`Error calculating score for user ${score.user_id}:`, error);
          return {
            ...score,
            score: null // Error calculating score
          };
        }
      })
    );

    return {
      scores: scoresWithCalculatedScores as unknown as IvfScore[],
      total,
      error: null,
    };
  } catch (error) {
    console.error('Error in getIvfScores:', error);
    return {
      scores: [],
      total: 0,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

export async function deleteIvfScore(formData: FormData) {
  const scoreId = formData.get("scoreId") as string;
  
  try {
    await prisma.ivf_scores.delete({
      where: {
        id: scoreId
      }
    });
    
    redirect("/admin/ivf-scores");
  } catch (error) {
    console.error("Error deleting IVF score:", error);
    throw error;
  }
}

/**
 * Determine the status of an IVF assessment based on current_step and other criteria
 */
export async function determineIVFAssessmentStatus(currentStep: number, hasError: boolean = false): Promise<'pending' | 'completed' | 'failed'> {
  if (hasError) {
    return 'failed';
  }
  
  if (currentStep >= 3) {
    return 'completed';
  }
  
  return 'pending';
}

/**
 * Shared function to fetch IVF scores and calculate results for a user.
 * Returns { error } if not found or incomplete, else { scoreData, ivfScores }.
 */
export async function getUserIVFScoresAndCalculate({
  userId,
  debug = false,
  minStep = 3,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  selectUser = false
}: {
  userId: string;
  debug?: boolean;
  minStep?: number;
  selectUser?: boolean;
}): Promise<
  | { error: { status: number; message: string } }
  | { scoreData: IVFScoreResult; ivfScores: any }
> {
  const ivfScores = await prisma.ivf_scores.findUnique({
    where: { user_id: userId },
    include: {
      user: {
        select: {
          display_name: true,
          email: true
        }
      }
    }
  });


  if (!ivfScores) {
    return { error: { status: 404, message: "IVF scores not found for this user" } };
  }
  if (ivfScores.current_step < minStep) {
    return { error: { status: 400, message: "All steps must be completed to view results" } };
  }

  const scoreData = await calculateDynamicIVFScore(
    {
      id: ivfScores.id,
      user_id: ivfScores.user_id,
      form_data: ivfScores.form_data as Record<string, FieldValue>,
      current_step: ivfScores.current_step,
      created_at: ivfScores.created_at,
      updated_at: ivfScores.updated_at,
      selected_track: ivfScores.selected_track ?? undefined
    },
    debug,
    ivfScores.selected_track ?? undefined,
    ['biological','lifestyle','environmental']
  );


  return { scoreData, ivfScores } as {
    scoreData: IVFScoreResult;
    ivfScores: any;
  };
}
