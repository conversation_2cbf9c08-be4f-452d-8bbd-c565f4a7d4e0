// src/lib/services/question-service.ts
import { PrismaClient, questions, options, question_tracks, tracks } from '@/generated/prisma';
import {
  getFieldTypeLabel,
  getFieldTypeDescription,
  getFieldTypeCapabilities,
  getCategoryFromFormName,
  getTrackDistribution,
  getFieldTypeDistribution,
} from '@/lib/utils/form-helpers';
import { TrackInfo } from '@/types/track/track-data';
import { ConsolidatedQuestionData } from '@/types/form/form-api-data';
import { ScoringConfig } from '@/types/scoring';

const prisma = new PrismaClient();

type QuestionWithRelations = questions & {
    options: options[];
    question_tracks: (question_tracks & {
        track: tracks;
    })[];
    sub_questions: QuestionWithRelations[];
};

// Helper function to recursively build nested questions
export const buildNestedQuestions = (questionList: QuestionWithRelations[]): ConsolidatedQuestionData[] => {
  return questionList
    .map(question => {
      const questionTracks: TrackInfo[] = question.question_tracks.map(qt => qt.track);
      const nestedQuestions = (question.sub_questions && question.sub_questions.length > 0)
        ? buildNestedQuestions(question.sub_questions)
        : [];

      return {
        id: question.id,
        question_text: question.question_text,
        help_text: question.help_text,
        has_parent: !!question.parent_id,
        is_mandatory: question.is_mandatory,
        order: question.order,
        field_type: {
          value: question.field_type,
          label: getFieldTypeLabel(question.field_type),
          description: getFieldTypeDescription(question.field_type),
          capabilities: getFieldTypeCapabilities(question.field_type)
        },
        config: {
          placeholder: question.placeholder,
          min_value: question.min_value,
          max_value: question.max_value,
          step: question.step,
          unit: question.unit
        },
        options: question.options.map((option: options) => ({
          id: option.id,
          text: option.option_text,
          value: option.value,
          order: option.order,
          score: option.score ?? null
        })),
        tracks: questionTracks.map((track: TrackInfo) => ({
          id: track.id,
          code: track.code,
          name: track.name,
          description: track.description
        })),
        sub_questions: nestedQuestions,
        scoring: {
            type: question.scoring_type,
            config: question.scoring_config as ScoringConfig | null
        },
        depends_on_option_id: question.depends_on_option_id || null,
        scoring_mode: question.scoring_mode,
        collective_formula: question.collective_formula,
        parent_id: question.parent_id || null
      };
    });
};

// Helper to fetch and filter questions by category and tracks
export async function getQuestions(formCategory?: string, trackFilter: string[] = []) {
    const formNameMap: Record<string, string> = {
        biological: 'Biological Factors Form',
        lifestyle: 'Lifestyle & Psychosocial Form',
        environmental: 'Environmental & Socioeconomic Factors Form',
    };

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const whereClause: any = {};
    if (formCategory && formNameMap[formCategory]) {
        whereClause.name = formNameMap[formCategory];
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const questionWhereClause: any = {};
    if (trackFilter.length > 0) {
        questionWhereClause.question_tracks = {
            some: {
                track: {
                    code: {
                        in: trackFilter,
                    },
                },
            },
        };
    }

    // Fetch forms and tracks in parallel
    const [formsData, tracksData] = await Promise.all([
        prisma.forms.findMany({
            where: whereClause,
            include: {
                questions: {
                    where: questionWhereClause,
                    include: {
                        options: { orderBy: { order: 'asc' } },
                        question_tracks: { include: { track: true } },
                        sub_questions: {
                            include: {
                                options: { orderBy: { order: 'asc' } },
                                question_tracks: { include: { track: true } },
                            },
                        },
                    },
                    orderBy: { order: 'asc' },
                },
            },
            orderBy: { created_at: 'asc' },
        }),
        prisma.tracks.findMany({ orderBy: { code: 'asc' } }),
    ]);

    // Transform and organize the data for frontend consumption
    const transformedForms = formsData.map(form => {
        const category = getCategoryFromFormName(form.name);
        const allQuestions = form.questions as unknown as QuestionWithRelations[];
        const nestedQuestions = buildNestedQuestions(allQuestions);

        return {
            id: form.id,
            name: form.name,
            description: form.description,
            category: category,
            questions: nestedQuestions,
            summary: {
                total_questions: nestedQuestions.length,
                track_distribution: getTrackDistribution(nestedQuestions),
                field_type_distribution: getFieldTypeDistribution(nestedQuestions),
            },
        };
    }).filter(Boolean);

    return { transformedForms, tracksData };
}
