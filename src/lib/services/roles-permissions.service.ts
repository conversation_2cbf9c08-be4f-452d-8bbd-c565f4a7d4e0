"use server";

import { PrismaClient } from "@/generated/prisma";
import type { RoleWithPermissions } from "@/types/roles-permissions";
import { revalidatePath } from "next/cache";
import { AVAILABLE_RESOURCES } from "@/types/roles-permissions";

const prisma = new PrismaClient();

// Server Actions for Role Management
export async function createRole(formData: FormData) {
  const name = formData.get("name") as string;
  const description = formData.get("description") as string;
  
  if (!name || name.trim() === '') {
    throw new Error("Role name is required");
  }
  
  try {
    // Check if role name already exists
    const existingRole = await prisma.roles.findFirst({
      where: { name: name.trim() },
    });
    
    if (existingRole) {
      throw new Error("A role with this name already exists");
    }
    
    // Try to create the role
    const newRole = await prisma.roles.create({
      data: {
        name: name.trim(),
        description: description?.trim() || null,
      },
    });
    
    console.log("Role created successfully:", newRole);
    revalidatePath("/admin/roles-permissions");
  } catch (error: unknown) {
    console.error("Error creating role:", error);
    
    // Handle specific Prisma errors
    if (error && typeof error === 'object' && 'code' in error && error.code === 'P2002') {
      // Unique constraint violation
      if (error && typeof error === 'object' && 'meta' in error && 
          error.meta && typeof error.meta === 'object' && 'target' in error.meta &&
          Array.isArray(error.meta.target) && error.meta.target.includes('id')) {
        // ID constraint issue - try to fix sequence
        try {
          await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('roles', 'id'), (SELECT MAX(id) FROM roles) + 1, false)`;
          console.log("Sequence fixed, please try creating the role again");
        } catch (retryError) {
          console.error("Failed to fix sequence:", retryError);
        }
      }
      throw new Error("A role with this information already exists");
    }
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    throw new Error(`Failed to create role: ${errorMessage}`);
  }
}

export async function updateRole(formData: FormData) {
  const id = formData.get("id") as string;
  const name = formData.get("name") as string;
  const description = formData.get("description") as string;
  
  try {
    await prisma.roles.update({
      where: { id: BigInt(id) },
      data: {
        name,
        description,
      },
    });
    
    revalidatePath("/admin/roles-permissions");
  } catch (error) {
    console.error("Error updating role:", error);
    throw error;
  }
}

export async function deleteRole(formData: FormData) {
  const id = formData.get("id") as string;
  
  try {
    // Delete permissions first (cascade)
    await prisma.permissions.deleteMany({
      where: { role_id: BigInt(id) },
    });
    
    // Then delete the role
    await prisma.roles.delete({
      where: { id: BigInt(id) },
    });
    
    revalidatePath("/admin/roles-permissions");
  } catch (error) {
    console.error("Error deleting role:", error);
    throw error;
  }
}

export async function fixRoleSequence() {
  try {
    await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('roles', 'id'), (SELECT COALESCE(MAX(id), 0) + 1 FROM roles), false)`;
    console.log("Role sequence fixed successfully");
    revalidatePath("/admin/roles-permissions");
  } catch (error) {
    console.error("Error fixing role sequence:", error);
    throw new Error("Failed to fix role sequence");
  }
}

export async function fixPermissionSequence() {
  try {
    await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('permissions', 'id'), (SELECT COALESCE(MAX(id), 0) + 1 FROM permissions), false)`;
    console.log("Permission sequence fixed successfully");
    revalidatePath("/admin/roles-permissions");
  } catch (error) {
    console.error("Error fixing permission sequence:", error);
    throw new Error("Failed to fix permission sequence");
  }
}

export async function fixAllSequences() {
  try {
    await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('roles', 'id'), (SELECT COALESCE(MAX(id), 0) + 1 FROM roles), false)`;
    await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('permissions', 'id'), (SELECT COALESCE(MAX(id), 0) + 1 FROM permissions), false)`;
    console.log("All sequences fixed successfully");
    revalidatePath("/admin/roles-permissions");
  } catch (error) {
    console.error("Error fixing sequences:", error);
    throw new Error("Failed to fix database sequences");
  }
}

export async function togglePermission(formData: FormData) {
  const roleId = formData.get("roleId") as string;
  const resource = formData.get("resource") as string;
  const action = formData.get("action") as string;
  const currentValue = formData.get("currentValue") === "true";
  
  try {
    // Check if permission exists
    const existingPermission = await prisma.permissions.findFirst({
      where: { 
        role_id: BigInt(roleId),
        resource: resource,
      },
    });
    
    if (existingPermission) {
      // Update existing permission
      await prisma.permissions.update({
        where: { id: existingPermission.id },
        data: {
          [`can_${action}`]: !currentValue,
        },
      });
    } else {
      // Create new permission with the toggled action
      await prisma.permissions.create({
        data: {
          role_id: BigInt(roleId),
          resource: resource,
          can_create: action === 'create' ? !currentValue : false,
          can_read: action === 'read' ? !currentValue : false,
          can_update: action === 'update' ? !currentValue : false,
          can_delete: action === 'delete' ? !currentValue : false,
        },
      });
    }
    
    revalidatePath("/admin/roles-permissions");
  } catch (error: unknown) {
    console.error("Error toggling permission:", error);
    
    // Handle specific Prisma errors
    if (error && typeof error === 'object' && 'code' in error && error.code === 'P2002') {
      // Unique constraint violation on ID - fix sequence
      if (error && typeof error === 'object' && 'meta' in error && 
          error.meta && typeof error.meta === 'object' && 'target' in error.meta &&
          Array.isArray(error.meta.target) && error.meta.target.includes('id')) {
        try {
          await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('permissions', 'id'), (SELECT COALESCE(MAX(id), 0) + 1 FROM permissions), false)`;
          console.log("Permission sequence fixed. Please try toggling the permission again.");
          throw new Error("Database sequence was out of sync and has been fixed. Please try again.");
        } catch (sequenceError) {
          console.error("Failed to fix permission sequence:", sequenceError);
          throw new Error("Database sequence error. Please contact administrator.");
        }
      }
      throw new Error("A permission with this information already exists");
    }
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    throw new Error(`Failed to toggle permission: ${errorMessage}`);
  }
}

export async function createRoleWithPermissions(formData: FormData) {
  const name = formData.get("name") as string;
  const description = formData.get("description") as string;
  
  if (!name || name.trim() === '') {
    throw new Error("Role name is required");
  }
  
  try {
    // Check if role name already exists
    const existingRole = await prisma.roles.findFirst({
      where: { name: name.trim() },
    });
    
    if (existingRole) {
      throw new Error("A role with this name already exists");
    }
    
    // Get all permission-related form data
    const permissions: Array<{
      resource: string;
      can_create: boolean;
      can_read: boolean;
      can_update: boolean;
      can_delete: boolean;
    }> = [];
    
    // Process permissions from form data
    AVAILABLE_RESOURCES.forEach(resource => {
      const canCreate = formData.get(`${resource}_create`) === 'on';
      const canRead = formData.get(`${resource}_read`) === 'on';
      const canUpdate = formData.get(`${resource}_update`) === 'on';
      const canDelete = formData.get(`${resource}_delete`) === 'on';
      
      if (canCreate || canRead || canUpdate || canDelete) {
        permissions.push({
          resource,
          can_create: canCreate,
          can_read: canRead,
          can_update: canUpdate,
          can_delete: canDelete,
        });
      }
    });
    
    // Helper function to create role and permissions
    const createRoleAndPermissions = async (tx: Omit<typeof prisma, '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'>) => {
      const newRole = await tx.roles.create({
        data: {
          name: name.trim(),
          description: description?.trim() || null,
        },
      });
      
      if (permissions.length > 0) {
        await tx.permissions.createMany({
          data: permissions.map((permission: {
            resource: string;
            can_create: boolean;
            can_read: boolean;
            can_update: boolean;
            can_delete: boolean;
          }) => ({
            role_id: newRole.id,
            resource: permission.resource,
            can_create: permission.can_create,
            can_read: permission.can_read,
            can_update: permission.can_update,
            can_delete: permission.can_delete,
          })),
        });
      }
      
      return newRole;
    };
    
    // Create role with permissions in a transaction
    const result = await prisma.$transaction(createRoleAndPermissions);
    
    console.log("Role with permissions created successfully:", result);
    revalidatePath("/admin/roles-permissions");
  } catch (error: unknown) {
    console.error("Error creating role with permissions:", error);
    
    // Handle specific Prisma errors
    if (error && typeof error === 'object' && 'code' in error && error.code === 'P2002') {
      // Unique constraint violation
      if (error && typeof error === 'object' && 'meta' in error && 
          error.meta && typeof error.meta === 'object' && 'target' in error.meta &&
          Array.isArray(error.meta.target) && error.meta.target.includes('id')) {
        // ID constraint issue - fix both sequences since this function creates both roles and permissions
        try {
          await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('roles', 'id'), (SELECT COALESCE(MAX(id), 0) + 1 FROM roles), false)`;
          await prisma.$executeRaw`SELECT setval(pg_get_serial_sequence('permissions', 'id'), (SELECT COALESCE(MAX(id), 0) + 1 FROM permissions), false)`;
          console.log("Database sequences fixed, please try creating the role again");
          throw new Error("Database sequence was out of sync and has been fixed. Please try again.");
        } catch (retryError) {
          console.error("Failed to fix sequences:", retryError);
          throw new Error("Database sequence error. Please contact administrator.");
        }
      }
      throw new Error("A role with this information already exists");
    }
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    throw new Error(`Failed to create role with permissions: ${errorMessage}`);
  }
}

// Data fetching function
export async function getRolesWithPermissions(): Promise<RoleWithPermissions[]> {
  try {
    const roles = await prisma.roles.findMany({
      include: {
        permissions: true,
      },
      orderBy: {
        created_at: 'desc',
      },
    });

    // Convert BigInt to string for serialization
    return roles.map(role => ({
      ...role,
      id: role.id,
      permissions: role.permissions.map(permission => ({
        ...permission,
        id: permission.id,
        role_id: permission.role_id,
      })),
    })) as RoleWithPermissions[];
  } catch (error) {
    console.error("Error fetching roles:", error);
    return [];
  }
}

 