import nodemailer from 'nodemailer';
import { generateVerificationEmailTemplate, generateVerificationEmailText, EmailTemplateData } from '@/lib/email-templates/verify-email-with-otp';

export interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

export interface EmailData {
  to: string;
  subject: string;
  html: string;
  text?: string;
}

export interface EmailAttachment {
  filename: string;
  content: Buffer;
  contentType: string;
}

export interface EmailDataWithAttachment extends EmailData {
  attachments?: EmailAttachment[];
}

class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.SMTP_USER || '',
        pass: process.env.SMTP_PASS || '',
      },
    });
  }

  async sendEmail(emailData: EmailData): Promise<boolean> {
    try {
      const mailOptions = {
        from: process.env.EMAIL_FROM || '<EMAIL>',
        to: emailData.to,
        subject: emailData.subject,
        html: emailData.html,
        text: emailData.text || this.htmlToText(emailData.html),
      };

      const info = await this.transporter.sendMail(mailOptions);
      
      if (process.env.NODE_ENV === 'development') {
        console.log('Email sent (development mode):', {
          messageId: info.messageId,
          previewURL: nodemailer.getTestMessageUrl(info),
        });
      }

      return true;
    } catch (error) {
      console.error('Email sending failed:', error);
      return false;
    }
  }

  async sendEmailWithAttachment(emailData: EmailDataWithAttachment): Promise<boolean> {
    try {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const mailOptions: any = {
        from: process.env.EMAIL_FROM || '<EMAIL>',
        to: emailData.to,
        subject: emailData.subject,
        html: emailData.html,
        text: emailData.text || this.htmlToText(emailData.html),
      };

      // Add attachments if provided
      if (emailData.attachments && emailData.attachments.length > 0) {
        mailOptions.attachments = emailData.attachments.map(attachment => ({
          filename: attachment.filename,
          content: attachment.content,
          contentType: attachment.contentType,
        }));
      }

      const info = await this.transporter.sendMail(mailOptions);
      
      if (process.env.NODE_ENV === 'development') {
        console.log('Email with attachment sent (development mode):', {
          messageId: info.messageId,
          previewURL: nodemailer.getTestMessageUrl(info),
        });
      }

      return true;
    } catch (error) {
      console.error('Email with attachment sending failed:', error);
      return false;
    }
  }

  async sendVerificationEmail(email: string, otp: string, displayName?: string, sessionToken?: string): Promise<boolean> {
    const subject = 'Verify Your Email - GIVF';
    
    const templateData: EmailTemplateData = {
      otp,
      displayName,
      email,
      sessionToken: sessionToken || '',
    };
    
    const html = generateVerificationEmailTemplate(templateData);
    const text = generateVerificationEmailText(templateData);
    
    return this.sendEmail({
      to: email,
      subject,
      html,
      text,
    });
  }



  private htmlToText(html: string): string {
    // Simple HTML to text conversion
    return html
      .replace(/<[^>]*>/g, '')
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .trim();
  }
}

// Export singleton instance
export const emailService = new EmailService(); 