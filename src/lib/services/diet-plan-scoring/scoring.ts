import { ScoringMode } from "@/generated/prisma";
import { ProcessedQuestion } from './types';

/**
 * Calculate score for questions with sub-questions
 * Handles collective scoring mode for BMR calculation
 */
function calculateSubQuestionScore(
  parentQuestion: ProcessedQuestion,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  formData: any,
): { rawScore?: number } {
  if (!parentQuestion.sub_questions || parentQuestion.sub_questions.length === 0) {
    return { rawScore: 0 };
  }

  let rawScore: number | undefined = undefined;

  if (parentQuestion.scoring_mode === ScoringMode.collective) {

    const subQuestionValues: { [key: string]: number } = {};
    parentQuestion.sub_questions.forEach((subQuestion, index) => {
      const userAnswer = formData[subQuestion.id];
      if (userAnswer !== undefined) {
        const numericValue = Number(userAnswer);
        if (!isNaN(numericValue)) {
          subQuestionValues[`q${index + 1}`] = numericValue;
        }
      }
    });

    if (Object.keys(subQuestionValues).length > 0 && parentQuestion.collective_formula) {
      try {
        let formula = parentQuestion.collective_formula;

        for (const key in subQuestionValues) {
          formula = formula.replace(new RegExp(key, 'g'), subQuestionValues[key].toString());
        }

        const collectiveValue = new Function(`return ${formula}`)();
        
        rawScore = collectiveValue;
      } catch (error) {
        console.error(`Error evaluating collective formula for question ${parentQuestion.id}:`, error);
        rawScore = undefined;
      }
    }
  }

  return { rawScore };
}

export { calculateSubQuestionScore };
