import { CategoryScores } from './types';

/**
 * Determine which category a form belongs to based on form name
 */
export function getCategoryFromFormName(formName: string): keyof CategoryScores {
  const lowerName = formName.toLowerCase();
  
  if (lowerName.includes('biological') || lowerName.includes('physiological')) {
    return 'biological';
  } else if (lowerName.includes('lifestyle') || lowerName.includes('psychosocial')) {
    return 'lifestyle';
  } else if (lowerName.includes('environmental') || lowerName.includes('socioeconomic')) {
    return 'environmental';
  }
  
  // Default fallback - could be improved with more specific mapping
  return 'biological';
}
