# Diet Plan Scoring Service

This service calculates BMR (Basal Metabolic Rate) scores based on user-provided form data for diet plan questions.

## Structure

- `index.ts` - Main scoring function that orchestrates the BMR calculation
- `questions.ts` - Fetches questions with scoring configurations from the database
- `scoring.ts` - Handles collective scoring mode for BMR calculation
- `types.ts` - TypeScript interfaces for the service

## Usage

```typescript
import { calculateDietPlanScore } from '@/lib/services/diet-plan-scoring';

const formData = {
  // User's form answers
};

const result = await calculateDietPlanScore(formData);
// Returns: { score: number }
```

## Features

- Fetches diet plan questions with scoring configurations
- Handles collective scoring mode for complex BMR formulas
- Processes sub-questions for group questions
- Returns calculated BMR score

## Integration

The service is used by `bmr.service.ts` to calculate BMR scores for users based on their diet plan form data.
