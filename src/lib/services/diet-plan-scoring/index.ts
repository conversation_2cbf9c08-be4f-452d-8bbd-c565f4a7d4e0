import { calculateSubQuestionScore } from './scoring';
import { getDietPlanQuestionsWithScoring } from './questions';
import { ProcessedQuestion } from './types';

/**
 * Calculates a dynamic BMR score based on user-provided form data.
 * The process involves several steps:
 * 1. Fetching questions with their scoring configurations.
 * 2. Processing each question to calculate a raw score.
 */
export async function calculateDietPlanScore(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  formData: any
) {
  const questions = await getDietPlanQuestionsWithScoring(undefined, ['diet-plan']);
  const processedQuestions = processQuestions(questions);

  for (const question of processedQuestions) {
    if (question.sub_questions && question.sub_questions.length > 0) {
      const result = calculateSubQuestionScore(question, formData);
      if (result.rawScore !== undefined) {
        return {
            score: Number(result.rawScore.toFixed(2))
        };
      }
    }
  }

  return { score: 0 };
}

function processQuestions(questions: ProcessedQuestion[]): ProcessedQuestion[] {
    return questions.map(q => ({
      ...q,
      sub_questions: q.sub_questions ? processQuestions(q.sub_questions) : [],
    }));
}
