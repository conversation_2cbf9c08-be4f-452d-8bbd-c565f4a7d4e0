import { PrismaClient } from "@/generated/prisma";
const prisma = new PrismaClient();

export const getProfilesByUserIds = async (userIds: string[]) => {
    if (userIds.length === 0) {
        return [];
    }
    
    // get profiles with prisma
    const profiles = await prisma.profiles.findMany({
        where: {
            auth_id: {
                in: userIds
            }
        }
    });

    return profiles;
  };