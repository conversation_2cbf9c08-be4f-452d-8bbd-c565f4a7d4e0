"use server";

import { getServiceRoleSupabase } from "@/utils/supabase/service-role";
import { redirect } from "next/navigation";
import { PER_PAGE } from "@/lib/utils/user-management.utils";



// Types and Interfaces
export interface User {
  id: string;
  email?: string;
  user_metadata: {
    full_name?: string;
    first_name?: string;
    last_name?: string;
  };
  app_metadata: {
    provider?: string;
    providers?: string[];
  };
  created_at: string;
  last_sign_in_at?: string;
  email_confirmed_at?: string;
  role?: string;
}

export interface UsersPageProps {
  searchParams: Promise<{
    page?: string;
    search?: string;
    per_page?: string;
    sortBy?: string;
    sortDirection?: 'asc' | 'desc';
    status?: string;
    provider?: string;
    dateFrom?: string;
    dateTo?: string;
  }>;
}

export interface GetUsersResult {
  users: User[];
  total: number;
  error: string | null;
}

export interface FilterOptions {
  status?: 'all' | 'verified' | 'pending';
  provider?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface UserStats {
  totalUsers: number;
  verifiedUsers: number;
  activeTodayUsers: number;
  error: string | null;
}

// Server Actions for User Management
export async function deleteUser(formData: FormData) {
  const userId = formData.get("userId") as string;
  
  try {
    const supabase = getServiceRoleSupabase();
    
    const { error } = await supabase.auth.admin.deleteUser(userId);
    
    if (error) {
      throw error;
    }
    
    // Redirect to refresh the page
    redirect("/admin/users");
  } catch (error) {
    console.error("Error deleting user:", error);
    // Handle error appropriately
    throw error;
  }
}

export async function updateUserName(formData: FormData) {
  const userId = formData.get("userId") as string;
  const fullName = formData.get("fullName") as string;
  
  try {
    const supabase = getServiceRoleSupabase();
    
    const { error } = await supabase.auth.admin.updateUserById(userId, {
      user_metadata: {
        full_name: fullName
      }
    });
    
    if (error) {
      throw error;
    }
    
    // Redirect to refresh the page
    redirect("/admin/users");
  } catch (error) {
    console.error("Error updating user:", error);
    // Handle error appropriately
    throw error;
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const getNestedValue = (obj: any, path: string) => {
  if (!path) return undefined;
  return path.split('.').reduce((acc, part) => acc && acc[part], obj);
};

// Data fetching function
export async function getUsers(
  page: number = 1, 
  search?: string, 
  perPage: number = PER_PAGE,
  sortBy: string = 'created_at',
  sortDirection: 'asc' | 'desc' = 'desc',
  filters?: FilterOptions
): Promise<GetUsersResult> {
  try {
    const supabase = getServiceRoleSupabase();
    
    const allUsers: User[] = [];
    let currentPage = 1;
    const BATCH_SIZE = 1000; // Max per page for listUsers

    while (true) {
      const { data, error } = await supabase.auth.admin.listUsers({
        page: currentPage,
        perPage: BATCH_SIZE,
      });

      if (error) {
        console.error('Error fetching users page:', error);
        throw error;
      }

      allUsers.push(...(data.users as User[]));

      if (data.users.length < BATCH_SIZE) {
        break; // Last page
      }

      currentPage++;
    }

    // Apply filters
    let filteredUsers = allUsers;
    
    // Search filter
    if (search) {
      const searchLower = search.toLowerCase();
      filteredUsers = filteredUsers.filter(user => 
        user.email?.toLowerCase().includes(searchLower) ||
        (user.user_metadata?.full_name || '').toLowerCase().includes(searchLower) ||
        (user.user_metadata?.first_name || '').toLowerCase().includes(searchLower) ||
        (user.user_metadata?.last_name || '').toLowerCase().includes(searchLower)
      );
    }

    // Status filter
    if (filters?.status && filters.status !== 'all') {
      filteredUsers = filteredUsers.filter(user => {
        if (filters.status === 'verified') {
          return !!user.email_confirmed_at;
        }
        if (filters.status === 'pending') {
          return !user.email_confirmed_at;
        }
        return true;
      });
    }

    // Provider filter
    if (filters?.provider && filters.provider !== 'all') {
      filteredUsers = filteredUsers.filter(user => 
        user.app_metadata?.provider === filters.provider
      );
    }

    // Date range filter
    if (filters?.dateFrom || filters?.dateTo) {
      filteredUsers = filteredUsers.filter(user => {
        const createdDate = new Date(user.created_at);
        const fromDate = filters.dateFrom ? new Date(filters.dateFrom) : null;
        const toDate = filters.dateTo ? new Date(filters.dateTo) : null;
        
        if (fromDate && createdDate < fromDate) return false;
        if (toDate && createdDate > toDate) return false;
        return true;
      });
    }

    // Sort users
    filteredUsers.sort((a, b) => {
      const aValue = getNestedValue(a, sortBy);
      const bValue = getNestedValue(b, sortBy);

      if (aValue === bValue) return 0;

      const result = (aValue ?? '') < (bValue ?? '') ? -1 : 1;

      return sortDirection === 'asc' ? result : -result;
    });

    const total = filteredUsers.length;

    // Paginate results
    const start = (page - 1) * perPage;
    const end = start + perPage;
    const users = filteredUsers.slice(start, end);

    return {
      users,
      total,
      error: null
    };
  } catch (error) {
    console.error('Error in getUsers:', error);
    return { 
      users: [], 
      total: 0, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

// Get user statistics across all users
export async function getUserStats(filters?: FilterOptions): Promise<UserStats> {
  try {
    const supabase = getServiceRoleSupabase();
    
    const allUsers: User[] = [];
    let currentPage = 1;
    const BATCH_SIZE = 1000; // Max per page for listUsers

    while (true) {
      const { data, error } = await supabase.auth.admin.listUsers({
        page: currentPage,
        perPage: BATCH_SIZE,
      });

      if (error) {
        console.error('Error fetching users page:', error);
        throw error;
      }

      allUsers.push(...(data.users as User[]));

      if (data.users.length < BATCH_SIZE) {
        break; // Last page
      }

      currentPage++;
    }

    // Apply filters
    let filteredUsers = allUsers;
    
    // Status filter
    if (filters?.status && filters.status !== 'all') {
      filteredUsers = filteredUsers.filter(user => {
        if (filters.status === 'verified') {
          return !!user.email_confirmed_at;
        }
        if (filters.status === 'pending') {
          return !user.email_confirmed_at;
        }
        return true;
      });
    }

    // Provider filter
    if (filters?.provider && filters.provider !== 'all') {
      filteredUsers = filteredUsers.filter(user => 
        user.app_metadata?.provider === filters.provider
      );
    }

    // Date range filter
    if (filters?.dateFrom || filters?.dateTo) {
      filteredUsers = filteredUsers.filter(user => {
        const createdDate = new Date(user.created_at);
        const fromDate = filters.dateFrom ? new Date(filters.dateFrom) : null;
        const toDate = filters.dateTo ? new Date(filters.dateTo) : null;
        
        if (fromDate && createdDate < fromDate) return false;
        if (toDate && createdDate > toDate) return false;
        return true;
      });
    }

    const totalUsers = filteredUsers.length;
    const verifiedUsers = filteredUsers.filter(u => u.email_confirmed_at).length;
    const activeTodayUsers = filteredUsers.filter(u => 
      u.last_sign_in_at && 
      new Date(u.last_sign_in_at).toDateString() === new Date().toDateString()
    ).length;

    return {
      totalUsers,
      verifiedUsers,
      activeTodayUsers,
      error: null
    };
  } catch (error) {
    console.error('Error in getUserStats:', error);
    return { 
      totalUsers: 0, 
      verifiedUsers: 0, 
      activeTodayUsers: 0, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

 