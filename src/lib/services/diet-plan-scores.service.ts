"use server";

import { PrismaClient } from "@/generated/prisma";
import { calculateBMRForUser } from "./bmr.service";
import { FieldValue } from "@/types/ivf-score/ivf-score";

const prisma = new PrismaClient();

export interface DietPlanScore {
  id: string;
  user_id: string;
  form_data: Record<string, FieldValue>;
  current_step: number;
  created_at: Date;
  updated_at: Date;
  user?: {
    email: string;
  };
}

export interface GetDietPlanScoresResult {
  scores: DietPlanScore[];
  total: number;
  error: string | null;
}

export interface FilterOptions {
  dateFrom?: string;
  dateTo?: string;
}

export async function getDietPlanScores(
  page: number = 1, 
  search?: string, 
  perPage: number = 10,
  sortBy: string = 'created_at',
  sortDirection: 'asc' | 'desc' = 'desc',
  filters?: FilterOptions
): Promise<GetDietPlanScoresResult> {
  try {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const where: any = {};

    // Search filter
    if (search) {
      where.user = {
        email: {
          contains: search,
          mode: 'insensitive' as const
        }
      };
    }

    // Date range filter
    if (filters?.dateFrom || filters?.dateTo) {
      where.created_at = {};
      if (filters.dateFrom) {
        where.created_at.gte = new Date(filters.dateFrom);
      }
      if (filters.dateTo) {
        where.created_at.lte = new Date(filters.dateTo + 'T23:59:59.999Z');
      }
    }

    // Build orderBy clause
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const orderBy: any = {};
    if (sortBy === 'user.email') {
      orderBy.user = { email: sortDirection };
    } else {
      orderBy[sortBy] = sortDirection;
    }

    const scores = await prisma.diet_plan_scores.findMany({
      where,
      take: perPage,
      skip: (page - 1) * perPage,
      orderBy,
      include: {
        user: {
          select: {
            email: true
          }
        }
      }
    });

    const total = await prisma.diet_plan_scores.count({ where });

    return {
      scores: scores as unknown as DietPlanScore[],
      total,
      error: null,
    };
  } catch (error) {
    console.error('Error in getDietPlanScores:', error);
    return {
      scores: [],
      total: 0,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

export async function deleteDietPlanScore(formData: FormData) {
  const scoreId = formData.get("scoreId") as string;
  
  try {
    await prisma.diet_plan_scores.delete({
      where: { id: scoreId }
    });
  } catch (error) {
    console.error('Error deleting diet plan score:', error);
    throw new Error('Failed to delete diet plan score');
  }
}

export async function determineDietPlanStatus(currentStep: number, hasError: boolean = false): Promise<'pending' | 'completed' | 'failed'> {
  if (hasError) {
    return 'failed';
  }
  
  if (currentStep >= 1) {
    return 'completed';
  }
  
  return 'pending';
}

/**
 * Shared function to fetch diet plan scores and calculate BMR for a user.
 * Returns { error } if not found or incomplete, else { scoreData, dietPlanScore }.
 */
export async function getUserDietPlanScoreAndCalculate({
  userId,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  debug = false,
  minStep = 1,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  selectUser = false
}: {
  userId: string;
  debug?: boolean;
  minStep?: number;
  selectUser?: boolean;
}): Promise<
  | { error: { status: number; message: string } }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  | { scoreData: { score: number }; dietPlanScore: any }
> {
  const dietPlanScore = await prisma.diet_plan_scores.findUnique({
    where: { user_id: userId },
    include: {
      user: {
        select: {
          display_name: true,
          email: true
        }
      }
    }
  });

  if (!dietPlanScore) {
    return { error: { status: 404, message: "Diet plan score not found for this user" } };
  }
  
  if (dietPlanScore.current_step < minStep) {
    return { error: { status: 400, message: "All steps must be completed to view results" } };
  }

  const scoreData = await calculateBMRForUser(userId);

  return { scoreData, dietPlanScore } as {
    scoreData: { score: number };
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    dietPlanScore: any;
  };
}
