export interface EmailTemplateData {
  otp: string;
  displayName?: string;
  email: string;
  sessionToken: string;
}

export function generateVerificationEmailTemplate(
  data: EmailTemplateData
): string {
  const { otp, displayName } = data;
  const name = displayName || "there";

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Verify Your Email - GIVF</title>
      <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          background-color: #f8f9fa;
        }
        
        .container {
          max-width: 600px;
          margin: 0 auto;
          background-color: #ffffff;
          border-radius: 12px;
          overflow: hidden;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          padding: 40px 30px;
          text-align: center;
        }
        
        .header h1 {
          font-size: 32px;
          font-weight: 700;
          margin-bottom: 8px;
        }
        
        .header p {
          font-size: 16px;
          opacity: 0.9;
        }
        
        .content {
          padding: 40px 30px;
        }
        
        .greeting {
          font-size: 24px;
          font-weight: 600;
          color: #2d3748;
          margin-bottom: 20px;
        }
        
        .message {
          font-size: 16px;
          color: #4a5568;
          margin-bottom: 30px;
          line-height: 1.7;
        }
        
        .otp-container {
          background-color: #f7fafc;
          border: 2px solid #e2e8f0;
          border-radius: 12px;
          padding: 30px;
          text-align: center;
          margin: 30px 0;
        }
        
        .otp-label {
          font-size: 14px;
          color: #718096;
          text-transform: uppercase;
          letter-spacing: 1px;
          margin-bottom: 15px;
        }
        
        .otp-code {
          font-size: 48px;
          font-weight: 700;
          color: #2b6cb0;
          letter-spacing: 8px;
          font-family: 'Courier New', monospace;
        }
        
        .expiry-notice {
          font-size: 14px;
          color: #e53e3e;
          margin-top: 20px;
          font-weight: 500;
        }
        
        .security-notice {
          background-color: #fff5f5;
          border: 1px solid #fed7d7;
          border-radius: 8px;
          padding: 20px;
          margin: 30px 0;
        }
        
        .security-notice h3 {
          color: #c53030;
          font-size: 16px;
          margin-bottom: 10px;
        }
        
        .security-notice p {
          color: #742a2a;
          font-size: 14px;
          line-height: 1.5;
        }
        
        .footer {
          background-color: #f7fafc;
          padding: 30px;
          text-align: center;
          border-top: 1px solid #e2e8f0;
        }
        
        .footer p {
          color: #718096;
          font-size: 14px;
          margin-bottom: 10px;
        }
        
        .footer .company {
          color: #4a5568;
          font-weight: 600;
        }
        
        @media (max-width: 600px) {
          .container {
            margin: 20px;
            border-radius: 8px;
          }
          
          .header {
            padding: 30px 20px;
          }
          
          .header h1 {
            font-size: 28px;
          }
          
          .content {
            padding: 30px 20px;
          }
          
          .otp-code {
            font-size: 36px;
            letter-spacing: 4px;
          }
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <img width="140" style="margin-bottom: 10px;" src="${process.env.NEXT_PUBLIC_APP_URL}/assets/givfLogo.svg" alt="GIVF Logo" class="logo">
          <p>Email Verification</p>
        </div>
        
        <div class="content">
          <div class="greeting">Hello ${name}!</div>
          
          <div class="message">
            Thank you for signing up with GIVF. To complete your registration and access your personalized IVF insights, please use the verification code below:
          </div>
          
          <div class="otp-container">
            <div class="otp-label">Verification Code</div>
            <div class="otp-code">${otp}</div>
            <div class="expiry-notice">⏰ This code will expire in 5 minutes</div>
          </div>
          
          <div class="security-notice">
            <h3>🔒 Security Notice</h3>
            <p>
              For your security, this verification code will expire in 5 minutes. 
              If you didn't request this verification, please ignore this email and contact our support team immediately.
            </p>
          </div>
          
          <div class="message">
            Once verified, you'll be able to access your personalized IVF score results and recommendations.
          </div>
        </div>
        
        <div class="footer">
          <p>This is an automated email. Please do not reply to this message.</p>
          <p class="company">© 2025 GIVF. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;
}

export function generateVerificationEmailText(data: EmailTemplateData): string {
  const { otp, displayName } = data;
  const name = displayName || "there";

  return `
Hello ${name}!

Thank you for signing up with GIVF. To complete your registration and access your personalized IVF insights, please use the verification code below:

Verification Code: ${otp}

This code will expire in 5 minutes for security reasons.

For your security, this verification code will expire in 5 minutes. If you didn't request this verification, please ignore this email and contact our support team immediately.

Once verified, you'll be able to access your personalized IVF score results and recommendations.

Best regards,
The GIVF Team

---
This is an automated email. Please do not reply to this message.
© 2025 GIVF. All rights reserved.
  `.trim();
}
