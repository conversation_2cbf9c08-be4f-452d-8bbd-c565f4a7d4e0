import Link from "next/link";

export default function AuthCodeErrorPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Authentication Error
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Sorry, we couldn't authenticate you. This could be due to an expired
            link or an invalid authentication code.
          </p>
        </div>

        <div className="mt-8 space-y-6">
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  What can you do?
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  <ul className="list-disc pl-5 space-y-1">
                    <li>Try signing in again with your email and password</li>
                    <li>
                      Request a new confirmation email if you're signing up
                    </li>
                    <li>Check that you're using the latest email link</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <div className="flex space-x-4">
            <Link
              href="/login"
              className="flex-1 bg-primary-600 hover:bg-primary-700 text-white py-2 px-4 rounded-md text-sm font-medium text-center"
            >
              Go to Sign In
            </Link>
            <Link
              href="/signup"
              className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-md text-sm font-medium text-center"
            >
              Go to Sign Up
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
