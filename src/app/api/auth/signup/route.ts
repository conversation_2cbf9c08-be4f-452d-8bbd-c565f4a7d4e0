import { createClient } from "@/utils/supabase/server";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { PrismaClient } from "@/generated/prisma";

const signupSchema = z.object({
  fullName: z.string().min(1, "Full name is required"),
  email: z.string().email("Invalid email format").min(1, "Email is required"),
  phoneNumber: z.string().optional(),
  age: z.string().min(1, "Age is required"),
  sex: z.enum(["female", "male"]),
  dateOfBirth: z.string().optional(),
  password: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .max(100, "Password is too long"),
  acceptsTerms: z.boolean().refine((val) => val === true, {
    message: "You must accept the Terms and Privacy Policy",
  }),
});

export async function POST(request: NextRequest) {
  const prisma = new PrismaClient();

  try {
    const body = await request.json();
    const result = signupSchema.safeParse(body);

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => issue.message)
        .join(", ");
      return NextResponse.json({ error: errorMessage }, { status: 400 });
    }

    const {
      fullName,
      email,
      phoneNumber,
      age,
      sex,
      dateOfBirth,
      password,
    } = result.data;
    // check user email exists in profiles table or not
    const existingUser = await prisma.profiles.findFirst({
      where: { email: email },
    });
    if (existingUser) {
      return NextResponse.json(
        { error: "User is already exists, please login." }, // Custom error message
        { status: 409 }, // Use a status code that indicates a conflict
      );
    }


    // Create user in Supabase Auth
    const supabase = await createClient();

    // Split fullName into first and last name for Supabase metadata
    const nameParts = fullName.trim().split(" ");
    const firstName = nameParts[0] || "";
    const lastName = nameParts.slice(1).join(" ") || "";

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          first_name: firstName,
          last_name: lastName,
          phone_number: phoneNumber || null,
          age: age || null,
          sex: sex || null,
          date_of_birth: dateOfBirth || null,
        },
      },
    });

    if (error) {
      console.error("Signup error:", error);
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    // Save user data to profiles table using Prisma
    try {
      if (data.user) {
        await prisma.profiles.create({
          data: {
            auth_id: data.user.id,
            email: email,
            display_name: fullName,
            phone: phoneNumber || null,
            sex: sex,
            age: parseInt(age),
            date_of_birth: dateOfBirth ? new Date(dateOfBirth) : null
          },
        });
      }
    } catch (profileError) {
      console.error("Error saving user profile:", profileError);
      // Optionally, you could delete the Supabase user if profile creation fails
      return NextResponse.json({ error: "Failed to save user profile" }, { status: 500 });
    }

    return NextResponse.json({
      message: "Check your email to confirm your account",
      user: data.user,
    });
  } catch (error) {
    console.error("Signup API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
