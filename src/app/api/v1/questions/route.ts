// app/api/v1/questions/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient, questions, options, question_tracks, tracks } from '@/generated/prisma';
import {
  getCategoryFromFormName,
  getAllFieldTypesWithCapabilities,
  getTrackDistribution,
  getFieldTypeDistribution,
  getGlobalTrackDistribution,
  getGlobalFieldTypeDistribution
} from '@/lib/utils/form-helpers';
import { buildNestedQuestions } from '@/lib/services/question-service';

const prisma = new PrismaClient();

type QuestionWithRelations = questions & {
    options: options[];
    question_tracks: (question_tracks & {
        track: tracks;
    })[];
    sub_questions: QuestionWithRelations[];
};

// GET /api/v1/questions - Get all fertility questions with filtering capabilities
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Query parameters for filtering
    const trackFilter = searchParams.get('tracks')?.split(',').filter(Boolean) || [];
    const formCategory = searchParams.get('category'); // biological, lifestyle, environmental

    // Fetch all data in parallel for better performance
    const [formsData, tracksData] = await Promise.all([
      // Get forms with all related data
      prisma.forms.findMany({
        include: {
          questions: {
            include: {
              options: {
                orderBy: { order: 'asc' }
              },
              question_tracks: {
                include: {
                  track: true
                }
              },
              // Include sub-questions recursively
              sub_questions: {
                include: {
                  options: { orderBy: { order: 'asc' } },
                  question_tracks: { include: { track: true } }
                }
              }
            },
            orderBy: { order: 'asc' }
          }
        },
        orderBy: { created_at: 'asc' }
      }),
      
      // Get all tracks for reference
      prisma.tracks.findMany({
        orderBy: { code: 'asc' }
      })
    ]);

    // Transform and organize the data for frontend consumption
    const transformedForms = formsData.map(form => {
      const category = getCategoryFromFormName(form.name);
      
      // Filter form if category filter is applied
      if (formCategory && category !== formCategory) {
        return null;
      }

      // Process questions using the recursive helper
      const allQuestions = form.questions as unknown as QuestionWithRelations[];
      const nestedQuestions = buildNestedQuestions(allQuestions);

      // Apply track filter at the top level
      const filteredQuestions = nestedQuestions.filter(question => {
        if (trackFilter.length === 0) return true;
        const questionTracks = question.tracks.map((t) => t.id).concat(question.tracks.map((t) => t.code));
        return trackFilter.some(tf => questionTracks.includes(tf));
      });

      return {
        id: form.id,
        name: form.name,
        description: form.description,
        category: category,
        questions: filteredQuestions,
        
        // Summary for this form
        summary: {
          total_questions: filteredQuestions.length,
          track_distribution: getTrackDistribution(filteredQuestions),
          field_type_distribution: getFieldTypeDistribution(filteredQuestions)
        }
      };
    }).filter(Boolean); // Remove null forms (filtered out)

    // Create comprehensive response with all needed metadata
    const response = {
      success: true,
      data: {
        forms: transformedForms,
        
        // Global metadata for the frontend
        metadata: {
          // All available tracks
          tracks: tracksData.map(track => ({
            id: track.id,
            code: track.code,
            name: track.name,
            description: track.description
          })),
          
          // All available field types with capabilities
          field_types: getAllFieldTypesWithCapabilities(),
          
          // Form categories
          categories: [
            {
              key: 'biological',
              name: 'Biological Factors',
              description: 'Questions about biological and medical factors affecting fertility',
              icon: 'heart'
            },
            {
              key: 'lifestyle',
              name: 'Lifestyle Factors', 
              description: 'Questions about lifestyle choices and habits',
              icon: 'brain'
            },
            {
              key: 'environmental',
              name: 'Environmental Factors',
              description: 'Questions about environmental factors and living conditions',
              icon: 'globe'
            }
          ]
        },
        
        // Overall summary
        summary: {
          total_forms: transformedForms.length,
          total_questions: transformedForms.reduce((acc, form) => acc + (form?.questions.length || 0), 0),
          forms_by_category: transformedForms.reduce((acc, form) => {
            if (form) {
              acc[form.category] = (acc[form.category] || 0) + 1;
            }
            return acc;
          }, {} as Record<string, number>),
          questions_by_track: getGlobalTrackDistribution(transformedForms),
          questions_by_field_type: getGlobalFieldTypeDistribution(transformedForms)
        }
      },
      
      // Applied filters
      filters: {
        tracks: trackFilter,
        category: formCategory
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error fetching fertility questions:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch fertility questions',
        details: process.env.NODE_ENV === 'development' ? (error instanceof Error ? error.message : String(error)) : undefined
      },
      { status: 500 }
    );
  }
}