import { NextRequest } from "next/server";
import { z } from "zod";
import { apiResponse } from "@/utils/api/apiResponse";
import { getGuestSession, verifyGuestEmail } from "@/utils/api/guestSession";
import { otpService } from "@/lib/services/otp.service";

const verifyOtpSchema = z.object({
  session_token: z.string().min(1, "Session token is required"),
  email: z.string().email("Invalid email format").min(1, "Email is required"),
  code: z.string().length(6, "OTP must be 6 digits").regex(/^\d{6}$/, "OTP must contain only numbers"),
});

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const result = verifyOtpSchema.safeParse(body);

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ");
      return apiResponse(400, errorMessage);
    }

    const { session_token, email, code } = result.data;

    // Verify guest session exists
    const guestSession = await getGuestSession(session_token);
    
    if (!guestSession) {
      return apiResponse(404, "Guest session not found.");
    }

    if (guestSession.email !== email) {
      return apiResponse(400, "Email does not match the session.");
    }

    // Verify OTP
    const verificationResult = await otpService.verifyOTP(session_token, email, code);
    
    if (!verificationResult.success) {
      let statusCode = 400;
      
      if (verificationResult.error?.includes("No OTP found")) {
        statusCode = 404;
      } else if (verificationResult.error?.includes("expired")) {
        statusCode = 410;
      } else if (verificationResult.error?.includes("Maximum attempts")) {
        statusCode = 429;
      }

      return apiResponse(statusCode, verificationResult.error || "OTP verification failed", {
        ...(verificationResult.remainingAttempts && {
          remainingAttempts: verificationResult.remainingAttempts
        })
      });
    }

    // Mark guest session as verified
    const verificationSuccess = await verifyGuestEmail(session_token, email);
    
    if (!verificationSuccess) {
      return apiResponse(500, "Failed to mark session as verified");
    }

    return apiResponse(200, "Email verified successfully", {
      message: "Email verification completed successfully",
      verified: true,
      session_token,
      email
    });
  } catch (error) {
    console.error("OTP verification error:", error);
    return apiResponse(500, "Internal server error");
  }
}
