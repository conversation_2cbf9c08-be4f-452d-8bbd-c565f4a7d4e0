// signup with custom api

import { NextRequest } from "next/server";
import { z } from "zod";
import { apiResponse } from "@/utils/api/apiResponse";
import { getGuestSession, saveGuestSession } from "@/utils/api/guestSession";
import { emailService } from "@/lib/services/email.service";
import { otpService } from "@/lib/services/otp.service";

const signupSchema = z.object({
  session_token: z.string().min(1, "Session token is required"),
  email: z.string().email("Invalid email format").min(1, "Email is required"),
  display_name: z.string().optional(), // Optional for resending OTP
  phone: z.string().optional(),
  selected_track: z.enum(["1", "2", "3"]).optional(), // Optional for resending OTP
});

export async function POST(req: NextRequest) {
  // Initialize Prisma client
  const { PrismaClient } = await import("@/generated/prisma");
  const prisma = new PrismaClient();
  
  try {
    const body = await req.json();
    const result = signupSchema.safeParse(body);

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ");
      return apiResponse(400, errorMessage);
    }

    const { session_token, email, display_name, phone, selected_track } = result.data;

    // Check if email already exists in profiles table
    try {
      const existingProfile = await prisma.profiles.findFirst({
        where: { email: email.toLowerCase() }
      });
      
      if (existingProfile) {
        return apiResponse(409, "Email already exists", {
          message: "An account with this email already exists. Please use a different email or try logging in.",
          email: email
        });
      }
    } catch (error) {
      console.error("Error checking existing email:", error);
      return apiResponse(500, "Failed to check email availability");
    }

    // Verify guest session exists
    const guestSession = await getGuestSession(session_token);
    
    if (!guestSession) {
      return apiResponse(404, "Guest session not found. Please complete the IVF scoring steps first.");
    }

    // Update guest session with email
    await saveGuestSession(
      session_token,
      guestSession.ivf_data,
      guestSession.current_step,
      email
    );

    // Update additional fields in the database (only if provided)
    try {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const updateData: any = {};
      
      // Only update fields if they're provided
      if (display_name) {
        updateData.display_name = display_name;
      }
      if (phone !== undefined) {
        updateData.phone = phone || null;
      }
      if (selected_track) {
        updateData.selected_track = selected_track;
      }
      
      // Only update if there are fields to update
      if (Object.keys(updateData).length > 0) {
        await prisma.guest_sessions.update({
          where: { session_token },
          data: updateData,
        });
      }
    } catch (error) {
      console.error("Error updating guest session:", error);
      return apiResponse(500, "Failed to update guest session");
    }

    // Generate OTP
    const otp = otpService.generateOTP();
    
    // Store OTP
    await otpService.storeOTP(session_token, email, otp);

    // Send verification email
    const emailSent = await emailService.sendVerificationEmail(email, otp, display_name || guestSession.display_name, session_token);
    
    if (!emailSent) {
      // Clean up OTP if email failed
      await otpService.deleteOTP(session_token, email);
      return apiResponse(500, "Failed to send verification email. Please try again.");
    }

    return apiResponse(200, "Guest user info updated and verification email sent", {
      message: "Please check your email for the verification code",
      session_token,
      email,
      // In development mode, include OTP for testing
      ...(process.env.NODE_ENV === 'development' && {
        developmentCode: otp,
        expiresIn: "10 minutes"
      })
    });
  } catch (error) {
    console.error("Guest signup error:", error);
    return apiResponse(500, "Internal server error");
  } finally {
    await prisma.$disconnect();
  }
}