/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextRequest } from "next/server";
import { authenticate } from "@/utils/api/authenticate";
import { apiResponse } from "@/utils/api/apiResponse";
import { determineIVFAssessmentStatus } from '@/lib/services/ivf-scores.service';
import { PrismaClient } from '@/generated/prisma';

const prisma = new PrismaClient();

/**
 * GET /api/v1/ivf-assessment/check-status
 * Check current user's IVF form submission status
 */
export async function GET(req: NextRequest) {
  const startTime = Date.now();
  
  try {
    // This endpoint requires authentication
    const { user, error } = await authenticate(req);
    if (error) return error;

    // Get the user's IVF scores record
    const ivfScores = await prisma.ivf_scores.findUnique({
      where: { user_id: user.id },
      select: {
        current_step: true,
        status: true,
        created_at: true,
        updated_at: true
      }
    });

    if (!ivfScores) {
      return apiResponse(404, "No IVF assessment found for this user");
    }

    // Determine status based on current_step
    const calculatedStatus = await determineIVFAssessmentStatus(ivfScores.current_step);
    
    const processingTime = Date.now() - startTime;
    console.log(`Status checked for user ${user.email}, response time: ${processingTime}ms`);
    
    const response = {
      status: calculatedStatus,
      current_step: ivfScores.current_step,
      db_status: ivfScores.status,
      created_at: ivfScores.created_at,
      updated_at: ivfScores.updated_at,
      processingTime
    };

    return apiResponse(200, undefined, response);
  } catch (error) {
    console.error("GET check-status error:", error);
    const processingTime = Date.now() - startTime;
    console.log(`Error processing request, response time: ${processingTime}ms`);
    return apiResponse(500, "Internal server error");
  }
}