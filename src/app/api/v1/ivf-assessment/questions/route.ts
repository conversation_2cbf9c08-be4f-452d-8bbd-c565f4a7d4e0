// app/api/v1/ivf-assessment/questions/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient, questions, options, question_tracks, tracks } from '@/generated/prisma';
import {
  getCategoryFromFormName,
  getAllFieldTypesWithCapabilities,
  getTrackDistribution,
  getFieldTypeDistribution,
  getGlobalTrackDistribution,
  getGlobalFieldTypeDistribution
} from '@/lib/utils/form-helpers';
import { buildNestedQuestions } from '@/lib/services/question-service';

const prisma = new PrismaClient();

type QuestionWithRelations = questions & {
    options: options[];
    question_tracks: (question_tracks & {
        track: tracks;
    })[];
    sub_questions: QuestionWithRelations[];
};

// GET /api/v1/ivf-assessment/questions - Get IVF questions with filtering capabilities
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Query parameters for filtering
    const trackFilter = searchParams.get('tracks')?.split(',').filter(Boolean) || [];
    const categoryFilter = searchParams.get('category')?.split(',').filter(Boolean) || [];

    // Build where clause for forms based on category
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const formsWhereClause: any = {};
    if (categoryFilter.length > 0) {
      // Filter by slug directly
      formsWhereClause.slug = { in: categoryFilter };
    }

    // Build where clause for questions based on track filter
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const questionsWhereClause: any = {};
    if (trackFilter.length > 0) {
      questionsWhereClause.question_tracks = {
        some: {
          track: {
            code: { in: trackFilter }
          }
        }
      };
    }

    // Fetch only specific forms with their questions
    const [formsData, tracksData] = await Promise.all([
      // Get specific forms with all related data
      prisma.forms.findMany({
        where: formsWhereClause,
        include: {
          questions: {
            where: questionsWhereClause,
            include: {
              options: {
                orderBy: { order: 'asc' }
              },
              question_tracks: {
                include: {
                  track: true
                }
              },
              // Include sub-questions recursively
              sub_questions: {
                include: {
                  options: { orderBy: { order: 'asc' } },
                  question_tracks: { include: { track: true } }
                }
              }
            },
            orderBy: { order: 'asc' }
          }
        },
        orderBy: { created_at: 'asc' }
      }),
      
      // Get all tracks for reference
      prisma.tracks.findMany({
        orderBy: { code: 'asc' }
      })
    ]);

    // Transform and organize the data for frontend consumption
    const transformedForms = formsData.map(form => {
      const category = getCategoryFromFormName(form.name);
      
      // Filter form if category filter is applied
      if (categoryFilter.length > 0 && !categoryFilter.includes(category)) {
        return null;
      }

      // Process questions using the recursive helper
      const allQuestions = form.questions as unknown as QuestionWithRelations[];
      const nestedQuestions = buildNestedQuestions(allQuestions);
      return {
        id: form.id,
        name: form.name,
        description: form.description,
        slug: category,
        questions: nestedQuestions,
        
        // Summary for this form
        summary: {
          total_questions: nestedQuestions.length,
          track_distribution: getTrackDistribution(nestedQuestions),
          field_type_distribution: getFieldTypeDistribution(nestedQuestions)
        }
      };
    }).filter(Boolean); // Remove null forms (filtered out)

    // Create comprehensive response with all needed metadata
    const response = {
      success: true,
      data: {
        forms: transformedForms,
        
        // Global metadata for the frontend
        metadata: {
          // All available tracks
          tracks: tracksData.map(track => ({
            id: track.id,
            code: track.code,
            name: track.name,
            description: track.description
          })),
          
          // All available field types with capabilities
          field_types: getAllFieldTypesWithCapabilities(),
          
          // Form categories
          categories: [
            {
              key: 'biological',
              name: 'Biological Factors',
              description: 'Questions about biological and medical factors affecting fertility',
              icon: 'heart'
            },
            {
              key: 'lifestyle',
              name: 'Lifestyle Factors', 
              description: 'Questions about lifestyle choices and habits',
              icon: 'brain'
            },
            {
              key: 'environmental',
              name: 'Environmental Factors',
              description: 'Questions about environmental factors and living conditions',
              icon: 'globe'
            }
          ]
        },
        
        // Overall summary
        summary: {
          total_forms: transformedForms.length,
          total_questions: transformedForms.reduce((acc, form) => acc + (form?.questions.length || 0), 0),
          forms_by_category: transformedForms.reduce((acc, form) => {
            if (form) {
              acc[form.slug] = (acc[form.slug] || 0) + 1;
            }
            return acc;
          }, {} as Record<string, number>),
          questions_by_track: getGlobalTrackDistribution(transformedForms),
          questions_by_field_type: getGlobalFieldTypeDistribution(transformedForms)
        }
      },
      
      // Applied filters
      filters: {
        tracks: trackFilter,
        category: categoryFilter
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error fetching fertility questions:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch fertility questions',
        details: process.env.NODE_ENV === 'development' ? (error instanceof Error ? error.message : String(error)) : undefined
      },
      { status: 500 }
    );
  }
}