import { NextRequest } from "next/server";
import { apiResponse } from "@/utils/api/apiResponse";
import { getGuestSessionToken, getGuestSession, saveGuestSession, verifyGuestEmail } from "@/utils/api/guestSession";
import { z } from "zod";

const emailVerificationSchema = z.object({
  email: z.string().email("Invalid email format").min(1, "Email is required"),
  guestSessionToken: z.string().optional(),
});

const verifyEmailCodeSchema = z.object({
  email: z.string().email("Invalid email format").min(1, "Email is required"),
  code: z.string().length(6, "Verification code must be 6 digits"),
  guestSessionToken: z.string().optional(),
});

/**
 * POST /api/v1/ivf-assessment/verify-email
 * Send verification email to guest user after completing Step 3
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const result = emailVerificationSchema.safeParse(body);

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ");
      return apiResponse(400, errorMessage);
    }

    const { email, guestSessionToken: providedToken } = result.data;
    
    // Get guest session token from request or header
    const sessionToken = providedToken || getGuestSessionToken(req);
    
    // Verify guest session exists and has completed all steps
    const guestSession = await getGuestSession(sessionToken);
    
    if (!guestSession) {
      return apiResponse(404, "Guest session not found. Please complete the IVF scoring steps first.");
    }

    if (guestSession.current_step < 3) {
      return apiResponse(400, "All three steps must be completed before email verification.");
    }

    // Save email to guest session
    await saveGuestSession(
      sessionToken,
      guestSession.ivf_data,
      guestSession.current_step,
      email
    );

    // Generate verification code (in production, use a proper service)
    const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
    
    // Store verification code temporarily (in production, use Redis or database)
    verificationCodes.set(`${sessionToken}:${email}`, {
      code: verificationCode,
      expires: new Date(Date.now() + 10 * 60 * 1000), // 10 minutes
    });

    // In production, send actual email here
    console.log(`Verification code for ${email}: ${verificationCode}`);
    
    // For development, return the code in response (remove in production)
    return apiResponse(200, "Verification email sent successfully", {
      message: "Please check your email for the verification code",
      // Remove this in production:
      developmentCode: verificationCode,
      expiresIn: "10 minutes"
    });
  } catch (error) {
    console.error("Email verification error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * PUT /api/v1/ivf-assessment/verify-email
 * Verify email code and mark guest session as verified
 */
export async function PUT(req: NextRequest) {
  try {
    const body = await req.json();
    const result = verifyEmailCodeSchema.safeParse(body);

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ");
      return apiResponse(400, errorMessage);
    }

    const { email, code, guestSessionToken: providedToken } = result.data;
    
    // Get guest session token from request or header
    const sessionToken = providedToken || getGuestSessionToken(req);
    
    // Verify guest session exists
    const guestSession = await getGuestSession(sessionToken);
    
    if (!guestSession) {
      return apiResponse(404, "Guest session not found.");
    }

    if (guestSession.email !== email) {
      return apiResponse(400, "Email does not match the session.");
    }

    // Check verification code
    const storedVerification = verificationCodes.get(`${sessionToken}:${email}`);
    
    if (!storedVerification) {
      return apiResponse(404, "Verification code not found or expired. Please request a new code.");
    }

    if (storedVerification.expires < new Date()) {
      verificationCodes.delete(`${sessionToken}:${email}`);
      return apiResponse(410, "Verification code has expired. Please request a new code.");
    }

    if (storedVerification.code !== code) {
      return apiResponse(400, "Invalid verification code.");
    }

    // Mark session as verified (use correct function)
    await verifyGuestEmail(sessionToken, email);

    // Clean up verification code
    verificationCodes.delete(`${sessionToken}:${email}`);

    return apiResponse(200, "Email verified successfully", {
      message: "Email verification completed. You can now view your IVF score results.",
      verified: true,
      canViewResults: true
    });
  } catch (error) {
    console.error("Email verification code error:", error);
    return apiResponse(500, "Internal server error");
  }
}

// Simple in-memory store for verification codes (use Redis in production)
const verificationCodes = new Map<string, { code: string; expires: Date }>();

// Clean up expired codes every 5 minutes
setInterval(() => {
  const now = new Date();
  for (const [key, verification] of verificationCodes.entries()) {
    if (verification.expires <= now) {
      verificationCodes.delete(key);
    }
  }
}, 5 * 60 * 1000); // 5 minutes
