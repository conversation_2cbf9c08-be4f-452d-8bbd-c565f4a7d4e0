import { NextRequest } from "next/server";
import { authenticate } from "@/utils/api/authenticate";
import { apiResponse } from "@/utils/api/apiResponse";
import { PrismaClient } from "@/generated/prisma";

const prisma = new PrismaClient();

/**
 * GET /api/v1/ivf-assessment/check-guest-session
 * Check if user has a valid guest session that can be converted
 */
export async function GET(req: NextRequest) {
  try {
    // This endpoint requires authentication
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { user, error } = await authenticate(req);
    if (error) return error;

    const { searchParams } = new URL(req.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return apiResponse(400, "User ID is required");
    }

    // Check if user profile exists
    const userProfile = await prisma.profiles.findUnique({
      where: { auth_id: userId },
    });

    if (!userProfile) {
      return apiResponse(400, "User profile not found");
    }

    // Check if user has email (required for guest session conversion)
    if (!userProfile.email) {
      return apiResponse(400, "User email not found in profile");
    }

    // Find guest session with user's email
    const guestSession = await prisma.guest_sessions.findFirst({
      where: {
        email: userProfile.email,
        is_verified: true,
        expires_at: {
          gt: new Date(), // Not expired
        },
        current_step: 3, // Assessment completed
      },
      orderBy: {
        created_at: 'desc', // Get the most recent session
      },
    });

    if (!guestSession) {
      return apiResponse(200, undefined, {
        hasGuestSession: false,
      });
    }

    // Check if guest session has IVF data
    if (!guestSession.ivf_data) {
      return apiResponse(200, undefined, {
        hasGuestSession: false,
      });
    }

    return apiResponse(200, undefined, {
      hasGuestSession: true,
      guestSession: {
        session_token: guestSession.session_token,
        ivf_data: guestSession.ivf_data,
        is_verified: guestSession.is_verified,
        expires_at: guestSession.expires_at.toISOString(),
        current_step: guestSession.current_step,
      },
    });
  } catch (error) {
    console.error("GET check-guest-session error:", error);
    return apiResponse(500, "Internal server error");
  }
}
