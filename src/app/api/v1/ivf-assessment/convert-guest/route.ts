import { NextRequest } from "next/server";
import { authenticate } from "@/utils/api/authenticate";
import { apiResponse } from "@/utils/api/apiResponse";
import { PrismaClient } from "@/generated/prisma";

const prisma = new PrismaClient();

/**
 * POST /api/v1/ivf-assessment/convert-guest
 * Convert guest session data to authenticated user account
 */
export async function POST(req: NextRequest) {
  try {
    // This endpoint requires authentication
    const { user, error } = await authenticate(req);
    if (error) return error;

    const body = await req.json();
    const { guestSessionToken, userId, overwrite = false } = body;
    
    if (!guestSessionToken) {
      return apiResponse(400, "Guest session token is required");
    }
    
    // Use the provided userId if available, otherwise use the one from the token
    const userIdToUse = userId || user.id;
    
    // Check if user profile exists
    const userProfile = await prisma.profiles.findUnique({
      where: { auth_id: userIdToUse },
    });
    
    if (!userProfile) {
      return apiResponse(400, "User profile not found");
    }
    
    // Get guest session data
    const guestSession = await prisma.guest_sessions.findUnique({
      where: { session_token: guestSessionToken },
    });
    
    if (!guestSession) {
      return apiResponse(400, "Guest session not found");
    }
    
    // Check conditions:
    // 1. expired_at is still valid
    if (guestSession.expires_at <= new Date()) {
      return apiResponse(400, "Guest session has expired");
    }
    
    // 2. current_step is 3
    if (guestSession.current_step !== 3) {
      return apiResponse(400, "Guest session is not at the correct step");
    }
    
    // 3. User's email exists in profiles table
    if (!userProfile.email) {
      return apiResponse(400, "User email not found in profile");
    }
    
    // Check if user already has IVF scores
    const existingIVFScores = await prisma.ivf_scores.findUnique({
      where: { user_id: userIdToUse },
    });

    if (existingIVFScores && !overwrite) {
      return apiResponse(409, "User already has IVF scores. Overwrite flag required to proceed.");
    }
    
    // Import the necessary function to convert guest to user
    const { convertGuestToUser } = await import("@/utils/api/guestSession");
    
    // Convert guest to user
    const conversionSuccess = await convertGuestToUser(
      guestSessionToken,
      userIdToUse,
      overwrite
    );
    
    if (!conversionSuccess) {
      return apiResponse(400, "Failed to convert guest to user");
    }
    
    // // Get the converted IVF scores
    // const ivfScores = await prisma.ivf_scores.findUnique({
    //   where: { user_id: user.id }
    // });
    
    // if (!ivfScores) {
    //   return apiResponse(404, "IVF scores not found after conversion");
    // }
    
    // // Calculate score using dynamic scoring system
    // const score = await calculateDynamicIVFScore({
    //   id: ivfScores.id,
    //   user_id: ivfScores.user_id,
    //   form_data: ivfScores.form_data as Record<string, FieldValue>,
    //   current_step: ivfScores.current_step,
    //   created_at: ivfScores.created_at,
    //   updated_at: ivfScores.updated_at,
    //   selected_track: ivfScores.selected_track || undefined
    // });
    
    return apiResponse(200, "Guest data successfully converted to user account");
  } catch (error) {
    console.error("POST results error:", error);
    return apiResponse(500, "Internal server error");
  }
}
