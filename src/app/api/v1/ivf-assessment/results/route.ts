/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextRequest } from "next/server";
import { authenticate } from "@/utils/api/authenticate";
import { apiResponse } from "@/utils/api/apiResponse";
import { getUserIVFScoresAndCalculate, determineIVFAssessmentStatus } from '@/lib/services/ivf-scores.service';

/**
 * GET /api/v1/ivf-assessment/results
 * Get IVF score results for authenticated users only (OPTIMIZED)
 */
export async function GET(req: NextRequest) {
  const startTime = Date.now();
  
  try {
    // This endpoint requires authentication
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { user, error } = await authenticate(req);
    if (error) return error;

    const { searchParams } = new URL(req.url);
    const debug_score = searchParams.get('debug_score') === 'true';
    
    // Get the user's IVF scores and calculate results using shared function
    const result = await getUserIVFScoresAndCalculate({ userId: user.id, debug: debug_score });
    if ('error' in result) {
      return apiResponse(result.error.status, result.error.message);
    }
    const { scoreData, ivfScores } = result;
    
    // Determine status based on current_step
    const status = await determineIVFAssessmentStatus(ivfScores.current_step);
    
    const processingTime = Date.now() - startTime;
    console.log(`Score calculated for user ${user.email}, response time: ${processingTime}ms`);
    const response: {
      score: any;
      status: 'pending' | 'completed' | 'failed';
      cached: boolean;
      processingTime: number;
      questions?: any;
    } = { 
      score: scoreData,
      status,
      cached: false,
      processingTime
    };
    if (debug_score) {
      response.questions = scoreData.questions;
      delete scoreData.questions;
    }
    return apiResponse(200, undefined, response);
  } catch (error) {
    console.error("GET results error:", error);
    const processingTime = Date.now() - startTime;
    console.log(`Error processing request, response time: ${processingTime}ms`);
    return apiResponse(500, "Internal server error");
  }
}

