import { NextRequest } from "next/server";
import { authenticate } from "@/utils/api/authenticate";
import { apiResponse } from "@/utils/api/apiResponse";
import { PrismaClient } from "@/generated/prisma";

const prisma = new PrismaClient();

/**
 * GET /api/v1/ivf-assessment/check-existing-scores
 * Check if user has existing IVF scores
 */
export async function GET(req: NextRequest) {
  try {
    // This endpoint requires authentication
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { user, error } = await authenticate(req);
    if (error) return error;

    const { searchParams } = new URL(req.url);
    const userId = searchParams.get("userId");

    if (!userId) {
      return apiResponse(400, "User ID is required");
    }

    // Check if user has existing IVF scores
    const existingIVFScores = await prisma.ivf_scores.findUnique({
      where: { user_id: userId },
    });

    return apiResponse(200, "Existing scores check completed", {
      hasExistingScores: !!existingIVFScores,
    });
  } catch (error) {
    console.error("GET check-existing-scores error:", error);
    return apiResponse(500, "Internal server error");
  }
} 