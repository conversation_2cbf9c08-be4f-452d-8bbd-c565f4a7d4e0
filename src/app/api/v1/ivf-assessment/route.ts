import { NextRequest } from "next/server";
import { apiResponse } from "@/utils/api/apiResponse";
import { getUserOrGuestContext, getIVFData, saveIVFData } from "@/utils/api/userOrGuest";
import { validateFormData } from "@/utils/api/formDataUtils";

/**
 * GET /api/v1/ivf-assessment
 * Retrieve IVF scores for authenticated users or guests
 */
export async function GET(req: NextRequest) {
  try {
    const { context, error } = await getUserOrGuestContext(req);
    if (error) return error;
    if (!context) {
      return apiResponse(401, "Could not determine user or guest context");
    }

    const { success, data, error: dataError } = await getIVFData(context);

    if (!success) {
      return apiResponse(500, dataError || "Failed to retrieve data");
    }

    if (!data) {
      return apiResponse(404, "IVF scores not found");
    }

    return apiResponse(200, undefined, {
      ivfScores: data,
      isGuest: !context?.isAuthenticated
    });
  } catch (error) {
    console.error("GET IVF scores error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * POST /api/v1/ivf-assessment
 * Create new IVF scores for authenticated users or guests
 */
export async function POST(req: NextRequest) {
  try {
    const { context, error } = await getUserOrGuestContext(req);
    if (error) return error;
    if (!context) {
      return apiResponse(401, "Could not determine user or guest context");
    }

    const { searchParams } = new URL(req.url);
    const tracks = searchParams.get('tracks'); // e.g., T1,T2,T3
    const validTracks = ['T1', 'T2', 'T3'];
    let selectedTrack: string | undefined = undefined;
    if (tracks) {
      // Only allow a single track code for IVF flow currently
      if (!validTracks.includes(tracks)) {
        return apiResponse(400, "Tracks parameter must be one of 'T1', 'T2', or 'T3'");
      }
      selectedTrack = tracks;
    }

    const body = await req.json();
    
    // Validate the complete form data
    const validation = await validateFormData(body, selectedTrack);
    if (!validation.success) {
      return apiResponse(400, validation.errors?.join(", ") || "Invalid form data");
    }

    // Check if data already exists
    const { success: getSuccess, data: existingData } = await getIVFData(context);

    if (getSuccess && existingData) {
      return apiResponse(409, "IVF scores already exist. Use PUT to update.");
    }

    const currentStep = body.current_step || 1;
    delete body.current_step; // Remove from form data

    const { success, data, error: saveError, guestSessionToken } = await saveIVFData(
      context,
      { form_data: body },
      currentStep,
      selectedTrack
    );

    if (!success) {
      return apiResponse(500, saveError || "Failed to create IVF scores");
    }

    const response = apiResponse(201, "IVF scores created successfully", {
      ivfScores: data,
      isGuest: !context?.isAuthenticated
    });

    // Add guest session token to response headers for guest users
    if (guestSessionToken) {
      response.headers.set("X-Guest-Session", guestSessionToken);
    }

    return response;
  } catch (error) {
    console.error("POST IVF scores error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * PUT /api/v1/ivf-assessment
 * Update existing IVF scores for authenticated users or guests
 */
export async function PUT(req: NextRequest) {
  try {
    const { context, error } = await getUserOrGuestContext(req);
    if (error) return error;
    if (!context) {
      return apiResponse(401, "Could not determine user or guest context");
    }

    const body = await req.json();
    const { step, ...formData } = body;
    
    // Get existing data to merge with updates
    const { success: getSuccess, data: existingData } = await getIVFData(context);

    if (!getSuccess || !existingData) {
      return apiResponse(404, "IVF scores not found. Use POST to create.");
    }
    
    // Handle step progression if provided
    let currentStep = existingData.current_step || 1;
    if (step !== undefined) {
      if (step < 1 || step > 3) {
        return apiResponse(400, "Step must be between 1 and 3");
      }
      currentStep = step;
    }
    
    // Merge existing form data with updates
    const existingFormData = existingData.form_data || {};
    const updatedFormData = { ...existingFormData, ...formData };

    const { success, data: updatedData, error: saveError, guestSessionToken } = await saveIVFData(
      context,
      { form_data: updatedFormData },
      currentStep
    );

    if (!success) {
      return apiResponse(500, saveError || "Failed to update IVF scores");
    }

    const response = apiResponse(200, "IVF scores updated successfully", {
      ivfScores: updatedData,
      isGuest: !context?.isAuthenticated
    });

    // Add guest session token to response headers for guest users
    if (guestSessionToken) {
      response.headers.set("X-Guest-Session", guestSessionToken);
    }

    return response;
  } catch (error) {
    console.error("PUT IVF scores error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * DELETE /api/v1/ivf-assessment
 * Delete IVF scores for authenticated users or clear guest session
 */
export async function DELETE(req: NextRequest) {
  try {
    const { context, error } = await getUserOrGuestContext(req);
    if (error) return error;

    if (context?.isAuthenticated && context.user) {
      // For authenticated users, delete from database
      const { PrismaClient } = await import("@/generated/prisma");
      const prisma = new PrismaClient();

      const existingScores = await prisma.ivf_scores.findUnique({
        where: { user_id: context.user.id },
      });

      if (!existingScores) {
        return apiResponse(404, "IVF scores not found for this user");
      }

      await prisma.ivf_scores.delete({
        where: { user_id: context.user.id },
      });

      return apiResponse(200, "IVF scores deleted successfully");
    } else {
      // For guests, clear the session
      return apiResponse(200, "Guest session cleared successfully", {
        message: "Guest data will be automatically cleared when session expires"
      });
    }
  } catch (error) {
    console.error("DELETE IVF scores error:", error);
    return apiResponse(500, "Internal server error");
  }
}
