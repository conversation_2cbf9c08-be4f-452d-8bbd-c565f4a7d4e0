/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextRequest } from "next/server";
import { apiResponse } from "@/utils/api/apiResponse";
import { PrismaClient } from "@/generated/prisma";
import { calculateDynamicIVFScore } from "@/lib/services/ivf-scoring";
import { FieldValue } from "@/types/ivf-score/ivf-score";
import { determineIVFAssessmentStatus } from '@/lib/services/ivf-scores.service';

const prisma = new PrismaClient();

/**
 * GET /api/v1/ivf-assessment/guest-results
 * Get IVF score results for guest users using session_token (no authentication required)
 */
export async function GET(req: NextRequest) {
  const startTime = Date.now();
  
  try {
    const { searchParams } = new URL(req.url);
    const session_token = searchParams.get('session_token');
    const debug_score = searchParams.get('debug_score') === 'true';
        
    if (!session_token) {
      return apiResponse(400, "Session token is required");
    }

    // Find guest session by session_token
    const guestSession = await prisma.guest_sessions.findUnique({
      where: { session_token },
    });

    
    if (!guestSession) {
      return apiResponse(404, "Guest session not found");
    }

    if (!guestSession.is_verified) {
      return apiResponse(400, "Email verification required before viewing results");
    }

    if (guestSession.current_step < 3) {
      return apiResponse(400, "All steps must be completed to view results");
    }

    // Check if session has expired
    if (guestSession.expires_at < new Date()) {
      return apiResponse(400, "Session has expired");
    }

    // Ensure ivf_data is in the correct format
    const formData = guestSession.ivf_data as Record<string, FieldValue>;

    const trackCode = guestSession.selected_track ? 'T'+ guestSession.selected_track : undefined;
    // Calculate score using the same logic as authenticated users
    const scoreData = await calculateDynamicIVFScore(
      {
        id: guestSession.id,
        user_id: guestSession.id, // Use session ID as user_id for guest
        form_data: formData, // This is the actual form data for guests
        current_step: guestSession.current_step,
        created_at: guestSession.created_at,
        updated_at: guestSession.updated_at,
        selected_track: trackCode,
      },
      debug_score,
      trackCode
    );

    // Determine status based on current_step
    const status = await determineIVFAssessmentStatus(guestSession.current_step);

    const processingTime = Date.now() - startTime;
    console.log(`Guest score calculated for session ${session_token}, response time: ${processingTime}ms`);
    
    const response: {
      score: any;
      status: 'pending' | 'completed' | 'failed';
      cached: boolean;
      processingTime: number;
      questions?: any;
    } = { 
      score: scoreData,
      status,
      cached: false,
      processingTime
    };
    
    if (debug_score) {
      response.questions = scoreData.questions;
      delete scoreData.questions;
    }
    
    return apiResponse(200, undefined, response);
  } catch (error) {
    console.error("GET guest-results error:", error);
    const processingTime = Date.now() - startTime;
    console.log(`Error processing guest request, response time: ${processingTime}ms`);
    return apiResponse(500, "Internal server error");
  }
} 