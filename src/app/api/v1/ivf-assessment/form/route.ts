import { NextRequest } from "next/server";
import { apiResponse } from "@/utils/api/apiResponse";
import { getUserOrGuestContext, saveIVFData } from "@/utils/api/userOrGuest";
import { 
  validateFormData, 
} from "@/utils/api/formDataUtils";
import { determineIVFAssessmentStatus } from '@/lib/services/ivf-scores.service';

/**
 * POST /api/v1/ivf-assessment/form
 * Create or update IVF form data for authenticated users or guests.
 * This endpoint accepts the complete form data from all steps (biological, lifestyle, environmental).
 */
export async function POST(req: NextRequest) {
  try {
    const { context, error } = await getUserOrGuestContext(req);
    if (error) return error;

    const { searchParams } = new URL(req.url);
    const tracks = searchParams.get('tracks'); // T1,T2,T3

    // Validate tracks: must be T1, T2, or T3 (single value)
    const validTracks = ['T1', 'T2', 'T3'];
    let selectedTrack: string | undefined = undefined;
    if (tracks) {
      if (!validTracks.includes(tracks)) {
        return apiResponse(400, "Tracks parameter must be one of 'T1', 'T2', or 'T3'");
      }
      selectedTrack = tracks;
    }

    const body = await req.json();
    const stepNumber = 3; // All forms are submitted together

    const categories = ['biological', 'lifestyle', 'environmental'];
    
    // Validate the form data against all questions with selected track filtering
    const validation = await validateFormData(categories, body, selectedTrack);
    
    if (!validation.success) {
      return apiResponse(400, validation.errors?.join(", ") || "Invalid form data");
    }
    
    const { success, data, error: saveError, guestSessionToken } = await saveIVFData(
      context!,
      body,
      stepNumber,
      selectedTrack // pass as fourth argument
    );

    if (!success) {
      return apiResponse(500, saveError || `Failed to save form data`);
    }

    // Determine status based on current step
    const status = await determineIVFAssessmentStatus(stepNumber);

    // Prepare response data
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const responseData: any = {
      ivfScores: data,
      status,
      isGuest: !context!.isAuthenticated,
      allStepsCompleted: true,
      nextAction: !context!.isAuthenticated ? "email_verification" : "view_results",
    };

    const response = apiResponse(200, `Form data saved successfully`, responseData);
    
    // Add guest session token to response headers for guest users
    if (guestSessionToken) {
      response.headers.set("X-Guest-Session", guestSessionToken);
    }

    return response;
  } catch (error) {
    console.error("POST form error:", error);
    return apiResponse(500, "Internal server error");
  }
}
