import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";

const prisma = new PrismaClient();

// GET - Fetch availability templates
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const doctorId = searchParams.get("doctor_id");
    const clinicId = searchParams.get("clinic_id");

    if (!doctorId) {
      return NextResponse.json(
        { success: false, error: 'Doctor ID is required' },
        { status: 400 }
      );
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const whereClause: any = {
      doctor_id: parseInt(doctorId)
    };

    if (clinicId) {
      whereClause.clinic_id = parseInt(clinicId);
    }

    const templates = await prisma.availability_templates.findMany({
      where: whereClause,
      include: {
        clinic: {
          select: {
            id: true,
            clinic_name: true,
            address: true,
            city: {
              select: {
                id: true,
                city_name: true,
                state_name: true,
              },
            },
          },
        },
      },
      orderBy: [
        { clinic_id: 'asc' },
        { day_of_week: 'asc' },
        { start_time: 'asc' }
      ]
    });

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(templates)
    });
  } catch (error) {
    console.error('Error fetching availability templates:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch availability templates' },
      { status: 500 }
    );
  }
}

// POST - Create availability templates (bulk)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { doctorId, templates } = body;

    if (!doctorId || !templates || !Array.isArray(templates)) {
      return NextResponse.json(
        { success: false, error: 'Doctor ID and templates array are required' },
        { status: 400 }
      );
    }

    // Validate doctor exists
    const doctor = await prisma.doctors.findUnique({
      where: { id: parseInt(doctorId) }
    });

    if (!doctor) {
      return NextResponse.json(
        { success: false, error: 'Doctor not found' },
        { status: 404 }
      );
    }

    // Create availability templates
    const createdTemplates = await prisma.availability_templates.createMany({
      data: templates.map(
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (template: any) => ({
        doctor_id: parseInt(doctorId),
        clinic_id: template.clinicId ? parseInt(template.clinicId) : null,
        day_of_week: template.dayOfWeek,
        start_time: template.startTime,
        end_time: template.endTime,
        duration: template.duration || 15,
        is_available: template.isAvailable !== undefined ? template.isAvailable : true,
        fee: template.fee ? parseFloat(template.fee) : null,
        currency: template.currency
      })),
      skipDuplicates: true
    });

    return NextResponse.json({
      success: true,
      data: {
        created: createdTemplates.count
      }
    });
  } catch (error) {
    console.error('Error creating availability templates:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create availability templates' },
      { status: 500 }
    );
  }
}

// PUT - Update availability templates (bulk)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { updates } = body;

    if (!updates || !Array.isArray(updates)) {
      return NextResponse.json(
        { success: false, error: 'Updates array is required' },
        { status: 400 }
      );
    }

    const updatePromises = updates.map(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      async (update: any) => {
      const { id, isAvailable, fee, currency } = update;

      return prisma.availability_templates.update({
        where: { id: parseInt(id) },
        data: {
          is_available: isAvailable !== undefined ? isAvailable : undefined,
          fee: fee !== undefined ? parseFloat(fee) : undefined,
          currency: currency !== undefined ? currency : undefined
        }
      });
    });

    const updatedTemplates = await Promise.all(updatePromises);

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(updatedTemplates)
    });
  } catch (error) {
    console.error('Error updating availability templates:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update availability templates' },
      { status: 500 }
    );
  }
}

// DELETE - Delete all templates for a doctor (to reset)
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const doctorId = searchParams.get("doctor_id");

    if (!doctorId) {
      return NextResponse.json(
        { success: false, error: 'Doctor ID is required' },
        { status: 400 }
      );
    }

    const deletedTemplates = await prisma.availability_templates.deleteMany({
      where: {
        doctor_id: parseInt(doctorId)
      }
    });

    return NextResponse.json({
      success: true,
      data: {
        deleted: deletedTemplates.count
      }
    });
  } catch (error) {
    console.error('Error deleting availability templates:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete availability templates' },
      { status: 500 }
    );
  }
}
