import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";

const prisma = new PrismaClient();

// GET - Fetch single availability template
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const templateId = parseInt((await params).id);

    const template = await prisma.availability_templates.findUnique({
      where: { id: templateId },
      include: {
        doctor: {
          include: {
            profile: {
              select: {
                display_name: true,
                email: true
              }
            }
          }
        }
      }
    });

    if (!template) {
      return NextResponse.json(
        { success: false, error: 'Availability template not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(template)
    });
  } catch (error) {
    console.error('Error fetching availability template:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch availability template' },
      { status: 500 }
    );
  }
}

// PUT - Update single availability template
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const templateId = parseInt((await params).id);
    const body = await request.json();
    const { isAvailable, fee, currency } = body;

    const updatedTemplate = await prisma.availability_templates.update({
      where: { id: templateId },
      data: {
        is_available: isAvailable !== undefined ? isAvailable : undefined,
        fee: fee !== undefined ? parseFloat(fee) : undefined,
        currency: currency !== undefined ? currency : undefined
      }
    });

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(updatedTemplate)
    });
  } catch (error) {
    console.error('Error updating availability template:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update availability template' },
      { status: 500 }
    );
  }
}

// DELETE - Delete single availability template
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const templateId = parseInt((await params).id);

    await prisma.availability_templates.delete({
      where: { id: templateId }
    });

    return NextResponse.json({
      success: true,
      data: { message: 'Availability template deleted successfully' }
    });
  } catch (error) {
    console.error('Error deleting availability template:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete availability template' },
      { status: 500 }
    );
  }
}
