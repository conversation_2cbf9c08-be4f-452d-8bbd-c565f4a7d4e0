import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";

const prisma = new PrismaClient();

export async function GET(
  request: NextRequest,
  { params } : { params: Promise<{ id: string }> }
) {
  try {
    const authId = (await params)?.id; // This is the UUID from user.id

    // First, find the profile using auth_id
    const profile = await prisma.profiles.findUnique({
      where: {
        auth_id: authId,
      },
    });

    if (!profile) {
      return NextResponse.json(
        { success: false, message: "Profile not found" },
        { status: 404 }
      );
    }

    // Then, find the doctor using profile.id (BigInt)
    const doctor = await prisma.doctors.findUnique({
      where: {
        id: profile.id, // profile.id is BigInt, doctor.id references profile.id
      },
      include: {
        profile: {
          select: {
            id: true,
            display_name: true,
            email: true,
            phone: true,
            biography: true,
            profile_image: true,
            languages: true,
            created_at: true,
            updated_at: true,
          },
        },
        doctor_clinics: {
          include: {
            clinic: {
              select: {
                id: true,
                clinic_name: true,
                address: true,
                city: {
                  select: {
                    id: true,
                    city_name: true,
                    state_name: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!doctor) {
      return NextResponse.json(
        { success: false, message: "Doctor not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(doctor),
    });

  } catch (error) {
    console.error("Error fetching doctor:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params } : { params: Promise<{ id: string }> }
) {
  try {
    const authId = (await params)?.id; // This is the UUID from user.id

    // First, find the profile using auth_id
    const profile = await prisma.profiles.findUnique({
      where: {
        auth_id: authId,
      },
    });

    if (!profile) {
      return NextResponse.json(
        { success: false, message: "Profile not found" },
        { status: 404 }
      );
    }

    const body = await request.json();
    const { 
      specialization_name, 
      years_of_experience,
      consultation_fees,
      consultation_currency,
      consultation_mode,
      display_name,
      email,
      phone,
      biography,
      profile_image,
      languages,
      clinic_ids 
    } = body;

    // Update profile information
    await prisma.profiles.update({
      where: {
        id: profile.id,
      },
      data: {
        display_name: display_name || profile.display_name,
        email: email || profile.email,
        phone: phone || profile.phone,
        biography: biography || profile.biography,
        profile_image: profile_image || profile.profile_image,
        languages: languages || profile.languages,
        updated_at: new Date(),
      },
    });

    // Update doctor information using profile.id
    const updatedDoctor = await prisma.doctors.update({
      where: {
        id: profile.id, // profile.id is BigInt, doctor.id references profile.id
      },
      data: {
        specialization_name: specialization_name || undefined,
        years_of_experience: years_of_experience ? parseInt(years_of_experience) : undefined,
        consultation_fees: consultation_fees ? parseFloat(consultation_fees) : undefined,
        consultation_currency: consultation_currency || undefined,
        consultation_mode: consultation_mode || undefined,
        updated_at: new Date(),
      },
      include: {
        profile: {
          select: {
            id: true,
            display_name: true,
            email: true,
            phone: true,
            biography: true,
            profile_image: true,
            languages: true,
            created_at: true,
            updated_at: true,
          },
        },
        doctor_clinics: {
          include: {
            clinic: {
              select: {
                id: true,
                clinic_name: true,
                address: true,
                city: {
                  select: {
                    id: true,
                    city_name: true,
                    state_name: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    // Update doctor-clinic relationships if clinic_ids provided
    if (clinic_ids && Array.isArray(clinic_ids)) {
      // First, remove existing relationships
      await prisma.doctor_clinics.deleteMany({
        where: { doctor_id: profile.id }
      });

      // Then create new relationships
      if (clinic_ids.length > 0) {
        await prisma.doctor_clinics.createMany({
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          data: clinic_ids.map((cId: any) => ({
            doctor_id: profile.id,
            clinic_id: parseInt(cId.toString()),
          })),
          skipDuplicates: true
        });
      }
    }

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(updatedDoctor),
      message: "Doctor updated successfully",
    });
  } catch (error) {
    console.error("Error updating doctor:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params } : { params: Promise<{ id: string }> }
) {
  try {
    const authId = (await params)?.id; // This is the UUID from user.id

    // First, find the profile using auth_id
    const profile = await prisma.profiles.findUnique({
      where: {
        auth_id: authId,
      },
    });

    if (!profile) {
      return NextResponse.json(
        { success: false, message: "Profile not found" },
        { status: 404 }
      );
    }

    // Check if doctor has any appointments
    const existingAppointments = await prisma.appointments.findFirst({
      where: {
        doctor_id: profile.id, // profile.id is BigInt, doctor.id references profile.id
      },
    });

    if (existingAppointments) {
      return NextResponse.json(
        { 
          success: false, 
          message: "Cannot delete doctor with existing appointments" 
        },
        { status: 400 }
      );
    }

    // Check if doctor has any time slots
    const existingTimeSlots = await prisma.availability_templates.findFirst({
      where: {
        doctor_id: profile.id, // profile.id is BigInt, doctor.id references profile.id
      },
    });

    if (existingTimeSlots) {
      return NextResponse.json(
        { 
          success: false, 
          message: "Cannot delete doctor with existing time slots" 
        },
        { status: 400 }
      );
    }

    // Delete doctor
    await prisma.doctors.delete({
      where: {
        id: profile.id, // profile.id is BigInt, doctor.id references profile.id
      },
    });

    return NextResponse.json({
      success: true,
      message: "Doctor deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting doctor:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}
