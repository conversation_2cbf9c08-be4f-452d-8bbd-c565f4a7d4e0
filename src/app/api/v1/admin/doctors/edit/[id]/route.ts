import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";

const prisma = new PrismaClient();

export async function GET(
  request: NextRequest,
  { params } : { params: Promise<{ id: string }> }
) {
  try {
    const doctorId = parseInt((await params)?.id);

    if (isNaN(doctorId)) {
      return NextResponse.json(
        { success: false, message: "Invalid doctor ID" },
        { status: 400 }
      );
    }

    // Find the doctor using the database ID
    const doctor = await prisma.doctors.findUnique({
      where: {
        id: doctorId,
      },
      include: {
        profile: {
          select: {
            id: true,
            display_name: true,
            email: true,
            phone: true,
            auth_id: true,
            biography: true,
            profile_image: true,
            languages: true,
            created_at: true,
            updated_at: true,
          },
        },
        doctor_clinics: {
          include: {
            clinic: {
              select: {
                id: true,
                clinic_name: true,
                address: true,
                city: {
                  select: {
                    id: true,
                    city_name: true,
                    state_name: true,
                  },
                },
              },
            },
          },
        },
        _count: {
          select: {
            appointments: true,
            availability_templates: true
          },
        },
      },
    });

    if (!doctor) {
      return NextResponse.json(
        { success: false, message: "Doctor not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(doctor),
    });

  } catch (error) {
    console.error("Error fetching doctor:", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}
