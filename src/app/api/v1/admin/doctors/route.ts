import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";

const prisma = new PrismaClient();

type Availability_Template_Type = {
  day_of_week: number;
  start_time: string;
  end_time: string;
  duration?: number;
  is_available?: boolean;
  fee?: string;
  currency?: string;
  clinic_id?: number;
};

// GET - Fetch all doctors with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const perPage = parseInt(searchParams.get("per_page") || "10");
    const search = searchParams.get("search") || "";
    const clinicId = searchParams.get("clinic_id");

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const where: any = {};

    if (search) {
      where.OR = [
        {
          profile: {
            display_name: {
              contains: search,
              mode: 'insensitive' as const
            }
          }
        },
        {
          profile: {
            email: {
              contains: search,
              mode: 'insensitive' as const
            }
          }
        },
        {
          specialization_name: {
            contains: search,
            mode: 'insensitive' as const
          }
        }
      ];
    }

    if (clinicId) {
      where.doctor_clinics = {
        some: {
          clinic_id: parseInt(clinicId)
        }
      };
    }

    const [doctors, total] = await Promise.all([
      prisma.doctors.findMany({
        where,
        take: perPage,
        skip: (page - 1) * perPage,
        include: {
          profile: {
            select: {
              id: true,
              display_name: true,
              email: true,
              phone: true,
              auth_id: true,
              biography: true,
              profile_image: true,
              languages: true,
              created_at: true,
              updated_at: true,
            },
          },

          doctor_clinics: {
            include: {
              clinic: {
                select: {
                  id: true,
                  clinic_name: true,
                  address: true,
                  city: {
                    select: {
                      id: true,
                      city_name: true,
                      state_name: true,
                    },
                  },
                },
              },
            },
          },
          _count: {
            select: {
              appointments: true,
              availability_templates: true
            },
          },
        },
        orderBy: { 
          profile: { 
            display_name: 'asc' 
          } 
        }
      }),
      prisma.doctors.count({ where })
    ]);

    return NextResponse.json({
      success: true,
      data: {
        doctors: serializePrismaResponse(doctors),
        total,
        page,
        perPage,
        totalPages: Math.ceil(total / perPage)
      }
    });
  } catch (error) {
    console.error('Error fetching doctors:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch doctors' },
      { status: 500 }
    );
  }
}

// POST - Create a new doctor
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      display_name,
      email,
      phone,
      specialization_name,
      years_of_experience,
      biography,
      consultation_fees,
      consultation_currency,
      consultation_mode,
      profile_image,
      languages,
      clinic_ids,
      availability_templates
    } = body;

    // Validate required fields
    if (!display_name || !email) {
      return NextResponse.json(
        { success: false, error: 'Display name and email are required' },
        { status: 400 }
      );
    }

    // Check if email already exists
    const existingProfile = await prisma.profiles.findFirst({
      where: { email }
    });

    if (existingProfile) {
      return NextResponse.json(
        { success: false, error: 'Email already exists' },
        { status: 400 }
      );
    }

    // Create profile first
    const profile = await prisma.profiles.create({
      data: {
        display_name,
        email,
        phone: phone || null,
        biography: biography || null,
        profile_image: profile_image || null,
        languages: languages || [],
      }
    });

    // Create doctor record
    const doctor = await prisma.doctors.create({
      data: {
        id: profile.id,
        specialization_name: specialization_name || null,
        years_of_experience: years_of_experience ? parseInt(years_of_experience) : null,
        consultation_fees: consultation_fees ? parseFloat(consultation_fees) : null,
        consultation_currency: consultation_currency || "INR",
        consultation_mode: consultation_mode || "both",
      },
      include: {
        profile: {
          select: {
            id: true,
            display_name: true,
            email: true,
            phone: true,
            auth_id: true,
            biography: true,
            profile_image: true,
            languages: true,
          },
        },
        doctor_clinics: {
          include: {
            clinic: {
              select: {
                id: true,
                clinic_name: true,
                address: true,
                city: {
                  select: {
                    id: true,
                    city_name: true,
                    state_name: true,
                  },
                },
              },
            },
          },
        },
        _count: {
          select: {
            appointments: true,
            availability_templates: true
          },
        },
      }
    });

    // Create doctor-clinic relationships if clinic_ids provided
    if (clinic_ids && Array.isArray(clinic_ids) && clinic_ids.length > 0) {
      await prisma.doctor_clinics.createMany({
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        data: clinic_ids.map((cId: any) => ({
          doctor_id: doctor.id,
          clinic_id: parseInt(cId.toString()),
        })),
        skipDuplicates: true
      });
    }

    // Create availability templates if provided
    if (availability_templates && Array.isArray(availability_templates) && availability_templates.length > 0) {
      await prisma.availability_templates.createMany({
        data: availability_templates.map((template: Availability_Template_Type) => ({
          doctor_id: doctor.id,
          clinic_id: template.clinic_id || null,
          day_of_week: template.day_of_week,
          start_time: template.start_time,
          end_time: template.end_time,
          duration: template.duration || 15,
          is_available: template.is_available !== undefined ? template.is_available : true,
          fee: template.fee ? parseFloat(template.fee) : null,
          currency: template.currency || null,
        })),
        skipDuplicates: true
      });
    }

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(doctor)
    });
  } catch (error) {
    console.error('Error creating doctor:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create doctor' },
      { status: 500 }
    );
  }
}
