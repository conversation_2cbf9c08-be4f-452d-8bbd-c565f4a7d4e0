import { NextRequest, NextResponse } from "next/server";
import { PrismaClient, diet_condition } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";

const prisma = new PrismaClient();

// GET - Fetch condition meal plans with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const perPage = parseInt(searchParams.get("per_page") || "10");
    const condition = searchParams.get("condition") as diet_condition | null;
    const search = searchParams.get("search") || "";

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const where: any = { is_active: true };

    if (condition) {
      where.condition = condition;
    }

    if (search) {
      where.OR = [
        {
          doctor: {
            display_name: {
              contains: search,
              mode: 'insensitive' as const
            }
          }
        },
        {
          doctor: {
            email: {
              contains: search,
              mode: 'insensitive' as const
            }
          }
        }
      ];
    }

    const [mealPlans, total] = await Promise.all([
      prisma.condition_meal_plans.findMany({
        where,
        take: perPage,
        skip: (page - 1) * perPage,
        include: {
          doctor: {
            select: {
              display_name: true,
              email: true
            }
          }
        },
        orderBy: { created_at: 'desc' }
      }),
      prisma.condition_meal_plans.count({ where })
    ]);

    return NextResponse.json({
      success: true,
      data: {
        mealPlans: serializePrismaResponse(mealPlans),
        total,
        page,
        perPage,
        totalPages: Math.ceil(total / perPage)
      }
    });
  } catch (error) {
    console.error('Error fetching condition meal plans:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch meal plans' },
      { status: 500 }
    );
  }
}

// POST - Create a new condition meal plan
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      doctorId,
      condition,
      targetCaloriesMin,
      targetCaloriesMax,
      recommendedTargetCalories,
      nutritionalAdvice,
      mealPlanDocUrl
    } = body;

    // Validate required fields
    if (!doctorId || !condition || !targetCaloriesMin || !targetCaloriesMax || !recommendedTargetCalories) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Prepare meal plan data
    const mealPlanData = {
      doctor_id: doctorId,
      condition,
      target_calories_min: targetCaloriesMin,
      target_calories_max: targetCaloriesMax,
      recommended_target_calories: recommendedTargetCalories,
      nutritional_advice: nutritionalAdvice,
      meal_plan_doc_url: mealPlanDocUrl
    };

    const mealPlan = await prisma.condition_meal_plans.create({
      data: mealPlanData,
      include: {
        doctor: {
          select: {
            display_name: true,
            email: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(mealPlan)
    });
  } catch (error) {
    console.error('Error creating condition meal plan:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create meal plan' },
      { status: 500 }
    );
  }
}
