import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";

const prisma = new PrismaClient();

// GET - Fetch single profile
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const profileId = parseInt((await params).id);

    if (isNaN(profileId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid profile ID' },
        { status: 400 }
      );
    }

    const profile = await prisma.profiles.findUnique({
      where: { id: profileId },
      select: {
        id: true,
        display_name: true,
        email: true,
        phone: true,
        auth_id: true,
        created_at: true,
        updated_at: true,
      }
    });

    if (!profile) {
      return NextResponse.json(
        { success: false, error: 'Profile not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(profile)
    });
  } catch (error) {
    console.error('Error fetching profile:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch profile' },
      { status: 500 }
    );
  }
}

// PUT - Update profile
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const profileId = parseInt((await params).id);

    if (isNaN(profileId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid profile ID' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { display_name, email, phone } = body;

    // Validate required fields
    if (!display_name || !email) {
      return NextResponse.json(
        { success: false, error: 'Display name and email are required' },
        { status: 400 }
      );
    }

    // Check if email already exists for another profile
    const existingProfile = await prisma.profiles.findFirst({
      where: {
        email,
        NOT: {
          id: profileId
        }
      }
    });

    if (existingProfile) {
      return NextResponse.json(
        { success: false, error: 'Email already exists' },
        { status: 400 }
      );
    }

    const updatedProfile = await prisma.profiles.update({
      where: { id: profileId },
      data: {
        display_name,
        email,
        phone: phone || null,
        updated_at: new Date(),
      },
      select: {
        id: true,
        display_name: true,
        email: true,
        phone: true,
        auth_id: true,
        created_at: true,
        updated_at: true,
      }
    });

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(updatedProfile)
    });
  } catch (error) {
    console.error('Error updating profile:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update profile' },
      { status: 500 }
    );
  }
}

// DELETE - Delete profile
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const profileId = parseInt((await params).id);

    if (isNaN(profileId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid profile ID' },
        { status: 400 }
      );
    }

    // Check if profile has associated doctors or patients
    const profile = await prisma.profiles.findUnique({
      where: { id: profileId },
      select: {
        id: true,
        doctor_profile: true,
        patient_profile: true
      }
    });

    if (!profile) {
      return NextResponse.json(
        { success: false, error: 'Profile not found' },
        { status: 404 }
      );
    }

    if (profile.doctor_profile || profile.patient_profile) {
      return NextResponse.json(
        { success: false, error: 'Cannot delete profile with associated doctors or patients' },
        { status: 409 }
      );
    }

    await prisma.profiles.delete({
      where: { id: profileId }
    });

    return NextResponse.json({
      success: true,
      message: 'Profile deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting profile:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete profile' },
      { status: 500 }
    );
  }
}
