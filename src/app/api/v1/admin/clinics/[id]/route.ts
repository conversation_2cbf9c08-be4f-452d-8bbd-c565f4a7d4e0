import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";

const prisma = new PrismaClient();

// GET - Fetch a specific clinic
export async function GET(
  request: NextRequest,
  { params } : { params: Promise<{ id: string }> }
) {
  try {
    const clinicId = parseInt((await params)?.id);

    if (isNaN(clinicId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid clinic ID' },
        { status: 400 }
      );
    }

    const clinic = await prisma.clinics.findUnique({
      where: { id: clinicId },
      include: {
        city: {
          select: {
            city_name: true,
            state_name: true
          }
        },
        doctor_clinics: {
          include: {
            doctor: {
              select: {
                id: true,
                profile: {
                  select: {
                    display_name: true,
                    email: true,
                    phone: true
                  }
                },
                specialization_name: true,
                years_of_experience: true,
                consultation_fees: true,
                consultation_currency: true
              }
            }
          }
        },
        _count: {
          select: {
            appointments: true
          }
        }
      }
    });

    if (!clinic) {
      return NextResponse.json(
        { success: false, error: 'Clinic not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(clinic)
    });
  } catch (error) {
    console.error('Error fetching clinic:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch clinic' },
      { status: 500 }
    );
  }
}

// PUT - Update a clinic
export async function PUT(
  request: NextRequest,
  { params } : { params: Promise<{ id: string }> }
) {
  try {
    const clinicId = parseInt((await params)?.id);
    const body = await request.json();
    const {
      clinicName,
      address,
      cityId,
      contactInfo,
      latitude,
      longitude,
      clinicStartTime,
      clinicEndTime
    } = body;

    if (isNaN(clinicId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid clinic ID' },
        { status: 400 }
      );
    }

    if (!clinicName) {
      return NextResponse.json(
        { success: false, error: 'Clinic name is required' },
        { status: 400 }
      );
    }

    // Validate clinic hours if provided
    if (clinicStartTime && clinicEndTime) {
      const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
      if (!timeRegex.test(clinicStartTime) || !timeRegex.test(clinicEndTime)) {
        return NextResponse.json(
          { success: false, error: 'Invalid time format. Use HH:mm format (e.g., 09:00)' },
          { status: 400 }
        );
      }

      const [startHour, startMinute] = clinicStartTime.split(':').map(Number);
      const [endHour, endMinute] = clinicEndTime.split(':').map(Number);
      const startMinutes = startHour * 60 + startMinute;
      const endMinutes = endHour * 60 + endMinute;

      if (startMinutes >= endMinutes) {
        return NextResponse.json(
          { success: false, error: 'End time must be after start time' },
          { status: 400 }
        );
      }
    }

    // Check if clinic exists
    const existingClinic = await prisma.clinics.findUnique({
      where: { id: clinicId }
    });

    if (!existingClinic) {
      return NextResponse.json(
        { success: false, error: 'Clinic not found' },
        { status: 404 }
      );
    }

    // Validate city exists if provided
    if (cityId) {
      const city = await prisma.cities.findUnique({
        where: { id: parseInt(cityId) }
      });

      if (!city) {
        return NextResponse.json(
          { success: false, error: 'Invalid city ID' },
          { status: 400 }
        );
      }
    }

    const updatedClinic = await prisma.clinics.update({
      where: { id: clinicId },
      data: {
        clinic_name: clinicName,
        address,
        city_id: cityId ? parseInt(cityId) : undefined,
        contact_info: contactInfo,
        latitude: latitude ? parseFloat(latitude) : undefined,
        longitude: longitude ? parseFloat(longitude) : undefined,
        clinic_start_time: clinicStartTime || undefined,
        clinic_end_time: clinicEndTime || undefined
      },
      include: {
        city: {
          select: {
            city_name: true,
            state_name: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(updatedClinic)
    });
  } catch (error) {
    console.error('Error updating clinic:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update clinic' },
      { status: 500 }
    );
  }
}

// DELETE - Delete a clinic
export async function DELETE(
  request: NextRequest,
  { params } : { params: Promise<{ id: string }> }
) {
  try {
    const clinicId = parseInt((await params)?.id);

    if (isNaN(clinicId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid clinic ID' },
        { status: 400 }
      );
    }

    // Check if clinic has associated doctors or appointments
    const clinicWithRelations = await prisma.clinics.findUnique({
      where: { id: clinicId },
      include: {
        _count: {
          select: {
            doctor_clinics: true,
            appointments: true
          }
        }
      }
    });

    if (!clinicWithRelations) {
      return NextResponse.json(
        { success: false, error: 'Clinic not found' },
        { status: 404 }
      );
    }

    if (clinicWithRelations._count.appointments > 0) {
      return NextResponse.json(
        { success: false, error: 'Cannot delete clinic with associated appointments' },
        { status: 409 }
      );
    }

    await prisma.$transaction([
      prisma.doctor_clinics.deleteMany({
        where: { clinic_id: clinicId }
      }),
      prisma.clinics.delete({
        where: { id: clinicId }
      })
    ]);

    return NextResponse.json({
      success: true,
      message: 'Clinic deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting clinic:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete clinic' },
      { status: 500 }
    );
  }
}
