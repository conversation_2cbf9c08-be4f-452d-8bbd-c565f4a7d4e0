import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";

const prisma = new PrismaClient();

// GET - Fetch clinics with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const perPage = parseInt(searchParams.get("per_page") || "10");
    const search = searchParams.get("search") || "";
    const cityId = searchParams.get("city_id");
    const doctorId = searchParams.get("doctor_id");

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const where: any = {};

    if (search) {
      where.OR = [
        {
          clinic_name: {
            contains: search,
            mode: 'insensitive' as const
          }
        },
        {
          address: {
            contains: search,
            mode: 'insensitive' as const
          }
        },
        {
          contact_info: {
            contains: search,
            mode: 'insensitive' as const
          }
        }
      ];
    }

    if (cityId) {
      where.city_id = parseInt(cityId);
    }

    if (doctorId) {
      where.doctor_clinics = {
        some: {
          doctor_id: parseInt(doctorId)
        }
      };
    }

    const [clinics, total] = await Promise.all([
      prisma.clinics.findMany({
        where,
        take: perPage,
        skip: (page - 1) * perPage,
        include: {
          city: {
            select: {
              city_name: true,
              state_name: true
            }
          },
          _count: {
            select: {
              doctor_clinics: true,
              appointments: true
            }
          }
        },
        orderBy: { clinic_name: 'asc' }
      }),
      prisma.clinics.count({ where })
    ]);

    return NextResponse.json({
      success: true,
      data: {
        clinics: serializePrismaResponse(clinics),
        total,
        page,
        perPage,
        totalPages: Math.ceil(total / perPage)
      }
    });
  } catch (error) {
    console.error('Error fetching clinics:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch clinics' },
      { status: 500 }
    );
  }
}

// POST - Create a new clinic
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      clinicName,
      address,
      cityId,
      contactInfo,
      latitude,
      longitude,
      clinicStartTime,
      clinicEndTime
    } = body;

    if (!clinicName) {
      return NextResponse.json(
        { success: false, error: 'Clinic name is required' },
        { status: 400 }
      );
    }

    // Validate clinic hours if provided
    if (clinicStartTime && clinicEndTime) {
      const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
      if (!timeRegex.test(clinicStartTime) || !timeRegex.test(clinicEndTime)) {
        return NextResponse.json(
          { success: false, error: 'Invalid time format. Use HH:mm format (e.g., 09:00)' },
          { status: 400 }
        );
      }

      const [startHour, startMinute] = clinicStartTime.split(':').map(Number);
      const [endHour, endMinute] = clinicEndTime.split(':').map(Number);
      const startMinutes = startHour * 60 + startMinute;
      const endMinutes = endHour * 60 + endMinute;

      if (startMinutes >= endMinutes) {
        return NextResponse.json(
          { success: false, error: 'End time must be after start time' },
          { status: 400 }
        );
      }
    }

    // Validate city exists if provided
    if (cityId) {
      const city = await prisma.cities.findUnique({
        where: { id: parseInt(cityId) }
      });

      if (!city) {
        return NextResponse.json(
          { success: false, error: 'Invalid city ID' },
          { status: 400 }
        );
      }
    }

    const clinic = await prisma.clinics.create({
      data: {
        clinic_name: clinicName,
        address,
        city_id: cityId ? parseInt(cityId) : null,
        contact_info: contactInfo,
        latitude: latitude ? parseFloat(latitude) : null,
        longitude: longitude ? parseFloat(longitude) : null,
        clinic_start_time: clinicStartTime || null,
        clinic_end_time: clinicEndTime || null
      },
      include: {
        city: {
          select: {
            city_name: true,
            state_name: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(clinic)
    });
  } catch (error) {
    console.error('Error creating clinic:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create clinic' },
      { status: 500 }
    );
  }
}
