import { NextRequest, NextResponse } from "next/server";
import { PrismaClient, AppointmentStatus } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";

const prisma = new PrismaClient();

// GET - Fetch a specific appointment
export async function GET(
  request: Request,
  { params } : { params: Promise<{ id: string }> }
) {
  try {
    const appointmentId = parseInt((await params)?.id);

    if (isNaN(appointmentId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid appointment ID' },
        { status: 400 }
      );
    }

    const appointment = await prisma.appointments.findUnique({
      where: { id: appointmentId },
      include: {
        doctor: {
          include: {
            profile: {
              select: {
                display_name: true,
                email: true,
                phone: true
              }
            }
          }
        },
        patient: {
          include: {
            profile: {
              select: {
                display_name: true,
                email: true,
                phone: true
              }
            }
          }
        },
        clinic: {
          include: {
            city: {
              select: {
                city_name: true,
                state_name: true
              }
            }
          }
        }
      }
    });

    if (!appointment) {
      return NextResponse.json(
        { success: false, error: 'Appointment not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(appointment)
    });
  } catch (error) {
    console.error('Error fetching appointment:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch appointment' },
      { status: 500 }
    );
  }
}

// PATCH - Update appointment status
export async function PATCH(
  request: NextRequest,
  { params } : { params: Promise<{ id: string }> }
) {
  try {
    const appointmentId = parseInt((await params)?.id);
    const body = await request.json();
    const { status, startTime, endTime, appointmentDate } = body;

    if (isNaN(appointmentId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid appointment ID' },
        { status: 400 }
      );
    }

    // Check if appointment exists
    const existingAppointment = await prisma.appointments.findUnique({
      where: { id: appointmentId }
    });

    if (!existingAppointment) {
      return NextResponse.json(
        { success: false, error: 'Appointment not found' },
        { status: 404 }
      );
    }

  // Prepare update data
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const updateData: any = {};

    if (status && Object.values(AppointmentStatus).includes(status)) {
      updateData.status = status;
    }

    if (startTime && endTime && appointmentDate) {
      // Check if the new time slot is available
      const conflictingAppointment = await prisma.appointments.findFirst({
        where: {
          doctor_id: existingAppointment.doctor_id,
          appointment_date: new Date(appointmentDate),
          start_time: new Date(startTime),
          end_time: new Date(endTime),
          status: {
            not: 'cancelled'
          },
          id: {
            not: appointmentId
          }
        }
      });

      if (conflictingAppointment) {
        return NextResponse.json(
          { success: false, error: 'Time slot is already booked' },
          { status: 409 }
        );
      }

      updateData.start_time = new Date(startTime);
      updateData.end_time = new Date(endTime);
      updateData.appointment_date = new Date(appointmentDate);
    }

    const updatedAppointment = await prisma.appointments.update({
      where: { id: appointmentId },
      data: updateData,
      include: {
        doctor: {
          include: {
            profile: {
              select: {
                display_name: true,
                email: true
              }
            }
          }
        },
        patient: {
          include: {
            profile: {
              select: {
                display_name: true,
                email: true,
                phone: true
              }
            }
          }
        },
        clinic: {
          select: {
            clinic_name: true,
            address: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(updatedAppointment)
    });
  } catch (error) {
    console.error('Error updating appointment:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update appointment' },
      { status: 500 }
    );
  }
}
