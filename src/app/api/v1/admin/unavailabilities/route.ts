import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const doctorId = searchParams.get("doctor_id");
    const clinicId = searchParams.get("clinic_id");
    const startDate = searchParams.get("start_date");
    const endDate = searchParams.get("end_date");

    if (!doctorId) {
      return NextResponse.json(
        { success: false, error: "Doctor ID is required" },
        { status: 400 }
      );
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const where: any = {
      doctor_id: BigInt(doctorId),
    };

    if (clinicId) {
      where.clinic_id = BigInt(clinicId);
    }

    if (startDate && endDate) {
      where.date = {
        gte: new Date(startDate),
        lte: new Date(endDate),
      };
    } else if (startDate) {
      where.date = {
        gte: new Date(startDate),
      };
    }

    const unavailabilities = await prisma.unavailabilities.findMany({
      where,
      include: {
        clinic: {
          include: {
            city: true,
          },
        },
      },
      orderBy: [
        { date: "asc" },
        { start_time: "asc" },
      ],
    });

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(unavailabilities),
    });
  } catch (error) {
    console.error("Error fetching unavailabilities:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch unavailabilities" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const { doctorId, unavailabilities: unavailabilityData } = body;

    if (!doctorId || !unavailabilityData || !Array.isArray(unavailabilityData)) {
      return NextResponse.json(
        { success: false, error: "Invalid request data" },
        { status: 400 }
      );
    }

    // Validate doctor exists
    const doctor = await prisma.doctors.findUnique({
      where: { id: BigInt(doctorId) },
    });

    if (!doctor) {
      return NextResponse.json(
        { success: false, error: "Doctor not found" },
        { status: 404 }
      );
    }

    const createdUnavailabilities = [];

    for (const unavailability of unavailabilityData) {
      const {
        clinicId,
        date,
        start_time,
        end_time,
        duration = 15,
        reason,
        notes,
      } = unavailability;

      // Validate clinic if provided
      if (clinicId && clinicId !== null && clinicId !== undefined) {
        const clinic = await prisma.clinics.findUnique({
          where: { id: BigInt(clinicId) },
        });

        if (!clinic) {
          return NextResponse.json(
            { success: false, error: `Clinic with ID ${clinicId} not found` },
            { status: 404 }
          );
        }
      }

      const unavailabilityData = {
        doctor_id: BigInt(doctorId),
        clinic_id: clinicId ? BigInt(clinicId) : null,
        date: new Date(date),
        start_time: start_time,
        end_time: end_time,
        duration,
        reason,
        notes,
      };
      
      const createdUnavailability = await prisma.unavailabilities.create({
        data: unavailabilityData,
        include: {
          clinic: {
            include: {
              city: true,
            },
          },
        },
      });

      createdUnavailabilities.push(createdUnavailability);
    }

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(createdUnavailabilities),
      message: "Unavailabilities created successfully",
    });
  } catch (error) {
    console.error("Error creating unavailabilities:", error);
    
    if ((error as { code?: string }).code === 'P2002') {
      return NextResponse.json(
        { success: false, error: "Unavailability slot already exists for this doctor, clinic, date, and time" },
        { status: 409 }
      );
    }

    // Check for other common Prisma errors
    if ((error as { code?: string }).code === 'P2003') {
      return NextResponse.json(
        { success: false, error: "Foreign key constraint failed - check if doctor or clinic exists" },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Failed to create unavailabilities" },
      { status: 500 }
    );
  }
}
