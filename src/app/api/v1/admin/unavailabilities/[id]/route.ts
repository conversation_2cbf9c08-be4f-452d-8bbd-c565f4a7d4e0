import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";

const prisma = new PrismaClient();

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const unavailabilityId = (await params).id;

    if (!unavailabilityId) {
      return NextResponse.json(
        { success: false, error: "Unavailability ID is required" },
        { status: 400 }
      );
    }

    // Check if unavailability exists
    const existingUnavailability = await prisma.unavailabilities.findUnique({
      where: { id: BigInt(unavailabilityId) },
    });

    if (!existingUnavailability) {
      return NextResponse.json(
        { success: false, error: "Unavailability not found" },
        { status: 404 }
      );
    }

    // Delete the unavailability
    await prisma.unavailabilities.delete({
      where: { id: BigInt(unavailabilityId) },
    });

    return NextResponse.json({
      success: true,
      message: "Unavailability deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting unavailability:", error);
    return NextResponse.json(
      { success: false, error: "Failed to delete unavailability" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const unavailabilityId = (await params).id;
    const body = await request.json();

    if (!unavailabilityId) {
      return NextResponse.json(
        { success: false, error: "Unavailability ID is required" },
        { status: 400 }
      );
    }

    // Check if unavailability exists
    const existingUnavailability = await prisma.unavailabilities.findUnique({
      where: { id: BigInt(unavailabilityId) },
    });

    if (!existingUnavailability) {
      return NextResponse.json(
        { success: false, error: "Unavailability not found" },
        { status: 404 }
      );
    }

    const {
      clinicId,
      date,
      startTime,
      endTime,
      duration,
      reason,
      notes,
    } = body;

    // Validate clinic if provided
    if (clinicId) {
      const clinic = await prisma.clinics.findUnique({
        where: { id: BigInt(clinicId) },
      });

      if (!clinic) {
        return NextResponse.json(
          { success: false, error: `Clinic with ID ${clinicId} not found` },
          { status: 404 }
        );
      }
    }

    // Update the unavailability
    const updatedUnavailability = await prisma.unavailabilities.update({
      where: { id: BigInt(unavailabilityId) },
      data: {
        clinic_id: clinicId ? BigInt(clinicId) : null,
        date: date ? new Date(date) : undefined,
        start_time: startTime,
        end_time: endTime,
        duration,
        reason,
        notes,
      },
      include: {
        clinic: {
          include: {
            city: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(updatedUnavailability),
      message: "Unavailability updated successfully",
    });
  } catch (error) {
    console.error("Error updating unavailability:", error);
    
    if ((error as { code?: string }).code === 'P2002') {
      return NextResponse.json(
        { success: false, error: "Unavailability slot already exists for this doctor, clinic, date, and time" },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Failed to update unavailability" },
      { status: 500 }
    );
  }
}
