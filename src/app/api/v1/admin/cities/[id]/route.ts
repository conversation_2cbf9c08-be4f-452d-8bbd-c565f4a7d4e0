import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";

const prisma = new PrismaClient();

// GET - Fetch a specific city
export async function GET(
  request: NextRequest,
  { params } : { params: Promise<{ id: string }> }
) {
  try {
    const cityId = parseInt((await params)?.id);

    if (isNaN(cityId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid city ID' },
        { status: 400 }
      );
    }

    const city = await prisma.cities.findUnique({
      where: { id: cityId },
      include: {
        clinics: {
          select: {
            id: true,
            clinic_name: true
          }
        }
      }
    });

    if (!city) {
      return NextResponse.json(
        { success: false, error: 'City not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(city)
    });
  } catch (error) {
    console.error('Error fetching city:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch city' },
      { status: 500 }
    );
  }
}

// PUT - Update a city
export async function PUT(
  request: NextRequest,
  { params } : { params: Promise<{ id: string }> }
) {
  try {
    const cityId = parseInt((await params)?.id);
    const body = await request.json();
    const { cityName, stateName } = body;

    if (isNaN(cityId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid city ID' },
        { status: 400 }
      );
    }

    if (!cityName) {
      return NextResponse.json(
        { success: false, error: 'City name is required' },
        { status: 400 }
      );
    }

    // Check if city exists
    const existingCity = await prisma.cities.findUnique({
      where: { id: cityId }
    });

    if (!existingCity) {
      return NextResponse.json(
        { success: false, error: 'City not found' },
        { status: 404 }
      );
    }

    // Check if the new name conflicts with another city
    const conflictingCity = await prisma.cities.findFirst({
      where: {
        city_name: {
          equals: cityName,
          mode: 'insensitive'
        },
        state_name: stateName ? {
          equals: stateName,
          mode: 'insensitive'
        } : null,
        id: {
          not: cityId
        }
      }
    });

    if (conflictingCity) {
      return NextResponse.json(
        { success: false, error: 'City name already exists' },
        { status: 409 }
      );
    }

    const updatedCity = await prisma.cities.update({
      where: { id: cityId },
      data: {
        city_name: cityName,
        state_name: stateName
      }
    });

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(updatedCity)
    });
  } catch (error) {
    console.error('Error updating city:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update city' },
      { status: 500 }
    );
  }
}

// DELETE - Delete a city
export async function DELETE(
  request: NextRequest,
  { params } : { params: Promise<{ id: string }> }
) {
  try {
    const cityId = parseInt((await params)?.id);

    if (isNaN(cityId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid city ID' },
        { status: 400 }
      );
    }

    // Check if city has associated clinics
    const cityWithClinics = await prisma.cities.findUnique({
      where: { id: cityId },
      include: {
        _count: {
          select: {
            clinics: true
          }
        }
      }
    });

    if (!cityWithClinics) {
      return NextResponse.json(
        { success: false, error: 'City not found' },
        { status: 404 }
      );
    }

    if (cityWithClinics._count.clinics > 0) {
      return NextResponse.json(
        { success: false, error: 'Cannot delete city with associated clinics' },
        { status: 409 }
      );
    }

    await prisma.cities.delete({
      where: { id: cityId }
    });

    return NextResponse.json({
      success: true,
      message: 'City deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting city:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete city' },
      { status: 500 }
    );
  }
}
