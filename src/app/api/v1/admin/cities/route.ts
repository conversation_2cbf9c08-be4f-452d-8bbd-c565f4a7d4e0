import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";

const prisma = new PrismaClient();

// GET - Fetch cities with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const perPage = parseInt(searchParams.get("per_page") || "10");
    const search = searchParams.get("search") || "";

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const where: any = {};

    if (search) {
      where.OR = [
        {
          city_name: {
            contains: search,
            mode: 'insensitive' as const
          }
        },
        {
          state_name: {
            contains: search,
            mode: 'insensitive' as const
          }
        }
      ];
    }

    const [cities, total] = await Promise.all([
      prisma.cities.findMany({
        where,
        take: perPage,
        skip: (page - 1) * perPage,
        include: {
          _count: {
            select: {
              clinics: true
            }
          }
        },
        orderBy: { city_name: 'asc' }
      }),
      prisma.cities.count({ where })
    ]);

    return NextResponse.json({
      success: true,
      data: {
        cities: serializePrismaResponse(cities),
        total,
        page,
        perPage,
        totalPages: Math.ceil(total / perPage)
      }
    });
  } catch (error) {
    console.error('Error fetching cities:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch cities' },
      { status: 500 }
    );
  }
}

// POST - Create a new city
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { cityName, stateName } = body;

    if (!cityName) {
      return NextResponse.json(
        { success: false, error: 'City name is required' },
        { status: 400 }
      );
    }

    // Check if city already exists
    const existingCity = await prisma.cities.findFirst({
      where: {
        city_name: {
          equals: cityName,
          mode: 'insensitive'
        },
        state_name: stateName ? {
          equals: stateName,
          mode: 'insensitive'
        } : null
      }
    });

    if (existingCity) {
      return NextResponse.json(
        { success: false, error: 'City already exists' },
        { status: 409 }
      );
    }

    const city = await prisma.cities.create({
      data: {
        city_name: cityName,
        state_name: stateName
      }
    });

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(city)
    });
  } catch (error) {
    console.error('Error creating city:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create city' },
      { status: 500 }
    );
  }
}
