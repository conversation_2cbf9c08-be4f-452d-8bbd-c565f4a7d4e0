import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";
import { authenticate } from "@/utils/api/authenticate";

const prisma = new PrismaClient();

// GET - Fetch a specific doctor by ID
export async function GET(
  request: NextRequest,
  { params } : { params: Promise<{ id: string }> }
) {
  try {
    // Authenticate the request
    const authResult = await authenticate(request);
    if (authResult.error) {
      return authResult.error;
    }
    const doctorId = (await params).id;

    if (!doctorId) {
      return NextResponse.json(
        { success: false, error: 'Doctor ID is required' },
        { status: 400 }
      );
    }

    // Validate that the ID is a valid number
    const numericId = parseInt(doctorId);
    if (isNaN(numericId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid doctor ID format' },
        { status: 400 }
      );
    }

    const doctor = await prisma.doctors.findUnique({
      where: {
        id: numericId
      },
      select: {
        id: true,
        specialization_name: true,
        years_of_experience: true,
        consultation_fees: true,
        consultation_currency: true,
        consultation_mode: true,
        profile: {
          select: {
            id: true,
            display_name: true,
            email: true,
            phone: true,
            biography: true,
            profile_image: true,
            languages: true
          }
        },
        doctor_clinics: {
          include: {
            clinic: {
              select: {
                id: true,
                clinic_name: true,
                address: true,
                contact_info: true,
                latitude: true,
                longitude: true,
                clinic_start_time: true,
                clinic_end_time: true,
                city: {
                  select: {
                    id: true,
                    city_name: true,
                    state_name: true,
                    lat: true,
                    lng: true
                  }
                }
              }
            }
          }
        },
        _count: {
          select: {
            availability_templates: {
              where: {
                is_available: true,
                created_at: {
                  gte: new Date()
                }
              }
            }
          }
        }
      }
    });

    if (!doctor) {
      return NextResponse.json(
        { success: false, error: 'Doctor not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(doctor)
    });
  } catch (error) {
    console.error('Error fetching doctor:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch doctor' },
      { status: 500 }
    );
  }
}
