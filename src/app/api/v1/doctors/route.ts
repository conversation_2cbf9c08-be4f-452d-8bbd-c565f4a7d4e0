import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";
import { authenticate } from "@/utils/api/authenticate";

const prisma = new PrismaClient();

// GET - Fetch doctors by clinic (appointment booking)
export async function GET(request: NextRequest) {
  try {
    // Authenticate the request
    const authResult = await authenticate(request);
    if (authResult.error) {
      return authResult.error;
    }
    const { searchParams } = new URL(request.url);
    const clinicId = searchParams.get("clinic_id");
    const search = searchParams.get("search") || "";

    if (!clinicId) {
      return NextResponse.json(
        { success: false, error: 'Clinic ID is required' },
        { status: 400 }
      );
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const where: any = {
      doctor_clinics: {
        some: {
          clinic_id: parseInt(clinicId)
        }
      }
    };

    if (search) {
      where.OR = [
        {
          profile: {
            display_name: {
              contains: search,
              mode: 'insensitive' as const
            }
          }
        },
        {
          specialization_name: {
            contains: search,
            mode: 'insensitive' as const
          }
        }
      ];
    }

    const doctors = await prisma.doctors.findMany({
      where,
      select: {
        id: true,
        specialization_name: true,
        consultation_fees: true,
        years_of_experience: true,
        profile: {
          select: {
            id: true,
            display_name: true,
            email: true,
            phone: true,
            profile_image: true
          }
        },
        // doctor_clinics: {
        //   include: {
        //     clinic: {
        //       select: {
        //         id: true,
        //         clinic_name: true,
        //         address: true,
        //         city: {
        //           select: {
        //             id: true,
        //             city_name: true,
        //             state_name: true
        //           }
        //         }
        //       }
        //     }
        //   }
        // },
        _count: {
          select: {
            availability_templates: {
              where: {
                is_available: true,
                created_at: {
                  gte: new Date()
                }
              }
            }
          }
        }
      },
      orderBy: { 
        profile: { 
          display_name: 'asc' 
        } 
      }
    });

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(doctors)
    });
  } catch (error) {
    console.error('Error fetching doctors:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch doctors' },
      { status: 500 }
    );
  }
}
