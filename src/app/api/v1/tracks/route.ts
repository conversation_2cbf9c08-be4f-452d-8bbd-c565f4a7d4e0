import { NextResponse } from 'next/server';
import { PrismaClient } from '@/generated/prisma';

const prisma = new PrismaClient();

// GET /api/v1/tracks - Fetch all tracks
export async function GET() {
  try {
    const tracks = await prisma.tracks.findMany({
      orderBy: {
        code: 'asc', // Order by T1, T2, T3
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        tracks,
      }
    });
  } catch (error) {
    console.error('Error fetching tracks:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch tracks' 
      },
      { status: 500 }
    );
  }
} 