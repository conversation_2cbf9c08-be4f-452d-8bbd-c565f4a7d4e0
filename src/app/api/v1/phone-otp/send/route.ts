import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { dbOtpStore } from "@/utils/api/otp/dbOtpStore";
import fakeTwilio from "@/utils/api/otp/fakeTwilio";

const sendOtpSchema = z.object({
  phoneNumber: z
    .string()
    .min(10, "Phone number must be at least 10 digits")
    .regex(/^\+?[1-9]\d{1,14}$/, "Invalid phone number format"),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const result = sendOtpSchema.safeParse(body);

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => issue.message)
        .join(", ");
      return NextResponse.json({ error: errorMessage }, { status: 400 });
    }

    const { phoneNumber } = result.data;

    // Generate OTP and store it
    const otp = dbOtpStore.generateOTP();
    await dbOtpStore.storeOTP(phoneNumber, otp);

    try {
      // Use fake Twilio for development
      // Set default values if environment variables are not set
      const accountSid = process.env.TWILIO_ACCOUNT_SID || "fake_account_sid";
      const authToken = process.env.TWILIO_AUTH_TOKEN || "fake_auth_token";
      const fromNumber = process.env.TWILIO_FROM_NUMBER || "+**********";

      // Initialize fake Twilio client
      const twilio = fakeTwilio(accountSid, authToken);

      // "Send" SMS (fake implementation)
      const message = await twilio.messages.create({
        body: `Your verification code is: ${otp}. This code will expire in 5 minutes.`,
        from: fromNumber,
        to: phoneNumber,
      });

      console.log(
        `[DEVELOPMENT MODE] OTP generated for ${phoneNumber}, SID: ${message.sid}`,
      );

      // Return OTP in response for development purposes
      return NextResponse.json({
        message: "OTP sent successfully",
        success: true,
        // Include OTP in response for development/testing
        otp: otp,
        note: "In development mode - OTP is returned in response. In production, it will be sent via SMS.",
      });
    } catch (error) {
      console.error("Fake Twilio error:", error);

      // Remove the stored OTP if there was an error
      await dbOtpStore.deleteOTP(phoneNumber);

      return NextResponse.json(
        { error: "Failed to generate OTP. Please try again." },
        { status: 500 },
      );
    }
  } catch (error) {
    console.error("Send OTP API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
