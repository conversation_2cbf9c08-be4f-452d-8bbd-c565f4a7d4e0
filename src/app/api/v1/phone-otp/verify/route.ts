import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { dbOtpStore } from "@/utils/api/otp/dbOtpStore";

const verifyOtpSchema = z.object({
  phoneNumber: z
    .string()
    .min(10, "Phone number must be at least 10 digits")
    .regex(/^\+?[1-9]\d{1,14}$/, "Invalid phone number format"),
  code: z
    .string()
    .length(6, "OTP must be 6 digits")
    .regex(/^\d{6}$/, "OTP must contain only numbers"),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const result = verifyOtpSchema.safeParse(body);

    if (!result.success) {
      const errorMessage = result.error.issues
        .map((issue) => issue.message)
        .join(", ");
      return NextResponse.json({ error: errorMessage }, { status: 400 });
    }

    const { phoneNumber, code } = result.data;

    // Verify the OTP using the database store
    const verificationResult = await dbOtpStore.verifyOTP(phoneNumber, code);

    if (!verificationResult.success) {
      let statusCode = 401; // Default to unauthorized

      if (verificationResult.error?.includes("No OTP found")) {
        statusCode = 404;
      } else if (verificationResult.error?.includes("expired")) {
        statusCode = 410;
      } else if (verificationResult.error?.includes("Maximum attempts")) {
        statusCode = 429; // Too many requests
      }

      return NextResponse.json(
        { error: verificationResult.error },
        { status: statusCode },
      );
    }

    // Here you can add additional logic like:
    // - Creating a JWT token
    // - Updating user verification status in database
    // - Logging the successful verification

    console.log(`OTP verified successfully for ${phoneNumber}`);

    return NextResponse.json({
      message: "OTP verified successfully",
      success: true,
      phoneNumber: phoneNumber,
      verifiedAt: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Verify OTP API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 },
    );
  }
}
