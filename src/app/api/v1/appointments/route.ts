import { NextRequest, NextResponse } from "next/server";
import { PrismaClient, ConsultationType, AppointmentStatus, PaymentStatus } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";
import { authenticate } from "@/utils/api/authenticate";

const prisma = new PrismaClient();

// POST - Create a new appointment
export async function POST(request: NextRequest) {
  try {
    // Authenticate the request
    const authResult = await authenticate(request);
    if (authResult.error) {
      return authResult.error;
    }

    const { user } = authResult;
    const body = await request.json();
    const { doctor_id, clinic_id, start_time, end_time, duration, appointment_date } = body;

    // Validate required fields
    if (!doctor_id || !clinic_id || !start_time || !end_time || !duration) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: doctor_id, clinic_id, start_time, end_time, duration' },
        { status: 400 }
      );
    }

    // Get user profile to find patient_id
    const userProfile = await prisma.profiles.findUnique({
      where: { auth_id: user.id },
    });
    console.log('User Profile: ',userProfile);
    if (!userProfile) {
      return NextResponse.json(
        { success: false, error: 'User profile not found' },
        { status: 404 }
      );
    }

    // Check if patient record exists, create if it doesn't
    let patient = await prisma.patients.findUnique({
      where: { id: userProfile.id }
    });

    if (!patient) {
      // Create patient record
      patient = await prisma.patients.create({
        data: {
          id: userProfile.id
        }
      });
      console.log('Created patient record:', patient);
    }

    // Get doctor information to retrieve fees and currency
    const doctor = await prisma.doctors.findUnique({
      where: { id: parseInt(doctor_id) },
      select: {
        consultation_fees: true,
        consultation_currency: true,
        profile: {
          select: {
            display_name: true
          }
        }
      }
    });

    if (!doctor) {
      return NextResponse.json(
        { success: false, error: 'Doctor not found' },
        { status: 404 }
      );
    }

    if (!doctor.consultation_fees || !doctor.consultation_currency) {
      return NextResponse.json(
        { success: false, error: 'Doctor consultation fees not configured' },
        { status: 400 }
      );
    }

    // Verify clinic exists
    const clinic = await prisma.clinics.findUnique({
      where: { id: parseInt(clinic_id) },
      select: { id: true, clinic_name: true }
    });

    if (!clinic) {
      return NextResponse.json(
        { success: false, error: 'Clinic not found' },
        { status: 404 }
      );
    }

    // Parse and validate time data
    let startTimeDate: Date;
    let endTimeDate: Date;
    let appointmentDateOnly: Date;

    // Check if start_time and end_time are ISO timestamps or HH:mm format
    const isISOTimestamp = (timeStr: string) => {
      return timeStr.includes('T') || timeStr.includes('Z') || timeStr.length > 10;
    };

    if (isISOTimestamp(start_time) && isISOTimestamp(end_time)) {
      // Handle ISO timestamp format (e.g., "2025-09-09T02:30:00.000Z")
      startTimeDate = new Date(start_time);
      endTimeDate = new Date(end_time);

      if (isNaN(startTimeDate.getTime()) || isNaN(endTimeDate.getTime())) {
        return NextResponse.json(
          { success: false, error: 'Invalid timestamp format for start_time or end_time' },
          { status: 400 }
        );
      }

      appointmentDateOnly = new Date(startTimeDate);
      appointmentDateOnly.setHours(0, 0, 0, 0);
    } else {
      // Handle HH:mm format (e.g., "09:00", "22:00")
      const timeRegex = /^([0-1]?[0-9]|2[0-3]):([0-5][0-9])$/;

      if (!timeRegex.test(start_time) || !timeRegex.test(end_time)) {
        return NextResponse.json(
          { success: false, error: 'Invalid time format. Expected HH:mm format (e.g., "09:00") or ISO timestamp' },
          { status: 400 }
        );
      }

      // If appointment_date is provided, use it; otherwise use today's date
      // For HH:mm format, we need a date to combine with the time
      const baseDate = appointment_date ? new Date(appointment_date) : new Date();

      if (appointment_date && isNaN(baseDate.getTime())) {
        return NextResponse.json(
          { success: false, error: 'Invalid appointment_date format. Expected YYYY-MM-DD' },
          { status: 400 }
        );
      }

      // Parse HH:mm format and combine with date
      const [startHours, startMinutes] = start_time.split(':').map(Number);
      const [endHours, endMinutes] = end_time.split(':').map(Number);

      startTimeDate = new Date(baseDate);
      startTimeDate.setHours(startHours, startMinutes, 0, 0);

      endTimeDate = new Date(baseDate);
      endTimeDate.setHours(endHours, endMinutes, 0, 0);

      appointmentDateOnly = new Date(baseDate);
      appointmentDateOnly.setHours(0, 0, 0, 0);
    }

    // Validate that start_time is in the future
    const now = new Date();
    if (startTimeDate <= now) {
      return NextResponse.json(
        { success: false, error: 'Appointment time must be in the future' },
        { status: 400 }
      );
    }

    // Validate that end_time is after start_time
    if (endTimeDate <= startTimeDate) {
      return NextResponse.json(
        { success: false, error: 'End time must be after start time' },
        { status: 400 }
      );
    }

    // Check if the time slot is already booked
    const existingAppointment = await prisma.appointments.findFirst({
      where: {
        doctor_id: parseInt(doctor_id),
        clinic_id: parseInt(clinic_id),
        start_time: startTimeDate,
        end_time: endTimeDate,
        status: {
          not: 'cancelled'
        }
      }
    });

    if (existingAppointment) {
      return NextResponse.json(
        { success: false, error: 'Time slot is already booked' },
        { status: 409 }
      );
    }

    const appointmentData = {
        doctor_id: parseInt(doctor_id),
        patient_id: patient.id, // Use the patient.id (BigInt)
        clinic_id: parseInt(clinic_id),
        appointment_date: appointmentDateOnly,
        start_time: startTimeDate,
        end_time: endTimeDate,
        consultation_type: 'in_person' as ConsultationType, // Default to in-person
        status: 'upcoming' as AppointmentStatus,
        duration: parseInt(duration),
        fees: doctor.consultation_fees,
        currency: doctor.consultation_currency,
        payment_status: 'paid' as PaymentStatus, // Set to paid as requested
        booking_date: new Date()
      }

      console.log(appointmentData)

    // Create the appointment
    const appointment = await prisma.appointments.create({
      data: appointmentData,
      include: {
        doctor: {
          include: {
            profile: {
              select: {
                display_name: true,
                email: true
              }
            }
          }
        },
        patient: {
          include: {
            profile: {
              select: {
                display_name: true,
                email: true,
                phone: true
              }
            }
          }
        },
        clinic: {
          select: {
            clinic_name: true,
            address: true,
            city: {
              select: {
                city_name: true,
                state_name: true
              }
            }
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(appointment),
      message: 'Appointment booked successfully'
    });

  } catch (error) {
    console.error('Error creating appointment:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create appointment' },
      { status: 500 }
    );
  }
}

// GET - Fetch user's appointments
export async function GET(request: NextRequest) {
  try {
    // Authenticate the request
    const authResult = await authenticate(request);
    if (authResult.error) {
      return authResult.error;
    }

    const { user } = authResult;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const perPage = parseInt(searchParams.get("per_page") || "10");
    const status = searchParams.get("status") as AppointmentStatus | null;

    // Get user profile to find patient_id
    const userProfile = await prisma.profiles.findUnique({
      where: { auth_id: user.id },
    });

    if (!userProfile) {
      return NextResponse.json(
        { success: false, error: 'User profile not found' },
        { status: 404 }
      );
    }

    // Build where clause
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const where: any = {
      patient_id: userProfile.id
    };

    if (status) {
      where.status = status;
    }

    const [appointments, total] = await Promise.all([
      prisma.appointments.findMany({
        where,
        take: perPage,
        skip: (page - 1) * perPage,
        include: {
          doctor: {
            include: {
              profile: {
                select: {
                  display_name: true,
                  email: true
                }
              }
            }
          },
          clinic: {
            select: {
              clinic_name: true,
              address: true,
              city: {
                select: {
                  city_name: true,
                  state_name: true
                }
              }
            }
          }
        },
        orderBy: { appointment_date: 'desc' }
      }),
      prisma.appointments.count({ where })
    ]);

    return NextResponse.json({
      success: true,
      data: {
        appointments: serializePrismaResponse(appointments),
        total,
        page,
        perPage,
        totalPages: Math.ceil(total / perPage)
      }
    });

  } catch (error) {
    console.error('Error fetching appointments:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch appointments' },
      { status: 500 }
    );
  }
}
