import { NextRequest, NextResponse } from "next/server";
import { PrismaClient, ConsultationType, AppointmentStatus, PaymentStatus } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";
import { authenticate } from "@/utils/api/authenticate";

const prisma = new PrismaClient();

// POST - Create a new appointment
export async function POST(request: NextRequest) {
  try {
    // Authenticate the request
    const authResult = await authenticate(request);
    if (authResult.error) {
      return authResult.error;
    }

    const { user } = authResult;
    const body = await request.json();
    const { doctor_id, clinic_id, start_time, end_time, duration } = body;

    // Validate required fields
    if (!doctor_id || !clinic_id || !start_time || !end_time || !duration) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: doctor_id, clinic_id, start_time, end_time, duration' },
        { status: 400 }
      );
    }

    // Get user profile to find patient_id
    const userProfile = await prisma.profiles.findUnique({
      where: { auth_id: user.id },
    });

    if (!userProfile) {
      return NextResponse.json(
        { success: false, error: 'User profile not found' },
        { status: 404 }
      );
    }

    // Get doctor information to retrieve fees and currency
    const doctor = await prisma.doctors.findUnique({
      where: { id: parseInt(doctor_id) },
      select: {
        consultation_fees: true,
        consultation_currency: true,
        profile: {
          select: {
            display_name: true
          }
        }
      }
    });

    if (!doctor) {
      return NextResponse.json(
        { success: false, error: 'Doctor not found' },
        { status: 404 }
      );
    }

    if (!doctor.consultation_fees || !doctor.consultation_currency) {
      return NextResponse.json(
        { success: false, error: 'Doctor consultation fees not configured' },
        { status: 400 }
      );
    }

    // Verify clinic exists
    const clinic = await prisma.clinics.findUnique({
      where: { id: parseInt(clinic_id) },
      select: { id: true, clinic_name: true }
    });

    if (!clinic) {
      return NextResponse.json(
        { success: false, error: 'Clinic not found' },
        { status: 404 }
      );
    }

    // Parse and validate time data
    const startTimeDate = new Date(start_time);
    const endTimeDate = new Date(end_time);
    const appointmentDate = new Date(startTimeDate);
    appointmentDate.setHours(0, 0, 0, 0); // Set to start of day for appointment_date

    // Validate that start_time is in the future
    const now = new Date();
    if (startTimeDate <= now) {
      return NextResponse.json(
        { success: false, error: 'Appointment time must be in the future' },
        { status: 400 }
      );
    }

    // Check if the time slot is already booked
    const existingAppointment = await prisma.appointments.findFirst({
      where: {
        doctor_id: parseInt(doctor_id),
        clinic_id: parseInt(clinic_id),
        start_time: startTimeDate,
        end_time: endTimeDate,
        status: {
          not: 'cancelled'
        }
      }
    });

    if (existingAppointment) {
      return NextResponse.json(
        { success: false, error: 'Time slot is already booked' },
        { status: 409 }
      );
    }

    // Create the appointment
    const appointment = await prisma.appointments.create({
      data: {
        doctor_id: parseInt(doctor_id),
        patient_id: userProfile.id, // Use profile.id as patient_id
        clinic_id: parseInt(clinic_id),
        appointment_date: appointmentDate,
        start_time: startTimeDate,
        end_time: endTimeDate,
        consultation_type: 'in_person' as ConsultationType, // Default to in-person
        status: 'upcoming' as AppointmentStatus,
        duration: parseInt(duration),
        fees: doctor.consultation_fees,
        currency: doctor.consultation_currency,
        payment_status: 'paid' as PaymentStatus, // Set to paid as requested
        booking_date: new Date()
      },
      include: {
        doctor: {
          include: {
            profile: {
              select: {
                display_name: true,
                email: true
              }
            }
          }
        },
        patient: {
          include: {
            profile: {
              select: {
                display_name: true,
                email: true,
                phone: true
              }
            }
          }
        },
        clinic: {
          select: {
            clinic_name: true,
            address: true,
            city: {
              select: {
                city_name: true,
                state_name: true
              }
            }
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(appointment),
      message: 'Appointment booked successfully'
    });

  } catch (error) {
    console.error('Error creating appointment:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create appointment' },
      { status: 500 }
    );
  }
}

// GET - Fetch user's appointments
export async function GET(request: NextRequest) {
  try {
    // Authenticate the request
    const authResult = await authenticate(request);
    if (authResult.error) {
      return authResult.error;
    }

    const { user } = authResult;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const perPage = parseInt(searchParams.get("per_page") || "10");
    const status = searchParams.get("status") as AppointmentStatus | null;

    // Get user profile to find patient_id
    const userProfile = await prisma.profiles.findUnique({
      where: { auth_id: user.id },
    });

    if (!userProfile) {
      return NextResponse.json(
        { success: false, error: 'User profile not found' },
        { status: 404 }
      );
    }

    // Build where clause
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const where: any = {
      patient_id: userProfile.id
    };

    if (status) {
      where.status = status;
    }

    const [appointments, total] = await Promise.all([
      prisma.appointments.findMany({
        where,
        take: perPage,
        skip: (page - 1) * perPage,
        include: {
          doctor: {
            include: {
              profile: {
                select: {
                  display_name: true,
                  email: true
                }
              }
            }
          },
          clinic: {
            select: {
              clinic_name: true,
              address: true,
              city: {
                select: {
                  city_name: true,
                  state_name: true
                }
              }
            }
          }
        },
        orderBy: { appointment_date: 'desc' }
      }),
      prisma.appointments.count({ where })
    ]);

    return NextResponse.json({
      success: true,
      data: {
        appointments: serializePrismaResponse(appointments),
        total,
        page,
        perPage,
        totalPages: Math.ceil(total / perPage)
      }
    });

  } catch (error) {
    console.error('Error fetching appointments:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch appointments' },
      { status: 500 }
    );
  }
}
