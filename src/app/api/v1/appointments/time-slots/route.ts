import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";
import { authenticate } from "@/utils/api/authenticate";

const prisma = new PrismaClient();

// GET - Fetch available time slots for a doctor based on availability templates and unavailabilities
export async function GET(request: NextRequest) {
  try {
    // Authenticate the request
    const authResult = await authenticate(request);
    if (authResult.error) {
      return authResult.error;
    }
    const { searchParams } = new URL(request.url);
    const doctorId = searchParams.get("doctor_id");
    const clinicId = searchParams.get("clinic_id");
    const startDate = searchParams.get("start_date");
    const endDate = searchParams.get("end_date");

    if (!doctorId) {
      return NextResponse.json(
        { success: false, error: 'Doctor ID is required' },
        { status: 400 }
      );
    }

    // Default to next 7 days if no date range provided
    const start = startDate ? new Date(startDate) : new Date();
    const end = endDate ? new Date(endDate) : (() => {
      const endDate = new Date(start);
      endDate.setDate(start.getDate() + 6); // Add 6 days to start date, not current time
      return endDate;
    })();

    // Ensure we're looking at future dates only
    const now = new Date();
    if (start < now) {
      start.setTime(now.getTime());
    }

    const doctorIdInt = parseInt(doctorId);

    // Build where clause for templates
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const whereClause: any = {
      doctor_id: doctorIdInt,
      is_available: true // Only fetch available templates
    };

    if (clinicId) {
      whereClause.clinic_id = parseInt(clinicId);
    }
    console.log('Where Clause: ', whereClause)

    // Fetch doctor's availability templates
    const templates = await prisma.availability_templates.findMany({
      where: whereClause,
      include: {
        doctor: {
          select: {
            profile: {
              select: {
                display_name: true
              }
            }
          }
        },
        clinic: {
          select: {
            id: true,
            clinic_name: true,
            address: true,
            city: {
              select: {
                id: true,
                city_name: true,
                state_name: true,
              },
            },
          },
        },
      },
      orderBy: [
        { clinic_id: 'asc' },
        { day_of_week: 'asc' },
        { start_time: 'asc' }
      ]
    });

    if (templates.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          slots: [],
          grouped_by_date: {}
        }
      });
    }

    // Fetch doctor's unavailabilities for the date range
    // Convert date strings to DateTime objects for proper querying
    const startOfDay = new Date(start);
    startOfDay.setHours(0, 0, 0, 0);

    const endOfDay = new Date(end);
    endOfDay.setHours(23, 59, 59, 999);

    const unavailabilities = await prisma.unavailabilities.findMany({
      where: {
        doctor_id: doctorIdInt,
        date: {
          gte: startOfDay,
          lte: endOfDay
        }
      },
      orderBy: [
        { date: 'asc' },
        { start_time: 'asc' }
      ]
    });

    console.log('Date Range Debug:')
    console.log('  Start Date: ', start.toISOString())
    console.log('  End Date: ', end.toISOString())
    console.log('  Start of Day: ', startOfDay.toISOString())
    console.log('  End of Day: ', endOfDay.toISOString())
    console.log('  Now: ', now.toISOString())
    console.log('  Date Range Valid: ', start <= end)
    console.log('Templates found: ', templates.length)
    console.log('Unavailabilities: ', unavailabilities.length)

    // Create a map of unavailabilities by date for quick lookup
    const unavailabilityMap = new Map<string, Array<typeof unavailabilities[0]>>();
    unavailabilities.forEach(unavailability => {
      const dateKey = unavailability.date instanceof Date
        ? unavailability.date.toISOString().split('T')[0]
        : unavailability.date;
      if (!unavailabilityMap.has(dateKey)) {
        unavailabilityMap.set(dateKey, []);
      }
      unavailabilityMap.get(dateKey)!.push(unavailability);
    });

      console.log('Unavailability Map: ', unavailabilityMap)

    // Generate time slots from templates for the requested date range
    const generatedSlots = [];

    // Generate slots for each day in the requested range
    console.log('Starting slot generation for date range...')
    for (let currentDate = new Date(start); currentDate <= end; currentDate.setDate(currentDate.getDate() + 1)) {
      const dayOfWeek = currentDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
      const dateKey = currentDate.toISOString().split('T')[0];
      console.log(`Processing date: ${dateKey} (day of week: ${dayOfWeek})`);

      // Check if doctor has any unavailabilities for this date
      const dayUnavailabilities = unavailabilityMap.get(dateKey) || [];

      // If doctor is unavailable for the full day, skip generating slots
      const hasFullDayUnavailability = dayUnavailabilities.some(unavailability =>
        unavailability.start_time === '00:00:00' && unavailability.end_time === '23:59:00'
      );

      if (hasFullDayUnavailability) {
        continue; // Skip this entire day
      }

      // Find templates for this day of week
      const dayTemplates = templates.filter(template => template.day_of_week === dayOfWeek);
      console.log(`  Found ${dayTemplates.length} templates for day ${dayOfWeek}`);

      for (const template of dayTemplates) {
        // Parse time strings to check if slot is in the future
        const [startHours, startMinutes] = template.start_time.split(':').map(Number);
        const [endHours, endMinutes] = template.end_time.split(':').map(Number);

        const startDateTime = new Date(currentDate);
        startDateTime.setHours(startHours, startMinutes, 0, 0);

        const endDateTime = new Date(currentDate);
        endDateTime.setHours(endHours, endMinutes, 0, 0);

        // Only include future time slots
        if (startDateTime >= now) {
          // Check if this time slot conflicts with any unavailabilities
          const hasConflict = dayUnavailabilities.some(unavailability => {
            const unavailStartTime = unavailability.start_time;
            const unavailEndTime = unavailability.end_time;
            const templateStartTime = template.start_time;
            const templateEndTime = template.end_time;

            // Check for time overlap
            return (
              (templateStartTime >= unavailStartTime && templateStartTime < unavailEndTime) ||
              (templateEndTime > unavailStartTime && templateEndTime <= unavailEndTime) ||
              (templateStartTime <= unavailStartTime && templateEndTime >= unavailEndTime)
            );
          });

          // Only add slot if there's no conflict with unavailabilities
          if (!hasConflict) {
            // Convert HH:mm format to full timestamp
            const [startHours, startMinutes] = template.start_time.split(':').map(Number);
            const [endHours, endMinutes] = template.end_time.split(':').map(Number);

            const startDateTime = new Date(currentDate);
            startDateTime.setHours(startHours, startMinutes, 0, 0);

            const endDateTime = new Date(currentDate);
            endDateTime.setHours(endHours, endMinutes, 0, 0);

            generatedSlots.push({
              date: new Date(currentDate),
              start_time: startDateTime.toISOString(), // Convert to ISO timestamp
              end_time: endDateTime.toISOString(),     // Convert to ISO timestamp
              duration: template.duration
            });
          }
        }
      }
    }

    console.log('Generated Slots: ', generatedSlots)

    // Transform to match the expected format
    const timeSlots = generatedSlots.map((slot, index) => ({
      id: index + 1, // Generate temporary IDs since we're not storing these
      date: slot.date,
      start_time: slot.start_time,
      end_time: slot.end_time,
      duration: slot.duration
    }));

    // Create a complete date range with all dates, even if no slots available
    const dateRangeSlots = [];
    const allDates = [];
    
    for (let currentDate = new Date(start); currentDate <= end; currentDate.setDate(currentDate.getDate() + 1)) {
      const dateKey = currentDate.toISOString().split('T')[0];
      allDates.push(dateKey);
      
      // Check doctor's unavailability status for this date
      const dayUnavailabilities = unavailabilityMap.get(dateKey) || [];
      
      // Determine status based on unavailability
      let status = 'available'; // Default status
      if (dayUnavailabilities.length > 0) {
        // Check if there's a full-day unavailability
        const hasFullDayUnavailability = dayUnavailabilities.some(unavailability =>
          unavailability.start_time === '00:00' && unavailability.end_time === '23:59'
        );
        
        status = hasFullDayUnavailability ? 'unavailable' : 'available';
      }
            
      // Find slots for this specific date
      const dateSlots = timeSlots.filter(slot => 
        slot.date.toISOString().split('T')[0] === dateKey
      );
      
      // Transform slots to frontend-friendly format
      const availableSlots = dateSlots.map(slot => {
        // Convert ISO timestamp to 12-hour format with AM/PM for display
        const formatTimestampTo12Hour = (isoTimestamp: string) => {
          const date = new Date(isoTimestamp);
          const hours = date.getHours();
          const minutes = date.getMinutes();
          const period = hours >= 12 ? 'PM' : 'AM';
          const hours12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
          return `${hours12}:${minutes.toString().padStart(2, '0')} ${period}`;
        };

        return {
          id: slot.id,
          start_time: slot.start_time, // ISO timestamp format
          start_time_display: formatTimestampTo12Hour(slot.start_time), // Convert to 12-hour format
          end_time: slot.end_time, // ISO timestamp format
          end_time_display: formatTimestampTo12Hour(slot.end_time), // Convert to 12-hour format
          duration: slot.duration
        };
      });
      
      dateRangeSlots.push({
        date: dateKey,
        status: status,
        available_slots: availableSlots
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        date_range: {
          start_date: start.toISOString().split('T')[0],
          end_date: end.toISOString().split('T')[0]
        },
        total_slots: timeSlots.length,
        date_slots: serializePrismaResponse(dateRangeSlots)
      }
    });
  } catch (error) {
    console.error('Error fetching time slots from templates:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch time slots' },
      { status: 500 }
    );
  }
}
