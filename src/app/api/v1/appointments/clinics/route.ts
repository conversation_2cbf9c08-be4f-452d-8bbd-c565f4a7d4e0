import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/generated/prisma";
import { serializePrismaResponse } from "@/lib/utils/prisma-serializer";
import { authenticate } from "@/utils/api/authenticate";

const prisma = new PrismaClient();

// GET - Fetch clinics by city (appointment booking)
export async function GET(request: NextRequest) {
  try {
    // Authenticate the request
    const authResult = await authenticate(request);
    if (authResult.error) {
      return authResult.error;
    }
    const { searchParams } = new URL(request.url);
    const cityId = searchParams.get("city_id");
    const search = searchParams.get("search") || "";

    if (!cityId) {
      return NextResponse.json(
        { success: false, error: 'City ID is required' },
        { status: 400 }
      );
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const where: any = {
      city_id: parseInt(cityId),
      // Only include clinics that have doctors
      doctor_clinics: {
        some: {}
      }
    };

    if (search) {
      where.OR = [
        {
          clinic_name: {
            contains: search,
            mode: 'insensitive' as const
          }
        },
        {
          address: {
            contains: search,
            mode: 'insensitive' as const
          }
        }
      ];
    }

    const clinics = await prisma.clinics.findMany({
      where,
      select: {
        id: true,
        clinic_name: true,
        address: true,
        contact_info: true,
        latitude: true,
        longitude: true,
        city: {
          select: {
            id: true,
            city_name: true,
            state_name: true
          }
        },
        _count: {
          select: {
            doctor_clinics: true
          }
        }
      },
      orderBy: { clinic_name: 'asc' }
    });

    return NextResponse.json({
      success: true,
      data: serializePrismaResponse(clinics)
    });
  } catch (error) {
    console.error('Error fetching clinics:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch clinics' },
      { status: 500 }
    );
  }
}
