import { NextRequest } from "next/server";
import { apiResponse } from "@/utils/api/apiResponse";
import { getDietPlanContext, getDietPlanData, saveDietPlanData } from "@/utils/api/dietPlanDataUtils";
import { 
  getFormQuestions, 
  validateFormData
} from "@/utils/api/formDataUtils";
import { determineDietPlanStatus } from '@/utils/api/dietPlanDataUtils';

/**
 * GET /api/v1/fertility-diet-plan/form
 * Retrieve diet plan form data for authenticated users only
 */
export async function GET(req: NextRequest) {
  try {
    const { context, error } = await getDietPlanContext(req);
    if (error) return error;

    const { success, data, error: dataError } = await getDietPlanData(context!);

    if (!success) {
      return apiResponse(500, dataError || "Failed to retrieve data");
    }

    if (!data) {
      return apiResponse(200, undefined, {
        formData: null,
        stepCompleted: false,
        status: 'completed' as const,
        isAuthenticated: context!.isAuthenticated
      });
    }

    const hasData = true;

    // Return all form data
    const formData = {
      id: data.id,
      ...data.form_data,
      current_step: data.current_step,
      created_at: data.created_at,
      updated_at: data.updated_at,
    };
    
    const stepCompleted = data.current_step >= 1;

    // Determine status based on current_step
    const status = await determineDietPlanStatus(data.current_step);

    return apiResponse(200, undefined, {
      formData,
      stepCompleted,
      status,
      isAuthenticated: context!.isAuthenticated,
      currentStep: data.current_step,
      hasData
    });
  } catch (error) {
    console.error("GET form error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * POST /api/v1/fertility-diet-plan/form
 * Create or update diet plan form data for authenticated users only
 */
export async function POST(req: NextRequest) {
  try {
    const { context, error } = await getDietPlanContext(req);
    if (error) return error;

    const body = await req.json();
    const stepNumber = 1; // For diet plan, we only have step 1
    
    // Get questions for diet plan to validate against
    const questionsResult = await getFormQuestions(['diet-plan']);
    
    if (!questionsResult.success) {
      return apiResponse(500, "Failed to fetch questions for validation");
    }
    
    // Validate the form data against questions for diet plan
    const validation = await validateFormData(['diet-plan'], body);
    
    if (!validation.success) {
      return apiResponse(400, validation.errors?.join(", ") || "Invalid form data");
    }

    // Get existing data to merge with new data
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { success: getSuccess, data: existingData } = await getDietPlanData(context!);
    
    // For diet plan, we only have step 1, so always start fresh or merge with existing data
    const formData = existingData?.form_data || {};
    const updatedFormData = { ...formData, ...body };
    
    const { success, data, error: saveError } = await saveDietPlanData(
      context!,
      updatedFormData,
      stepNumber
    );

    if (!success) {
      return apiResponse(500, saveError || `Failed to save Fertility Diet Plan data`);
    }

    // Determine status based on current step
    const status = await determineDietPlanStatus(stepNumber);

    // Prepare response data
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const responseData: any = {
      dietPlanData: data,
      status,
      isAuthenticated: context!.isAuthenticated
    };

    // Add step-specific response data
    // For diet plan, we only have step 1, so it's always completed
    responseData.allStepsCompleted = true;
    responseData.nextAction = "bmr_calculation";

    return apiResponse(200, `Fertility Diet Plan data saved successfully`, responseData);
  } catch (error) {
    console.error("POST form error:", error);
    return apiResponse(500, "Internal server error");
  }
}

/**
 * PUT /api/v1/fertility-diet-plan/form
 * Update diet plan form data for authenticated users only
 */
export async function PUT(req: NextRequest) {
  try {
    const { context, error } = await getDietPlanContext(req);
    if (error) return error;

    const body = await req.json();
    
    // Get questions for diet plan to validate against
    const questionsResult = await getFormQuestions(['diet-plan']);
    
    if (!questionsResult.success) {
      return apiResponse(500, "Failed to fetch questions for validation");
    }
    
    // Validate the form data against questions for diet plan
    const validation = await validateFormData(['diet-plan'], body);
    
    if (!validation.success) {
      return apiResponse(400, validation.errors?.join(", ") || "Invalid form data");
    }

    // Get existing data to merge with updates
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { success: getSuccess, data: existingData } = await getDietPlanData(context!);

    if (!getSuccess || !existingData) {
      return apiResponse(404, "Diet plan data not found. Use POST to create.");
    }

    // For diet plan, we only have step 1, so always merge with existing data
    const formData = existingData.form_data || {};
    const updatedFormData = { ...formData, ...body };
    
    const { success, data, error: saveError } = await saveDietPlanData(
      context!,
      updatedFormData,
      1 // For diet plan, we only have step 1
    );

    if (!success) {
      return apiResponse(500, saveError || `Failed to update diet plan data`);
    }

    // Determine status based on current step
    const status = await determineDietPlanStatus(existingData.current_step || 1);

    return apiResponse(200, `Diet plan data updated successfully`, {
      dietPlanData: data,
      status,
      isAuthenticated: context!.isAuthenticated
    });
  } catch (error) {
    console.error("PUT form error:", error);
    return apiResponse(500, "Internal server error");
  }
}


/**
 * DELETE /api/v1/fertility-diet-plan/form
 * Delete user's diet plan score data
 */
export async function DELETE(req: NextRequest) {
  try {
    const { context, error } = await getDietPlanContext(req);
    if (error) return error;

    if (!context?.user) {
      return apiResponse(401, "Unauthorized");
    }

    const { PrismaClient } = await import("@/generated/prisma");
    const prisma = new PrismaClient();

    try {
      await prisma.diet_plan_scores.delete({
        where: { user_id: context.user.id },
      });
      return apiResponse(200, "Diet plan scores deleted successfully");

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (deleteError: any) {
      if (deleteError.code === "P2025") {
        // Record not found
        return apiResponse(404, "Diet plan scores not found");
      }
      console.error("DELETE diet_plan_scores error:", deleteError);
      return apiResponse(500, "Failed to delete diet plan scores");
    }
  } catch (error) {
    console.error("DELETE form error:", error);
    return apiResponse(500, "Internal server error");
  }
}
