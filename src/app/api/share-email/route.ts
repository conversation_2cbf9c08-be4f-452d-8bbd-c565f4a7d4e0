import { NextRequest } from 'next/server';
import { authenticate } from '@/utils/api/authenticate';
import { emailService } from '@/lib/services/email.service';
import { emailRateLimiter } from '@/utils/rateLimit';
import { validateBase64, base64To<PERSON>uffer, getMimeTypeFromBase64 } from '@/utils/base64Validation';
import { PrismaClient } from '@/generated/prisma';
import { apiResponse } from '@/utils/api/apiResponse';

const prisma = new PrismaClient();

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { imageBase64, guestSessionToken } = body;

    // Validate required fields
    if (!imageBase64) {
      return apiResponse(400, 'Image data is required');
    }

    // Validate base64 string
    const validation = validateBase64(imageBase64);
    if (!validation.isValid) {
      return apiResponse(400, `Invalid image data: ${validation.error}`);
    }

    // Get user email and identifier for rate limiting
    let userEmail: string | undefined = undefined;
    let rateLimitIdentifier: string;

    // Check if user is authenticated
    const authResult = await authenticate(request);
    if (authResult.user) {
      // Authenticated user
      userEmail = authResult.user.email;
      rateLimitIdentifier = `auth_${authResult.user.id}`;
    } else if (guestSessionToken) {
      // Guest user
      const guestSession = await prisma.guest_sessions.findUnique({
        where: { session_token: guestSessionToken },
        select: { email: true, id: true }
      });

      if (!guestSession || !guestSession.email) {
        return apiResponse(400, 'Invalid guest session or email not found');
      }

      userEmail = guestSession.email;
      rateLimitIdentifier = `guest_${guestSession.id}`;
    } else {
      return apiResponse(401, 'Authentication required or valid guest session token');
    }

    // Check rate limiting
    if (!emailRateLimiter.isAllowed(rateLimitIdentifier)) {
      const remainingAttempts = emailRateLimiter.getRemainingAttempts(rateLimitIdentifier);
      const resetTime = emailRateLimiter.getResetTime(rateLimitIdentifier);
      
      return apiResponse(429, `Rate limit exceeded. You can send ${remainingAttempts} more emails today. Limit resets at ${new Date(resetTime!).toLocaleString()}`);
    }

    // Convert base64 to buffer for attachment
    const imageBuffer = base64ToBuffer(imageBase64);
    if (!imageBuffer) {
      return apiResponse(400, 'Failed to process image data');
    }

    // Get MIME type
    const mimeType = getMimeTypeFromBase64(imageBase64) || 'image/png';

    // Prepare email content
    const subject = 'Your Fertility Assessment Results - GIVF';
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <h1 style="color: #333; text-align: center;">Your Fertility Assessment Results</h1>
        <p style="color: #666; line-height: 1.6;">
          Thank you for completing your fertility assessment with GIVF. 
          Please find your detailed results attached to this email.
        </p>
        <p style="color: #666; line-height: 1.6;">
          If you have any questions about your results or would like to discuss them with our fertility specialists, 
          please don't hesitate to reach out to us.
        </p>
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #333; margin-top: 0;">Next Steps:</h3>
          <ul style="color: #666; line-height: 1.6;">
            <li>Review your results carefully</li>
            <li>Book a free consultation with our specialists</li>
            <li>Explore our fertility treatment options</li>
            <li>Contact us for personalized guidance</li>
          </ul>
        </div>
        <p style="color: #666; line-height: 1.6; text-align: center;">
          <strong>GIVF Team</strong><br>
          Your trusted partner in fertility care
        </p>
      </div>
    `;

    // Send email with attachment
    const emailSent = await emailService.sendEmailWithAttachment({
      to: userEmail || '',
      subject,
      html,
      attachments: [{
        filename: 'fertility-assessment-results.png',
        content: imageBuffer,
        contentType: mimeType
      }]
    });

    if (!emailSent) {
      return apiResponse(500, 'Failed to send email');
    }

    return apiResponse(200, 'Email sent successfully');

  } catch (error) {
    console.error('Share email error:', error);
    return apiResponse(500, 'Internal server error');
  }
} 