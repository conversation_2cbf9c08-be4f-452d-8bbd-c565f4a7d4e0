"use client";

import { useBreadcrumb } from "@/contexts/BreadcrumbContext";
import { usePageHeader } from "@/contexts/PageHeaderContext";
import { useEffect } from "react";

export default function Page() {
  const { setTitle, setSubtitle } = usePageHeader();
  const { setBreadcrumbs } = useBreadcrumb();

  useEffect(() => {
    setTitle("Good News Wall");
    setSubtitle("Celebrate this week's miracle babies from the GIVF family!");
    setBreadcrumbs(null);
  }, [setTitle, setSubtitle, setBreadcrumbs]);
  return <div>Good News Wall</div>;
}
