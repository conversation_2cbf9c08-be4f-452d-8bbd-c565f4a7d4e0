"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { usePageHeader } from "@/contexts/PageHeaderContext";
import { useBreadcrumb } from "@/contexts/BreadcrumbContext";
import { useAppointmentBooking as useAppointmentBookingContext } from "@/contexts/AppointmentBookingContext";
import AppointmentModal, { AppointmentModalType } from "@/components/shared/Appointments/AppointmentModal/AppointmentModal";
import { useDoctorDetails } from "@/hooks/useDoctorDetails";
import { useGetClinic } from "@/hooks/useGetClinic";
import { useGetCity } from "@/hooks/useGetCity";
import { useAppointmentBooking } from "@/hooks/useAppointmentBooking";

interface AppointmentData {
  doctorImageUrl: string;
  doctorName: string;
  clinicName: string;
  clinicLocation: string;
  appointmentDate: string;
  appointmentTime: string;
  consultationFee: number;
}

const AppointmentConfirmationPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { setTitle, setSubtitle } = usePageHeader();
  const { setBreadcrumbs } = useBreadcrumb();

  // Get appointment data from context
  const { appointmentData } = useAppointmentBookingContext();

  // State
  const [isModalOpen, setIsModalOpen] = useState(true);
  const [modalType, setModalType] = useState<AppointmentModalType>(AppointmentModalType.CONFIRM);
  
  // Use appointment booking hook
  const { bookAppointment, isBooking } = useAppointmentBooking();

  // Fetch data using context data
  const { doctor, loading: doctorLoading } = useDoctorDetails(appointmentData?.doctor_id || 0);
  const clinicName = useGetClinic(appointmentData?.clinic_id || 0);
  const cityName = useGetCity(appointmentData?.city_id || 0);

  // Set page header and breadcrumbs
  useEffect(() => {
    setTitle("Confirm Appointment");
    setSubtitle("Review and confirm your appointment details");
    
    setBreadcrumbs([
      { label: "Home", href: "/user" },
      { label: "Schedule Appointments", href: "/user/schedule-appointments" },
      { label: "Confirm Appointment", href: "#" }
    ]);
  }, [setTitle, setSubtitle, setBreadcrumbs]);

  // Don't render if no required data
  if (!appointmentData) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Missing Information</h2>
          <p className="text-gray-500">Please go back and select all required appointment details.</p>
        </div>
      </div>
    );
  }

  // Prepare appointment data
  const appointmentDisplayData: AppointmentData = {
    doctorImageUrl: doctor?.profile?.profile_image || "/assets/avatar.jpg",
    doctorName: doctor?.profile?.display_name || appointmentData.doctor_name || "Dr. Unknown",
    clinicName: clinicName || appointmentData.clinic_name || "Clinic",
    clinicLocation: cityName || appointmentData.city_name || "Location",
    appointmentDate: new Date(appointmentData.date).toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    }),
    appointmentTime: `${appointmentData.slot.start_time_display} - ${appointmentData.slot.end_time_display}`,
    consultationFee: doctor?.consultation_fees || 0,
  };

  // Handle appointment booking
  const handleConfirmAppointment = async () => {
    if (!doctor || !appointmentData) {
      return;
    }

    try {
      // Use slot times directly from context (already in ISO format)
      const result = await bookAppointment({
        doctor_id: appointmentData.doctor_id,
        clinic_id: appointmentData.clinic_id,
        start_time: appointmentData.slot.start_time,
        end_time: appointmentData.slot.end_time,
        duration: appointmentData.slot.duration,
      });

      if (result.success) {
        // Show success modal without closing
        setModalType(AppointmentModalType.CONFIRMED);
      } else {
        alert(`Failed to book appointment: ${result.error}`);
      }
      
    } catch (error) {
      console.error("Error booking appointment:", error);
      alert(`Failed to book appointment: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    // Navigate back to appointments list or dashboard
    router.push("/user/appointments");
  };

  const handleBack = () => {
    // Go back to date-time selection
    router.back();
  };

  if (doctorLoading) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Loading appointment details...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full flex items-start justify-center px-5 md:px-8 xl:px-10">
      {/* Appointment Confirmation Modal */}
      <AppointmentModal
        isOpen={isModalOpen}
        type={modalType}
        appointmentData={appointmentDisplayData}
        onClose={handleModalClose}
        onConfirm={handleConfirmAppointment}
        onBack={handleBack}
        isLoading={isBooking}
      />
    </div>
  );
};

export default AppointmentConfirmationPage;
