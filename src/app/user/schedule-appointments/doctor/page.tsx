"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import SelectDoctor from "@/components/shared/Appointments/ScheduleAppointments/SelectDoctor/SelectDoctor";

const DoctorSelectionPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selectedDoctorId, setSelectedDoctorId] = useState<
    string | undefined
  >();
  const [selectedDoctorName, setSelectedDoctorName] = useState<string>("");

  // Get clinic and city data from URL parameters
  const cityId = searchParams.get("cityId");
  const cityName = searchParams.get("cityName");
  const clinicId = searchParams.get("clinicId");
  const clinicName = searchParams.get("clinicName");

  useEffect(() => {
    // Redirect if no clinic data is provided
    if (!cityId || !cityName || !clinicId || !clinicName) {
      router.push("/user/schedule-appointments/city");
    }
  }, [cityId, cityName, clinicId, clinicName, router]);

  const handleDoctorSelect = (doctorId: string, doctorName: string) => {
    setSelectedDoctorId(doctorId);
    setSelectedDoctorName(doctorName);

    // Navigate to the next step (time selection or appointment confirmation)
    // You can modify this URL based on your next step in the appointment flow
    router.push(
      `/user/schedule-appointments/date-time?cityId=${cityId}&cityName=${encodeURIComponent(cityName || "")}&clinicId=${clinicId}&clinicName=${encodeURIComponent(clinicName || "")}&doctorId=${doctorId}&doctorName=${encodeURIComponent(doctorName)}`
    );
  };

  const handleBack = () => {
    // Navigate back to clinic selection page with city data
    router.push(
      `/user/schedule-appointments/clinic?cityId=${cityId}&cityName=${encodeURIComponent(cityName || "")}`
    );
  };

  // Don't render if no clinic data
  if (!cityId || !cityName || !clinicId || !clinicName) {
    return null;
  }

  return (
    <div className="w-full h-full flex items-start justify-center py-25">
      <SelectDoctor
        centerId={parseInt(clinicId)}
        onDoctorSelect={handleDoctorSelect}
        onBack={handleBack}
        selectedDoctorId={selectedDoctorId}
      />
    </div>
  );
};

export default DoctorSelectionPage;
