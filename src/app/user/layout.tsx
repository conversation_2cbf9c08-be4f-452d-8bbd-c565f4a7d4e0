"use client";
import React, { useEffect, useState } from "react";
import Sidebar from "@/components/shared/Sidebar/Sidebar";
import {
  PageHeaderProvider,
  usePageHeader,
} from "@/contexts/PageHeaderContext";
import {
  BreadcrumbProvider,
  useBreadcrumb,
} from "@/contexts/BreadcrumbContext";
import { useScreenWidth } from "@/hooks/useScreenWidth";
import {
  BabyIcon,
  BowlFoodIcon,
  CalendarCheckIcon,
  ChartDonutIcon,
  HandHeartIcon,
  IslandIcon,
  PersonSimpleTaiChiIcon,
  SquaresFourIcon,
  TextIndentIcon,
  UsersIcon,
} from "@phosphor-icons/react";
import Avatar from "@/components/shared/Avatar/Avatar";
import NotificationButton from "@/components/Notification/NotificationButton/NotificationButton";
import PopUpSidebar from "@/components/shared/Sidebar/PopUpSidebar/PopUpSidebar";
import UserProfileDropdown from "@/components/shared/UserProfileDropdown/UserProfileDropdown";
import { usePathname, useRouter } from "next/navigation";
import UserProfileSlider from "@/components/shared/UserProfileSlider";
import Image from "next/image";
import { useAuth } from "@/contexts/AuthContext";
import { useUserProfile } from "@/lib/services/client/user.service";
import PageLoader from "@/components/shared/PageLoader";
import NotificationDropdown from "@/components/Notification/NotificationDropdown";
import { NotificationTrayIcons } from "@/components/Notification/NotificationTray";
import { NotificationItemData } from "@/components/Notification/NotificationItem";
import NotificationSlider from "@/components/Notification/NotificationSlider/NotificationSlider";
import Breadcrumb from "@/components/shared/Breadcrumb/Breadcrumb";

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <PageHeaderProvider>
      <BreadcrumbProvider>
        <LayoutContent>{children}</LayoutContent>
      </BreadcrumbProvider>
    </PageHeaderProvider>
  );
}

// const sidebarItems = [
//   {
//     icon: <SquaresFourIcon size={20} />,
//     title: "Dashboard",
//     onClick: () => console.log("Dashboard clicked"),
//   },
//   {
//     icon: <ChartDonutIcon size={20} />,
//     title: "IVF Success Score",
//     onClick: () => console.log("IVF Success Score clicked"),
//   },
//   {
//     icon: <IslandIcon size={20} />,
//     title: "IVF Journey",
//     onClick: () => console.log("IVF Journey clicked"),
//   },
//   {
//     icon: <CalendarCheckIcon size={20} />,
//     title: "My Appointments",
//     onClick: () => console.log("Appointments clicked"),
//   },
//   {
//     icon: <PersonSimpleTaiChiIcon size={20} />,
//     title: "Wellness & Lifestyle",
//     onClick: () => console.log("Wellness & Lifestyle clicked"),
//   },
//   {
//     icon: <BowlFoodIcon size={20} />,
//     title: "Fertility Diet Plan",
//     onClick: () => console.log("Fertility Diet Plan clicked"),
//   },
//   {
//     icon: <BabyIcon size={20} />,
//     title: "Good News Wall",
//     onClick: () => console.log("Good News Wall clicked"),
//   },
//   {
//     icon: <HandHeartIcon size={20} />,
//     title: "Period Tracker",
//     onClick: () => console.log("Period Tracker clicked"),
//   },
//   {
//     icon: <UsersIcon size={20} />,
//     title: "Community",
//     onClick: () => console.log("Community clicked"),
//   },
// ];

function LayoutContent({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const pathname = usePathname();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [isProfileDropdownOpen, setIsProfileDropdownOpen] = useState(false);
  const [isNotificationDropdownOpen, setIsNotificationDropdownOpen] =
    useState(false);
  const [isUserProfileSliderOpen, setIsUserProfileSliderOpen] = useState(false);
  const { user, isLoading, signOut } = useAuth();
  const { data: profile } = useUserProfile();
  const screenWidth = useScreenWidth();
  const { title, subtitle } = usePageHeader();
  const { breadcrumbs } = useBreadcrumb();

  useEffect(() => {
    if (!isLoading && !user) {
      router.push(`/login?redirect=${pathname}`);
    }
  }, [user, isLoading, router, pathname]);

  useEffect(() => {
    if (isSidebarOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }

    return () => {
      document.body.style.overflow = "auto";
    };
  }, [isSidebarOpen]);
  const [isNotificationSliderOpen, setIsNotificationSliderOpen] =
    useState(false);

  const notifications: NotificationItemData[] = [
    {
      id: "1",
      title: "Appointment Confirmed",
      description:
        "Your appointment with Dr. Neha Sharma is scheduled for 27 June at 10:00 AM.",
      timestamp: "2 hours ago",
      icon: NotificationTrayIcons.appointment,
      isUnread: true,
      type: "appointment",
    },
    {
      id: "2",
      title: "Stage Unlocked",
      description:
        "You've unlocked Stage 4 — Embryo Growth in your IVF journey.",
      timestamp: "Yesterday",
      icon: NotificationTrayIcons.stage,
      isUnread: true,
      type: "stage",
    },
    {
      id: "3",
      title: "New Diet Plan Available",
      description:
        "Your personalized 7-day diet plan is now ready to download.",
      timestamp: "2 days ago",
      icon: NotificationTrayIcons.diet,
      isUnread: false,
      type: "diet",
    },
    {
      id: "4",
      title: "Good News Wall Update",
      description:
        "Baby Aarav's IVF story has been added to the Good News Wall.",
      timestamp: "3 days ago",
      icon: NotificationTrayIcons.news,
      isUnread: false,
      type: "news",
    },
    {
      id: "5",
      title: "Wellness Tip of the Day",
      description: '"Breathe. Believe. Receive." Your daily quote is ready.',
      timestamp: "3 days ago",
      icon: NotificationTrayIcons.wellness,
      isUnread: false,
      type: "wellness",
    },
    {
      id: "6",
      title: "Good News Wall Update",
      description:
        "Baby Aarav's IVF story has been added to the Good News Wall.",
      timestamp: "3 days ago",
      icon: NotificationTrayIcons.news,
      isUnread: false,
      type: "news",
    },
    {
      id: "7",
      title: "Wellness Tip of the Day",
      description: '"Breathe. Believe. Receive." Your daily quote is ready.',
      timestamp: "3 days ago",
      icon: NotificationTrayIcons.wellness,
      isUnread: false,
      type: "wellness",
    },
    {
      id: "8",
      title: "Good News Wall Update",
      description:
        "Baby Aarav's IVF story has been added to the Good News Wall.",
      timestamp: "3 days ago",
      icon: NotificationTrayIcons.news,
      isUnread: false,
      type: "news",
    },
    {
      id: "9",
      title: "Wellness Tip of the Day",
      description: '"Breathe. Believe. Receive." Your daily quote is ready.',
      timestamp: "3 days ago",
      icon: NotificationTrayIcons.wellness,
      isUnread: false,
      type: "wellness",
    },
    {
      id: "10",
      title: "Good News Wall Update",
      description:
        "Baby Aarav's IVF story has been added to the Good News Wall.",
      timestamp: "3 days ago",
      icon: NotificationTrayIcons.news,
      isUnread: false,
      type: "news",
    },
    {
      id: "11",
      title: "Wellness Tip of the Day",
      description: '"Breathe. Believe. Receive." Your daily quote is ready.',
      timestamp: "3 days ago",
      icon: NotificationTrayIcons.wellness,
      isUnread: false,
      type: "wellness",
    },
  ];

  const onItemClick = (pathname: string) => {
    setIsSidebarOpen(false);
    router.push(pathname);
  };

  const sidebarItems = [
    {
      icon: <SquaresFourIcon size={20} />,
      title: "Dashboard",
      pathname: "/user/dashboard",
      onClick: () => onItemClick("/user/dashboard"),
    },
    {
      icon: <ChartDonutIcon size={20} />,
      title: "IVF Success Score",
      pathname: "/user/ivf-success-score",
      onClick: () => onItemClick("/user/ivf-success-score"),
    },
    {
      icon: <IslandIcon size={20} />,
      title: "IVF Journey",
      pathname: "/user/ivf-journey",
      onClick: () => onItemClick("/user/ivf-journey"),
    },
    {
      icon: <CalendarCheckIcon size={20} />,
      title: "My Appointments",
      pathname: "/user/appointments",
      onClick: () => onItemClick("/user/appointments"),
    },
    {
      icon: <PersonSimpleTaiChiIcon size={20} />,
      title: "Wellness & Lifestyle",
      pathname: "/user/wellness-lifestyle",
      onClick: () => onItemClick("/user/wellness-lifestyle"),
    },
    {
      icon: <BowlFoodIcon size={20} />,
      title: "Fertility Diet Plan",
      pathname: "/user/fertility-diet-plan",
      onClick: () => onItemClick("/user/fertility-diet-plan/diet-assessment"),
    },
    {
      icon: <BabyIcon size={20} />,
      title: "Good News Wall",
      pathname: "/user/good-news-wall",
      onClick: () => onItemClick("/user/good-news-wall"),
    },
    {
      icon: <HandHeartIcon size={20} />,
      title: "Period Tracker",
      pathname: "/user/period-tracker",
      onClick: () => onItemClick("/user/period-tracker"),
    },
    {
      icon: <UsersIcon size={20} />,
      title: "Community",
      pathname: "/user/community",
      onClick: () => onItemClick("/user/community"),
    },
  ];

  const toggleUserProfileSlider = () => {
    setIsUserProfileSliderOpen(!isUserProfileSliderOpen);
  };

  const closeUserProfileSlider = () => {
    setIsUserProfileSliderOpen(false);
  };

  const toggleNotificationSlider = () => {
    setIsNotificationSliderOpen(!isNotificationSliderOpen);
  };

  const closeNotificationSlider = () => {
    setIsNotificationSliderOpen(false);
  };

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const closeSidebar = () => {
    setIsSidebarOpen(false);
  };

  const toggleProfileDropdown = () => {
    setIsProfileDropdownOpen(!isProfileDropdownOpen);
  };

  const toggleNotificationDropdown = () => {
    setIsNotificationDropdownOpen(!isNotificationDropdownOpen);
  };

  const closeProfileDropdown = () => {
    setIsProfileDropdownOpen(false);
  };

  const closeNotificationDropdown = () => {
    setIsNotificationDropdownOpen(false);
  };

  if (isLoading) {
    return <PageLoader />;
  }

  return (
    <>
      {screenWidth <= 768 ? (
        <div className="relative min-h-screen bg-[#FAFAFC] flex flex-col items-center pb-4">
          {/* Top Bar */}
          <div className="w-full h-[5rem] flex items-center justify-between px-2 py-2 bg-white mb-4 sticky top-0 left-0 z-30">
            <div className="flex items-center gap-1">
              <button className="p-2" onClick={toggleSidebar}>
                <TextIndentIcon size={24} className="text-[var(--grey-6)]" />
              </button>
              <Image
                src="/assets/givfLogo.svg"
                alt="Logo"
                className="h-8 w-auto"
                width={65}
                height={32}
              />
            </div>
            <div className="flex items-center gap-3">
              <div onClick={toggleNotificationSlider}>
                <NotificationButton count={9} />
              </div>
              <div onClick={toggleUserProfileSlider}>
                <Avatar
                  src="/assets/avatar.jpg"
                  alt="Logo"
                  height={40}
                  width={40}
                />
              </div>
            </div>
          </div>
          {/* Title & Subtitle */}
          <div className="w-full text-left px-5 pb-4">
            {title && (
              <h1 className="text-2xl font-bold text-[var(--grey-7)]">
                {title}
              </h1>
            )}
            {subtitle && (
              <p className="text-sm font-medium text-[var(--grey-6)]">
                {subtitle}
              </p>
            )}
            {breadcrumbs && (
              <div className="mb-4">
                <Breadcrumb items={breadcrumbs} />
              </div>
            )}
          </div>
          {/* Card */}
          <div className="flex items-center justify-center w-full px-4">
            {children}
          </div>

          <PopUpSidebar
            items={sidebarItems}
            isOpen={isSidebarOpen}
            onClose={closeSidebar}
            currentPathname={pathname}
            className="!absolute !top-0 !left-0 !z-50"
          />
          <UserProfileSlider
            isOpen={isUserProfileSliderOpen}
            onClose={closeUserProfileSlider}
            user={{
              name: "Priya Patel",
              email: "<EMAIL>",
              avatar: "/assets/avatar.jpg",
            }}
            onProfileClick={() => router.push("/user/settings")}
            onSettingsClick={() => router.push("/user/settings")}
            onHelpSupportClick={() => router.push("/user/help-support")}
            onNotificationPreferencesClick={() =>
              console.log("Notification preferences")
            }
            onLogoutClick={signOut}
          />
          <NotificationSlider
            isOpen={isNotificationSliderOpen}
            onClose={closeNotificationSlider}
            notifications={notifications as NotificationItemData[]}
          />
          <NotificationSlider
            isOpen={isNotificationSliderOpen}
            onClose={closeNotificationSlider}
            notifications={notifications as NotificationItemData[]}
          />
        </div>
      ) : (
        <div className="flex min-h-screen bg-[#F7F7F9]">
          {/* Fixed Sidebar */}
          <Sidebar
            items={sidebarItems}
            currentPathname={pathname}
            isCollapsed={isSidebarCollapsed}
            onToggleCollapse={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
          />
          {/* Main Content Area with dynamic margin */}
          <div
            className={`flex-1 flex flex-col transition-all duration-600 ${
              isSidebarCollapsed ? "ml-[6rem]" : "ml-64"
            }`}
          >
            <div className="flex justify-between items-center">
              {/* Page Title and Subtitle */}
              <div className="px-10 pt-8 pb-2">
                {title && (
                  <h1 className="text-2xl font-bold text-[var(--grey-7)] mb-1">
                    {title}
                  </h1>
                )}
                {subtitle && (
                  <p className="text-sm font-medium text-[var(--grey-6)]">
                    {subtitle}
                  </p>
                )}
                {breadcrumbs && (
                  <div className="mb-4">
                    <Breadcrumb items={breadcrumbs} />
                  </div>
                )}
              </div>
              {/* Header */}
              <div className="flex justify-end items-center gap-6 px-10 py-6">
                <div className="relative">
                  <div onClick={toggleNotificationDropdown}>
                    <NotificationButton count={9} className="cursor-pointer" />
                  </div>
                  <NotificationDropdown
                    isOpen={isNotificationDropdownOpen}
                    onClose={closeNotificationDropdown}
                    notifications={notifications as NotificationItemData[]}
                  />
                </div>
                <div className="relative">
                  <div
                    onClick={toggleProfileDropdown}
                    className="cursor-pointer"
                  >
                    <Avatar
                      src="/assets/avatar.jpg"
                      alt="User"
                      height={48}
                      width={48}
                    />
                  </div>
                  <UserProfileDropdown
                    isOpen={isProfileDropdownOpen}
                    onClose={closeProfileDropdown}
                    user={{
                      name: profile?.display_name || "Priya Patel",
                      email: user?.email || "<EMAIL>",
                      avatar: "/assets/avatar.jpg",
                    }}
                    onProfileClick={() => router.push("/user/settings")}
                    onSettingsClick={() => router.push("/user/settings")}
                    onHelpSupportClick={() => router.push("/user/help-support")}
                    onNotificationPreferencesClick={() =>
                      console.log("Notification preferences")
                    }
                    onLogoutClick={signOut}
                  />
                </div>
              </div>
            </div>
            {/* Centered Card for Children */}
            <div className="flex-1 flex items-center justify-center">
              {children}
            </div>
          </div>
        </div>
      )}
    </>
  );
}
