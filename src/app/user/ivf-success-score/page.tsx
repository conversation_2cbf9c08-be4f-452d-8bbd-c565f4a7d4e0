"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import UserSuccessResultsPage from "@/components/SuccessResultsPage/UserSuccessResultsPage";
import { useShareEmail } from "@/hooks/useShareEmail";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import { usePageHeader } from "@/contexts/PageHeaderContext";
import { useBreadcrumb } from "@/contexts/BreadcrumbContext";

export default function IVFSuccessScorePage() {
  const { user } = useAuth();
  const [emailSentSuccessfully, setEmailSentSuccessfully] = useState(false);
  const { setTitle, setSubtitle } = usePageHeader();
  const { setBreadcrumbs } = useBreadcrumb();

  const router = useRouter();
  const { captureAndShareEmail, isCapturing, isSending, error, success } =
    useShareEmail();

  useEffect(() => {
    setTitle("IVF Success Score");
    setSubtitle("Here Check your IVF Success Score or take assessment");
    setBreadcrumbs(null);
  }, [setTitle, setSubtitle, setBreadcrumbs]);

  useEffect(() => {
    if (!user) {
      router.push("/login");
      return;
    }
  }, [router, user]);

  useEffect(() => {
    if (success) {
      setEmailSentSuccessfully(true);
    }
  }, [success]);

  const handleRetakeAssessment = () => {
    // Navigate to the type selection page to restart the assessment
    router.push("/ivf-assessment/user-type");
  };

  const handleVisitIVFCenter = () => {
    console.log("Visit IVF Center Near You clicked");
  };

  const handleShareEmail = async () => {
    try {
      await captureAndShareEmail();

      if (success) {
        toast.success("Email sent successfully! Check your inbox.");
      }
    } catch (err) {
      console.error("Share email error:", err);
      toast.error(error || "Failed to send email. Please try again.");
    }
  };

  const handleHome = () => {
    router.push("/");
  };

  const handleBookConsultation = () => {
    console.log("Book a Free Consultation clicked");
  };

  return (
    <UserSuccessResultsPage
      onRetakeAssessment={handleRetakeAssessment}
      onVisitIVFCenter={handleVisitIVFCenter}
      onShareEmail={handleShareEmail}
      onHome={handleHome}
      onBookConsultation={handleBookConsultation}
      isSharing={isCapturing || isSending}
      shareError={error}
      shareSuccess={success}
      emailSentSuccessfully={emailSentSuccessfully}
    />
  );
}
