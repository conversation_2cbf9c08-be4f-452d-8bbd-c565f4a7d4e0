"use client";

import { useBreadcrumb } from "@/contexts/BreadcrumbContext";
import { usePageHeader } from "@/contexts/PageHeaderContext";
import { useEffect } from "react";

export default function Page() {
  const { setTitle, setSubtitle } = usePageHeader();
  const { setBreadcrumbs } = useBreadcrumb();

  useEffect(() => {
    setTitle("Period Tracker");
    setSubtitle("Your Cycle Calendar");
    setBreadcrumbs(null);
  }, [setTitle, setSubtitle, setBreadcrumbs]);
  return <div>Period Tracker</div>;
}
