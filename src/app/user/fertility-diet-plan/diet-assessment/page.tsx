"use client";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import FertilityDietPlan from "@/components/FertilityDietPlan/FertilityDietPlan";
import { Loader } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";

export default function Page() {
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const { user_token } = useAuth();

  useEffect(() => {
    const checkAssessment = async () => {
      try {
        const response = await fetch("/api/v1/fertility-diet-plan/form",{
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${user_token}`,
          },
        });
        const result = await response.json();

        if (result.hasData) {
          router.replace("/user/fertility-diet-plan/bmr-calories-calculation");
        } else {
          setLoading(false);
        }
      } catch (error) {
        console.error("Error checking assessment:", error);
        setLoading(false);
      }
    };

    checkAssessment();
  }, [router, user_token]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader className="animate-spin" />
      </div>
    );
  }

  return <FertilityDietPlan />;
}