"use client";

import { useBreadcrumb } from "@/contexts/BreadcrumbContext";
import { usePageHeader } from "@/contexts/PageHeaderContext";
import { useEffect } from "react";

export default function Page() {
  const { setTitle, setSubtitle } = usePageHeader();
  const { setBreadcrumbs } = useBreadcrumb();

  useEffect(() => {
    setTitle("Community");
    setSubtitle(
      "Get expert answers and connect with others on the same IVF journey."
    );
    setBreadcrumbs(null);
  }, [setTitle, setSubtitle, setBreadcrumbs]);
  return <div>Community</div>;
}
