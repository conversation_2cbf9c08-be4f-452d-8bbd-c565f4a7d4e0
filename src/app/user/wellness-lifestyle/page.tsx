"use client";

import { useBreadcrumb } from "@/contexts/BreadcrumbContext";
import { usePageHeader } from "@/contexts/PageHeaderContext";
import { useEffect } from "react";

export default function Page() {
  const { setTitle, setSubtitle } = usePageHeader();
  const { setBreadcrumbs } = useBreadcrumb();

  useEffect(() => {
    setTitle("Your Wellness & Lifestyle Hub");
    setSubtitle(
      "Support your body, mind, and emotions through every step of your fertility journey."
    );
    setBreadcrumbs(null);
  }, [setTitle, setSubtitle, setBreadcrumbs]);
  return <div>Wellness Lifestyle</div>;
}
