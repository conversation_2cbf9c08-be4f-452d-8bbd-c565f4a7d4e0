"use client";

import { useRouter, usePathname } from "next/navigation";
import Navbar from "@/components/shared/Settings/Navbar/Navbar";
import { usePageHeader } from "@/contexts/PageHeaderContext";
import { useEffect } from "react";
import { useBreadcrumb } from "@/contexts/BreadcrumbContext";

export default function Layout({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const pathname = usePathname();
  const { setTitle, setSubtitle } = usePageHeader();
  const { setBreadcrumbs } = useBreadcrumb();

  useEffect(() => {
    setTitle("Settings");
    setSubtitle("View and edit your information");
    setBreadcrumbs(null);
  }, [setTitle, setSubtitle, setBreadcrumbs]);

  const handleNavItemClick = (itemId: string) => {
    const routeMap: Record<string, string> = {
      "personal-details": "/user/settings/personal-details",
      "medical-fertility-info": "/user/settings/medical-info",
      "change-password": "/user/settings/change-password",
      "help-support": "/user/settings/help-support",
      "notification-preferences": "/user/settings/notification",
      "delete-account": "/user/settings/delete-account",
    };

    const route = routeMap[itemId];
    if (route) {
      router.push(route);
    }
  };

  return (
    <div className="h-full w-full flex flex-col xl:flex-row flex-1 gap-6 py-0 md:py-6 md:px-6">
      <Navbar onItemClick={handleNavItemClick} currentPathname={pathname} />
      <div className="xl:flex-1">{children}</div>
    </div>
  );
}
