"use client";

import { useBreadcrumb } from "@/contexts/BreadcrumbContext";
import { usePageHeader } from "@/contexts/PageHeaderContext";
import { useEffect } from "react";

export default function Page() {
  const { setTitle, setSubtitle } = usePageHeader();
  const { setBreadcrumbs } = useBreadcrumb();

  useEffect(() => {
    setTitle("Help & Support");
    setSubtitle("Get help and support from our experts.");
    setBreadcrumbs(null);
  }, [setTitle, setSubtitle, setBreadcrumbs]);
  return <div>Help & Support</div>;
}
