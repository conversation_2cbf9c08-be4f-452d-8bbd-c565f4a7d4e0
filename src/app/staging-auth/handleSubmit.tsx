'use server';
import { cookies } from "next/headers";

// Update the server action to return an object indicating success/error
export async function handleSubmit(formData: FormData) {
  const inputPassword = formData.get("password");
  const STAGING_PASSWORD = process.env.STAGING_PASSWORD;

  if (inputPassword === STAGING_PASSWORD) {
    const cookieStore = await cookies();
    cookieStore.set({
      name: 'staging_password',
      value: inputPassword as string,
      httpOnly: true,
      path: '/',
      maxAge: 60 * 60 * 24,
      secure: process.env.NODE_ENV === 'production',
    });
    console.log("Server: Password correct. Staging cookie set.");
    return { success: true }; // indicate success
  } else {
    console.log("Server: Incorrect password.");
    return { error: "Incorrect password. Please try again." }; // return error message
  }
}
