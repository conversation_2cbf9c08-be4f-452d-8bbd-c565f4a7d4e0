"use client"; // This directive enables client-side React features

import React, { useState } from "react";
import { handleSubmit } from "./handleSubmit"; // import the server function

export function StagingAuthForm() {
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  async function onSubmit(event: React.FormEvent<HTMLFormElement>) {
    event.preventDefault();
    setError(null); // clear previous error
    setLoading(true);

    const formData = new FormData(event.currentTarget);
    const result = await handleSubmit(formData);

    setLoading(false);
    if (result?.error) {
      setError(result.error); // show error message
    } else {
      setError(null);
      // You can redirect or reload page here on success
      window.location.reload();
    }
  }

  return (
    <div className="flex justify-center items-center h-screen">
      <form className="w-full max-w-xl p-5" onSubmit={onSubmit}>
        <input
          type="password"
          id="password"
          name="password"
          className="w-full px-[1.25rem] py-[0.875rem] border rounded-sm text-base my-4"
          placeholder="Enter password"
          required
        />
        <button
          type="submit"
          disabled={loading}
          className="px-6 py-4 text-base h-[3.125rem] bg-[var(--red-6)] text-white hover:bg-[var(--red-7)] border-0 rounded-full cursor-pointer w-full"
        >
          {loading ? "Submitting..." : "Submit"}
        </button>

        {error && (
          <div className="text-center mt-4 text-pink-700 font-semibold">
            {error}
          </div>
        )}
      </form>
    </div>
  );
}
