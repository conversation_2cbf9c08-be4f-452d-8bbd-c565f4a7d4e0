"use client";

import { useCallback, useEffect, useState, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { createClient, supabase } from "@/utils/supabase/client";
import LoginPage from "@/components/LoginPage";
import { useAuth, UserWithRole } from "@/contexts/AuthContext";
import { useToast } from "@/contexts/ToastContext";
import {
  getGuestSessionToken,
  clearGuestSessionToken,
} from "@/utils/guestSessionUtils";
import { convertGuestFormDataToUser } from "@/utils/formLocalStorage";
import ConfirmationModal from "@/components/shared/ConfirmationModal/ConfirmationModal";

function LoginPageContent() {
  const { user, session, isLoading } = useAuth();
  const router = useRouter();
  const toast = useToast();
  const searchParams = useSearchParams();
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [isConvertingGuestData, setIsConvertingGuestData] = useState(false);
  const [pendingConversion, setPendingConversion] = useState<{
    user: UserWithRole;
    guestSessionToken: string;
  } | null>(null);
  const [isOTPLoading, setIsOTPLoading] = useState(false);
  const [otpErrorMessage, setOtpErrorMessage] = useState<string | null>(null);

  const getProfileData = useCallback(async (user: UserWithRole) => {
    const { data: profilesData, error: profilesError } = await supabase
      .from("profiles")
      .select("*")
      .eq("auth_id", user.id);

    if (profilesError) {
      console.error("Error fetching profiles data:", profilesError);
    } else {
      console.log("Profiles data:", profilesData);
    }
    return profilesData;
  }, []);

  const handleUserRedirect = useCallback((user: UserWithRole, redirectUrl?: string | null) => {
    const isAdminOrDoctor = user?.user_role === "admin" || user?.user_role === "doctor";
    
    if (isAdminOrDoctor) {
      router.replace("/admin");
      router.refresh();
      return;
    }

    // For regular users, if redirectUrl is root route (/), redirect to dashboard instead
    if (redirectUrl) {
      const decodedRedirectUrl = decodeURIComponent(redirectUrl);
      const finalRedirectUrl = decodedRedirectUrl === "/" ? "/user/dashboard" : decodedRedirectUrl;
      router.replace(finalRedirectUrl);
    } else {
      router.replace("/user/dashboard");
    }
    
    router.refresh();
  }, [router]);

  const checkExistingIVFScores = useCallback(
    async (userId: string) => {
      try {
        const response = await fetch(
          `/api/v1/ivf-assessment/check-existing-scores?userId=${userId}`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${session?.access_token}`,
            },
          }
        );

        if (response.ok) {
          const data = await response.json();
          return data.hasExistingScores;
        }
        return false;
      } catch (error) {
        console.error("Error checking existing IVF scores:", error);
        return false;
      }
    },
    [session?.access_token]
  );

  const convertGuestData = useCallback(
    async (
      user: UserWithRole,
      guestSessionToken: string,
      overwrite: boolean = false
    ) => {
      setIsConvertingGuestData(true);
      try {
        // Call the convert-guest API
        const response = await fetch("/api/v1/ivf-assessment/convert-guest", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${session?.access_token}`,
          },
          body: JSON.stringify({
            guestSessionToken,
            userId: user.id,
            overwrite,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error("Guest conversion failed:", errorData);
          toast.error("Failed to convert guest data. Please try again.");
          setIsConvertingGuestData(false);
          return false;
        }

        const result = await response.json();
        console.log("Guest data converted successfully:", result);

        // Clear the guest session token
        clearGuestSessionToken();
        sessionStorage.removeItem("Test_status");
        sessionStorage.setItem("Result_status", "completed");

        // Redirect to results page
        router.push("/ivf-assessment/results");
        return true;
      } catch (error) {
        console.error("Error converting guest data:", error);
        toast.error("An error occurred while converting guest data.");
        setIsConvertingGuestData(false);
        return false;
      }
    },
    [router, session?.access_token, toast]
  );

  const handleGuestConversion = useCallback(
    async (user: UserWithRole) => {
      const guestSessionToken = getGuestSessionToken();

      if (!guestSessionToken) {
        console.log(
          "No guest session token found, proceeding with normal login"
        );
        return false;
      }

      // Check if user is coming from verify-details page
      const pages = ["verify-details", "signup"];
      const fromVerifyDetails = pages.includes(searchParams.get("from") || "");
      if (!fromVerifyDetails) {
        console.log(
          "Not coming from verify-details or signup page, proceeding with normal login"
        );
        return false;
      }

      // Check if user has existing IVF scores
      const hasExistingScores = await checkExistingIVFScores(user.id);

      if (hasExistingScores) {
        // Show confirmation modal
        setPendingConversion({ user, guestSessionToken });
        setShowConfirmationModal(true);
        return true; // Return true to prevent normal login flow
      } else {
        // No existing scores, proceed with conversion directly
        return await convertGuestData(user, guestSessionToken);
      }
    },
    [checkExistingIVFScores, convertGuestData, searchParams]
  );

  const handleConfirmConversion = async () => {
    if (!pendingConversion) return;

    setShowConfirmationModal(false);
    const success = await convertGuestData(
      pendingConversion.user,
      pendingConversion.guestSessionToken,
      true
    );

    if (!success) {
      // If conversion failed, proceed with normal login flow
      setIsConvertingGuestData(false);
      const redirectUrl = searchParams.get("redirect");
      handleUserRedirect(pendingConversion.user, redirectUrl);
    }

    setPendingConversion(null);
  };

  const handleCancelConversion = () => {
    setShowConfirmationModal(false);
    setPendingConversion(null);

    // Proceed with normal login flow
    if (user) {
      const redirectUrl = searchParams.get("redirect");
      handleUserRedirect(user, redirectUrl);
    }
  };

  useEffect(() => {
    if (!isLoading && user) {

      // Convert localStorage guest form data to user data if conditions are met
      const converted = convertGuestFormDataToUser(user.id);
      if (converted) {
        console.log("User form data has been transferred to your account.");
      }

      // Check if we need to convert guest data
      const guestSessionToken = getGuestSessionToken();
      const pages = ["verify-details", "signup"];
      const fromVerifyDetails = pages.includes(searchParams.get("from") || "");

      const redirectUrl = searchParams.get("redirect");

      if (guestSessionToken && fromVerifyDetails) {
        // Try to convert guest data first
        handleGuestConversion(user).then((converted) => {
          if (!converted) {
            // If conversion failed or wasn't needed, proceed with normal login flow
            handleUserRedirect(user, redirectUrl);
          }
        });
      } else {
        // Normal login flow
        handleUserRedirect(user, redirectUrl);
      }

      // get profiles data
      (async () => {
        const profile = await getProfileData(user);
        if (profile && profile.length > 0) {
          localStorage.setItem("ivf_user_profile", JSON.stringify(profile[0]));
        }
      })();
    }
  }, [
    user,
    isLoading,
    router,
    searchParams,
    handleGuestConversion,
    getProfileData,
    toast,
    handleUserRedirect,
  ]);

  const handleSignIn = async (email: string, password: string) => {
    try {
      const supabase = createClient();
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        toast.error(error.message);
      } else {
        if (data.session) {
          localStorage.setItem("ivf_auth_token", JSON.stringify(data.session));
        }
      }
    } catch (error) {
      toast.error("An unexpected error occurred : " + error);
    }
  };

  const handleForgotPassword = () => {
    router.push("/forgot-password");
  };

  const handleContinueWithEmail = () => {
    const from = searchParams.get("from");
    const redirect = searchParams.get("redirect");
    if (from || redirect) {
      const qs = new URLSearchParams();
      if (from) qs.set("from", from);
      if (redirect) qs.set("redirect", redirect);
      router.push(`/register?${qs.toString()}`);
    } else {
      router.push("/register");
    }
  };



  const handleVerifyOTP = async (email: string, otp: string) => {
    setIsOTPLoading(true);
    setOtpErrorMessage(null);

    try {
      const { data, error } = await supabase.auth.verifyOtp({
        email,
        token: otp,
        type: "email",
      });

      if (error) {
        setOtpErrorMessage(error.message);
      } else if (data.user) {
        // Store session if available
        if (data.session) {
          localStorage.setItem("ivf_auth_token", JSON.stringify(data.session));
        }
        
        // Convert user to UserWithRole format
        const userWithRole: UserWithRole = {
          ...data.user,
          user_role: "user", // Default role, will be updated by AuthContext
        };

        // Handle user redirect
        const redirectUrl = searchParams.get("redirect");
        handleUserRedirect(userWithRole, redirectUrl);
      }
    } catch (error) {
      console.error("OTP verification error:", error);
      setOtpErrorMessage("Failed to verify code. Please try again.");
    } finally {
      setIsOTPLoading(false);
    }
  };



  return (
    <>
      <LoginPage
        onLogin={handleSignIn}
        onVerifyOTP={handleVerifyOTP}
        onForgotPassword={handleForgotPassword}
        onContinueWithEmail={handleContinueWithEmail}
        isLoading={isOTPLoading}
        errorMessage={otpErrorMessage || undefined}
      />

      {/* Loading Overlay for Guest Data Conversion */}
      {isConvertingGuestData && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-8 max-w-md mx-4 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Converting Guest Data
            </h3>
            <p className="text-gray-600">
              Please wait while we transfer your assessment data to your
              account...
            </p>
          </div>
        </div>
      )}

      {/* Confirmation Modal */}
      {showConfirmationModal && (
        <ConfirmationModal
          open={showConfirmationModal}
          onConfirm={handleConfirmConversion}
          onCancel={handleCancelConversion}
          title="Overwrite Previous Assessment?"
          message="You have a previous assessment record. Do you want to overwrite it? Because you just recently took an assessment as a guest user."
          confirmText="Overwrite"
          cancelText="Cancel"
        />
      )}
    </>
  );
}

export default function LoginPageContainer() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <LoginPageContent />
    </Suspense>
  );
}
