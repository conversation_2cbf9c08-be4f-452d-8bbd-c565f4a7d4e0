"use client";

import {
  addQuestion,
  updateQuestion,
  deleteQuestion,
  reorderQuestions,
} from "@/lib/services/client/questions.service";
import { FormBuilder } from "@/components/admin/questions/FormBuilder";
import { Card, CardContent } from "@/components/ShadcnUI/card";
import Button, { ButtonType } from "@/components/shared/Button/Button";
import { RefreshCw, AlertCircle } from "lucide-react";
import { useFormsByCategory } from "@/hooks/useFormsByCategory";
import { FormBuilderQuestion } from "@/types";
import PageLoader from "@/components/shared/PageLoader";

export default function FertilityMeterQuestionsPage() {
  const {
    data: forms,
    isLoading,
    error,
    refetch,
  } = useFormsByCategory();

  const handleSaveQuestion = async (
    formId: string,
    question: FormBuilderQuestion
  ) => {
    try {
      await addQuestion(formId, question);
      await refetch(); // Refresh forms after adding
    } catch (error) {
      console.error("Error saving question:", error);
      throw error;
    }
  };

  const handleUpdateQuestion = async (
    questionId: string,
    questionData: Partial<FormBuilderQuestion>
  ) => {
    try {
      await updateQuestion(questionId, questionData);
      await refetch(); // Refresh forms after updating
    } catch (error) {
      console.error("Error updating question:", error);
      throw error;
    }
  };

  const handleDeleteQuestion = async (questionId: string) => {
    try {
      await deleteQuestion(questionId);
      await refetch(); // Refresh forms after deleting
    } catch (error) {
      console.error("Error deleting question:", error);
      throw error;
    }
  };

  const handleReorderQuestions = async (
    formId: string,
    questionOrders: { id: string; order: number }[]
  ) => {
    try {
      await reorderQuestions(formId, questionOrders);
      await refetch(); // Refresh forms after reordering
    } catch (error) {
      console.error("Error reordering questions:", error);
      throw error;
    }
  };

  if (isLoading) {
    return <PageLoader />;
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-lg font-semibold mb-2">Error Loading Forms</h2>
            <p className="text-muted-foreground mb-4">{error.message}</p>
            <Button
              type={ButtonType.PRIMARY}
              onClick={async () => {
                await refetch();
              }}
              text="Try Again"
              icon={<RefreshCw className="h-4 w-4" />}
              className="gap-2"
            />
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background p-6">
      <FormBuilder
        forms={forms!}
        onSaveQuestion={handleSaveQuestion}
        onUpdateQuestion={handleUpdateQuestion}
        onDeleteQuestion={handleDeleteQuestion}
        onReorderQuestions={handleReorderQuestions}
        onRefresh={async () => {
          await refetch();
        }}
      />
    </div>
  );
}