"use client";

import React, { useEffect, useState, useCallback } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ShadcnUI/card";
import Button, { ButtonType } from "@/components/shared/Button/Button";
import { FileText, PlusCircle, Eye, Trash2, Download, Search, ChevronLeft, ChevronRight } from "lucide-react";
import Link from "next/link";
import { FileUploadService } from "@/lib/services/file-upload.service";

interface ConditionMealPlan {
  id: string;
  condition: string;
  target_calories_min: number;
  target_calories_max: number;
  recommended_target_calories: number;
  meal_plan_doc_url?: string;
  nutritional_advice?: string;
  created_at: string | Date;
  doctor?: {
    display_name?: string;
    email?: string;
  };
}

interface PaginationData {
  total: number;
  page: number;
  perPage: number;
  totalPages: number;
}

export default function DietPlansPage() {
  const [mealPlans, setMealPlans] = useState<ConditionMealPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  
  // Pagination state
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    perPage: 10,
    totalPages: 0
  });
  const [search, setSearch] = useState("");
  const [condition, setCondition] = useState<string>("");

  const fetchMealPlans = useCallback(async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        per_page: pagination.perPage.toString(),
      });

      if (search) {
        params.append('search', search);
      }

      if (condition) {
        params.append('condition', condition);
      }

      const response = await fetch(`/api/v1/admin/condition-meal-plans?${params}`);
      const result = await response.json();
      
      if (result.success) {
        setMealPlans(result.data.mealPlans);
        setPagination(prev => ({
          ...prev,
          total: result.data.total,
          totalPages: result.data.totalPages
        }));
      } else {
        setError('Failed to fetch meal plans');
      }
    } catch (error) {
      console.error('Error fetching meal plans:', error);
      setError('Failed to fetch meal plans');
    } finally {
      setLoading(false);
    }
  }, [pagination.page, pagination.perPage, search, condition]);

  useEffect(() => {
    fetchMealPlans();
  }, [fetchMealPlans]);

  const handleDelete = async (mealPlanId: string, mealPlanDocUrl?: string) => {
    if (!confirm('Are you sure you want to delete this meal plan template?')) {
      return;
    }

    setDeletingId(mealPlanId);

    try {
      // Delete the meal plan from the database
      const response = await fetch(`/api/v1/admin/condition-meal-plans/${mealPlanId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        // If there's an associated PDF file, delete it from storage
        if (mealPlanDocUrl) {
          try {
            await FileUploadService.deleteFile(mealPlanDocUrl);
          } catch (fileError) {
            console.error('Error deleting file from storage:', fileError);
            // Continue with the deletion even if file deletion fails
          }
        }

        // Refresh the data to update pagination
        fetchMealPlans();
      } else {
        alert('Failed to delete meal plan');
      }
    } catch (error) {
      console.error('Error deleting meal plan:', error);
      alert('Failed to delete meal plan');
    } finally {
      setDeletingId(null);
    }
  };

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const handlePerPageChange = (newPerPage: number) => {
    setPagination(prev => ({ ...prev, page: 1, perPage: newPerPage }));
  };

  const handleSearch = (value: string) => {
    setSearch(value);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleConditionFilter = (value: string) => {
    setCondition(value);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const formatCondition = (condition: string) => {
    return condition.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const formatDate = (dateString: string | Date) => {
    try {
      // Debug: Log the incoming date value
      console.log('formatDate received:', dateString, 'type:', typeof dateString);
      
      // Handle both string and Date objects
      const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
      
      // Check if the date is valid
      if (isNaN(date.getTime())) {
        console.warn('Invalid date received:', dateString);
        return 'Invalid Date';
      }
      
      return date.toLocaleDateString();
    } catch (error) {
      console.error('Error formatting date:', error, 'Date value:', dateString);
      return 'Invalid Date';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <FileText className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-foreground">Diet Plans</h1>
              <p className="text-muted-foreground">
                Manage condition-based meal plans for different fertility conditions
              </p>
            </div>
          </div>
        </div>
        <Card>
          <CardContent className="py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-muted-foreground">Loading meal plans...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <FileText className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-foreground">Diet Plans</h1>
            <p className="text-muted-foreground">
              Manage condition-based meal plan templates for different fertility conditions
            </p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Link href="/admin/diet-plans/create">
            <Button
              type={ButtonType.PRIMARY}
              size="sm"
              text="Add Meal Plan Template"
              icon={<PlusCircle className="h-4 w-4" />}
              className="gap-2 whitespace-nowrap"
            />
          </Link>
        </div>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="py-4">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <input
                  type="text"
                  placeholder="Search by doctor name or email..."
                  value={search}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>
            </div>

            {/* Condition Filter */}
            <div className="sm:w-48">
              <select
                value={condition}
                onChange={(e) => handleConditionFilter(e.target.value)}
                className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="">All Conditions</option>
                <option value="PCOS">PCOS</option>
                <option value="LOW_AMH">Low AMH</option>
                <option value="INCREASE_FERTILITY_NATURALLY">Increase Fertility Naturally</option>
                <option value="INCREASE_IRON">Increase Iron</option>
              </select>
            </div>

            {/* Items per page */}
            <div className="sm:w-32">
              <select
                value={pagination.perPage}
                onChange={(e) => handlePerPageChange(Number(e.target.value))}
                className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value={5}>5 per page</option>
                <option value={10}>10 per page</option>
                <option value={20}>20 per page</option>
                <option value={50}>50 per page</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Diet Plans Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Condition Meal Plan Templates
          </CardTitle>
          <CardDescription>
            Manage meal plan templates for different fertility conditions
            {pagination.total > 0 && (
              <span className="ml-2 text-sm text-muted-foreground">
                ({pagination.total} total)
              </span>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error ? (
            <div className="text-center py-12">
              <p className="text-red-600 mb-4">{error}</p>
              <Button
                type={ButtonType.SECONDARY}
                text="Retry"
                onClick={fetchMealPlans}
              />
            </div>
          ) : mealPlans.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No meal plan templates found</h3>
              <p className="text-muted-foreground mb-4">
                {search || condition 
                  ? "No meal plans match your current filters. Try adjusting your search criteria."
                  : "No condition-based meal plan templates have been created yet"
                }
              </p>
              {(search || condition) ? (
                <Button
                  type={ButtonType.SECONDARY}
                  text="Clear Filters"
                  onClick={() => {
                    setSearch("");
                    setCondition("");
                    setPagination(prev => ({ ...prev, page: 1 }));
                  }}
                />
              ) : (
                <div className="max-w-sm w-full mx-auto">
                  <Link href="/admin/diet-plans/create">
                    <Button
                      type={ButtonType.PRIMARY}
                      text="Create First Meal Plan Template"
                      icon={<PlusCircle className="h-4 w-4" />}
                    />
                  </Link>
                </div>
              )}
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-3 px-4 font-medium">Condition</th>
                      <th className="text-left py-3 px-4 font-medium">Calorie Range</th>
                      <th className="text-left py-3 px-4 font-medium">Recommended</th>
                      <th className="text-left py-3 px-4 font-medium">Doctor</th>
                      <th className="text-left py-3 px-4 font-medium">Created</th>
                      <th className="text-left py-3 px-4 font-medium">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {mealPlans.map((plan) => (
                      <tr key={plan.id} className="border-b hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <span className="font-medium">{formatCondition(plan.condition)}</span>
                        </td>
                        <td className="py-3 px-4">
                          {plan.target_calories_min} - {plan.target_calories_max} cal
                        </td>
                        <td className="py-3 px-4">
                          {plan.recommended_target_calories} cal
                        </td>
                        <td className="py-3 px-4">
                          <div>
                            <p className="font-medium">{plan.doctor?.display_name || 'Unknown'}</p>
                            <p className="text-sm text-muted-foreground">{plan.doctor?.email}</p>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          {formatDate(plan.created_at)}
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center gap-2">
                            {plan.meal_plan_doc_url && (
                              <Button
                                type={ButtonType.SECONDARY}
                                size="sm"
                                text=""
                                icon={<Download className="h-4 w-4" />}
                                onClick={() => window.open(plan.meal_plan_doc_url, '_blank')}
                              />
                            )}
                            <Button
                              type={ButtonType.SECONDARY}
                              size="sm"
                              text=""
                              icon={<Eye className="h-4 w-4" />}
                              onClick={() => alert('View functionality to be implemented')}
                            />
                            <Button
                              type={ButtonType.SECONDARY}
                              size="sm"
                              text=""
                              icon={<Trash2 className="h-4 w-4" />}
                              onClick={() => handleDelete(plan.id, plan.meal_plan_doc_url)}
                              disabled={deletingId === plan.id}
                            />
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination Controls */}
              {pagination.totalPages > 1 && (
                <div className="flex items-center justify-between mt-6 pt-4 border-t">
                  <div className="text-sm text-muted-foreground">
                    Showing {((pagination.page - 1) * pagination.perPage) + 1} to{' '}
                    {Math.min(pagination.page * pagination.perPage, pagination.total)} of{' '}
                    {pagination.total} results
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button
                      type={ButtonType.SECONDARY}
                      size="sm"
                      text=""
                      icon={<ChevronLeft className="h-4 w-4" />}
                      onClick={() => handlePageChange(pagination.page - 1)}
                      disabled={pagination.page <= 1}
                    />
                    
                    <div className="flex items-center gap-1">
                      {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                        let pageNum;
                        if (pagination.totalPages <= 5) {
                          pageNum = i + 1;
                        } else if (pagination.page <= 3) {
                          pageNum = i + 1;
                        } else if (pagination.page >= pagination.totalPages - 2) {
                          pageNum = pagination.totalPages - 4 + i;
                        } else {
                          pageNum = pagination.page - 2 + i;
                        }
                        
                        return (
                          <Button
                            key={pageNum}
                            type={pageNum === pagination.page ? ButtonType.PRIMARY : ButtonType.SECONDARY}
                            size="sm"
                            text={pageNum.toString()}
                            onClick={() => handlePageChange(pageNum)}
                            className="min-w-[2rem]"
                          />
                        );
                      })}
                    </div>
                    
                    <Button
                      type={ButtonType.SECONDARY}
                      size="sm"
                      text=""
                      icon={<ChevronRight className="h-4 w-4" />}
                      onClick={() => handlePageChange(pagination.page + 1)}
                      disabled={pagination.page >= pagination.totalPages}
                    />
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
