"use client";

import React, { useState } from "react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ShadcnUI/card";
import Button, { ButtonType } from "@/components/shared/Button/Button";
import { FileText, ArrowLeft, Upload } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { FileUploadService } from "@/lib/services/file-upload.service";
import { useUserCan } from "@/hooks/useUserCan";

export default function CreateMealPlanPage() {
  const router = useRouter();
  const [condition, setCondition] = useState("");
  const [targetCaloriesMin, setTargetCaloriesMin] = useState("");
  const [targetCaloriesMax, setTargetCaloriesMax] = useState("");
  const [recommendedTargetCalories, setRecommendedTargetCalories] = useState("");
  const [nutritionalAdvice, setNutritionalAdvice] = useState("");
  const [pdfFile, setPdfFile] = useState<File | null>(null);
  const [fileName, setFileName] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<string>("");

  // Check permissions
  const { data: canCreateStorage, isLoading: loadingStoragePermission } = useUserCan('storage.create');
  const { data: canCreateMealPlan, isLoading: loadingMealPlanPermission } = useUserCan('condition_meal_plans.create');

  // useEffect(() => {
  //   // Ensure the storage bucket exists when component mounts
  //   if (canCreateStorage) {
  //     FileUploadService.ensureBucketExists().catch(console.error);
  //   }
  // }, [canCreateStorage]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && file.type === "application/pdf") {
      setPdfFile(file);
      setFileName(file.name);
    } else {
      alert("Please select a valid PDF file");
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    console.log('what!');
    
    if (!pdfFile) {
      alert("Please upload a PDF file for the meal plan");
      return;
    }

    if (!canCreateStorage) {
      alert("You do not have permission to upload files");
      return;
    }

    if (!canCreateMealPlan) {
      alert("You do not have permission to create meal plan templates");
      return;
    }

    setIsSubmitting(true);
    setUploadProgress("Uploading PDF file...");
    
    try {
      // Upload PDF file to Supabase storage
      const uploadResult = await FileUploadService.uploadPDF(pdfFile);
      
      if (!uploadResult.success) {
        alert(`File upload failed: ${uploadResult.error}`);
        return;
      }

      setUploadProgress("Creating meal plan template...");
      
      // Create meal plan with the uploaded file URL
      const response = await fetch('/api/v1/admin/condition-meal-plans', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          doctorId: 1, // This should come from the authenticated user context
          condition,
          targetCaloriesMin: parseInt(targetCaloriesMin),
          targetCaloriesMax: parseInt(targetCaloriesMax),
          recommendedTargetCalories: parseInt(recommendedTargetCalories),
          nutritionalAdvice,
          mealPlanDocUrl: uploadResult.url
        }),
      });

      const result = await response.json();

      if (result.success) {
        console.log("Meal plan template created successfully:", result.data);
        router.push("/admin/diet-plans");
      } else {
        // If meal plan creation fails, delete the uploaded file
        if (uploadResult.url) {
          await FileUploadService.deleteFile(uploadResult.url);
        }
        alert(`Error creating meal plan: ${result.error}`);
      }
    } catch (error) {
      console.error('Error creating meal plan template:', error);
      alert('Failed to create meal plan template. Please try again.');
    } finally {
      setIsSubmitting(false);
      setUploadProgress("");
    }
  };

  // Show loading state while checking permissions
  if (loadingStoragePermission || loadingMealPlanPermission) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Link href="/admin/diet-plans">
              <Button
                type={ButtonType.SECONDARY}
                size="sm"
                text="Back"
                icon={<ArrowLeft className="h-4 w-4" />}
              />
            </Link>
            <div className="p-2 bg-primary/10 rounded-lg">
              <FileText className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-foreground">Create Meal Plan Template</h1>
              <p className="text-muted-foreground">
                Create a meal plan template for a specific fertility condition
              </p>
            </div>
          </div>
        </div>
        <Card>
          <CardContent className="py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-muted-foreground">Checking permissions...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show error if user doesn't have required permissions
  if (!canCreateStorage || !canCreateMealPlan) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Link href="/admin/diet-plans">
              <Button
                type={ButtonType.SECONDARY}
                size="sm"
                text="Back"
                icon={<ArrowLeft className="h-4 w-4" />}
              />
            </Link>
            <div className="p-2 bg-primary/10 rounded-lg">
              <FileText className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-foreground">Create Meal Plan Template</h1>
              <p className="text-muted-foreground">
                Create a meal plan template for a specific fertility condition
              </p>
            </div>
          </div>
        </div>
        <Card>
          <CardContent className="py-12">
            <div className="text-center">
              <FileText className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">Access Denied</h3>
              <p className="text-muted-foreground mb-4">
                You do not have the required permissions to create meal plan templates.
              </p>
              {!canCreateStorage && (
                <p className="text-sm text-red-600 mb-2">Missing: File upload permission</p>
              )}
              {!canCreateMealPlan && (
                <p className="text-sm text-red-600 mb-2">Missing: Meal plan creation permission</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Link href="/admin/diet-plans">
            <Button
              type={ButtonType.SECONDARY}
              size="sm"
              text="Back"
              icon={<ArrowLeft className="h-4 w-4" />}
            />
          </Link>
          <div className="p-2 bg-primary/10 rounded-lg">
            <FileText className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-foreground">Create Meal Plan Template</h1>
            <p className="text-muted-foreground">
              Create a meal plan template for a specific fertility condition
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        {/* Basic Information */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
            <CardDescription>
              Set the condition and calorie ranges for this meal plan template
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Condition</label>
                <select
                  value={condition}
                  onChange={(e) => setCondition(e.target.value)}
                  className="w-full px-3 py-2 border rounded-md"
                  required
                >
                  <option value="">Select Condition</option>
                  <option value="NO_CONDITION">No Condition</option>
                  <option value="PCOS">PCOS</option>
                  <option value="LOW_AMH">Low AMH</option>
                  <option value="PCOS_AND_LOW_AMH">PCOS and Low AMH</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Min Calories</label>
                <input
                  type="number"
                  value={targetCaloriesMin}
                  onChange={(e) => setTargetCaloriesMin(e.target.value)}
                  className="w-full px-3 py-2 border rounded-md"
                  placeholder="1200"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Max Calories</label>
                <input
                  type="number"
                  value={targetCaloriesMax}
                  onChange={(e) => setTargetCaloriesMax(e.target.value)}
                  className="w-full px-3 py-2 border rounded-md"
                  placeholder="1800"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Recommended Target Calories</label>
                <input
                  type="number"
                  value={recommendedTargetCalories}
                  onChange={(e) => setRecommendedTargetCalories(e.target.value)}
                  className="w-full px-3 py-2 border rounded-md"
                  placeholder="1500"
                  required
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Nutritional Advice</label>
              <textarea
                value={nutritionalAdvice}
                onChange={(e) => setNutritionalAdvice(e.target.value)}
                className="w-full px-3 py-2 border rounded-md h-24"
                placeholder="Enter nutritional advice for this condition..."
              />
            </div>
          </CardContent>
        </Card>

        {/* PDF Upload */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>7 Days Meal Plan PDF</CardTitle>
            <CardDescription>
              Upload the pre-made PDF file for the 7-day meal plan
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <input
                  type="file"
                  accept=".pdf"
                  onChange={handleFileChange}
                  className="hidden"
                  id="pdf-upload"
                  required
                  disabled={isSubmitting}
                />
                <label htmlFor="pdf-upload" className={`cursor-pointer ${isSubmitting ? 'opacity-50' : ''}`}>
                  <div className="flex flex-col items-center gap-2">
                    <Upload className="h-8 w-8 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-700">
                        {fileName ? fileName : "Click to upload PDF file"}
                      </p>
                      <p className="text-xs text-gray-500">
                        {fileName ? "File selected" : "PDF files only (max 10MB)"}
                      </p>
                    </div>
                  </div>
                </label>
              </div>
              {fileName && (
                <div className="flex items-center gap-2 text-sm text-green-600">
                  <FileText className="h-4 w-4" />
                  <span>{fileName}</span>
                </div>
              )}
              {uploadProgress && (
                <div className="text-sm text-blue-600">
                  {uploadProgress}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-end gap-3 mt-6">
          <div className="flex gap-4 max-w-md w-full">
            <Link href="/admin/diet-plans">
              <Button
                type={ButtonType.SECONDARY}
                text="Cancel"
                disabled={isSubmitting}
              />
            </Link>
            <button
              type="button"
              className="w-full flex items-center justify-center gap-2 font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer bg-[var(--red-6)] text-white hover:bg-[var(--red-7)] border-0 rounded-none px-6 py-4 text-base h-[3.125rem]"
              onClick={handleSubmit}
              disabled={isSubmitting}
            >
              {isSubmitting ? "Saving..." : "Save Meal Plan Template"}
              {isSubmitting && <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
}
