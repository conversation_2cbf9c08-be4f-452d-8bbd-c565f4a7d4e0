"use client";
import React, { useEffect, useState } from "react";
import { usePageHeader } from "@/contexts/PageHeaderContext";
import { Button } from "@/components/ShadcnUI/button";
import { Input } from "@/components/ShadcnUI/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ShadcnUI/table";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ShadcnUI/dialog";
import { Label } from "@/components/ShadcnUI/label";
import { Badge } from "@/components/ShadcnUI/badge";
import { Search, Plus, Edit, Trash2 } from "lucide-react";

interface City {
  id: number;
  city_name: string;
  state_name?: string;
  _count: {
    clinics: number;
  };
}

export default function CitiesPage() {
  const [cities, setCities] = useState<City[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [search, setSearch] = useState("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingCity, setEditingCity] = useState<City | null>(null);
  const [formData, setFormData] = useState({
    cityName: "",
    stateName: "",
  });

  useEffect(() => {
    fetchCities();
  }, [page, search]);

  const fetchCities = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        per_page: "10",
        search,
      });

      const response = await fetch(`/api/v1/admin/cities?${params}`);
      const data = await response.json();

      if (data.success) {
        setCities(data.data.cities);
        setTotalPages(data.data.totalPages);
      }
    } catch (error) {
      console.error("Error fetching cities:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const url = editingCity
        ? `/api/v1/admin/cities/${editingCity.id}`
        : "/api/v1/admin/cities";

      const method = editingCity ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          cityName: formData.cityName,
          stateName: formData.stateName,
        }),
      });

      if (response.ok) {
        setIsDialogOpen(false);
        setEditingCity(null);
        setFormData({ cityName: "", stateName: "" });
        fetchCities();
      }
    } catch (error) {
      console.error("Error saving city:", error);
    }
  };

  const handleEdit = (city: City) => {
    setEditingCity(city);
    setFormData({
      cityName: city.city_name,
      stateName: city.state_name || "",
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (cityId: number) => {
    if (!confirm("Are you sure you want to delete this city?")) {
      return;
    }

    try {
      const response = await fetch(`/api/v1/admin/cities/${cityId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        fetchCities();
      }
    } catch (error) {
      console.error("Error deleting city:", error);
    }
  };

  const openCreateDialog = () => {
    setEditingCity(null);
    setFormData({ cityName: "", stateName: "" });
    setIsDialogOpen(true);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">Loading...</div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search cities..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={openCreateDialog}>
              <Plus className="h-4 w-4 mr-2" />
              Add City
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {editingCity ? "Edit City" : "Add New City"}
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="cityName">City Name *</Label>
                <Input
                  id="cityName"
                  value={formData.cityName}
                  onChange={(e) =>
                    setFormData({ ...formData, cityName: e.target.value })
                  }
                  required
                />
              </div>
              <div>
                <Label htmlFor="stateName">State Name</Label>
                <Input
                  id="stateName"
                  value={formData.stateName}
                  onChange={(e) =>
                    setFormData({ ...formData, stateName: e.target.value })
                  }
                />
              </div>
              <div className="flex justify-end gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">
                  {editingCity ? "Update" : "Create"}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Cities Table */}
      <div className="bg-white rounded-lg shadow-sm">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>City Name</TableHead>
              <TableHead>State</TableHead>
              <TableHead>Clinics</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {cities.map((city) => (
              <TableRow key={city.id}>
                <TableCell className="font-medium">{city.city_name}</TableCell>
                <TableCell>
                  {city.state_name || (
                    <span className="text-gray-400">Not specified</span>
                  )}
                </TableCell>
                <TableCell>
                  <Badge variant="secondary">
                    {city._count.clinics} clinic
                    {city._count.clinics !== 1 ? "s" : ""}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEdit(city)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(city.id)}
                      disabled={city._count.clinics > 0}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-500">
          Page {page} of {totalPages}
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setPage(page - 1)}
            disabled={page === 1}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            onClick={() => setPage(page + 1)}
            disabled={page === totalPages}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
