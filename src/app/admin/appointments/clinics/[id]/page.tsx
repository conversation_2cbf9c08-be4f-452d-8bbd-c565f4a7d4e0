"use client";

import React, { useEffect, useState } from "react";
import { usePageHeader } from "@/contexts/PageHeaderContext";
import { useParams } from "next/navigation";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ShadcnUI/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ShadcnUI/table";
import Link from "next/link";
import { Button } from "@/components/ShadcnUI/button";
import { ArrowLeft, Eye } from "lucide-react";
import DoctorTemplatesDialog from "@/components/admin/DoctorTemplatesDialog";

interface DoctorClinic {
  doctor: {
    id: number;
    profile: {
      display_name: string;
    };
    specialization_name: string;
    years_of_experience: number;
    consultation_fees: number;
    consultation_currency: string;
  }
}

interface Doctor {
  id: number;
  profile: {
    display_name: string;
  };
  specialization_name: string;
  years_of_experience: number;
  consultation_fees: number;
  consultation_currency: string;
}

interface Clinic {
  id: number;
  clinic_name: string;
  address?: string;
  city?: {
    city_name: string;
    state_name?: string;
  };
  contact_info?: string;
  latitude?: number;
  longitude?: number;
  doctor_clinics: DoctorClinic[];
}

export default function ClinicDetailPage() {
  const { setTitle, setSubtitle } = usePageHeader();
  const params = useParams();
  const clinicId = params.id;

  const [clinic, setClinic] = useState<Clinic | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedDoctor, setSelectedDoctor] = useState<Doctor | null>(null);
  const [templatesDialogOpen, setTemplatesDialogOpen] = useState(false);

  useEffect(() => {
    setTitle("Clinic Details");
  }, [setTitle]);

  useEffect(() => {
    if (clinicId) {
      fetchClinicDetails();
    }
  }, [clinicId]);

  const fetchClinicDetails = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/v1/admin/clinics/${clinicId}`);
      const data = await response.json();

      if (data.success) {
        setClinic(data.data);
        setSubtitle(`Details for ${data.data.clinic_name}`);
      }
    } catch (error) {
      console.error("Error fetching clinic details:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleDoctorClick = (doctor: Doctor) => {
    setSelectedDoctor(doctor);
    setTemplatesDialogOpen(true);
  };

  const handleTemplatesDialogClose = () => {
    setTemplatesDialogOpen(false);
    setSelectedDoctor(null);
  };

  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  if (!clinic) {
    return <div className="flex justify-center items-center h-64">Clinic not found.</div>;
  }

  return (
    <div className="space-y-6">
        <Link href="/admin/appointments/clinics">
            <Button variant="outline" className="mb-4">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Clinics
            </Button>
        </Link>

      <Card>
        <CardHeader>
          <CardTitle>{clinic.clinic_name}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold">Address</h3>
              <p>{clinic.address || "N/A"}</p>
            </div>
            <div>
              <h3 className="font-semibold">City</h3>
              <p>{clinic.city ? `${clinic.city.city_name}, ${clinic.city.state_name}` : "N/A"}</p>
            </div>
            <div>
              <h3 className="font-semibold">Contact Information</h3>
              <p>{clinic.contact_info || "N/A"}</p>
            </div>
            <div>
              <h3 className="font-semibold">Location (Lat, Long)</h3>
              <p>{clinic.latitude && clinic.longitude ? `${clinic.latitude}, ${clinic.longitude}` : "N/A"}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Linked Doctors</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Specialization</TableHead>
                <TableHead>Years of Experience</TableHead>
                <TableHead>Consultation Fees</TableHead>
                <TableHead>Consultation Currency</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {clinic.doctor_clinics && clinic.doctor_clinics.length > 0 ? (
                clinic.doctor_clinics.map((dc) => (
                  <TableRow 
                    key={dc.doctor.id} 
                    className="cursor-pointer hover:bg-muted/50 transition-colors"
                    onClick={() => handleDoctorClick(dc.doctor)}
                  >
                    <TableCell className="flex items-center gap-2">
                      {dc.doctor.profile.display_name}
                      <Eye className="h-4 w-4 text-muted-foreground" />
                    </TableCell>
                    <TableCell>{dc.doctor.specialization_name}</TableCell>
                    <TableCell>{dc.doctor.years_of_experience}</TableCell>
                    <TableCell>{dc.doctor.consultation_fees}</TableCell>
                    <TableCell>{dc.doctor.consultation_currency}</TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={2} className="text-center">
                    No doctors linked to this clinic.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Doctor Templates Dialog */}
      {selectedDoctor && (
        <DoctorTemplatesDialog
          open={templatesDialogOpen}
          onOpenChange={handleTemplatesDialogClose}
          doctor={selectedDoctor}
        />
      )}
    </div>
  );
}
