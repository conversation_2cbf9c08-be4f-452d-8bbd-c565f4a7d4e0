"use client";
import React, { useEffect, useState, useCallback } from "react";
import { usePageHeader } from "@/contexts/PageHeaderContext";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ShadcnUI/select";
import { Input } from "@/components/ShadcnUI/input";
import { Button } from "@/components/ShadcnUI/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ShadcnUI/card";
import { AlertCircle, Search, User, Settings, Calendar, Plus } from "lucide-react";
import UnavailabilityCalendar from "@/components/admin/UnavailabilityCalendar";
import CreateUnavailabilityDialog from "@/components/admin/CreateUnavailabilityDialog";

interface Unavailability {
  id: number;
  doctor_id: number;
  clinic_id?: number;
  date: string;
  start_time: string;
  end_time: string;
  duration: number;
  reason?: string;
  notes?: string;
  clinic?: {
    id: number;
    clinic_name: string;
    address?: string;
    city?: {
      id: number;
      city_name: string;
      state_name?: string;
    };
  };
}

interface Doctor {
  id: number;
  profile: {
    id: number;
    display_name: string;
    email: string;
    phone?: string;
    auth_id: string;
  };
  specialization_name?: string;
  doctor_clinics: {
    clinic: {
      id: number;
      clinic_name: string;
    }
  }[];
  _count?: {
    appointments: number;
    time_slots: number;
  };
}

interface CurrentDoctor {
  id: number;
  profile: {
    display_name: string;
    email: string;
  };
  auth_id: string;
}

export default function DoctorUnavailabilitiesCalendarPage() {
  const { setTitle, setSubtitle } = usePageHeader();
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [selectedDoctorId, setSelectedDoctorId] = useState<string>("");
  const [currentDoctor, setCurrentDoctor] = useState<CurrentDoctor | null>(null);
  const [allUnavailabilities, setAllUnavailabilities] = useState<Unavailability[]>([]);
  const [loading, setLoading] = useState(true);
  const [doctorsLoading, setDoctorsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  const fetchDoctors = useCallback(async () => {
    try {
      setDoctorsLoading(true);
      const params = new URLSearchParams({
        per_page: "100", // Get all doctors for selection
      });

      if (searchTerm) {
        params.append("search", searchTerm);
      }

      const response = await fetch(`/api/v1/admin/doctors?${params}`);
      const data = await response.json();

      if (data.success) {
        setDoctors(data.data.doctors);
      }
    } catch (error) {
      console.error("Error fetching doctors:", error);
      setMessage({ type: 'error', text: 'Failed to load doctors list' });
    } finally {
      setDoctorsLoading(false);
    }
  }, [searchTerm]);

  const fetchSelectedDoctor = useCallback(async () => {
    if (!selectedDoctorId) return;

    try {
      const selectedDoctor = doctors.find(d => d.id.toString() === selectedDoctorId);
      if (selectedDoctor) {
        setCurrentDoctor({
          id: selectedDoctor.id,
          profile: {
            display_name: selectedDoctor.profile.display_name,
            email: selectedDoctor.profile.email,
          },
          auth_id: selectedDoctor.profile.auth_id,
        });
      }
    } catch (error) {
      console.error("Error setting selected doctor:", error);
      setMessage({ type: 'error', text: 'Failed to select doctor' });
    }
  }, [selectedDoctorId, doctors]);

  const fetchAllUnavailabilities = useCallback(async () => {
    if (!currentDoctor) return;

    try {
      setLoading(true);
      const params = new URLSearchParams({
        doctor_id: currentDoctor.id.toString(),
      });

      const response = await fetch(`/api/v1/admin/unavailabilities?${params}`);
      const data = await response.json();

      console.log('All Unavailabilities API response:', data); // Debug log

      if (data.success) {
        setAllUnavailabilities(data.data);
        console.log('Set all unavailabilities:', data.data); // Debug log
      }
    } catch (error) {
      console.error("Error fetching all unavailabilities:", error);
      setMessage({ type: 'error', text: 'Failed to load unavailabilities' });
    } finally {
      setLoading(false);
    }
  }, [currentDoctor]);

  const handleUnavailabilityCreated = () => {
    if (currentDoctor) {
      fetchAllUnavailabilities();
    }
  };

  useEffect(() => {
    setTitle("Doctor Unavailabilities");
    setSubtitle("View doctor availability calendar");
  }, [setTitle, setSubtitle]);

  useEffect(() => {
    fetchDoctors();
  }, [fetchDoctors]);

  useEffect(() => {
    if (selectedDoctorId) {
      fetchSelectedDoctor();
    }
  }, [selectedDoctorId, fetchSelectedDoctor]);

  useEffect(() => {
    if (currentDoctor) {
      fetchAllUnavailabilities();
    }
  }, [currentDoctor, fetchAllUnavailabilities]);

  // Clear message after 5 seconds
  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setMessage(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [message]);

  if (doctorsLoading) {
    return <div className="flex justify-center items-center h-64">Loading doctors...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Message */}
      {message && (
        <div className={`p-4 rounded-lg flex items-center gap-2 ${
          message.type === 'success' 
            ? 'bg-green-50 text-green-800 border border-green-200' 
            : 'bg-red-50 text-red-800 border border-red-200'
        }`}>
          <AlertCircle className="h-4 w-4" />
          {message.text}
        </div>
      )}

      {/* Controls */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <div className="space-y-4">
          {/* Doctor Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Doctor
              </label>
              <Select value={selectedDoctorId} onValueChange={setSelectedDoctorId}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a doctor to view calendar" />
                </SelectTrigger>
                <SelectContent>
                  {doctors.map((doctor) => (
                    <SelectItem key={doctor.id} value={doctor.id.toString()}>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        <div>
                          <div className="font-medium">{doctor.profile.display_name}</div>
                          <div className="text-xs text-gray-500">
                            {doctor.specialization_name || "General Practice"}
                          </div>
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Search Doctors
              </label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by name or specialization..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      fetchDoctors();
                    }
                  }}
                  className="pl-10"
                />
              </div>
            </div>
          </div>

          {/* Current Doctor Info */}
          {currentDoctor && (
            <div className="pt-4 border-t">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <Settings className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <div className="font-medium">
                      {currentDoctor.profile.display_name}
                    </div>
                    <div className="text-sm text-gray-500">
                      Availability Calendar View
                    </div>
                  </div>
                </div>
                <Button
                  onClick={() => setIsCreateDialogOpen(true)}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Create New Unavailability
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* No Doctor Selected */}
      {!currentDoctor && !doctorsLoading && (
        <div className="bg-white rounded-lg shadow-sm p-8 text-center">
          <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Select a Doctor
          </h3>
          <p className="text-gray-600">
            Choose a doctor from the dropdown above to view their availability calendar.
          </p>
        </div>
      )}

      {/* Calendar Loading */}
      {currentDoctor && loading && (
        <div className="bg-white rounded-lg shadow-sm p-8 text-center">
          <div className="text-lg">Loading calendar...</div>
        </div>
      )}

      {/* Calendar View */}
      {currentDoctor && !loading && (
        <div className="space-y-6">
          {/* Calendar */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Uavailability Calendar
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <UnavailabilityCalendar 
                unavailabilities={allUnavailabilities}
                onMonthChange={(date) => {
                  console.log('Month changed to:', date);
                }}
              />
            </CardContent>
          </Card>

          {/* Existing Unavailabilities List */}
          {/* <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                All Unavailabilities
              </CardTitle>
            </CardHeader>
            <CardContent>
              {allUnavailabilities.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Clock className="h-8 w-8 mx-auto mb-2" />
                  <p>No unavailabilities found</p>
                  <p className="text-sm">Create new unavailabilities using the button above</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {allUnavailabilities.map((unavailability) => (
                    <div
                      key={unavailability.id}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div className="flex-1">
                        <div className="font-medium">
                          {format(new Date(unavailability.date), "EEEE, MMMM d, yyyy")}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {unavailability.start_time} - {unavailability.end_time}
                          {unavailability.clinic && (
                            <span className="ml-2">
                              • {unavailability.clinic.clinic_name}
                            </span>
                          )}
                          {unavailability.reason && (
                            <span className="ml-2">
                              • {unavailability.reason}
                            </span>
                          )}
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => deleteUnavailability(unavailability.id)}
                        className="flex items-center gap-2"
                      >
                        <Trash2 className="h-4 w-4" />
                        Delete
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card> */}
        </div>
      )}

      {/* Instructions */}
      <div className="bg-white p-4 rounded-lg shadow-sm">
        <div className="text-sm text-gray-600">
          <h4 className="font-medium mb-2">How to use:</h4>
          <ul className="list-disc list-inside space-y-1 text-xs">
            <li>Select a doctor from the dropdown above</li>
            <li>View their availability calendar with color-coded unavailable dates</li>
            <li>Click on unavailable dates to see details</li>
            <li>Use the &quot;Create New Unavailability&quot; button to open a dialog and add new unavailable periods</li>
            <li>Delete unavailabilities from the list below</li>
          </ul>
        </div>
      </div>

      {/* Create Unavailability Dialog */}
      <CreateUnavailabilityDialog
        doctor={currentDoctor}
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onUnavailabilityCreated={handleUnavailabilityCreated}
      />
    </div>
  );
}
