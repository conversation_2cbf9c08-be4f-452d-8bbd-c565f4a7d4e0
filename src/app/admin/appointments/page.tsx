"use client";
import React, { useEffect, useState } from "react";
import { usePageHeader } from "@/contexts/PageHeaderContext";
import { Button } from "@/components/ShadcnUI/button";
import { Input } from "@/components/ShadcnUI/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ShadcnUI/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ShadcnUI/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ShadcnUI/dropdown-menu";
import { Badge } from "@/components/ShadcnUI/badge";
import { MoreHorizontal, Search, Filter } from "lucide-react";
import { format } from "date-fns";
import { useBreadcrumb } from "@/contexts/BreadcrumbContext";

interface Appointment {
  id: number;
  doctor: {
    profile: {
      display_name: string;
      email: string;
    };
  };
  patient?: {
    profile: {
      display_name: string;
      email: string;
      phone: string;
    };
  };
  clinic: {
    clinic_name: string;
    address: string;
  };
  appointment_date: string;
  start_time: string;
  end_time: string;
  consultation_type: "in_person" | "online";
  status: "completed" | "upcoming" | "cancelled";
  duration: number;
  fees: number;
  currency: string;
  payment_status: "paid" | "unpaid";
  booking_date?: string;
}

export default function AppointmentsPage() {
  const { setTitle, setSubtitle } = usePageHeader();
  const { setBreadcrumbs } = useBreadcrumb();
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filters, setFilters] = useState({
    search: "",
    status: "all",
    consultationType: "all",
    doctorId: "",
    clinicId: "",
    startDate: "",
    endDate: "",
  });

  useEffect(() => {
    setTitle("Appointments");
    setSubtitle("Manage all appointments");
    setBreadcrumbs(null);
  }, [setTitle, setSubtitle]);

  useEffect(() => {
    fetchAppointments();
  }, [page, filters]);

  const fetchAppointments = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        per_page: "10",
      });

      // Only add filter params if they have meaningful values
      if (filters.search) params.append("search", filters.search);
      if (filters.status && filters.status !== "all")
        params.append("status", filters.status);
      if (filters.consultationType && filters.consultationType !== "all")
        params.append("consultationType", filters.consultationType);
      if (filters.doctorId) params.append("doctorId", filters.doctorId);
      if (filters.clinicId) params.append("clinicId", filters.clinicId);
      if (filters.startDate) params.append("startDate", filters.startDate);
      if (filters.endDate) params.append("endDate", filters.endDate);

      const response = await fetch(`/api/v1/admin/appointments?${params}`);
      const data = await response.json();

      if (data.success) {
        setAppointments(data.data.appointments);
        setTotalPages(data.data.totalPages);
      }
    } catch (error) {
      console.error("Error fetching appointments:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleStatusUpdate = async (
    appointmentId: number,
    newStatus: string
  ) => {
    try {
      const response = await fetch(
        `/api/v1/admin/appointments/${appointmentId}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ status: newStatus }),
        }
      );

      if (response.ok) {
        fetchAppointments();
      }
    } catch (error) {
      console.error("Error updating appointment status:", error);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      completed: { variant: "default" as const, text: "Completed" },
      upcoming: { variant: "secondary" as const, text: "Upcoming" },
      cancelled: { variant: "destructive" as const, text: "Cancelled" },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    return <Badge variant={config.variant}>{config.text}</Badge>;
  };

  const getConsultationTypeBadge = (type: string) => {
    return (
      <Badge variant={type === "online" ? "outline" : "default"}>
        {type === "online" ? "Online" : "In-Person"}
      </Badge>
    );
  };

  const getPaymentStatusBadge = (status: string) => {
    return (
      <Badge variant={status === "paid" ? "default" : "secondary"}>
        {status === "paid" ? "Paid" : "Unpaid"}
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">Loading...</div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search patients..."
              value={filters.search}
              onChange={(e) =>
                setFilters({ ...filters, search: e.target.value })
              }
              className="pl-10"
            />
          </div>
          <Select
            value={filters.status}
            onValueChange={(value) => setFilters({ ...filters, status: value })}
          >
            <SelectTrigger>
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="upcoming">Upcoming</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>
          <Select
            value={filters.consultationType}
            onValueChange={(value) =>
              setFilters({ ...filters, consultationType: value })
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Consultation Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="in_person">In-Person</SelectItem>
              <SelectItem value="online">Online</SelectItem>
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            onClick={() =>
              setFilters({
                search: "",
                status: "all",
                consultationType: "all",
                doctorId: "",
                clinicId: "",
                startDate: "",
                endDate: "",
              })
            }
          >
            <Filter className="h-4 w-4 mr-2" />
            Clear Filters
          </Button>
        </div>
      </div>

      {/* Appointments Table */}
      <div className="bg-white rounded-lg shadow-sm">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Patient</TableHead>
              <TableHead>Doctor</TableHead>
              <TableHead>Clinic</TableHead>
              <TableHead>Date & Time</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Payment</TableHead>
              <TableHead>Fees</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {appointments.map((appointment) => (
              <TableRow key={appointment.id}>
                <TableCell>
                  <div>
                    <div className="font-medium">
                      {appointment.patient?.profile.display_name || "N/A"}
                    </div>
                    <div className="text-sm text-gray-500">
                      {appointment.patient?.profile.email ||
                        "No patient assigned"}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">
                      {appointment.doctor.profile.display_name}
                    </div>
                    <div className="text-sm text-gray-500">
                      {appointment.doctor.profile.email}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">
                      {appointment.clinic.clinic_name}
                    </div>
                    <div className="text-sm text-gray-500">
                      {appointment.clinic.address}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">
                      {format(
                        new Date(appointment.appointment_date),
                        "MMM dd, yyyy"
                      )}
                    </div>
                    <div className="text-sm text-gray-500">
                      {format(new Date(appointment.start_time), "HH:mm")} -{" "}
                      {format(new Date(appointment.end_time), "HH:mm")}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  {getConsultationTypeBadge(appointment.consultation_type)}
                </TableCell>
                <TableCell>{getStatusBadge(appointment.status)}</TableCell>
                <TableCell>
                  {getPaymentStatusBadge(appointment.payment_status)}
                </TableCell>
                <TableCell>
                  {appointment.currency} {appointment.fees}
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>View Details</DropdownMenuItem>
                      {appointment.status === "upcoming" && (
                        <>
                          <DropdownMenuItem
                            onClick={() =>
                              handleStatusUpdate(appointment.id, "completed")
                            }
                          >
                            Mark Completed
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() =>
                              handleStatusUpdate(appointment.id, "cancelled")
                            }
                          >
                            Cancel Appointment
                          </DropdownMenuItem>
                        </>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-500">
          Page {page} of {totalPages}
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setPage(page - 1)}
            disabled={page === 1}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            onClick={() => setPage(page + 1)}
            disabled={page === totalPages}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
