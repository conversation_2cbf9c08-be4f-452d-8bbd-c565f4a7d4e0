import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ShadcnUI/card";
import { Button as ShadcnButton } from "@/components/ShadcnUI/button";
import Button, { ButtonType } from "@/components/shared/Button/Button";

import { Badge } from "@/components/ShadcnUI/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ShadcnUI/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ShadcnUI/select";
import {
  Users,
  UserPlus,
  Edit3,
  Trash2,
  ChevronLeft,
  ChevronRight,
  Search,
  Filter,
  MoreHorizontal,
  Mail,
  Calendar as CalendarIcon2,
  User,
  ArrowUpDown,
  ArrowUp,
  ArrowDown
} from "lucide-react";
import {
  deleteUser,
  getUsers,
  getUserStats,
  type UsersPageProps,
} from "@/lib/services/user-management.service";
import {
  getUserDisplayName,
  getProviderBadge,
  formatDate,
  PER_PAGE,
} from "@/lib/utils/user-management.utils";

// Helper function to build URL with query parameters
function buildQueryUrl(params: Record<string, string | number | undefined>) {
  const searchParams = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== '') {
      searchParams.append(key, String(value));
    }
  });
  return searchParams.toString() ? `?${searchParams.toString()}` : '';
}

export default async function UsersPage({ searchParams }: UsersPageProps) {
  const resolvedSearchParams = await searchParams;
  const page = parseInt(resolvedSearchParams?.page || '1');
  const search = resolvedSearchParams?.search || '';
  const perPage = parseInt(resolvedSearchParams?.per_page || PER_PAGE.toString());
  const sortBy = resolvedSearchParams?.sortBy || 'created_at';
  const sortDirection = (resolvedSearchParams?.sortDirection as 'asc' | 'desc') || 'desc';
  const status = resolvedSearchParams?.status || 'all';
  const provider = resolvedSearchParams?.provider || 'all';
  const dateFrom = resolvedSearchParams?.dateFrom || '';
  const dateTo = resolvedSearchParams?.dateTo || '';
  
  const filters = {
    status: status as 'all' | 'verified' | 'pending',
    provider: provider === 'all' ? undefined : provider,
    dateFrom: dateFrom || undefined,
    dateTo: dateTo || undefined,
  };
  
  const { users, total, error } = await getUsers(page, search, perPage, sortBy, sortDirection, filters);
  const userStats = await getUserStats(filters);
  
  const totalPages = Math.ceil(total / perPage);

  // Helper function to build sort URL
  const buildSortUrl = (newSortBy: string, newSortDirection: 'asc' | 'desc') => {
    const params = {
      page: '1', // Reset to first page when sorting
      search,
      per_page: perPage.toString(),
      sortBy: newSortBy,
      sortDirection: newSortDirection,
      status,
      provider,
      dateFrom,
      dateTo,
    };
    return buildQueryUrl(params);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Users className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-foreground">User Management</h1>
            <p className="text-muted-foreground">
              Manage user accounts and permissions
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            type={ButtonType.PRIMARY}
            size="sm"
            text="Add User"
            icon={<UserPlus className="h-4 w-4" />}
            className="rounded whitespace-nowrap px-4 py-3 text-base font-semibold"
          />
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Total Users</span>
            </div>
            <div className="text-2xl font-bold">{userStats.totalUsers}</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Mail className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Verified</span>
            </div>
            <div className="text-2xl font-bold">
              {userStats.verifiedUsers}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <CalendarIcon2 className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Active Today</span>
            </div>
            <div className="text-2xl font-bold">
              {userStats.activeTodayUsers}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form method="get" className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <input
                  type="text"
                  name="search"
                  placeholder="Search users..."
                  defaultValue={search}
                  className="pl-10 pr-4 py-2 border rounded-md text-sm w-full"
                />
              </div>
            </div>

            {/* Status Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <Select name="status" defaultValue={status}>
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="verified">Verified</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Provider Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Provider</label>
              <Select name="provider" defaultValue={provider}>
                <SelectTrigger>
                  <SelectValue placeholder="All providers" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Providers</SelectItem>
                  <SelectItem value="email">Email</SelectItem>
                  <SelectItem value="google">Google</SelectItem>
                  <SelectItem value="github">GitHub</SelectItem>
                  <SelectItem value="facebook">Facebook</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Date Range Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Created Date</label>
              <div className="flex gap-2">
                <input
                  type="date"
                  name="dateFrom"
                  defaultValue={dateFrom}
                  className="px-3 py-2 border rounded-md text-sm w-full"
                  placeholder="From date"
                />
                <input
                  type="date"
                  name="dateTo"
                  defaultValue={dateTo}
                  className="px-3 py-2 border rounded-md text-sm w-full"
                  placeholder="To date"
                />
              </div>
            </div>

            {/* Hidden fields for current state */}
            <input type="hidden" name="page" value="1" />
            <input type="hidden" name="per_page" value={perPage} />
            <input type="hidden" name="sortBy" value={sortBy} />
            <input type="hidden" name="sortDirection" value={sortDirection} />

            {/* Filter Actions */}
            <div className="md:col-span-4 flex items-center gap-2">
              <ShadcnButton type="submit" size="sm">
                Apply Filters
              </ShadcnButton>
              <a 
                href="/admin/users"
                className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] h-8 px-3 border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground"
              >
                Clear All
              </a>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Users List
              </CardTitle>
              <CardDescription>
                {search ? `Showing results for "${search}"` : `Showing ${users.length} of ${total} users`}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {error ? (
            <div className="text-center py-8">
              <div className="text-red-600 mb-2">Error loading users</div>
              <div className="text-sm text-muted-foreground">{error}</div>
            </div>
          ) : (
            <>
              {users.length === 0 ? (
                <div className="text-center py-12">
                  <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No users found</h3>
                  <p className="text-muted-foreground mb-4">
                    {search ? 'Try adjusting your search criteria' : 'Get started by adding your first user'}
                  </p>
                  <Button
                    type={ButtonType.PRIMARY}
                    size="sm"
                    text="Add User"
                    icon={<UserPlus className="h-4 w-4" />}
                    className="rounded whitespace-nowrap px-4 py-3 text-base font-semibold"
                  />
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Table */}
                  <div className="rounded-lg border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[300px]">User</TableHead>
                          <TableHead>
                            <a href={buildSortUrl('app_metadata.provider', sortBy === 'app_metadata.provider' && sortDirection === 'asc' ? 'desc' : 'asc')} className="flex items-center gap-1 hover:text-primary">
                              Provider
                              {sortBy === 'app_metadata.provider' ? (
                                sortDirection === 'asc' ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />
                              ) : (
                                <ArrowUpDown className="h-3 w-3 text-muted-foreground" />
                              )}
                            </a>
                          </TableHead>
                          <TableHead>
                            <a href={buildSortUrl('email_confirmed_at', sortBy === 'email_confirmed_at' && sortDirection === 'asc' ? 'desc' : 'asc')} className="flex items-center gap-1 hover:text-primary">
                              Status
                              {sortBy === 'email_confirmed_at' ? (
                                sortDirection === 'asc' ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />
                              ) : (
                                <ArrowUpDown className="h-3 w-3 text-muted-foreground" />
                              )}
                            </a>
                          </TableHead>
                          <TableHead>
                            <a href={buildSortUrl('created_at', sortBy === 'created_at' && sortDirection === 'asc' ? 'desc' : 'asc')} className="flex items-center gap-1 hover:text-primary">
                              Created
                              {sortBy === 'created_at' ? (
                                sortDirection === 'asc' ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />
                              ) : (
                                <ArrowUpDown className="h-3 w-3 text-muted-foreground" />
                              )}
                            </a>
                          </TableHead>
                          <TableHead>
                            <a href={buildSortUrl('last_sign_in_at', sortBy === 'last_sign_in_at' && sortDirection === 'asc' ? 'desc' : 'asc')} className="flex items-center gap-1 hover:text-primary">
                              Last Sign In
                              {sortBy === 'last_sign_in_at' ? (
                                sortDirection === 'asc' ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />
                              ) : (
                                <ArrowUpDown className="h-3 w-3 text-muted-foreground" />
                              )}
                            </a>
                          </TableHead>
                          <TableHead className="w-[100px]">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {users.map((user) => (
                          <TableRow key={user.id}>
                            <TableCell>
                              <div className="flex items-center gap-3">
                                <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                                  <User className="h-4 w-4 text-primary" />
                                </div>
                                <div>
                                  <div className="font-medium">{getUserDisplayName(user)}</div>
                                  <div className="text-sm text-muted-foreground">{user.email}</div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge 
                                variant="secondary" 
                                className={getProviderBadge(user)}
                              >
                                {user.app_metadata?.provider || 'email'}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <div className={`w-2 h-2 rounded-full ${
                                  user.email_confirmed_at ? 'bg-green-500' : 'bg-yellow-500'
                                }`} />
                                <span className="text-sm">
                                  {user.email_confirmed_at ? 'Verified' : 'Pending'}
                                </span>
                              </div>
                            </TableCell>
                            <TableCell className="text-sm text-muted-foreground">
                              {formatDate(user.created_at)}
                            </TableCell>
                            <TableCell className="text-sm text-muted-foreground">
                              {formatDate(user.last_sign_in_at)}
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-1">
                                <ShadcnButton size="sm" variant="ghost" className="h-8 w-8 p-0" title="Edit User">
                                  <Edit3 className="h-3 w-3" />
                                </ShadcnButton>
                                <form action={deleteUser} style={{ display: 'inline' }}>
                                  <input type="hidden" name="userId" value={user.id} />
                                  <ShadcnButton 
                                    type="submit" 
                                    size="sm" 
                                    variant="ghost" 
                                    className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                                    title="Delete User"
                                  >
                                    <Trash2 className="h-3 w-3" />
                                  </ShadcnButton>
                                </form>
                                <ShadcnButton size="sm" variant="ghost" className="h-8 w-8 p-0" title="More Options">
                                  <MoreHorizontal className="h-3 w-3" />
                                </ShadcnButton>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>

                  {/* Pagination */}
                  {totalPages > 1 && (
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-muted-foreground">
                        Showing {((page - 1) * perPage) + 1} to {Math.min(page * perPage, total)} of {total} users
                      </div>
                      <div className="flex items-center gap-2">
                        <ShadcnButton
                          variant="outline"
                          size="sm"
                          disabled={page <= 1}
                          asChild
                        >
                          <a href={buildQueryUrl({
                            page: page - 1,
                            search,
                            per_page: perPage,
                            sortBy,
                            sortDirection,
                            status,
                            provider,
                            dateFrom,
                            dateTo,
                          })}>
                            <ChevronLeft className="h-4 w-4" />
                            Previous
                          </a>
                        </ShadcnButton>
                        
                        <div className="flex items-center gap-1">
                          {[...Array(Math.min(totalPages, 5))].map((_, i) => {
                            const pageNum = i + 1;
                            return (
                              <ShadcnButton
                                key={pageNum}
                                variant={page === pageNum ? "default" : "outline"}
                                size="sm"
                                asChild
                              >
                                <a href={buildQueryUrl({
                                  page: pageNum,
                                  search,
                                  per_page: perPage,
                                  sortBy,
                                  sortDirection,
                                  status,
                                  provider,
                                  dateFrom,
                                  dateTo,
                                })}>
                                  {pageNum}
                                </a>
                              </ShadcnButton>
                            );
                          })}
                        </div>

                        <ShadcnButton
                          variant="outline"
                          size="sm"
                          disabled={page >= totalPages}
                          asChild
                        >
                          <a href={buildQueryUrl({
                            page: page + 1,
                            search,
                            per_page: perPage,
                            sortBy,
                            sortDirection,
                            status,
                            provider,
                            dateFrom,
                            dateTo,
                          })}>
                            Next
                            <ChevronRight className="h-4 w-4" />
                          </a>
                        </ShadcnButton>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
