import React from "react";
import <PERSON> from "next/link";
import { <PERSON><PERSON> } from "@/components/ShadcnUI/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ShadcnUI/card";
import { Badge } from "@/components/ShadcnUI/badge";
import {
  Shield,
  Users,
  Settings,
  BarChart3,
  ArrowRight,
  CheckCircle,
  AlertCircle,
  Heart
} from "lucide-react";

export default function AdminDashboard() {
  const quickActions = [
    {
      title: "Manage Roles & Permissions",
      description: "Create and configure user roles with specific permissions",
      href: "/admin/roles-permissions",
      icon: Shield,
      color: "bg-blue-500",
    },
    {
      title: "User Management",
      description: "View and manage user accounts and their role assignments",
      href: "/admin/users",
      icon: Users,
      color: "bg-green-500",
    },
    {
      title: "System Analytics",
      description: "View system usage statistics and performance metrics",
      href: "/admin/analytics",
      icon: BarChart3,
      color: "bg-purple-500",
    },
    {
      title: "Fertility Questions",
      description: "Configure and manage fertility assessment questions",
      href: "/admin/fertility-meter-questions",
      icon: Heart,
      color: "bg-pink-500",
    },
    {
      title: "System Settings",
      description: "Configure system-wide settings and preferences",
      href: "/admin/settings",
      icon: Settings,
      color: "bg-orange-500",
    },
  ];

  const systemStatus = [
    {
      name: "Database",
      status: "healthy",
      description: "All connections active",
    },
    {
      name: "Authentication",
      status: "healthy",
      description: "JWT tokens working",
    },
    {
      name: "API Services",
      status: "healthy",
      description: "All endpoints responding",
    },
    {
      name: "Guest Sessions",
      status: "healthy",
      description: "Session management active",
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
        <p className="text-muted-foreground">
          Welcome to the GIVF administration panel. Manage your system from here.
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,234</div>
            <p className="text-xs text-muted-foreground">
              +12% from last month
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Roles</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">8</div>
            <p className="text-xs text-muted-foreground">
              Configured roles
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">IVF Assessments</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">567</div>
            <p className="text-xs text-muted-foreground">
              Completed this month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Guest Sessions</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">89</div>
            <p className="text-xs text-muted-foreground">
              Active sessions
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common administrative tasks and system management
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            {quickActions.map((action) => {
              const Icon = action.icon;
              return (
                <Card key={action.href} className="transition-colors hover:bg-muted/50">
                  <CardContent className="p-4">
                    <div className="flex items-start gap-4">
                      <div className={`p-2 rounded-lg ${action.color} text-white`}>
                        <Icon className="h-5 w-5" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-semibold mb-1">{action.title}</h3>
                        <p className="text-sm text-muted-foreground mb-3">
                          {action.description}
                        </p>
                        <Button variant="outline" size="sm" asChild>
                          <Link href={action.href} className="gap-2">
                            Open
                            <ArrowRight className="h-3 w-3" />
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle>System Status</CardTitle>
          <CardDescription>
            Current status of system components and services
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {systemStatus.map((item) => (
              <div key={item.name} className="flex items-center justify-between p-3 rounded-lg border">
                <div className="flex items-center gap-3">
                  {item.status === "healthy" ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <AlertCircle className="h-5 w-5 text-red-500" />
                  )}
                  <div>
                    <div className="font-medium">{item.name}</div>
                    <div className="text-sm text-muted-foreground">{item.description}</div>
                  </div>
                </div>
                <Badge 
                  variant={item.status === "healthy" ? "default" : "destructive"}
                  className={item.status === "healthy" ? "bg-green-500" : ""}
                >
                  {item.status}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
