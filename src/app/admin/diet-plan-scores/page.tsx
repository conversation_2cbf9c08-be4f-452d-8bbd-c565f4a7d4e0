import React from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ShadcnUI/card";
import Button, { ButtonType } from "@/components/shared/Button/Button";
import { Button as ShadcnButton } from "@/components/ShadcnUI/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ShadcnUI/table";
import {
  FileText,
  PlusCircle,
  Edit,
  Trash2,
  Search,
  Filter,
  Eye,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { getDietPlanScores, deleteDietPlanScore } from "@/lib/services/diet-plan-scores.service";
import { formatDate } from "@/lib/utils/user-management.utils";
import Link from "next/link";
import { getProfilesByUserIds } from "@/lib/services/user.service";
import {
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
} from "lucide-react";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export default async function DietPlanScoresPage({ searchParams }: any) {
  // Await searchParams if it's a Promise
  if (typeof searchParams?.then === "function") {
    searchParams = await searchParams;
  }

  const page = parseInt(searchParams?.page || "1");
  const search = searchParams?.search || "";
  const perPage = parseInt(searchParams?.per_page || "10");
  const sortBy = searchParams?.sortBy || 'created_at';
  const sortDirection = (searchParams?.sortDirection as 'asc' | 'desc') || 'desc';
  const dateFrom = searchParams?.dateFrom || '';
  const dateTo = searchParams?.dateTo || '';
  
  const filters = {
    dateFrom: dateFrom || undefined,
    dateTo: dateTo || undefined,
  };

  const { scores: dietPlanScores, total, error } = await getDietPlanScores(page, search, perPage, sortBy, sortDirection, filters);

  const userIds = dietPlanScores.map((score) => score.user_id);
  const profiles = await getProfilesByUserIds(userIds);
  const totalPages = Math.ceil(total / perPage);

  // Helper function to build URL with query parameters
  function buildQueryUrl(params: Record<string, string | number | undefined>) {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== '') {
        searchParams.append(key, String(value));
      }
    });
    return searchParams.toString() ? `?${searchParams.toString()}` : '';
  }

  // Helper function to build sort URL
  const buildSortUrl = (newSortBy: string, newSortDirection: 'asc' | 'desc') => {
    const params = {
      page: '1', // Reset to first page when sorting
      search,
      per_page: perPage.toString(),
      sortBy: newSortBy,
      sortDirection: newSortDirection,
      dateFrom,
      dateTo,
    };
    return buildQueryUrl(params);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <FileText className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-foreground">Diet Plan Scores</h1>
            <p className="text-muted-foreground">
              Manage and view BMR (Basal Metabolic Rate) scores
            </p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Button
            type={ButtonType.PRIMARY}
            size="sm"
            text="Add Score"
            icon={<PlusCircle className="h-4 w-4" />}
            className="gap-2 whitespace-nowrap"
          />
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form method="get" className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <input
                  type="text"
                  name="search"
                  placeholder="Search by email..."
                  defaultValue={search}
                  className="pl-10 pr-4 py-2 border rounded-md text-sm w-full"
                />
              </div>
            </div>

            {/* Date Range Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Created Date</label>
              <div className="flex gap-2">
                <input
                  type="date"
                  name="dateFrom"
                  defaultValue={dateFrom}
                  className="px-3 py-2 border rounded-md text-sm w-full"
                  placeholder="From date"
                />
                <input
                  type="date"
                  name="dateTo"
                  defaultValue={dateTo}
                  className="px-3 py-2 border rounded-md text-sm w-full"
                  placeholder="To date"
                />
              </div>
            </div>

            {/* Hidden fields for current state */}
            <input type="hidden" name="page" value="1" />
            <input type="hidden" name="per_page" value={perPage} />
            <input type="hidden" name="sortBy" value={sortBy} />
            <input type="hidden" name="sortDirection" value={sortDirection} />

            {/* Filter Actions */}
            <div className="md:col-span-4 flex items-center gap-2">
              <ShadcnButton type="submit" size="sm">
                Apply Filters
              </ShadcnButton>
              <Link
                href="/admin/diet-plan-scores"
                className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] h-8 px-3 border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground"
              >
                Clear All
              </Link>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Diet Plan Scores Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Diet Plan Scores List
              </CardTitle>
              <CardDescription>
                {search
                  ? `Showing results for "${search}"`
                  : `Showing ${dietPlanScores.length} of ${total} scores`}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {error ? (
            <div className="text-center py-8">
              <div className="text-red-600 mb-2">Error loading scores</div>
              <div className="text-sm text-muted-foreground">{error}</div>
            </div>
          ) : dietPlanScores.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No diet plan scores found</h3>
              <p className="text-muted-foreground mb-4">
                {search
                  ? "Try adjusting your search criteria"
                  : "No BMR scores have been recorded yet"}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Table */}
              <div className="rounded-lg border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>
                        <a href={buildSortUrl('user.email', sortBy === 'user.email' && sortDirection === 'asc' ? 'desc' : 'asc')} className="flex items-center gap-1 hover:text-primary">
                          Patient
                          {sortBy === 'user.email' ? (
                            sortDirection === 'asc' ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />
                          ) : (
                            <ArrowUpDown className="h-3 w-3 text-muted-foreground" />
                          )}
                        </a>
                      </TableHead>
                     
                      <TableHead>
                        <a href={buildSortUrl('created_at', sortBy === 'created_at' && sortDirection === 'asc' ? 'desc' : 'asc')} className="flex items-center gap-1 hover:text-primary">
                          Created
                          {sortBy === 'created_at' ? (
                            sortDirection === 'asc' ? <ArrowUp className="h-3 w-3" /> : <ArrowDown className="h-3 w-3" />
                          ) : (
                            <ArrowUpDown className="h-3 w-3 text-muted-foreground" />
                          )}
                        </a>
                      </TableHead>
                      <TableHead className="w-[100px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {dietPlanScores.map((score) => {
                      const profile = profiles.find(p=> p.auth_id === score.user_id);
                      return (
                        <TableRow key={score.id}>
                          <TableCell>
                            <Link href={`/admin/diet-plan-scores/${score.user_id}`}>
                            <div className="font-medium">{profile?.display_name || 'N/A'}</div>
                            <div className="text-sm text-muted-foreground">{profile?.email || 'N/A'}</div>
                            </Link>
                          </TableCell>
                          <TableCell className="text-sm text-muted-foreground">
                            {formatDate(score.created_at.toString())}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <ShadcnButton
                                size="sm"
                                variant="ghost"
                                className="h-8 w-8 p-0"
                                title="View Details"
                                asChild
                              >
                                <Link href={`/admin/diet-plan-scores/${score.user_id}`}>
                                  <Eye className="h-4 w-4" />
                                </Link>
                              </ShadcnButton>
                              <ShadcnButton
                                size="sm"
                                variant="ghost"
                                className="h-8 w-8 p-0"
                                title="Edit Score"
                                asChild
                              >
                                <Link href={`/admin/diet-plan-scores/${score.user_id}/edit`}>
                                  <Edit className="h-4 w-4" />
                                </Link>
                              </ShadcnButton>
                              <form action={deleteDietPlanScore} style={{ display: 'inline' }}>
                                <input type="hidden" name="scoreId" value={score.id} />
                                <ShadcnButton
                                  type="submit"
                                  size="sm"
                                  variant="ghost"
                                  className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                                  title="Delete Score"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </ShadcnButton>
                              </form>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    Showing {((page - 1) * perPage) + 1} to {Math.min(page * perPage, total)} of {total} scores
                  </div>
                  <div className="flex items-center gap-2">
                    <ShadcnButton
                      variant="outline"
                      size="sm"
                      disabled={page <= 1}
                      asChild
                    >
                      <a href={buildQueryUrl({
                        page: page - 1,
                        search,
                        per_page: perPage,
                        sortBy,
                        sortDirection,
                        dateFrom,
                        dateTo,
                      })}>
                        <ChevronLeft className="h-4 w-4" />
                        Previous
                      </a>
                    </ShadcnButton>
                    
                    <div className="flex items-center gap-1">
                      {[...Array(Math.min(totalPages, 5))].map((_, i) => {
                        const pageNum = i + 1;
                        return (
                          <ShadcnButton
                            key={pageNum}
                            variant={page === pageNum ? "default" : "outline"}
                            size="sm"
                            asChild
                          >
                            <a href={buildQueryUrl({
                              page: pageNum,
                              search,
                              per_page: perPage,
                              sortBy,
                              sortDirection,
                              dateFrom,
                              dateTo,
                            })}>
                              {pageNum}
                            </a>
                          </ShadcnButton>
                        );
                      })}
                    </div>

                    <ShadcnButton
                      variant="outline"
                      size="sm"
                      disabled={page >= totalPages}
                      asChild
                    >
                      <a href={buildQueryUrl({
                        page: page + 1,
                        search,
                        per_page: perPage,
                        sortBy,
                        sortDirection,
                        dateFrom,
                        dateTo,
                      })}>
                        Next
                        <ChevronRight className="h-4 w-4" />
                      </a>
                    </ShadcnButton>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
