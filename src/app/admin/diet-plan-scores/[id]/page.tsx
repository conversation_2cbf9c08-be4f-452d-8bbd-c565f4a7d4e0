"use client";
import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ShadcnUI/card";
import { Badge } from "@/components/ShadcnUI/badge";
import { getUserDietPlanScoreAndCalculate } from '@/lib/services/diet-plan-scores.service';
import { FileText } from "lucide-react";
import PageLoader from "@/components/shared/PageLoader";

async function getScoreDetails(userId: string) {
  const result = await getUserDietPlanScoreAndCalculate({ userId, debug: true, selectUser: true });
  if ('error' in result) {
    return null;
  }
  const { scoreData, dietPlanScore } = result;
  console.log(scoreData)
  return {
    score: scoreData,
    user: dietPlanScore.user,
  };
}

export default function DietPlanScoreDetailPage() {
  const params = useParams();
  const id = params?.id as string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!id) return;
    setLoading(true);
    getScoreDetails(id)
      .then((res) => {
        if (!res) {
          setError("Not found");
        } else {
          setData(res);
        }
        setLoading(false);
      })
      .catch(() => {
        setError("Error loading data");
        setLoading(false);
      });
  }, [id]);

  if (loading) {
    return <PageLoader />;
  }

  if (error || !data) {
    return (
      <div className="flex justify-center items-center min-h-[400px] text-red-500">{typeof error === 'string' ? error : "Not found"}</div>
    );
  }

  const { score } = data;
  const { score: bmrScore } = score;

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <div className="p-2 bg-primary/10 rounded-lg">
          <FileText className="h-6 w-6 text-primary" />
        </div>
        <div>
          <h1 className="text-3xl font-bold text-foreground">Diet Plan Score Details</h1>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User Info Card */}
        <Card>
          <CardHeader>
            <CardTitle>User Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Name:</span>
                <span className="font-semibold">{data?.user?.display_name || "N/A"}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">Email:</span>
                <span className="font-semibold">{data?.user?.email || "N/A"}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* BMR Score Card */}
        <Card>
          <CardHeader>
            <CardTitle>BMR (Basal Metabolic Rate)</CardTitle>
            <CardDescription>Target daily calorie intake</CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <div className="space-y-4">
              <div>
                <p className="text-4xl font-bold text-primary">{bmrScore.toFixed(0)}</p>
                <p className="text-muted-foreground">calories/day</p>
              </div>
              <Badge variant="secondary" className="text-lg">
                Target Calorie Intake
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Diet Plan Recommendation */}
      
    </div>
  );
}
