"use client";
import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ShadcnUI/card";
import { Badge } from "@/components/ShadcnUI/badge";
import { getUserIVFScoresAndCalculate } from '@/lib/services/ivf-scores.service';
import { FileText } from "lucide-react";
import PageLoader from "@/components/shared/PageLoader";

async function getScoreDetails(userId: string) {
  const result = await getUserIVFScoresAndCalculate({ userId, debug: true, selectUser: true });
  if ('error' in result) {
    return null;
  }
  const { scoreData, ivfScores } = result;
  console.log(scoreData)
  return {
    score: scoreData,
    user: ivfScores.user,
    selected_track: ivfScores.selected_track || "N/A"
  };
}

export default function ScoreDetailPage() {
  const params = useParams();
  const id = params?.id as string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!id) return;
    setLoading(true);
    getScoreDetails(id)
      .then((res) => {
        if (!res) {
          setError("Not found");
        } else {
          setData(res);
        }
        setLoading(false);
      })
      .catch(() => {
        setError("Error loading data");
        setLoading(false);
      });
  }, [id]);

  if (loading) {
    return <PageLoader />;
  }

  if (error || !data) {
    return (
      <div className="flex justify-center items-center min-h-[400px] text-red-500">{typeof error === 'string' ? error : "Not found"}</div>
    );
  }

  const { score, selected_track } = data;
  const { totalScore, maxScore, percentage, category, factors, avgFactors } = score;


  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <div className="p-2 bg-primary/10 rounded-lg">
          <FileText className="h-6 w-6 text-primary" />
        </div>
        <div>
          <h1 className="text-3xl font-bold text-foreground">IVF Score Details</h1>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          {/* User Info and Selected Track Card */}
          <Card>
            <CardHeader>
              <CardTitle>User Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Name:</span>
                  <span className="font-semibold">{data?.user?.display_name || "N/A"}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Email:</span>
                  <span className="font-semibold">{data?.user?.email || "N/A"}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-muted-foreground">Selected Track:</span>
                  <Badge variant="secondary">{selected_track}</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
          {/* Please add user's info and selected track */}
          <Card>
            <CardHeader>
              <CardTitle>Overall Score</CardTitle>
            </CardHeader>
            <CardContent className="flex items-center justify-around text-center">
              <div>
                <p className="text-4xl font-bold">{totalScore.toFixed(2)}</p>
                <p className="text-muted-foreground">Total Score</p>
              </div>
              <div>
                <p className="text-4xl font-bold">{maxScore}</p>
                <p className="text-muted-foreground">Max Score</p>
              </div>
              <div>
                <p className="text-4xl font-bold">{percentage}%</p>
                <p className="text-muted-foreground">Percentage</p>
              </div>
              <div>
                <Badge className="text-lg">{category}</Badge>
                <p className="text-muted-foreground mt-2">Category</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Factor Breakdown</CardTitle>
              <CardDescription>Scores by contributing factors</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(factors).map(([key, value]) => (
                  <div key={key} className="flex justify-between items-center">
                    <span className="capitalize text-muted-foreground">{key}</span>
                    <span className="font-semibold">{String(value)}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Average Factors</CardTitle>
              <CardDescription>Average scores for each factor category</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(avgFactors).map(([key, value]) => {
                  const arr = value as [number, number];
                  return (
                    <div key={key} className="flex justify-between items-center">
                      <span className="capitalize text-muted-foreground">{key}</span>
                      <div>
                        <Badge variant="secondary" className="mr-2">Avg: {(Number(arr[0]) * 100).toFixed(2)}%</Badge>
                        <Badge>Count: {arr[1]}</Badge>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Questions & Answers</CardTitle>
              <CardDescription>Detailed scores for each question</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 max-h-[600px] overflow-y-auto">
                {!score.questions && (
                  <p className="text-muted-foreground">No questions found</p>
                )}
                {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
                {score.questions?.map((q: any, index: number) => (
                  <div key={index} className="p-3 bg-muted/50 rounded-lg">
                    <p className="font-semibold">{q.question}</p>
                    <div className="flex justify-between items-center text-sm mt-1">
                      <span className="text-muted-foreground">Score:</span>
                      <div className={`text-white p-1 text-xs rounded-full ${q.score > 0.5 ? 'bg-gray-700' : 'bg-[var(--red-6)]'}`}>{q.score.toFixed(2)}</div>
                    </div>
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-muted-foreground">Raw Score:</span>
                      <span>{q.rawScore} / {q.maxScore}</span>
                    </div>
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-muted-foreground">Answer:</span>
                      <span>{q.answer}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
