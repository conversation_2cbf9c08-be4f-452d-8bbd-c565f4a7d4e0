import React from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ShadcnUI/card";
import Button, { ButtonType } from "@/components/shared/Button/Button";
import { Button as ShadcnButton } from "@/components/ShadcnUI/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ShadcnUI/table";
import {
  FileText,
  PlusCircle,
  Edit,
  Trash2,
  Search,
  Eye,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { getIvfScores, deleteIvfScore } from "@/lib/services/ivf-scores.service";
import { formatDate } from "@/lib/utils/user-management.utils";
import Link from "next/link";
import { getProfilesByUserIds } from "@/lib/services/user.service";
import { DataTableFilters, FilterState } from "@/components/admin/DataTableFilters";
import { SortableHeaderClient } from "@/components/admin/SortableHeaderClient";
import { Label } from "@/components/ShadcnUI/label";
import { Input } from "@/components/ShadcnUI/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ShadcnUI/select";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export default async function IvfScoresPage({ searchParams }: any) {
  // Await searchParams if it's a Promise
  if (typeof searchParams?.then === "function") {
    searchParams = await searchParams;
  }

  const page = parseInt(searchParams?.page || "1");
  const search = searchParams?.search || "";
  const perPage = parseInt(searchParams?.per_page || "10");
  const sortBy = searchParams?.sort_by || "created_at";
  const sortDirection = (searchParams?.sort_direction || "desc") as 'asc' | 'desc';
  
  // Parse filters from search params
  const filters: FilterState = {
    search,
    dateFrom: searchParams?.date_from || undefined,
    dateTo: searchParams?.date_to || undefined,
    scoreMin: searchParams?.score_min ? parseInt(searchParams.score_min) : undefined,
    scoreMax: searchParams?.score_max ? parseInt(searchParams.score_max) : undefined,
    track: searchParams?.track && searchParams.track !== 'all' ? searchParams.track : undefined,
  };

  const { scores: ivfScores, total, error } = await getIvfScores(
    page, 
    search, 
    perPage, 
    sortBy, 
    sortDirection, 
    filters
  );

  const userIds     = ivfScores.map((score) => score.user_id);
  const profiles    = await getProfilesByUserIds(userIds);
  const totalPages = Math.ceil(total / perPage);

  // Helper function to build URL with params
  const buildUrl = (newParams: Record<string, string>) => {
    const params = new URLSearchParams();
    
    // Add existing params
    if (page > 1) params.set('page', page.toString());
    if (search) params.set('search', search);
    if (perPage !== 10) params.set('per_page', perPage.toString());
    if (sortBy !== 'created_at') params.set('sort_by', sortBy);
    if (sortDirection !== 'desc') params.set('sort_direction', sortDirection);
    if (filters.dateFrom) params.set('date_from', filters.dateFrom);
    if (filters.dateTo) params.set('date_to', filters.dateTo);
    if (filters.scoreMin !== undefined) params.set('score_min', filters.scoreMin.toString());
    if (filters.scoreMax !== undefined) params.set('score_max', filters.scoreMax.toString());
    if (filters.track) params.set('track', filters.track);
    
    // Override with new params
    Object.entries(newParams).forEach(([key, value]) => {
      if (value) {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });
    
    return `?${params.toString()}`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <FileText className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-foreground">IVF Scores</h1>
            <p className="text-muted-foreground">
              Manage and view IVF assessment scores
            </p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <DataTableFilters
            filters={filters}
            additionalFilters={
              <div className="space-y-2">
                <Label>Track</Label>
                <Select name="track" defaultValue={filters.track || "all"}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select track" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Tracks</SelectItem>
                    <SelectItem value="1">Track 1</SelectItem>
                    <SelectItem value="2">Track 2</SelectItem>
                    <SelectItem value="3">Track 3</SelectItem>
                  </SelectContent>
                </Select>
                
                <Label>Score Range</Label>
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <Label htmlFor="scoreMin" className="text-xs text-muted-foreground">
                      Min Score
                    </Label>
                    <Input
                      id="scoreMin"
                      type="number"
                      placeholder="0"
                      defaultValue={filters.scoreMin?.toString() || ""}
                      name="score_min"
                    />
                  </div>
                  <div>
                    <Label htmlFor="scoreMax" className="text-xs text-muted-foreground">
                      Max Score
                    </Label>
                    <Input
                      id="scoreMax"
                      type="number"
                      placeholder="100"
                      defaultValue={filters.scoreMax?.toString() || ""}
                      name="score_max"
                    />
                  </div>
                </div>
              </div>
            }
            searchPlaceholder="Search by email..."
          />
          <Button
            type={ButtonType.PRIMARY}
            size="sm"
            text="Add Score"
            icon={<PlusCircle className="h-4 w-4" />}
            className="gap-2 whitespace-nowrap"
          />
        </div>
      </div>

      {/* IVF Scores Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                IVF Scores List
              </CardTitle>
              <CardDescription>
                {search
                  ? `Showing results for "${search}"`
                  : `Showing ${ivfScores.length} of ${total} scores`}
              </CardDescription>
            </div>

            {/* Search */}
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <form method="get" className="flex gap-2">
                  <input
                    type="text"
                    name="search"
                    placeholder="Search scores..."
                    defaultValue={search}
                    className="pl-10 pr-4 py-2 border rounded-md text-sm w-64"
                  />
                  <input type="hidden" name="page" value="1" />
                  <input type="hidden" name="sort_by" value={sortBy} />
                  <input type="hidden" name="sort_direction" value={sortDirection} />
                  <Button
                    type={ButtonType.SECONDARY}
                    size="sm"
                    text="Search"
                  />
                </form>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {error ? (
            <div className="text-center py-8">
              <div className="text-red-600 mb-2">Error loading scores</div>
              <div className="text-sm text-muted-foreground">{error}</div>
            </div>
          ) : ivfScores.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No IVF scores found</h3>
              <p className="text-muted-foreground mb-4">
                {search
                  ? "Try adjusting your search criteria"
                  : "No scores have been recorded yet"}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Table */}
              <div className="rounded-lg border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>
                        <SortableHeaderClient
                          sortKey="user.email"
                          currentSort={sortBy}
                          currentDirection={sortDirection}
                        >
                          Patient
                        </SortableHeaderClient>
                      </TableHead>
                      <TableHead>
                        <SortableHeaderClient
                          sortKey="selected_track"
                          currentSort={sortBy}
                          currentDirection={sortDirection}
                        >
                          Track
                        </SortableHeaderClient>
                      </TableHead>
                      <TableHead>
                        <SortableHeaderClient
                          sortKey="created_at"
                          currentSort={sortBy}
                          currentDirection={sortDirection}
                        >
                          Date
                        </SortableHeaderClient>
                      </TableHead>
                      <TableHead>
                        <SortableHeaderClient
                          sortKey="score"
                          currentSort={sortBy}
                          currentDirection={sortDirection}
                        >
                          Score
                        </SortableHeaderClient>
                      </TableHead>
                      <TableHead className="w-[100px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {ivfScores.map((score) => {
                      const profile = profiles.find(p=> p.auth_id === score.user_id);
                      return (
                        <TableRow key={score.id}>
                          <TableCell>
                            <Link href={`/admin/ivf-scores/${score.user_id}`}>
                            <div className="font-medium">{profile?.display_name || 'N/A'}</div>
                            <div className="text-sm text-muted-foreground">{profile?.email || 'N/A'}</div>
                            </Link>
                          </TableCell>
                          <TableCell>
                            {score.selected_track || 'N/A'}
                          </TableCell>
                          <TableCell className="text-sm text-muted-foreground">
                            {formatDate(score.created_at.toString())}
                          </TableCell>
                          <TableCell className="font-medium">
                            {score.score !== null ? score.score : 'N/A'}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <ShadcnButton
                                size="sm"
                                variant="ghost"
                                className="h-8 w-8 p-0"
                                title="View Details"
                                asChild
                              >
                                <Link href={`/admin/ivf-scores/${score.user_id}`}>
                                  <Eye className="h-4 w-4" />
                                </Link>
                              </ShadcnButton>
                              <ShadcnButton
                                size="sm"
                                variant="ghost"
                                className="h-8 w-8 p-0"
                                title="Edit Score"
                                asChild
                              >
                                <Link href={`/admin/ivf-scores/${score.user_id}/edit`}>
                                  <Edit className="h-4 w-4" />
                                </Link>
                              </ShadcnButton>
                              <form action={deleteIvfScore} style={{ display: 'inline' }}>
                                <input type="hidden" name="scoreId" value={score.id} />
                                <ShadcnButton
                                  type="submit"
                                  size="sm"
                                  variant="ghost"
                                  className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                                  title="Delete Score"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </ShadcnButton>
                              </form>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    Showing {((page - 1) * perPage) + 1} to {Math.min(page * perPage, total)} of {total} scores
                  </div>
                  <div className="flex items-center gap-2">
                    <a href={buildUrl({ page: (page - 1).toString() })}>
                      <Button
                        type={ButtonType.SECONDARY}
                        size="sm"
                        disabled={page <= 1}
                        text="Previous"
                        icon={<ChevronLeft className="h-4 w-4" />}
                      />
                    </a>
                    
                    <div className="flex items-center gap-1">
                      {[...Array(Math.min(totalPages, 5))].map((_, i) => {
                        const pageNum = i + 1;
                        return (
                          <a key={pageNum} href={buildUrl({ page: pageNum.toString() })}>
                            <Button
                              type={page === pageNum ? ButtonType.PRIMARY : ButtonType.SECONDARY}
                              size="sm"
                              text={pageNum.toString()}
                            />
                          </a>
                        );
                      })}
                    </div>

                    <a href={buildUrl({ page: (page + 1).toString() })}>
                      <Button
                        type={ButtonType.SECONDARY}
                        size="sm"
                        disabled={page >= totalPages}
                        text="Next"
                        icon={<ChevronRight className="h-4 w-4" />}
                      />
                    </a>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}