"use client";
import React, { useEffect, useState } from "react";
import Sidebar from "@/components/shared/Admin/Sidebar/Sidebar";
import {
  PageHeaderProvider,
  usePageHeader,
} from "@/contexts/PageHeaderContext";
import {
  BreadcrumbProvider,
} from "@/contexts/BreadcrumbContext";
import { useScreenWidth } from "@/hooks/useScreenWidth";
import {
  Shield,
  Users,
  Settings,
  Home,
  Heart,
  FileText,
  Utensils,
  ChevronDown,
  Calendar,
  Clock,
  Building,
  Stethoscope,
} from "lucide-react";
import Avatar from "@/components/shared/Avatar/Avatar";
import NotificationButton from "@/components/Notification/NotificationButton/NotificationButton";
import PopUpSidebar from "@/components/shared/Admin/Sidebar/PopUpSidebar/PopUpSidebar";
import UserProfileDropdown from "@/components/shared/UserProfileDropdown/UserProfileDropdown";
import { usePathname, useRouter } from "next/navigation";
import UserProfileSlider from "@/components/shared/UserProfileSlider";
import Image from "next/image";
import { useAuth } from "@/contexts/AuthContext";
import { BowlFoodIcon, TextIndentIcon } from "@phosphor-icons/react";
import "./../../styles/admin-theme.css";
import "react-calendar/dist/Calendar.css";
import { useUserProfile } from "@/lib/services/client/user.service";
import PageLoader from "@/components/shared/PageLoader";
import SidebarItem from "@/components/shared/Admin/Sidebar/SidebarItem/SidebarItem";
import { filterSidebarItems } from "@/utils/adminRoleCheck";

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <PageHeaderProvider>
      <BreadcrumbProvider>
        <LayoutContent>{children}</LayoutContent>
      </BreadcrumbProvider>
    </PageHeaderProvider>
  );
}

function LayoutContent({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const pathname = usePathname();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [openMenus, setOpenMenus] = useState<{ [key: string]: boolean }>({});
  const [isProfileDropdownOpen, setIsProfileDropdownOpen] = useState(false);
  const [isUserProfileSliderOpen, setIsUserProfileSliderOpen] = useState(false);
  const { user, isLoading, signOut } = useAuth();
  const screenWidth = useScreenWidth();
  const { title, subtitle } = usePageHeader();
  const { data: profile } = useUserProfile();

  useEffect(() => {
    if (!isLoading && !user) {
      router.push("/login");
    }
    // Allow both admin and doctor roles to access admin dashboard
    if (user?.user_role && !["admin", "doctor"].includes(user.user_role)) {
      router.push("/user/dashboard");
      return;
    }
  }, [user, isLoading, router]);

  useEffect(() => {
    if (isSidebarOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }

    return () => {
      document.body.style.overflow = "auto";
    };
  }, [isSidebarOpen]);

  const onItemClick = (pathname: string) => {
    setIsSidebarOpen(false);
    router.push(pathname);
  };

  const allSidebarItems = [
    {
      icon: <Home size={20} />,
      title: "Dashboard",
      pathname: "/admin",
      onClick: () => onItemClick("/admin"),
    },
    {
      icon: <Shield size={20} />,
      title: "Roles & Permissions", // hide from doctor role
      pathname: "/admin/roles-permissions",
      onClick: () => onItemClick("/admin/roles-permissions"),
    },
    {
      icon: <Users size={20} />,
      title: "Users",  // hide from doctor role
      pathname: "/admin/users",
      onClick: () => onItemClick("/admin/users"),
    },
    {
      icon: <Stethoscope size={20} />,
      title: "Appointments",
      pathname: "/admin/appointments",
      onClick: () => onItemClick("/admin/appointments"),
      subItems: [
        {
          icon: <Calendar size={20} />,
          title: "All Appointments",
          pathname: "/admin/appointments",
          onClick: () => onItemClick("/admin/appointments"),
        },
        {
          icon: <Stethoscope size={20} />,
          title: "Doctors",
          pathname: "/admin/doctors",
          onClick: () => onItemClick("/admin/doctors"),
        },
        {
          icon: <Clock size={20} />,
          title: "Doctor Unavailabilities",
          pathname: "/admin/appointments/doctor-unavailabilities",
          onClick: () => onItemClick("/admin/appointments/doctor-unavailabilities"),
        },
        {
          icon: <Building size={20} />,
          title: "Clinics",  // hide from doctor role
          pathname: "/admin/appointments/clinics",
          onClick: () => onItemClick("/admin/appointments/clinics"),
        }
      ]
    },
    {
      icon: <Heart size={20} />,
      title: "IVF",
      subItems: [
        {
          icon: <FileText size={20} />,
          title: "Guest Scores",
          pathname: "/admin/guest-ivf-scores",
          onClick: () => onItemClick("/admin/guest-ivf-scores"),
        },
        {
          icon: <FileText size={20} />,
          title: "User Scores",
          pathname: "/admin/ivf-scores",
          onClick: () => onItemClick("/admin/ivf-scores"),
        },
        {
          icon: <Heart size={20} />,
          title: "IVF Questions",
          pathname: "/admin/ivf-questions",
          onClick: () => onItemClick("/admin/ivf-questions"),
        },
      ],
    },
    {
      icon: <Utensils size={20} />,
      title: "Diet Plan",
      subItems: [
        {
          icon: <BowlFoodIcon size={20} />,
          title: "Meal Plans",
          pathname: "/admin/diet-plans",
          onClick: () => onItemClick("/admin/diet-plans"),
        },
        {
          icon: <FileText size={20} />,
          title: "Diet Plan Scores",
          pathname: "/admin/diet-plan-scores",
          onClick: () => onItemClick("/admin/diet-plan-scores"),
        },
        {
          icon: <FileText size={20} />,
          title: "Diet Plan Questions",
          pathname: "/admin/diet-plan-questions",
          onClick: () => onItemClick("/admin/diet-plan-questions"),
        },
      ],
    },
    // {
    //   icon: <Calendar size={20} />,
    //   title: "Appointments",
    //   subItems: [
    //     // {
    //     //   icon: <FileText size={20} />,
    //     //   title: "Fee Management",
    //     //   pathname: "/admin/appointments/fee-management",
    //     //   onClick: () => onItemClick("/admin/appointments/fee-management"),
    //     // },
    //     // {
    //     //   icon: <MapPin size={20} />,
    //     //   title: "Cities",  // hide from doctor role
    //     //   pathname: "/admin/appointments/cities",
    //     //   onClick: () => onItemClick("/admin/appointments/cities"),
    //     // },
    //   ],
    // },
    {
      icon: <Settings size={20} />,
      title: "Settings",  // hide from doctor role
      pathname: "/admin/settings",
      onClick: () => onItemClick("/admin/settings"),
    },
  ];

  // Filter sidebar items based on user role
  const sidebarItems = filterSidebarItems(allSidebarItems, user);

  // Debug logging

  const toggleUserProfileSlider = () => {
    setIsUserProfileSliderOpen(!isUserProfileSliderOpen);
  };

  const closeUserProfileSlider = () => {
    setIsUserProfileSliderOpen(false);
  };

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const closeSidebar = () => {
    setIsSidebarOpen(false);
  };

  const toggleProfileDropdown = () => {
    setIsProfileDropdownOpen(!isProfileDropdownOpen);
  };

  const closeProfileDropdown = () => {
    setIsProfileDropdownOpen(false);
  };

  const toggleMenu = (menuTitle: string) => {
    setOpenMenus((prev) => ({
      ...prev,
      [menuTitle]: !prev[menuTitle],
    }));
  };

  if (isLoading) {
    return <PageLoader />;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const renderSidebarItems = (items: any[], isCollapsed: boolean) => {
    return items.map((item) => {
      if (item.subItems) {
        const isMenuOpen = openMenus[item.title] || false;
        return (
          <div key={item.title}>
            <SidebarItem
              icon={item.icon}
              title={isCollapsed ? "" : item.title}
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              isSelected={item.subItems.some((subItem: any) =>
                pathname.includes(subItem.pathname)
              )}
              onClick={() => toggleMenu(item.title)}
              className="cursor-pointer"
            >
              <ChevronDown
                size={20}
                className={`transition-transform duration-300 ${isMenuOpen ? "rotate-180" : ""
                  }`}
              />
            </SidebarItem>
            {isMenuOpen && !isCollapsed && (
              <div className="pl-4 space-y-2">
                {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
                {item.subItems.map((subItem: any) => (
                  <SidebarItem
                    key={subItem.title}
                    icon={subItem.icon}
                    title={subItem.title}
                    isSelected={pathname.includes(subItem.pathname)}
                    onClick={subItem.onClick}
                    className="cursor-pointer"
                  />
                ))}
              </div>
            )}
          </div>
        );
      }
      return (
        <SidebarItem
          key={item.title}
          icon={item.icon}
          title={isCollapsed ? "" : item.title}
          isSelected={pathname.includes(item.pathname)}
          onClick={item.onClick}
          className="cursor-pointer"
        />
      );
    });
  };

  return (
    <>
      {screenWidth <= 768 ? (
        <div className="relative min-h-screen bg-[#F7F7F9] flex flex-col items-center pb-4">
          {/* Top Bar */}
          <div className="w-full h-[5rem] flex items-center justify-between px-2 py-2 bg-white mb-4">
            <div className="flex items-center gap-1">
              <button className="p-2" onClick={toggleSidebar}>
                <TextIndentIcon size={24} className="text-[var(--grey-6)]" />
              </button>
              <Image
                src="/assets/givfLogo.svg"
                alt="Logo"
                className="h-8 w-auto"
                width={65}
                height={32}
              />
            </div>
            <div className="flex items-center gap-3">
              <NotificationButton count={9} />
              <div onClick={toggleUserProfileSlider}>
                <Avatar
                  src="/assets/avatar.jpg"
                  alt="Logo"
                  height={40}
                  width={40}
                />
              </div>
            </div>
          </div>
          {/* Title & Subtitle */}
          <div className="w-full text-left px-5 pb-4">
            {title && (
              <h1 className="text-2xl font-bold text-[var(--grey-7)]">
                {title}
              </h1>
            )}
            {subtitle && (
              <p className="text-sm font-medium text-[var(--grey-6)] whitespace-nowrap">
                {subtitle}
              </p>
            )}
          </div>
          {/* Card */}
          {children}

          <PopUpSidebar
            items={sidebarItems}
            isOpen={isSidebarOpen}
            onClose={closeSidebar}
            currentPathname={pathname}
            className="!absolute !top-0 !left-0 !z-50"
            renderSidebarItems={renderSidebarItems}
          />
          <UserProfileSlider
            isOpen={isUserProfileSliderOpen}
            onClose={closeUserProfileSlider}
            user={{
              name: "Priya Patel",
              email: "<EMAIL>",
              avatar: "/assets/avatar.jpg",
            }}
            onProfileClick={() => router.push("/user/settings")}
            onSettingsClick={() => router.push("/user/settings")}
            onHelpSupportClick={() => router.push("/user/help-support")}
            onNotificationPreferencesClick={() =>
              console.log("Notification preferences")
            }
            onLogoutClick={signOut}
          />
        </div>
      ) : (
        <div className="flex min-h-screen bg-[#F7F7F9]">
          {/* Sidebar */}
          <Sidebar
            items={sidebarItems}
            currentPathname={pathname}
            renderSidebarItems={renderSidebarItems}
          />
          {/* Main Content Area */}
          <div className="flex-1 flex flex-col">
            <div className="flex justify-between items-center">
              {/* Page Title and Subtitle */}
              <div className="px-10 pt-8 pb-2">
                {title && (
                  <h1 className="text-2xl font-bold text-[var(--grey-7)] mb-1">
                    {title}
                  </h1>
                )}
                {subtitle && (
                  <p className="text-sm font-medium text-[var(--grey-6)] whitespace-nowrap">
                    {subtitle}
                  </p>
                )}
              </div>
              {/* Header */}
              <div className="flex justify-end items-center gap-6 px-10 py-6">
                <NotificationButton count={9} />
                <div className="relative">
                  <div
                    onClick={toggleProfileDropdown}
                    className="cursor-pointer"
                  >
                    <Avatar
                      src="/assets/avatar.jpg"
                      alt="User"
                      height={48}
                      width={48}
                    />
                  </div>
                  <UserProfileDropdown
                    isOpen={isProfileDropdownOpen}
                    onClose={closeProfileDropdown}
                    user={{
                      name: profile?.display_name || "Priya Patel",
                      email: user?.email || "<EMAIL>",
                      avatar: "/assets/avatar.jpg",
                      role: user?.user_role || "",
                    }}
                    onProfileClick={() => router.push("/user/settings")}
                    onSettingsClick={() => router.push("/user/settings")}
                    onHelpSupportClick={() => router.push("/user/help-support")}
                    onNotificationPreferencesClick={() =>
                      console.log("Notification preferences")
                    }
                    onLogoutClick={signOut}
                  />
                </div>
              </div>
            </div>
            {/* Centered Card for Children */}
            <div className="p-8">{children}</div>
          </div>
        </div>
      )}
    </>
  );
}
