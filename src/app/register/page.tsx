"use client";

import { useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import RegisterPage from "@/components/RegisterPage";
import type { RegistrationData } from "@/components/RegisterPage/RegisterPage";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/contexts/ToastContext";

function RegisterPageContent() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const toast = useToast();

  useEffect(() => {
    if (!isLoading && user) {
      const redirectUrl = searchParams.get("redirect");
      if (redirectUrl) {
        router.replace(decodeURIComponent(redirectUrl));
      } else {
        router.replace("/user/dashboard");
      }
    }
  }, [user, isLoading, router, searchParams]);

  const handleRegister = async (data: RegistrationData) => {
    try {
      // Call your signup API
      const response = await fetch("/api/auth/signup", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        // Show error to user
        toast.error(result.error || "Registration failed");
        return;
      }

      // Store email for OTP verification step
      sessionStorage.setItem(
        "ivf_signup_data",
        JSON.stringify({ email: data.email })
      );

      // Redirect to verify account page, preserving redirect params if any
      const qs = new URLSearchParams();
      const from = searchParams.get("from");
      const redirect = searchParams.get("redirect");
      if (from) qs.set("from", from);
      if (redirect) qs.set("redirect", redirect);
      const suffix = qs.toString() ? `?${qs.toString()}` : "";
      router.push(`/register/verify-account${suffix}`);
    } catch (err) {
      console.log("err", err);
      toast.error("An unexpected error occurred. Please try again.");
    }
  };

  const handleLoginClick = () => {
    // Preserve params when navigating back to login
    const qs = new URLSearchParams();
    const from = searchParams.get("from");
    const redirect = searchParams.get("redirect");
    if (from) qs.set("from", from);
    if (redirect) qs.set("redirect", redirect);
    const suffix = qs.toString() ? `?${qs.toString()}` : "";
    router.push(`/login${suffix}`);
  };

  return (
    <RegisterPage onRegister={handleRegister} onLoginClick={handleLoginClick} />
  );
}

export default function SignUpPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <RegisterPageContent />
    </Suspense>
  );
}
