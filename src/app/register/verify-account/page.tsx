"use client";
import React, { Suspense } from "react";
import VerifyAccountPage from "@/components/VerifyAccountPage/VerifyAccountPage";
import { useSearchParams } from "next/navigation";

function VerifyAccount() {
  useSearchParams();
  return <VerifyAccountPage />;
}

export default function RegisterVerifyAccountPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <VerifyAccount />
    </Suspense>
  );
}
