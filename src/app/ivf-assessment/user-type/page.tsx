"use client";

import React from "react";
import { useRouter } from "next/navigation";
import TypeSelection from "@/components/Steps/TypeSelection/TypeSelection";
import { clearStoredFormData } from "@/utils/formLocalStorage";

export default function UserTypePage() {
  const router = useRouter();

  const handleTypeSelect = (type: number) => {
    console.log("Selected type:", type);
    // Store the selected type in localStorage or state management
    sessionStorage.setItem("ivf_selected_assessment_type", type.toString());
  };

  const handleNext = () => {
    clearStoredFormData();
    sessionStorage.removeItem("Result_status");
    sessionStorage.removeItem("ivf_guest_session_token");
    const guestSessionToken = "guest_" + crypto.randomUUID();
    sessionStorage.setItem("ivf_guest_session_token", guestSessionToken);
    sessionStorage.setItem("Test_status", "in-progress");
    // Navigate to the biological form page
    router.push("/ivf-assessment/forms/biological");
  };

  return <TypeSelection onTypeSelect={handleTypeSelect} onNext={handleNext} />;
}
