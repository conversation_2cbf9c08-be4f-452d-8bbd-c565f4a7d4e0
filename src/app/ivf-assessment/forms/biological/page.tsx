"use client";

import React, { useEffect } from "react";
import DynamicFormPage from "@/components/Steps/Common/DynamicFormPage";
import {
  useGuestFormSubmission,
} from "@/hooks/useGuestFormSubmission";
import { useAuthFormSubmission } from "@/hooks/useAuthFormSubmission";
import { useAuth } from "@/contexts/AuthContext";
import ShimmerFormPage from "@/components/Shimmer/ShimmerFormPage/ShimmerFormPage";
import { useHandleBeforeUnload } from "@/hooks/useHandleBeforeUnload";
import { useRouter } from "next/navigation";

export default function BiologicalPage() {
  const { isAuthenticated } = useAuth();
  const router = useRouter();
  const {
    submitForm: guestSubmit,
    isSubmitting: guestSubmitting,
    questions: guestQuestions,
    questionsLoading: guestQuestionsLoading,
    questionsError: guestQuestionsError,
  } = useGuestFormSubmission("biological");
  const {
    submitForm: authSubmit,
    isSubmitting: authSubmitting,
    questions: authQuestions,
    questionsLoading: authQuestionsLoading,
    questionsError: authQuestionsError,
  } = useAuthFormSubmission("biological");

  useEffect(() => {
    const resultStatus = sessionStorage.getItem("Result_status");
    if (resultStatus === "completed") {
      router.push("/ivf-assessment/alert");
      return;
    }
    const testStatus = sessionStorage.getItem("Test_status");
    if (!testStatus) {
      router.push("/ivf-assessment/user-type");
    }
  }, [router]);

  const submitForm = isAuthenticated ? authSubmit : guestSubmit;
  const isSubmitting = isAuthenticated ? authSubmitting : guestSubmitting;
  const questions = isAuthenticated ? authQuestions : guestQuestions;
  const questionsLoading = isAuthenticated
    ? authQuestionsLoading
    : guestQuestionsLoading;
  const questionsError = isAuthenticated
    ? authQuestionsError
    : guestQuestionsError;

  const [errorMessage, setErrorMessage] = React.useState<string | null>(null);
  const [formCompleted, setFormCompleted] = React.useState(false);

  useHandleBeforeUnload(formCompleted);

  const handleNext = async (data: Record<string, string>) => {
    try {
      // Submit the data
      const result = await submitForm(data);
      // Navigate to the lifestyle page
      // router.push("/ivf-assessment/forms/lifestyle-psychosocial");
      if (!result.success) {
        console.log("result", result);
        setErrorMessage(
          result.error instanceof Error
            ? result.error.message
            : "An error occurred"
        );
      } else {
        console.log("result", result);
        setErrorMessage(null);
        setFormCompleted(true); // Mark form as completed to disable warning
      }
    } catch (error) {
      console.error("Error transforming or submitting form data:", error);
      setErrorMessage(
        error instanceof Error ? error.message : "An error occurred"
      );
    }
  };

  // Show loading state while questions are being fetched
  if (questionsLoading) {
    return <ShimmerFormPage />;
  }

  // Show error state if questions failed to load
  if (questionsError) {
    return <div>Error loading questions: {questionsError}</div>;
  }

  return (
    <DynamicFormPage
      title="Biological Factors"
      currentStep={1}
      totalSteps={5}
      questions={questions}
      loading={questionsLoading}
      error={questionsError}
      submitButtonText="Next Step (Lifestyle)"
      onNext={handleNext}
      isSubmitting={isSubmitting}
      errorMessage={errorMessage || ""}
      category="biological"
    />
  );
}
