"use client";

import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import Signup from "@/components/Steps/Signup/Signup";
import { useAuth } from "@/contexts/AuthContext";

export default function SignupPage() {
  const router = useRouter();
  const { user } = useAuth();

  useEffect(() => {
    if (user) {
      router.back();
      return;
    }
    const resultStatus = sessionStorage.getItem("Result_status");
    if (resultStatus === "completed") {
        router.push("/ivf-assessment/alert");
        return;
    }
    const testStatus = sessionStorage.getItem("Test_status");
    if (!testStatus) {
        router.push("/ivf-assessment/user-type");
        return;
    }
  }, [router, user]);

  const handleVerifyEmail = () => {
    // Navigate to the verify email page
    router.push("/ivf-assessment/verify-email");
  };

  return <Signup onVerifyEmail={handleVerifyEmail} />;
}
