"use client";

import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import Verify from "@/components/Steps/Verify/VerifyDetails/Verify";
import { useAuth } from "@/contexts/AuthContext";

export default function VerifyDetailsPage() {
  const router = useRouter();
  const {user} = useAuth();

  useEffect(() => {
    if (user) {
      router.back();
      return;
    }
    const resultStatus = sessionStorage.getItem("Result_status");
    if (resultStatus === "completed") {
      router.push("/ivf-assessment/alert");
      return;
    }

    const testStatus = sessionStorage.getItem("Test_status");
    if (!testStatus) {
      router.push("/ivf-assessment/user-type");
      return;
    }
  }, [router, user]);


  const handleEmailContinue = () => {
    // Navigate to the signup page
    router.push("/ivf-assessment/signup");
  };

  const handleGoogleContinue = () => {
    // Handle Google authentication
    console.log("Continue with Google");
  };

  const handleSignIn = () => {
    // Navigate to login page with from parameter
    const redirect = encodeURIComponent("/ivf-assessment/results");
    router.push(`/login?from=verify-details&redirect=${redirect}`);
  };

  const handleBack = () => {
    // Navigate back to previous step
    router.back();
  };

  return (
    <Verify
      onEmailContinue={handleEmailContinue}
      onGoogleContinue={handleGoogleContinue}
      onSignIn={handleSignIn}
      onBack={handleBack}
    />
  );
}
