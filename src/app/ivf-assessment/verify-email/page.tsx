"use client";

import React, { useEffect } from "react";
import VerifyEmail from "@/components/Steps/Verify/VerifyEmail/Verify";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";

export default function VerifyEmailPage() {
  const router = useRouter();
  const {user} = useAuth();

  useEffect(() => {
    if(user){
      router.back();
      return;
    }
    
    const resultStatus = sessionStorage.getItem("Result_status");
    if (resultStatus === "completed") {
      router.push("/ivf-assessment/alert");
      return;
    }

    const testStatus = sessionStorage.getItem("Test_status");
    if (!testStatus) {
      router.push("/ivf-assessment/user-type");
      return;
    }
  }, [router, user]);

  return <VerifyEmail />;
}
