"use client";

import AlertPage from "@/components/AlertPage/AlertPage";
import { useRouter } from "next/navigation";

export default function Alert() {
  const router = useRouter();

  const handleRetakeAssessment = () => {
    router.push("/ivf-assessment/user-type");
  };

  const handleGoToResults = () => {
    router.push("/ivf-assessment/results");
  };
  return (
    <AlertPage
      onRetakeAssessment={handleRetakeAssessment}
      onGoToResults={handleGoToResults}
    />
  );
}
