"use client";

import { useRouter, usePathname } from "next/navigation";
import LandingPage from "@/components/LandingPage";

export default function Home() {
  const router = useRouter();
  const pathname = usePathname();

  const handleStartScore = () => {
    router.push("/ivf-assessment/user-type");
  };

  const handleLogin = () => {
    router.push(`/login?redirect=${pathname}`);
  };

  const handlePhoneClick = (phoneNumber: string) => {
    console.log("Phone clicked:", phoneNumber);
    // You can implement phone functionality here
  };

  return (
    <LandingPage
      onStartScore={handleStartScore}
      onLogin={handleLogin}
      onPhoneClick={handlePhoneClick}
    />
  );
}
