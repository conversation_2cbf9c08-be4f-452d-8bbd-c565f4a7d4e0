"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { createClient } from "@/utils/supabase/client";
import Dashboard from "@/components/Dasboard/Dashboard";
import PageLoader from "@/components/shared/PageLoader";

export default function DashboardPage() {
  const [isLoading, setIsLoading] = useState(true);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [user, setUser] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const checkUser = async () => {
      const supabase = createClient();
      const {
        data: { user },
        error,
      } = await supabase.auth.getUser();

      if (error || !user) {
        router.push("/login");
        return;
      }

      setUser(user);
      setIsLoading(false);
    };

    checkUser();
  }, [router]);

  if (isLoading) {
    return <PageLoader />;
  }

  if (!user) {
    return null; // Will redirect to login
  }

  // If user has a specific role, they will be redirected
  // This dashboard is for regular users without specific roles
  return <Dashboard />;
}
