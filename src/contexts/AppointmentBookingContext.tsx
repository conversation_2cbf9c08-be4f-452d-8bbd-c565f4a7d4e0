"use client";

import React, { createContext, useContext, useState, ReactNode } from "react";

export interface AppointmentSlot {
  id: number;
  start_time: string; // ISO string format from API
  end_time: string; // ISO string format from API
  duration: number;
  start_time_display: string; // 12-hour format for display
  end_time_display: string; // 12-hour format for display
}

export interface AppointmentBookingData {
  slot: AppointmentSlot;
  date: string; // YYYY-MM-DD format
  doctor_id: number;
  clinic_id: number;
  city_id: number;
  city_name: string;
  clinic_name: string;
  doctor_name: string;
}

interface AppointmentBookingContextType {
  appointmentData: AppointmentBookingData | null;
  setAppointmentData: (data: AppointmentBookingData) => void;
  clearAppointmentData: () => void;
}

const AppointmentBookingContext = createContext<AppointmentBookingContextType | undefined>(undefined);

export const useAppointmentBooking = () => {
  const context = useContext(AppointmentBookingContext);
  if (context === undefined) {
    throw new Error("useAppointmentBooking must be used within an AppointmentBookingProvider");
  }
  return context;
};

interface AppointmentBookingProviderProps {
  children: ReactNode;
}

export const AppointmentBookingProvider: React.FC<AppointmentBookingProviderProps> = ({ children }) => {
  const [appointmentData, setAppointmentDataState] = useState<AppointmentBookingData | null>(null);

  const setAppointmentData = (data: AppointmentBookingData) => {
    setAppointmentDataState(data);
  };

  const clearAppointmentData = () => {
    setAppointmentDataState(null);
  };

  const value: AppointmentBookingContextType = {
    appointmentData,
    setAppointmentData,
    clearAppointmentData,
  };

  return (
    <AppointmentBookingContext.Provider value={value}>
      {children}
    </AppointmentBookingContext.Provider>
  );
};
