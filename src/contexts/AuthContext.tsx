"use client";

import React, {
  createContext,
  useState,
  useEffect,
  useContext,
  ReactNode,
} from "react";
import { supabase } from "@/utils/supabase/client";
import { Session, User } from "@supabase/supabase-js";
import { useRouter } from "next/navigation";
import { useToast } from "@/contexts/ToastContext";
import { clearStoredFormData, convertGuestFormDataToUser } from "@/utils/formLocalStorage";

export interface UserWithRole extends User {
  user_role?: string | null;
}

interface AuthContextType {
  session: Session | null;
  user: UserWithRole | null;
  isLoading: boolean;
  user_token: string | null;
  isAuthenticated: boolean;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const getUserRole = async (user_id: string) => {
  const { data, error } = await supabase.rpc("get_user_role", {
    p_auth_id: user_id,
  });
  return !error && Array.isArray(data) && data.length > 0
    ? data[0]?.role_name
    : null;
};

export const AuthProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<UserWithRole | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [user_token, setUserToken] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const router = useRouter();
  const toast  = useToast();

  useEffect(() => {
    let unsubscribe: (() => void) | undefined;
    const initializeAuth = async () => {
      setIsLoading(true);

      // Get initial session
      const {
        data: { session },
        error,
      } = await supabase.auth.getSession();
      if (!error && session?.user) {
        const role = await getUserRole(session.user.id);
        setSession(session);
        setUser({ ...session.user, user_role: role });
        setUserToken(session.access_token || null);
        setIsAuthenticated(true);
        
        // Convert guest form data to user data if conditions are met
        const converted = convertGuestFormDataToUser(session.user.id);
        if (converted) {
          console.log("User form data has been transferred to your account.");
        }
      } else {
        setSession(null);
        setUser(null);
        setUserToken(null);
        setIsAuthenticated(false);
      }

      // Listen for auth state changes
      const {
        data: { subscription },
      } = supabase.auth.onAuthStateChange(async (event, session) => {
        if (session?.user) {
          // Wait for the session to be fully initialized before getting the user role
          // This is a supabase bug, it does not wait for the session to be fully initialized before calling the onAuthStateChange callback
          setTimeout(async () => {
            const role = await getUserRole(session.user.id);
            setSession(session);
            setUser({ ...session.user, user_role: role });
            setUserToken(session.access_token || null);
            setIsAuthenticated(true);
            
            // Convert guest form data to user data if conditions are met
            const converted = convertGuestFormDataToUser(session.user.id);
            if (converted) {
              console.log("User form data has been transferred to your account.");
            }
          });
        } else {
          setSession(null);
          setUser(null);
          setUserToken(null);
          setIsAuthenticated(false);
        }

        // Navigate to login page if user is signed out
        if (event === "SIGNED_OUT") {
          router.push("/login");
          localStorage.removeItem("ivf_user_profile");
          clearStoredFormData();
          // clear test & result status on logout
          sessionStorage.removeItem("Test_status");
          sessionStorage.removeItem("Result_status");
        }
      });

      // Cleanup the subscription on unmount
      unsubscribe = () => subscription?.unsubscribe();
      setIsLoading(false);
    };
    initializeAuth();
    return () => {
      if (unsubscribe) unsubscribe();
    };
  }, [router]);

  const signOut = async () => {
    setIsLoading(true);
    await supabase.auth.signOut();
    setSession(null);
    setUser(null);
    setUserToken(null);
    setIsAuthenticated(false);
    sessionStorage.removeItem("Test_status");
    setIsLoading(false);
    toast.success("Signed Out: You have been successfully signed out.");
    router.push("/login");
  };

  return (
    <AuthContext.Provider
      value={{
        session,
        user,
        isLoading,
        user_token,
        isAuthenticated,
        signOut,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// const public_routes = [
//   '/login',
//   '/register',
//   '/forgot-password',
//   '/auth/auth-code-error',
//   '/auth/reset-password',
//   '/help-support',
//   '/ivf-assessment/alert',
//   '/ivf-assessment/create-password',
//   '/ivf-assessment/verify-details',
//   '/ivf-assessment/signup',
//   '/ivf-assessment/verify-email',
//   '/ivf-assessment/results',
//   '/ivf-assessment/user-type',
//   '/ivf-assessment/forms/lifestyle-psychosocial',
//   '/ivf-assessment/forms/environmental',
//   '/ivf-assessment/forms/biological'
// ]

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) throw new Error("useAuth must be used within an AuthProvider");
  return context;
};
