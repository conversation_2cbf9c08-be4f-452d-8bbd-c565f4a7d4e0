"use client";

import Toast, { ToastType } from "@/components/shared/Toast/Toast";
import React, { createContext, useContext, useState, useCallback } from "react";

export interface ToastItem {
  id: string;
  type: ToastType;
  title: string;
  message: string;
}

interface ToastContextType {
  success: (message: string, title?: string) => void;
  error: (message: string, title?: string) => void;
  warning: (message: string, title?: string) => void;
  info: (message: string, title?: string) => void;
  show: (type: ToastType, message: string, title?: string) => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error("useToast must be used within a ToastProvider");
  }
  return context;
};

interface ToastProviderProps {
  children: React.ReactNode;
  duration?: number;
  maxToasts?: number;
}

export const ToastProvider: React.FC<ToastProviderProps> = ({
  children,
  duration = 4000,
  maxToasts = 5,
}) => {
  const [toasts, setToasts] = useState<ToastItem[]>([]);

  const removeToast = useCallback((id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id));
  }, []);

  const addToast = useCallback(
    (type: ToastType, message: string, title?: string) => {
      const id = Math.random().toString(36).substr(2, 9);
      const defaultTitles: Record<ToastType, string> = {
        success: "Success",
        error: "Error",
        warning: "Warning",
        info: "Info",
        default: "Notification",
      };

      const newToast: ToastItem = {
        id,
        type,
        title: title || defaultTitles[type],
        message,
      };

      setToasts((prev) => {
        const updated = [newToast, ...prev];
        return updated.slice(0, maxToasts);
      });

      // Auto remove after duration
      setTimeout(() => {
        removeToast(id);
      }, duration);
    },
    [duration, maxToasts, removeToast]
  );

  const contextValue: ToastContextType = {
    success: (message: string, title?: string) =>
      addToast("success", message, title),
    error: (message: string, title?: string) =>
      addToast("error", message, title),
    warning: (message: string, title?: string) =>
      addToast("warning", message, title),
    info: (message: string, title?: string) => addToast("info", message, title),
    show: addToast,
  };

  return (
    <ToastContext.Provider value={contextValue}>
      {children}

      {/* Toast Container */}
      <div className="fixed top-4 right-4 z-50 flex flex-col gap-3 pointer-events-none">
        {toasts.map((toast, index) => (
          <div
            key={toast.id}
            className="pointer-events-auto animate-in slide-in-from-right-full duration-300"
            style={{
              animationDelay: `${index * 100}ms`,
            }}
          >
            <Toast
              type={toast.type}
              title={toast.title}
              message={toast.message}
              onClose={() => removeToast(toast.id)}
            />
          </div>
        ))}
      </div>
    </ToastContext.Provider>
  );
};
