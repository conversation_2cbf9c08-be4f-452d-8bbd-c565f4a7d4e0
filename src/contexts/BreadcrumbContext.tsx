import React, { createContext, useContext, useState } from "react";

export interface BreadcrumbItem {
  label: string;
  href?: string;
  isActive?: boolean;
}

export type BreadcrumbContextType = {
  breadcrumbs: BreadcrumbItem[] | null;
  setBreadcrumbs: (breadcrumbs: BreadcrumbItem[] | null) => void;
};

const BreadcrumbContext = createContext<BreadcrumbContextType | undefined>(
  undefined
);

export const useBreadcrumb = () => {
  const ctx = useContext(BreadcrumbContext);
  if (!ctx)
    throw new Error(
      "useBreadcrumbContext must be used within BreadcrumbProvider"
    );
  return ctx;
};

export const BreadcrumbProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[] | null>(null);

  return (
    <BreadcrumbContext.Provider
      value={{
        breadcrumbs,
        setBreadcrumbs,
      }}
    >
      {children}
    </BreadcrumbContext.Provider>
  );
};
