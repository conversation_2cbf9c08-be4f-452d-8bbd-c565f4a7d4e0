import React, { createContext, useContext, useState } from "react";

export type PageHeaderContextType = {
  title: string | null;
  subtitle: string | null;
  setTitle: (title: string | null) => void;
  setSubtitle: (subtitle: string | null) => void;
};

const PageHeaderContext = createContext<PageHeaderContextType | undefined>(
  undefined
);

export const usePageHeader = () => {
  const ctx = useContext(PageHeaderContext);
  if (!ctx)
    throw new Error("usePageHeader must be used within PageHeaderProvider");
  return ctx;
};

export const PageHeaderProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [title, setTitle] = useState<string | null>(null);
  const [subtitle, setSubtitle] = useState<string | null>(null);
  return (
    <PageHeaderContext.Provider
      value={{
        title,
        subtitle,
        setTitle,
        setSubtitle,
      }}
    >
      {children}
    </PageHeaderContext.Provider>
  );
};
