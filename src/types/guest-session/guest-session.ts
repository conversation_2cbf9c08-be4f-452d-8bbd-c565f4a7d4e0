import { BasedEntities } from "../general";
import type { FieldValue } from "@/types/ivf-score";

// Guest session data structure
export interface GuestSessionRow extends BasedEntities {
  session_token: string;
  ivf_data: Record<string, FieldValue>;
  current_step: number;
  selected_track?: string; // Stores the selected track (T1, T2, T3)
  email?: string;
  display_name?: string;
  phone?: string;
  is_verified: boolean;
  status: 'pending' | 'completed' | 'failed';
  expires_at: Date;
}

export interface GuestSessionInsert extends BasedEntities {
  session_token: string;
  ivf_data: Record<string, FieldValue>;
  current_step: number;
  selected_track?: string; // Stores the selected track (T1, T2, T3)
  email?: string;
  display_name?: string;
  phone?: string;
  is_verified: boolean;
  expires_at: Date;
}

export interface GuestSessionUpdate extends BasedEntities {
  ivf_data?: Record<string, FieldValue>;
  current_step?: number;
  selected_track?: string; // Stores the selected track (T1, T2, T3)
  email?: string;
  display_name?: string;
  phone?: string;
  is_verified?: boolean;
  expires_at?: Date;
}
