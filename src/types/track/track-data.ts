// Track information for API
export interface TrackInfo {
  id: string;
  code: string;
  name: string;
  description: string;
}

// Track filtering types
export interface TrackFilter {
  selectedTrackIds: string[];
  mode: 'any' | 'all'; // 'any' = OR logic, 'all' = AND logic
}

export interface QuestionsWithTrackFilter {
  questions: import("../question").QuestionWithOptions[];
  filter: TrackFilter;
} 