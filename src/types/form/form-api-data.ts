import type { FieldTypeCapabilities } from "../field-type";
import type { ScoringConfig } from "../scoring";
import type { FormFieldType, ScoringType } from "@/generated/prisma";

// Track info type definition (local to avoid import issues)
interface TrackInfo {
  id: string;
  code: string;
  name: string;
  description: string;
}

// Consolidated question data for API
export interface ConsolidatedQuestionData {
  id: string;
  question_text: string;
  order: number;
  help_text: string | null;
  field_type: {
    value: FormFieldType;
    label: string;
    description: string;
    capabilities: FieldTypeCapabilities;
  };
  config: {
    placeholder: string | null;
    min_value: number | null;
    max_value: number | null;
    step: number | null;
    unit: string | null;
  };
  options: {
    id: string;
    text: string;
    value: string | null;
    order: number;
    score: number | null;
  }[];
  tracks: TrackInfo[];
  has_parent: boolean; // True if this is a sub-question
  scoring: {
    type: ScoringType | null;
    config: ScoringConfig | null;
  };
  sub_questions: ConsolidatedQuestionData[];
  depends_on_option_id: string | null;
  is_mandatory: boolean;
  parent_id: string | null;
  scoring_mode: string;
  collective_formula: string | null;
}

// Consolidated form data for API
export interface ConsolidatedFormData {
  id: string;
  name: string;
  description: string | null;
  category: string; // Dynamic category based on slug
  questions: ConsolidatedQuestionData[];
  summary: {
    total_questions: number;
    track_distribution: Record<string, number>;
    field_type_distribution: Record<string, number>;
  };
} 