import { FormFieldType, ScoringMode, ScoringType } from "@/generated/prisma";
import type { ScoringConfig } from "../scoring";

// Form builder option
export interface FormBuilderOption {
  id?: string;
  option_text: string;
  value?: string | null;
  order: number;
  score?: number;
}

// Form builder question
export interface FormBuilderQuestion {
  id?: string;
  parent_id?: string | null; // For sub-questions (one level of nesting)
  question_text: string;
  help_text?: string | null;
  is_mandatory?: boolean;
  field_type: FormFieldType;
  placeholder?: string | null;
  min_value?: number;
  max_value?: number;
  step?: number;
  unit?: string | null;
  order: number;
  options?: FormBuilderOption[];
  scoring_type?: ScoringType;
  scoring_config?: ScoringConfig;
  track_ids?: string[];
  sub_questions?: FormBuilderQuestion[]; // Sub-questions of this question
  scoring_mode?: ScoringMode;
  collective_formula?: string | null;
  depends_on_option_id?: string | null;
}

// Form builder form structure
export interface FormBuilderForm {
  id?: string;
  name: string;
  description?: string;
  questions: FormBuilderQuestion[];
}
