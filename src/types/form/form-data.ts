import type { forms } from "@/generated/prisma";
import type { QuestionWithOptions, QuestionApiData } from "../question";

// Base form type from Prisma
export type Form = forms;

// Extended form with questions for UI
export interface FormWithQuestions extends Form {
  questions: QuestionWithOptions[];
}

// Form API data structure
export interface FormApiData {
  id: string;
  name: string;
  description: string | null;
  category: string;
  created_at: string;
  updated_at: string;
  questions?: QuestionApiData[];
  summary?: {
    total_questions: number;
    tracks: Record<string, number>;
    field_types: Record<string, number>;
  };
}

// Form submission data
export interface FormSubmissionData {
  [questionId: string]: string | number | boolean;
} 