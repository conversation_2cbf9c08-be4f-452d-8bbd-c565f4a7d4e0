export interface FieldType {
  value: string;
  label: string;
  description: string;
  capabilities: {
    supportsOptions: boolean;
    supportsRange: boolean;
    supportsPlaceholder: boolean;
    supportsUnit: boolean;
    supportsScoring: boolean;
  };
}

export interface Option {
  id: string;
  text: string;
  value: string | null;
  order: number;
  score: number;
}

export interface Question {
  id: string;
  question_text: string;
  parent_id: string | null;
  sub_questions: Question[];
  collective_formula: string | null;
  scoring_type: string | null;
  scoring_config: string | null;
  is_mandatory: boolean;
  order: number;
  field_type: FieldType;
  config: {
    placeholder?: string;
    min_value?: number;
    max_value?: number;
    unit?: string;
  };
  options: Option[];
  depends_on_option_id?: string | null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  tracks: any[];
  scoring: {
    type: string;
    config: string;
  };
}

export interface Form {
  id: string;
  name: string;
  description: string;
  category: string;
  slug: string | null;
  questions: Question[];
}

export interface QuestionsResponse {
  success: boolean;
  data: {
    forms: Form[];
    questions: Question[];
  };
}

export interface FormPageProps {
  onNext?: (data: Record<string, string>) => void;
  className?: string;
  isSubmitting?: boolean;
  errorMessage?: string;
}