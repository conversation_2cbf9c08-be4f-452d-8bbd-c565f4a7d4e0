import type { FieldTypeInfo } from "../field-type";
import type { TrackInfo } from "../track";
import type { OptionInfo, ScoringInfo } from "../option";

// Question data for API responses
export interface QuestionApiData {
  id: string;
  form: {
    id: string;
    name: string;
    description: string | null;
  };
  question_text: string;
  field_type: FieldTypeInfo;
  placeholder: string | null;
  min_value: number | null;
  max_value: number | null;
  step: number | null;
  unit: string | null;
  order: number;
  options?: OptionInfo[];
  tracks?: TrackInfo[];
  scoring?: ScoringInfo;
  created_at: string;
  updated_at: string;
} 