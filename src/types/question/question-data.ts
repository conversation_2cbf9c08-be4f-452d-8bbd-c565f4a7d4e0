import type { questions, options, question_tracks, tracks } from "@/generated/prisma";

// Base types from Prisma
export type FertilityQuestion = questions;
export type FormOption = options;
export type QuestionTrack = question_tracks;
export type Track = tracks;

// Extended question with options
export interface QuestionWithOptions extends FertilityQuestion {
  options: FormOption[];
  question_tracks?: (QuestionTrack & { track: Track })[];
  sub_questions?: QuestionWithOptions[];
  depends_on_option_id: string | null;
  depends_on_option?: FormOption;
}

// Question with tracks
export interface QuestionWithTracks extends FertilityQuestion {
  question_tracks: (QuestionTrack & { track: Track })[];
  options: FormOption[];
} 