// Types for IVF Score calculation input
import { BasedEntities } from "@/types/general";

// Define common field types that might be used
export type FieldValue = string | number | boolean | null;

// Dynamic IVF Score data structure
export interface IVFScoreRow extends BasedEntities {
  user_id: string;
  form_data: Record<string, FieldValue>;
  current_step: number;
  created_at: Date;
  updated_at: Date;
  selected_track?: string | null;
}

// For creating new IVF Score records
export interface IVFScoreInsert {
  user_id: string;
  form_data: Record<string, FieldValue>;
  current_step?: number; // Optional as it has a default value in the schema
  selected_track?: string;
}

// For updating existing IVF Score records
export interface IVFScoreUpdate {
  form_data?: Record<string, FieldValue>;
  current_step?: number;
  selected_track?: string;
  status?: 'pending' | 'completed' | 'failed';
}
