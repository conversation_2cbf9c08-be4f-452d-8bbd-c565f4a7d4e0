// Query parameters for fertility questions API
export interface FertilityQuestionsParams {
  tracks?: string; // Comma-separated track IDs
  form_id?: string;
  field_type?: string;
  include_options?: string; // 'true' | 'false'
  include_tracks?: string; // 'true' | 'false'
  include_scoring?: string; // 'true' | 'false'
}

// Query parameters for forms API
export interface FormsParams {
  name?: string;
  tracks?: string; // Comma-separated track IDs
  include_questions?: string; // 'true' | 'false'
  include_options?: string; // 'true' | 'false'
  include_tracks?: string; // 'true' | 'false'
  include_scoring?: string; // 'true' | 'false'
}

// Query parameters for individual question API
export interface QuestionParams {
  include_options?: string; // 'true' | 'false'
  include_tracks?: string; // 'true' | 'false'
  include_scoring?: string; // 'true' | 'false'
}

// Query parameters for consolidated API
export interface ConsolidatedFormsParams {
  tracks?: string; // Comma-separated track IDs or codes
  category?: string; // Filter by form category (dynamic)
} 