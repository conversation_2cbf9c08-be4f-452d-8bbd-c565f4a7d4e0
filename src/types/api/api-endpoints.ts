import type { ApiResponse } from "./api-response";
import type { QuestionApiData } from "../question";
import type { FormApiData, ConsolidatedFormData } from "../form";
import type { TrackInfo } from "../track";
import type { FieldTypeApiData } from "../field-type";

// Form category information
export interface FormCategoryInfo {
  key: string;
  name: string;
  description: string;
  icon: string;
}

// GET /api/v1/ivf-assessment/questions
export interface FertilityQuestionsResponse extends ApiResponse {
  data: {
    questions: QuestionApiData[];
    summary: {
      total_questions: number;
      tracks: Record<string, number>;
      field_types: Record<string, number>;
    };
  };
  meta: {
    filters_applied: {
      track_ids: string[];
      form_id?: string;
      field_type?: string;
    };
    includes: {
      options: boolean;
      tracks: boolean;
      scoring: boolean;
    };
  };
}

// GET /api/v1/ivf-assessment/questions/[id]
export interface FertilityQuestionResponse extends ApiResponse {
  data: {
    question: QuestionApiData;
  };
  meta: {
    includes: {
      options: boolean;
      tracks: boolean;
      scoring: boolean;
    };
  };
}

// GET /api/v1/forms
export interface FormsResponse extends ApiResponse {
  data: {
    forms: FormApiData[];
    summary: {
      total_forms: number;
      total_questions: number;
      categories: Record<string, number>;
    };
  };
  meta: {
    filters_applied: {
      track_ids: string[];
      form_name?: string;
    };
    includes: {
      questions: boolean;
      options: boolean;
      tracks: boolean;
      scoring: boolean;
    };
  };
}

// GET /api/v1/tracks
export interface TracksResponse extends ApiResponse {
  data: {
    tracks: TrackInfo[];
  };
}

// GET /api/v1/field-types
export interface FieldTypesResponse extends ApiResponse {
  data: {
    field_types: FieldTypeApiData[];
  };
  meta: {
    total_types: number;
  };
}

// GET /api/v1/fertility-forms - Consolidated response
export interface ConsolidatedFormsResponse extends ApiResponse {
  data: {
    forms: ConsolidatedFormData[];
    metadata: {
      tracks: TrackInfo[];
      field_types: FieldTypeApiData[];
      categories: FormCategoryInfo[];
    };
    summary: {
      total_forms: number;
      total_questions: number;
      forms_by_category: Record<string, number>;
      questions_by_track: Record<string, number>;
      questions_by_field_type: Record<string, number>;
    };
  };
  filters: {
    tracks: string[];
    category?: string;
  };
} 