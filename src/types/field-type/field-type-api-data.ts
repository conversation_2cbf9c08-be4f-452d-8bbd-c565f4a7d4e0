import { FormFieldType } from "@/generated/prisma";
import { FieldTypeCapabilities } from "./field-type-capabilities";

// Basic field type information for API responses
export interface FieldTypeInfo {
  value: FormFieldType;
  label: string;
  description: string;
}

// Field type with capabilities for API responses
export interface FieldTypeApiData {
  value: FormFieldType;
  label: string;
  description: string;
  capabilities: FieldTypeCapabilities;
} 