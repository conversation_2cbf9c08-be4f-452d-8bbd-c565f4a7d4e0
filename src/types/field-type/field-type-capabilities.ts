import { FormFieldType } from "@/generated/prisma";

// Field type capabilities
export interface FieldTypeCapabilities {
  supportsOptions: boolean;
  supportsRange: boolean;
  supportsPlaceholder: boolean;
  supportsUnit: boolean;
  supportsScoring: boolean;
}

// Field configuration for the builder (from fertility-questions.ts)
export interface FieldConfig {
  type: FormFieldType;
  label: string;
  description: string;
  supportsOptions: boolean;
  supportsRange: boolean;
  supportsPlaceholder: boolean;
  supportsUnit: boolean;
}

// Field validation rules
export interface FieldValidation {
  required?: boolean;
  min?: number;
  max?: number;
  pattern?: string;
  message?: string;
} 