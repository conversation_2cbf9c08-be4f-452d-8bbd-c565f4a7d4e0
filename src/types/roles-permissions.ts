import type { roles as RolesPrisma, permissions as PermissionsPrisma } from "@/generated/prisma";

// Base types from Prisma
export type Role = RolesPrisma;
export type Permission = PermissionsPrisma;

// Extended types for UI
export interface RoleWithPermissions extends Role {
  permissions: Permission[];
}

export interface PermissionFormData {
  resource: string;
  can_create: boolean;
  can_read: boolean;
  can_update: boolean;
  can_delete: boolean;
  condition?: string;
}

export interface RoleFormData {
  name: string;
  description: string;
  permissions: PermissionFormData[];
}

// Available resources for permissions
export const AVAILABLE_RESOURCES = [
  'forms',
  'guest_sessions',
  'ivf_scores',
  'diet_plan_scores',
  'diet_plans',
  'condition_meal_plans',
  'options',
  'otps',
  'permissions',
  'profiles',
  'question_tracks',
  'questions',
  'roles',
  'storage',
  'tracks',
  'user_roles',
  'doctors',
  'appointments',
  'patients',
  'cities',
  'clinics',
  'time_slots'
] as const;


export type ResourceType = typeof AVAILABLE_RESOURCES[number];

// Permission actions
export const PERMISSION_ACTIONS = [
  'create',
  'read', 
  'update',
  'delete'
] as const;

export type PermissionAction = typeof PERMISSION_ACTIONS[number];

// Helper type for permission checking
export interface PermissionCheck {
  resource: ResourceType;
  action: PermissionAction;
}

// UI state types
export interface RolesPermissionsState {
  roles: RoleWithPermissions[];
  selectedRole: RoleWithPermissions | null;
  isCreateRoleDialogOpen: boolean;
  isEditRoleDialogOpen: boolean;
  isDeleteRoleDialogOpen: boolean;
  loading: boolean;
  error: string | null;
}

// API response types
export interface CreateRoleRequest {
  name: string;
  description?: string;
  permissions?: PermissionFormData[];
}

export interface UpdateRoleRequest extends Partial<CreateRoleRequest> {
  id: string;
}

export interface CreatePermissionRequest {
  role_id: string;
  resource: string;
  can_create?: boolean;
  can_read?: boolean;
  can_update?: boolean;
  can_delete?: boolean;
  condition?: string;
}

export interface UpdatePermissionRequest extends Partial<CreatePermissionRequest> {
  id: string;
}
