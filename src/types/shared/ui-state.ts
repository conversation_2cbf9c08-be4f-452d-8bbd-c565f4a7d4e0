import type { FormBuilderForm } from "../form";
import type { FormCategory } from "./categories";

// Form builder state
export interface FormBuilderState {
  selectedForm: FormCategory | null;
  forms: Record<FormCategory, FormBuilderForm>;
  isLoading: boolean;
  error: string | null;
  isDirty: boolean;
}

// Drag and drop types for form builder
export interface DragItem {
  id: string;
  type: 'question';
  index: number;
}

export interface DropResult {
  dragIndex: number;
  hoverIndex: number;
} 