import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/contexts/AuthContext";

export interface Appointment {
  id: string;
  doctor_id: string;
  patient_id: string;
  clinic_id: string;
  appointment_date: string;
  start_time: string;
  end_time: string;
  consultation_type: 'in_person' | 'online';
  status: 'completed' | 'upcoming' | 'cancelled';
  duration: number;
  fees: number;
  currency: string;
  payment_status: 'paid' | 'unpaid';
  booking_date: string;
  created_at: string;
  updated_at: string;
  doctor: {
    id: string;
    specialization_name: string;
    years_of_experience: number;
    consultation_fees: number;
    consultation_currency: string;
    consultation_mode: string;
    profile: {
      display_name: string;
      email: string;
    };
  };
  clinic: {
    clinic_name: string;
    address: string;
    city: {
      city_name: string;
      state_name: string;
    };
  };
}

export interface AppointmentsResponse {
  appointments: Appointment[];
  total: number;
  page: number;
  perPage: number;
  totalPages: number;
}

export interface UseAppointmentsOptions {
  page?: number;
  perPage?: number;
  status?: 'completed' | 'upcoming' | 'cancelled';
}

export const useAppointments = (options: UseAppointmentsOptions = {}) => {
  const { user_token } = useAuth();
  const { page = 1, perPage = 10, status } = options;

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['appointments', page, perPage, status, user_token],
    queryFn: async (): Promise<AppointmentsResponse> => {
      if (!user_token) {
        throw new Error('Authentication token is required');
      }

      const params = new URLSearchParams();
      params.append('page', page.toString());
      params.append('per_page', perPage.toString());
      if (status) {
        params.append('status', status);
      }

      const response = await fetch(`/api/v1/appointments?${params}`, {
        headers: {
          'Authorization': `Bearer ${user_token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch appointments');
      }

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch appointments');
      }

      return result.data;
    },
    enabled: !!user_token,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  return {
    appointments: data?.appointments || [],
    total: data?.total || 0,
    page: data?.page || 1,
    perPage: data?.perPage || 10,
    totalPages: data?.totalPages || 0,
    loading: isLoading,
    error: error ? (error as Error).message : null,
    refetch,
  };
};

export default useAppointments;
