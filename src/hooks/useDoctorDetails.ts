import { useState, useEffect } from 'react';
import { useAuth } from "@/contexts/AuthContext";
import { DoctorType } from "./useDoctors";


interface UseDoctorDetailsReturn {
  doctor: DoctorType | null;
  loading: boolean;
  error: string | null;
}

export const useDoctorDetails = (doctorId: number): UseDoctorDetailsReturn => {
  const { user_token } = useAuth();
  const [doctor, setDoctor] = useState<DoctorType | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!doctorId) {
      setError("Doctor ID is required");
      setLoading(false);
      return;
    }

    if (!user_token) {
      setError("Authentication token is required");
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    // Simulate API call delay
    const fetchDoctor = async (doctorId: number) => {
      try {
        // doctor details api
        const foundDoctor = await fetch(`/api/v1/doctors/${doctorId}`, {
          headers: {
            'Authorization': `Bearer ${user_token}`,
            'Content-Type': 'application/json',
          },
        });
        if (!foundDoctor.ok) {
          throw new Error('Failed to fetch doctor details');
        }
        const foundDoctorData = await foundDoctor.json();
        if (!foundDoctorData.success) {
          throw new Error(foundDoctorData.error || 'Failed to fetch doctor details');
        }

        if (!foundDoctorData.data) {
          setError(`Doctor with ID ${doctorId} not found`);
          setDoctor(null);
        } else {
          setDoctor(foundDoctorData.data);
        }
      } catch (err) {
        console.error(err);
        setError("Failed to fetch doctor details");
        setDoctor(null);
      } finally {
        setLoading(false);
      }
    };

    // Add a small delay to simulate real API behavior
    const timer = setTimeout(() => fetchDoctor(doctorId), 100);

    return () => clearTimeout(timer);
  }, [doctorId, user_token]);

  return { doctor, loading, error };
};

export default useDoctorDetails;
