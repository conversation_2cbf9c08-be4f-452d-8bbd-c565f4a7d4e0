import { useQuery } from "@tanstack/react-query";

type CityType = {
  id: number;
  city_name: string;
  state_name: string;
};

export const useGetCity = (cityId: number): string => {
  const { data } = useQuery({
    queryKey: ['cities'],
    queryFn: async () => {
      const response = await fetch('/api/v1/public/cities');
      if (!response.ok) {
        throw new Error('Failed to fetch cities');
      }
      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch cities');
      }
      return result.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  if (!data) {
    return "";
  }

  if (!cityId) {
    return "";
  }
  const city = data.find((city: CityType) => city.id === cityId);
  return city?.city_name || "";
};

export default useGetCity;
