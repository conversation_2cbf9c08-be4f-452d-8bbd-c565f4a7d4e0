import { useState, useEffect, useMemo, useCallback, useRef } from "react";
import { Question } from "@/types/questions";
import {
  getStoredFormData,
  storeFormData
} from "@/utils/formLocalStorage";

interface FormErrors {
  [questionId: string]: string;
}

export const useDynamicForm = (
  questions: Question[],
  onSubmit: (data: Record<string, string>) => void,
  formTitle: string,
  userId?: string,
  category?: string
) => {
  const getInitialData = () => {
    if (typeof window === "undefined") {
      return {};
    }
    if (category) {
      const storedData = getStoredFormData(userId);
      return storedData[category] || {};
    }
    const savedData = getStoredFormData(userId);
    return savedData[formTitle] || {};
  };

  const [formData, setFormData] = useState<Record<string, string>>(
    getInitialData
  );
  const [errors, setErrors] = useState<FormErrors>({});
  const [showValidation, setShowValidation] = useState(false);
  const initialized = useRef(false);

  const allQuestions = useMemo(() => {
    const flattened: Question[] = [];
    questions.forEach((q) => {
      if (q.field_type.value === "GROUP_QUESTION" && q.sub_questions) {
        flattened.push(...q.sub_questions);
      } else {
        flattened.push(q);
      }
    });
    return flattened;
  }, [questions]);

  const renderList = useMemo(() => {
    const list: (Question | { isGroup: true; text: string; id: string })[] =
      [];
    questions.forEach((q) => {
      if (q.field_type.value === "GROUP_QUESTION") {
        list.push({ isGroup: true, text: q.question_text, id: q.id });
        if (q.sub_questions) {
          list.push(...q.sub_questions);
        }
      } else {
        list.push(q);
      }
    });
    return list;
  }, [questions]);

  useEffect(() => {
    if (questions.length === 0) {
      initialized.current = false;
      return;
    }
    if (allQuestions.length > 0 && !initialized.current) {
      const initialData = getInitialData();
      allQuestions.forEach((question) => {
        if (!initialData[question.id]) {
          initialData[question.id] = "";
        }
      });
      setFormData(initialData);
      initialized.current = true;
    }
  }, [allQuestions, questions.length]);

  const isQuestionRequired = useCallback((question: Question): boolean => {
    return question.is_mandatory;
  }, []);

  const isQuestionVisible = useCallback(
    (question: Question, currentFormData: Record<string, string>): boolean => {
      if (!question.depends_on_option_id) return true;
      const parentQuestion = allQuestions.find((q) =>
        q.options.some((opt) => opt.id === question.depends_on_option_id)
      );
      const controllingOption = parentQuestion?.options.find(
        (opt) => opt.id === question.depends_on_option_id
      );
      if (parentQuestion && controllingOption) {
        const matchValue = controllingOption.value ?? controllingOption.text;
        return currentFormData[parentQuestion.id] === matchValue;
      }
      return false;
    },
    [allQuestions]
  );

  const validateForm = useCallback((): boolean => {
    const newErrors: FormErrors = {};

    allQuestions.forEach((question) => {
      if (!isQuestionVisible(question, formData)) return;
      if (isQuestionRequired(question)) {
        const value = formData[question.id] || "";

        if (!value.trim()) {
          newErrors[question.id] = `${question.question_text} is required`;
          return;
        }

        if (
          question.field_type.value === "NUMBER_INPUT" ||
          question.field_type.value === "RANGE_SLIDER"
        ) {
          const numValue = parseFloat(value);

          if (isNaN(numValue)) {
            newErrors[question.id] = "Please enter a valid number";
            return;
          }

          if (
            question.config?.min_value !== undefined &&
            question.config?.min_value !== null &&
            numValue < question.config.min_value
          ) {
            newErrors[
              question.id
            ] = `Minimum value is ${question.config.min_value}`;
            return;
          }

          if (
            question.config?.max_value !== undefined &&
            question.config?.max_value !== null &&
            numValue > question.config.max_value
          ) {
            newErrors[
              question.id
            ] = `Maximum value is ${question.config.max_value}`;
            return;
          }
        }

        if (
          question.field_type.value === "RANGE_SLIDER" &&
          question.config?.min_value === 0 &&
          parseFloat(value) === 0
        ) {
          newErrors[
            question.id
          ] = `Please select a value for ${question.question_text}`;
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [allQuestions, formData, isQuestionRequired, isQuestionVisible]);

  const handleInputChange = (questionId: string, value: string) => {
    const newFormData = {
      ...formData,
      [questionId]: value,
    };
    setFormData(newFormData);
    
    // Store raw form data during input changes (not normalized)
    if (category) {
      storeFormData({ [category]: newFormData }, userId);
    } else {
      // Fall back to old method for backward compatibility
      storeFormData({ [formTitle]: newFormData }, userId);
    }

    if (showValidation && errors[questionId]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[questionId];
        return newErrors;
      });
    }
  };

  const handleSubmit = () => {
    setShowValidation(true);
    if (validateForm()) {
      const submissionData: Record<string, string> = {};
      allQuestions.forEach((question) => {
        if (isQuestionVisible(question, formData)) {
          const value = formData[question.id] || "";
          submissionData[question.id] = value;
        }
      });
      onSubmit(submissionData);
    }
  };

  const isFormValid = useMemo(() => {
    return allQuestions.every((question) => {
      if (!isQuestionVisible(question, formData)) return true;
      if (!isQuestionRequired(question)) return true;
      const value = formData[question.id] || "";
      if (!value.trim()) return false;
      if (
        question.field_type.value === "RANGE_SLIDER" &&
        question.config?.min_value === 0 &&
        parseFloat(value) === 0
      ) {
        return false;
      }
      return true;
    });
  }, [allQuestions, formData, isQuestionRequired, isQuestionVisible]);

  return {
    formData,
    errors,
    showValidation,
    renderList,
    handleInputChange,
    handleSubmit,
    isFormValid,
    isQuestionVisible,
    allQuestions,
  };
};
