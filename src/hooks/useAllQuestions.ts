import { useQuery } from '@tanstack/react-query';
import { Question, Form, QuestionsResponse } from '@/types/questions';

interface AllQuestionsResponse {
  forms: Form[];
  questions: Question[];
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

/**
 * React Query hook to fetch all questions for all categories
 * Uses tracks parameter but omits category to get all relevant questions
 * Provides caching and background refetching capabilities
 */
export function useAllQuestions(): AllQuestionsResponse {
  const selected_track_type = typeof window !== 'undefined' 
    ? sessionStorage.getItem('ivf_selected_assessment_type') 
    : null;

  const {
    data,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['allQuestions', selected_track_type],
    queryFn: async (): Promise<QuestionsResponse> => {
      // Fetch all questions using tracks parameter but without category filter
      const api_url = selected_track_type 
        ? `/api/v1/ivf-assessment/questions?category=biological,lifestyle,environmental&tracks=T${selected_track_type}` 
        : '/api/v1/ivf-assessment/questions?category=biological,lifestyle,environmental';
      
      const response = await fetch(api_url);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch questions: ${response.status}`);
      }

      const data: QuestionsResponse = await response.json();
      
      if (!data.success) {
        throw new Error('API returned unsuccessful response');
      }

      return data;
    },
    // Only run query if we have a track type or if we're on the client
    enabled: typeof window !== 'undefined',
    // Cache for 10 minutes
    staleTime: 10 * 60 * 1000,
    // Keep cached data for 15 minutes
    gcTime: 15 * 60 * 1000,
    // Retry failed requests 3 times
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    // Don't refetch on window focus for this data
    refetchOnWindowFocus: false,
  });

  // Extract all questions from all forms
  const allQuestions = data?.data?.forms?.flatMap(form => 
    form.questions?.sort((a, b) => a.order - b.order) || []
  ) || [];

  return {
    forms: data?.data?.forms || [],
    questions: allQuestions,
    loading: isLoading,
    error: error ? (error as Error).message : null,
    refetch: () => refetch(),
  };
}

/**
 * Helper function to get questions for a specific category from cached data
 */
export function useQuestionsForCategory(category: string): {
  questions: Question[];
  form: Form | null;
  loading: boolean;
  error: string | null;
} {
  console.log('Category:', category)
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { forms, questions, loading, error } = useAllQuestions();
  // Filter questions by category using the expected form name
  const categoryForm = forms.find(form => form.slug === category);

  const categoryQuestions = categoryForm?.questions?.sort((a, b) => a.order - b.order) || [];

  return {
    questions: categoryQuestions,
    form: categoryForm || null,
    loading,
    error,
  };
} 