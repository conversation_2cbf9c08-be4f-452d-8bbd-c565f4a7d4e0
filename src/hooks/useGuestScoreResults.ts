import { useQuery } from '@tanstack/react-query';

export interface ScoreResult {
  totalScore: number;
  maxScore: number;
  percentage: number;
  category: string;
  avgFactors: {
    biological: [number, number];
    lifestyle: [number, number];
    environmental: [number, number];
  };
  factors: {
    biological: number;
    lifestyle: number;
    environmental: number;
  };
}

export interface ScoreResponse {
  score: ScoreResult;
  cached: boolean;
  processingTime: number;
  status: string;
}

/**
 * Custom hook to fetch IVF score results for guest users using React Query
 * Provides automatic caching, background refetching, and error handling
 */
export const useGuestScoreResults = (sessionToken: string | null) => {
  return useQuery({
    queryKey: ['guestScoreResults', sessionToken],
    queryFn: async (): Promise<ScoreResponse> => {
      if (!sessionToken) {
        throw new Error('Session token is required');
      }

      console.log("useGuestScoreResults: Fetching results with session token:", sessionToken);

      const response = await fetch(`/api/v1/ivf-assessment/guest-results?session_token=${sessionToken}`, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch results');
      }

      return response.json();
    },
    // Only run the query if we have a session token
    enabled: !!sessionToken,
    // Cache the result for 10 minutes (matches our API cache)
    staleTime: 10 * 60 * 1000,
    // Keep cached data for 15 minutes
    gcTime: 15 * 60 * 1000,
    // Retry failed requests 3 times with exponential backoff
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    // Don't refetch on window focus for this data
    refetchOnWindowFocus: false,
  });
}; 