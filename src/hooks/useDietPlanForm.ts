"use client";

import { useQuery } from "@tanstack/react-query";
import { getDietPlanForm, initializeDietPlanForm } from "@/lib/services/client/diet-plan.service";
import { FormWithQuestions } from "@/types";
import { Question } from "@/types/questions";
import { QuestionWithOptions } from "@/types/question";

// Helper function to convert QuestionWithOptions to Question type
const convertToQuestionType = (questionWithOptions: QuestionWithOptions): Question => {
  return {
    id: questionWithOptions.id,
    question_text: questionWithOptions.question_text,
    parent_id: questionWithOptions.parent_id,
    sub_questions: questionWithOptions.sub_questions?.map(convertToQuestionType) || [],
    collective_formula: questionWithOptions.collective_formula,
    scoring_type: questionWithOptions.scoring_type,
    scoring_config: questionWithOptions.scoring_config ? String(questionWithOptions.scoring_config) : null,
    is_mandatory: questionWithOptions.is_mandatory,
    order: questionWithOptions.order,
    field_type: {
      value: questionWithOptions.field_type,
      label: questionWithOptions.field_type,
      description: "",
      capabilities: {
        supportsOptions: true,
        supportsRange: true,
        supportsPlaceholder: true,
        supportsUnit: true,
        supportsScoring: true,
      },
    },
    config: {
      placeholder: questionWithOptions.placeholder || undefined,
      min_value: questionWithOptions.min_value || undefined,
      max_value: questionWithOptions.max_value || undefined,
      unit: questionWithOptions.unit || undefined,
    },
    options: questionWithOptions.options.map(option => ({
      id: option.id,
      text: option.option_text,
      value: option.value,
      order: option.order,
      score: option.score || 0,
    })),
    depends_on_option_id: questionWithOptions.depends_on_option_id,
    tracks: [],
    scoring: {
      type: questionWithOptions.scoring_type || "",
      config: questionWithOptions.scoring_config ? String(questionWithOptions.scoring_config) : "",
    },
  };
};

export function useDietPlanForm() {
  const query = useQuery<FormWithQuestions | null, Error>({
    queryKey: ["dietPlanForm"],
    queryFn: async () => {
      await initializeDietPlanForm();
      return await getDietPlanForm();
    },
  });

  // Convert questions to the expected Question type
  const questions = query.data?.questions?.map(convertToQuestionType) || [];

  return {
    ...query,
    questions,
  };
} 