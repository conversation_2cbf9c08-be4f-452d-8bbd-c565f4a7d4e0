"use client";

import { useQuery } from "@tanstack/react-query";
import {
  getFormsByCategory,
  initializeDefaultForms,
} from "@/lib/services/client/questions.service";
import { FormCategory, FormWithQuestions } from "@/types";

export function useFormsByCategory() {
  return useQuery<Record<FormCategory, FormWithQuestions | null>, Error>({
    queryKey: ["formsByCategory"],
    queryFn: async () => {
      await initializeDefaultForms();
      return await getFormsByCategory();
    },
  });
}
