import { useCallback, useEffect } from "react";

export const useHandleBeforeUnload = (formCompleted: boolean) => {
  // Handle beforeunload event with browser's native dialog
  const handleBeforeUnload = useCallback(
    (event: BeforeUnloadEvent) => {
      if (!formCompleted) {
        const message =
          "You are in the middle of a test. On leaving this page, all your responses will be lost. Are you sure you want to proceed?";
        event.preventDefault();
        return message;
      }
    },
    [formCompleted]
  );

  useEffect(() => {
    // Add beforeunload listener when component mounts
    window.addEventListener("beforeunload", handleBeforeUnload);

    // Cleanup event listener when component unmounts
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [handleBeforeUnload]);
};
