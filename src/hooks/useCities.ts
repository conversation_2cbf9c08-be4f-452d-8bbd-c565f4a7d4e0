import { useQuery } from "@tanstack/react-query";

export interface City {
  cityId: number;
  city: string;
}

type CityType = {
  id: number;
  city_name: string;
  state_name: string;
};

export const useCities = (): { cities: City[]; isLoading: boolean } => {
  const { data, isLoading, error } = useQuery({
    queryKey: ["cities"],
    queryFn: async () => {
      const response = await fetch("/api/v1/public/cities");
      if (!response.ok) {
        throw new Error("Failed to fetch cities");
      }
      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || "Failed to fetch cities");
      }
      return result.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  const cities =
    data && !error
      ? data.map((city: CityType) => ({
          cityId: city.id,
          city: city.city_name,
        }))
      : [];

  return { cities, isLoading };
};

export default useCities;
