import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/contexts/AuthContext";

export interface GuestSessionData {
  session_token: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  ivf_data: any;
  is_verified: boolean;
  expires_at: string;
  current_step: number;
}

export interface GuestSessionCheckResponse {
  hasGuestSession: boolean;
  guestSession?: GuestSessionData;
}

/**
 * Custom hook to check if user has a valid guest session that can be converted
 */
export const useGuestSessionCheck = () => {
  const { user_token, user, isLoading } = useAuth();

  return useQuery({
    queryKey: ['guestSessionCheck', user?.id],
    queryFn: async (): Promise<GuestSessionCheckResponse> => {
      if (!user_token || !user?.id) {
        throw new Error('Authentication token and user ID are required');
      }

      const response = await fetch(`/api/v1/ivf-assessment/check-guest-session?userId=${user.id}`, {
        headers: {
          'Authorization': `Bearer ${user_token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to check guest session');
      }

      return response.json();
    },
    // Only run the query if we have a user token, user ID, and auth is not loading
    enabled: !!user_token && !!user?.id && !isLoading,
    // Cache the result for 2 minutes
    staleTime: 2 * 60 * 1000,
    // Keep cached data for 5 minutes
    gcTime: 5 * 60 * 1000,
    // Retry failed requests 2 times with exponential backoff
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
    // Don't refetch on window focus for this data
    refetchOnWindowFocus: false,
  });
};

/**
 * Custom hook to convert guest session to authenticated user data
 */
export const useGuestSessionConversion = () => {
  const queryClient = useQueryClient();
  const { user_token } = useAuth();

  return useMutation({
    mutationFn: async ({ guestSessionToken, overwrite = false }: { guestSessionToken: string; overwrite?: boolean }) => {
      if (!user_token) {
        throw new Error('Authentication token is required');
      }

      const response = await fetch('/api/v1/ivf-assessment/convert-guest', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${user_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          guestSessionToken,
          overwrite,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to convert guest session');
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate and refetch relevant queries after successful conversion
      queryClient.invalidateQueries({ queryKey: ['checkExistingIVFScores'] });
      queryClient.invalidateQueries({ queryKey: ['scoreResults'] });
      queryClient.invalidateQueries({ queryKey: ['guestSessionCheck'] });
    },
  });
};
