"use client";

import { useQuery } from '@tanstack/react-query';
import { createClient } from '@/utils/supabase/client';

export const useUserCan = (resource_action: string) => {
  return useQuery({
    queryKey: ["authorize", resource_action],
    queryFn: async () => {
      const supabase = createClient();
      const { data, error } = await supabase.rpc('authorize', {
        resource_action
      });
      
      if (error) {
        console.error('Error checking permissions:', error);
        return false;
      }
      
      return !!data;
    },
    // Only run the query if we have a resource_action
    enabled: !!resource_action,
    // Cache the result for 5 minutes
    staleTime: 5 * 60 * 1000,
    // Keep the cached result for 10 minutes
    gcTime: 10 * 60 * 1000,
  });
}; 