import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';

export interface CheckExistingScoresResponse {
  hasExistingScores: boolean;
}

/**
 * Custom hook to check if user has existing IVF scores
 * This is a lightweight check before fetching full score data
 */
export const useCheckExistingIVFScores = () => {
  const { user_token, isLoading, user } = useAuth();

  return useQuery({
    queryKey: ['checkExistingIVFScores', user_token],
    queryFn: async (): Promise<CheckExistingScoresResponse> => {
      if (!user_token || !user?.id) {
        throw new Error('Authentication token and user ID are required');
      }

      const response = await fetch(`/api/v1/ivf-assessment/check-existing-scores?userId=${user.id}`, {
        headers: {
          'Authorization': `Bearer ${user_token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to check existing scores');
      }

      return response.json();
    },
    // Only run the query if we have a user token, user ID, and auth is not loading
    enabled: !!user_token && !!user?.id && !isLoading,
    // Cache the result for 5 minutes
    staleTime: 5 * 60 * 1000,
    // Keep cached data for 10 minutes
    gcTime: 10 * 60 * 1000,
    // Retry failed requests 2 times with exponential backoff
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
    // Don't refetch on window focus for this data
    refetchOnWindowFocus: false,
  });
};
