import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useToast } from '@/contexts/ToastContext';
import { useQuestionsForCategory, useAllQuestions } from './useAllQuestions';
import { storeFormData, getStoredFormData, transformFormDataForAPI } from '@/utils/formLocalStorage';
import { convertNumericValues } from '@/utils/formDataUtils';

// Map of categories to their next page
const NEXT_PAGE_MAP: Record<string, string> = {
  biological: '/ivf-assessment/forms/lifestyle-psychosocial',
  lifestyle: '/ivf-assessment/forms/environmental',
  environmental: '/ivf-assessment/verify-details'
};

export function useGuestFormSubmission(category: string) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();
  const toast = useToast();
  
  const { questions, loading: questionsLoading, error: questionsError } = useQuestionsForCategory(category);
  const { questions: allQuestions } = useAllQuestions();

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const submitForm = async (formData: Record<string, any>) => {
    setIsSubmitting(true);

    try {
      // Store it directly without transformation
      storeFormData({ [category]: formData });
      console.log('Form data stored locally:', formData);
      
      if(category !== 'environmental') {
        toast.success(`${category.charAt(0).toUpperCase() + category.slice(1)} form data submitted successfully.`);
        const nextPage = NEXT_PAGE_MAP[category] || '/';
        router.push(nextPage);
      }
      
      return { success: true };
    } catch (error) {
      console.error('Form storage error:', error);
      toast.error('Failed to save form data.');
      return { success: false, error };
    } finally {
      setIsSubmitting(false);
    }
  };

  const submitFinalForm = async () => {
    setIsSubmitting(true);
    try {
      const allFormData = getStoredFormData();

      // Use the already cached questions from useAllQuestions hook
      const payload = convertNumericValues(allFormData, allQuestions);
      
      const guestSessionToken = sessionStorage.getItem('ivf_guest_session_token');
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };
      if (guestSessionToken) {
        headers['X-Guest-Session'] = guestSessionToken;
      }
      
      const selected_track_type = sessionStorage.getItem('ivf_selected_assessment_type');
      const formApiUrl = selected_track_type ? `/api/v1/ivf-assessment/form?tracks=T${selected_track_type}` : `/api/v1/ivf-assessment/form`;
      
      const response = await fetch(formApiUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify(payload),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to submit final form');
      }

      toast.success('Form data submitted successfully!');
      router.push('/ivf-assessment/verify-details');
      
      return { success: true, data };
    } catch (error) {
      setIsSubmitting(false);
      console.error('Final form submission error:', error);
      toast.error(error instanceof Error ? error.message : 'An error occurred during final submission.');
      return { success: false, error };
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    submitForm,
    submitFinalForm,
    isSubmitting,
    questions,
    questionsLoading,
    questionsError,
  };
}

// Export the standardized transformFormData function
export { transformFormDataForAPI as transformFormData };