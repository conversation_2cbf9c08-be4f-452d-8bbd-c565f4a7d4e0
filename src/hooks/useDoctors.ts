import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/contexts/AuthContext";


export type DoctorType = {
  id: number;
  specialization_name?: string;
  years_of_experience?: number;
  consultation_fees?: number;
  consultation_currency?: string;
  consultation_mode?: "in_person" | "video" | "both";
  profile: {
    id: number;
    display_name: string;
    email: string;
    phone?: string;
    auth_id: string;
    biography?: string;
    profile_image?: string;
    languages?: string[];
  };
  doctor_clinics?: {
    clinic: {
      id: number;
      clinic_name: string;
      address?: string;
      city?: {
        id: number;
        city_name: string;
        state_name?: string;
      };
    };
  }[];
  _count?: {
    time_slots: number;
    appointments: number;
    availability_templates: number;
  };
}

export const useDoctors = (centerId: number) => {
  const { user_token } = useAuth();

  const { data, isLoading, error } = useQuery({
    queryKey: ["doctors", centerId, user_token],
    queryFn: async () => {
      const response = await fetch(
        `/api/v1/doctors?clinic_id=${centerId}`,
        {
          headers: {
            'Authorization': `Bearer ${user_token}`,
            'Content-Type': 'application/json',
          },
        }
      );
      if (!response.ok) {
        throw new Error("Failed to fetch doctors");
      }
      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || "Failed to fetch doctors");
      }
      return result.data;
    },
    enabled: !!centerId && !!user_token,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  const doctors = data || [];

  return {
    doctors,
    isLoading,
    error,
  };
};

export default useDoctors;
