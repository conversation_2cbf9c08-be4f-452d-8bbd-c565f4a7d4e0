import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/contexts/AuthContext";

export interface Clinic {
  centerId: number;
  name: string;
  address?: string;
  contact_info?: string;
}

type ClinicType = {
  id: number;
  clinic_name: string;
  address?: string;
  contact_info?: string;
};

export const useClinics = (
  cityId: number
): { clinics: Clinic[]; isLoading: boolean } => {
  const { user_token } = useAuth();

  const { data, isLoading, error } = useQuery({
    queryKey: ["clinics", cityId, user_token],
    queryFn: async () => {
      if (!user_token) {
        throw new Error('Authentication token is required');
      }

      const response = await fetch(`/api/v1/appointments/clinics?city_id=${cityId}`, {
        headers: {
          'Authorization': `Bearer ${user_token}`,
          'Content-Type': 'application/json',
        },
      });
      if (!response.ok) {
        throw new Error("Failed to fetch clinics");
      }
      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error || "Failed to fetch clinics");
      }
      return result.data;
    },
    enabled: !!cityId && !!user_token,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  const clinics =
    data && !error
      ? data.map((clinic: ClinicType) => ({
          centerId: clinic.id,
          name: clinic.clinic_name,
          address: clinic.address,
          contact_info: clinic.contact_info,
        }))
      : [];

  return { clinics, isLoading };
};

export default useClinics;
