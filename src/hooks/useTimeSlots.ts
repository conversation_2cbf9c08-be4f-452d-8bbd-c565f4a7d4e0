import { useQuery } from "@tanstack/react-query";
import { DateSlotGroup } from "@/types/api/public/api-timeslots";
import { useAuth } from "@/contexts/AuthContext";

export interface UseTimeSlotsResult {
  dateRange: {
    startDate: string;
    endDate: string;
  };
  totalSlots: number;
  dateSlots: DateSlotGroup[];
  loading: boolean;
  error: string | null;
}

export const useTimeSlots = (
  doctorId: number | string,
  clinicId: number | string,
  startDate?: string,
  endDate?: string
): UseTimeSlotsResult => {
  const { user_token } = useAuth();
  
  const { data, isLoading, error } = useQuery({
    queryKey: ['timeSlots', doctorId, clinicId, startDate, endDate, user_token],
    queryFn: async () => {
      if (!user_token) {
        throw new Error('Authentication token is required');
      }

      const params = new URLSearchParams();
      params.append('doctor_id', doctorId.toString());
      params.append('clinic_id', clinicId.toString());

      // Default to next 7 days if no dates provided
      const start = startDate || new Date().toISOString().split('T')[0];
      params.append('start_date', start);

      const response = await fetch(`/api/v1/appointments/time-slots?${params}`, {
        headers: {
          'Authorization': `Bearer ${user_token}`,
          'Content-Type': 'application/json',
        },
      });
      if (!response.ok) {
        throw new Error('Failed to fetch available time slots');
      }
      const result = await response.json();
      console.log(result)
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch available time slots');
      }
      return result.data;
    },
    enabled: !!doctorId && !!user_token,
    staleTime: 5 * 60 * 1000, // 5 minutes (templates change less frequently)
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  return {
    dateRange: {
      startDate: data?.date_range?.start_date || startDate || '',
      endDate: data?.date_range?.end_date || endDate || '',
    },
    totalSlots: data?.total_slots || 0,
    dateSlots: data?.date_slots || [],
    loading: isLoading,
    error: error ? (error as Error).message : null,
  };
};

export default useTimeSlots;
