import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/contexts/AuthContext";

type ClinicType = {
  id: number;
  clinic_name: string;
  address?: string;
  contact_info?: string;
};

export const useGetClinic = (centerId: number): string => {
  const { user_token } = useAuth();
  
  const { data } = useQuery({
    queryKey: ['all-clinics', user_token],
    queryFn: async () => {
      if (!user_token) {
        throw new Error('Authentication token is required');
      }

      // First get all cities
      const citiesResponse = await fetch('/api/v1/public/cities');
      if (!citiesResponse.ok) {
        throw new Error('Failed to fetch cities');
      }
      const citiesResult = await citiesResponse.json();
      if (!citiesResult.success) {
        throw new Error(citiesResult.error || 'Failed to fetch cities');
      }

      // Then get all clinics for all cities
      const allClinics = [];
      for (const city of citiesResult.data) {
        const clinicsResponse = await fetch(`/api/v1/appointments/clinics?city_id=${city.id}`, {
          headers: {
            'Authorization': `Bearer ${user_token}`,
            'Content-Type': 'application/json',
          },
        });
        if (clinicsResponse.ok) {
          const clinicsResult = await clinicsResponse.json();
          if (clinicsResult.success) {
            allClinics.push(...clinicsResult.data);
          }
        }
      }
      return allClinics;
    },
    enabled: !!user_token,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  if (!data) {
    return "";
  }

  if (!centerId) {
    return "";
  }
  const clinic = data.find((clinic: ClinicType) => clinic.id === centerId);
  return clinic?.clinic_name || "";
};

export default useGetClinic;
