import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';

export interface ScoreResult {
  totalScore: number;
  maxScore: number;
  percentage: number;
  category: string;
  avgFactors: {
    biological: [number, number];
    lifestyle: [number, number];
    environmental: [number, number];
  };
  factors: {
    biological: number;
    lifestyle: number;
    environmental: number;
  };
}

export interface ScoreResponse {
  score: ScoreResult;
  cached: boolean;
  processingTime: number;
  status: string;
}

/**
 * Custom hook to fetch IVF score results using React Query
 * Provides automatic caching, background refetching, and error handling
 */
export const useScoreResults = (hasExistingScores: boolean = true) => {
  const { user_token, isLoading } = useAuth();

  return useQuery({
    queryKey: ['scoreResults', user_token],
    queryFn: async (): Promise<ScoreResponse> => {
      if (!user_token) {
        throw new Error('Authentication token is required');
      }

      const response = await fetch('/api/v1/ivf-assessment/results', {
        headers: {
          'Authorization': `Bear<PERSON> ${user_token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to fetch results');
      }

      return response.json();
    },
    // Only run the query if we have a user token, auth is not loading, and user has existing scores
    enabled: !!user_token && !isLoading && hasExistingScores,
    // Cache the result for 10 minutes (matches our API cache)
    staleTime: 10 * 60 * 1000,
    // Keep cached data for 15 minutes
    gcTime: 15 * 60 * 1000,
    // Retry failed requests 3 times with exponential backoff
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    // Don't refetch on window focus for this data
    refetchOnWindowFocus: false,
  });
}; 