import { useAuth } from "@/contexts/AuthContext";
import { useState } from "react";

interface AppointmentBookingData {
  doctor_id: number;
  clinic_id: number;
  start_time: string;
  end_time: string;
  duration: number;
  appointment_date?: string; // Optional appointment date in YYYY-MM-DD format
}

interface AppointmentBookingResult {
  success: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data?: any;
  error?: string;
}

export const useAppointmentBooking = () => {
  const [isBooking, setIsBooking] = useState(false);
  const { user_token } = useAuth();

  const bookAppointment = async (bookingData: AppointmentBookingData): Promise<AppointmentBookingResult> => {
    setIsBooking(true);

    try {
      // Get auth token
      if (!user_token) {
        throw new Error("No authentication token found");
      }

      // Call the appointment API
      const response = await fetch("/api/v1/appointments", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bear<PERSON> ${user_token}`,
        },
        body: JSON.stringify(bookingData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to book appointment");
      }

      return {
        success: true,
        data: result.data,
      };

    } catch (error) {
      console.error("Error booking appointment:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      };
    } finally {
      setIsBooking(false);
    }
  };

  return {
    bookAppointment,
    isBooking,
  };
};
