import { useEffect } from "react";
import { useBreadcrumb, BreadcrumbItem } from "@/contexts/BreadcrumbContext";

export const useBreadcrumbs = (breadcrumbs: BreadcrumbItem[]) => {
  const { setBreadcrumbs } = useBreadcrumb();

  useEffect(() => {
    setBreadcrumbs(breadcrumbs);

    // Cleanup on unmount
    return () => {
      setBreadcrumbs([]);
    };
  }, [breadcrumbs, setBreadcrumbs]);
};

export default useBreadcrumbs;
