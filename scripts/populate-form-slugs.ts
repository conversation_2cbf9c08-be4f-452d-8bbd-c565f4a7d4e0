import { PrismaClient } from '@/generated/prisma';

const prisma = new PrismaClient();

async function populateFormSlugs() {
  try {
    console.log('Populating form slugs...');

    // Get all existing forms
    const forms = await prisma.forms.findMany();
    
    for (const form of forms) {
      let slug = '';
      
      // Determine slug based on form name
      const lowerName = form.name.toLowerCase();
      if (lowerName.includes('biological')) {
        slug = 'biological';
      } else if (lowerName.includes('lifestyle')) {
        slug = 'lifestyle';
      } else if (lowerName.includes('environmental')) {
        slug = 'environmental';
      } else {
        // Generate a slug from the name for other forms
        slug = form.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '');
      }

      // Update the form with the slug
      await prisma.forms.update({
        where: { id: form.id },
        data: { slug }
      });

      console.log(`Updated form "${form.name}" with slug "${slug}"`);
    }

    // Create default IVF forms if they don't exist
    const defaultForms = [
      { name: 'Biological Factors Form', slug: 'biological', description: 'Default form for biological factors' },
      { name: 'Lifestyle & Psychosocial Form', slug: 'lifestyle', description: 'Default form for lifestyle factors' },
      { name: 'Environmental & Socioeconomic Factors Form', slug: 'environmental', description: 'Default form for environmental factors' }
    ];

    for (const defaultForm of defaultForms) {
      const existingForm = await prisma.forms.findUnique({
        where: { slug: defaultForm.slug }
      });

      if (!existingForm) {
        await prisma.forms.create({
          data: {
            name: defaultForm.name,
            slug: defaultForm.slug,
            description: defaultForm.description
          }
        });
        console.log(`Created default form "${defaultForm.name}" with slug "${defaultForm.slug}"`);
      }
    }

    console.log('Form slugs populated successfully!');
  } catch (error) {
    console.error('Error populating form slugs:', error);
  } finally {
    await prisma.$disconnect();
  }
}

populateFormSlugs(); 