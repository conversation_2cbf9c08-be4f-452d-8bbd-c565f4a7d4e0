# Conditionally render Question Field based on user selected option answers. 

The most straightforward way to handle this with minimal changes is to add a new field directly to the `questions` table that points to a controlling option.

-----

### Simplified Schema for 2-3 Conditional Questions

Here's how you can modify your `questions` table:

> Note: Please check `prisma/schema/questions.schema` for your reference

### Explanation of Simplified Changes:

1.  **`questions` Model:**

      * **`depends_on_option_id String? @db.Uuid @map("depends_on_option_id")`**: This new field is a foreign key that directly points to the `id` of an `option`. If this field is populated for a question, it means that question is a "dependent" question and its visibility is controlled by the selection of the linked option.
      * **`depends_on_option options? @relation("DependentQuestions", fields: [depends_on_option_id], references: [id])`**: This creates the relation to the `options` table.

2.  **`options` Model:**

      * **`dependent_questions questions[] @relation("DependentQuestions")`**: This is the inverse relation, allowing you to easily see which questions are made visible by a particular option.

-----

### How to Use It for Your Scenario:

Let's use your example:

  * **Question 1 (Controlling):** "Have you been trying for a baby?"
      * **Option A:** "Yes"
      * **Option B:** "No"
  * **Question 2 (Dependent):** "How long have you been trying?"

Here's how you'd set up the data:

1.  **Create Question 1:**

      * `id: <uuid_Q1>`
      * `question_text: "Have you been trying for a baby?"`
      * `field_type: RADIO_SELECT`
      * `depends_on_option_id: null` (It's a primary question, not dependent)

2.  **Create Options for Question 1:**

      * **Option A ("Yes"):**
          * `id: <uuid_OA_Yes>`
          * `question_id: <uuid_Q1>`
          * `option_text: "Yes"`
          * `value: "yes"`
      * **Option B ("No"):**
          * `id: <uuid_OB_No>`
          * `question_id: <uuid_Q1>`
          * `option_text: "No"`
          * `value: "no"`

3.  **Create Question 2:**

      * `id: <uuid_Q2>`
      * `question_text: "How long have you been trying?"`
      * `field_type: NUMBER_INPUT`
      * **`depends_on_option_id: <uuid_OB_No>`** (This links Question 2 to the "No" option of Question 1)

-----

### Frontend Logic with Simplified Schema:

1.  When you fetch questions for a form, you'll **include the `depends_on_option` relation**.
    ```typescript
    // Example Prisma query
    const formQuestions = await prisma.questions.findMany({
      where: { formId: formUuid },
      include: {
        options: true,
        depends_on_option: true, // Crucial for conditional rendering
      },
      orderBy: { order: 'asc' },
    });
    ```
2.  Iterate through the `formQuestions`.
3.  If a question's `depends_on_option_id` is **not null**:
      * This question is a dependent question.
      * **Initially hide it.**
      * Identify the `id` of the option that controls its visibility (`question.depends_on_option_id`).
4.  When the user selects an option for a question (e.g., "Have you been trying for a baby?" and they pick "No"):
      * Get the `id` of the selected option (`<uuid_OB_No>`).
      * Go through all your dependent questions.
      * If a dependent question's `depends_on_option_id` matches the `id` of the currently selected option (`<uuid_OB_No>`), then **show** that dependent question.
      * If a previously shown dependent question's controlling option is no longer selected, **hide** it.

This approach is much simpler for a limited number of conditional questions. It directly expresses "this question is dependent on *that specific option*."