// Mock Prisma client for Storybook
const mockPrismaClient = {
  // Mock all your Prisma models here
  dietPlan: {
    findMany: () => Promise.resolve([]),
    findUnique: () => Promise.resolve(null),
    create: () => Promise.resolve({}),
    update: () => Promise.resolve({}),
    delete: () => Promise.resolve({}),
    count: () => Promise.resolve(0),
  },

  user: {
    findMany: () => Promise.resolve([]),
    findUnique: () => Promise.resolve(null),
    create: () => Promise.resolve({}),
    update: () => Promise.resolve({}),
    delete: () => Promise.resolve({}),
    count: () => Promise.resolve(0),
  },

  // Add other models as needed
  // model: {
  //   findMany: () => Promise.resolve([]),
  //   findUnique: () => Promise.resolve(null),
  //   create: () => Promise.resolve({}),
  //   update: () => Promise.resolve({}),
  //   delete: () => Promise.resolve({}),
  //   count: () => Promise.resolve(0),
  // },

  $connect: () => Promise.resolve(),
  $disconnect: () => Promise.resolve(),
  $transaction: (fn) => fn(mockPrismaClient),
};

// Export the mock client
module.exports = {
  PrismaClient: function () {
    return mockPrismaClient;
  },
  prisma: mockPrismaClient,
  default: mockPrismaClient,
};

// Also export as ES6 module for compatibility
module.exports.default = mockPrismaClient;
