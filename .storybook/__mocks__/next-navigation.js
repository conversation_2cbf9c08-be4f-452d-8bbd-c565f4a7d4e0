// Mock Next.js navigation for Storybook
const mockRouter = {
  push: (url) => {
    console.log(`[Storybook] Navigating to: ${url}`);
    return Promise.resolve(true);
  },
  replace: (url) => {
    console.log(`[Storybook] Replacing with: ${url}`);
    return Promise.resolve(true);
  },
  back: () => {
    console.log("[Storybook] Navigating back");
  },
  forward: () => {
    console.log("[Storybook] Navigating forward");
  },
  refresh: () => {
    console.log("[Storybook] Refreshing page");
  },
  prefetch: (url) => {
    console.log(`[Storybook] Prefetching: ${url}`);
    return Promise.resolve();
  },
  pathname: "/",
  route: "/",
  query: {},
  asPath: "/",
  basePath: "",
  isLocaleDomain: false,
  isReady: true,
  isPreview: false,
};

export const useRouter = () => mockRouter;

export const usePathname = () => "/";

export const useSearchParams = () => {
  const searchParams = new URLSearchParams();
  // Add get method and other URLSearchParams methods
  return searchParams;
};

export const useParams = () => ({});

export const notFound = () => {
  console.log("[Storybook] Not found called");
};

export const redirect = (url) => {
  console.log(`[Storybook] Redirecting to: ${url}`);
};

// For backward compatibility with pages router
export const withRouter = (Component) => Component;

export default {
  useRouter,
  usePathname,
  useSearchParams,
  useParams,
  notFound,
  redirect,
  withRouter,
};
