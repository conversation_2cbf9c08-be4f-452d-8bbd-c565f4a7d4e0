{"success": true, "data": {"forms": [{"id": "9c8be5e2-5b95-4b1e-9123-5d060fbd2a25", "name": "Biological Factors Form", "description": "Default form for biological factors", "category": "biological", "questions": [{"id": "bade0323-7b83-4b33-90a4-e59a75253a32", "question_text": "Age", "help_text": null, "is_mandatory": true, "order": 1, "field_type": {"value": "NUMBER_INPUT", "label": "Number Input", "description": "Numerical input with optional range", "capabilities": {"supportsOptions": false, "supportsRange": true, "supportsPlaceholder": true, "supportsUnit": true, "supportsScoring": true}}, "config": {"placeholder": "e.g 30", "min_value": 16, "max_value": 50, "step": null, "unit": "yrs"}, "options": [], "tracks": [{"id": "de70a14a-0edf-4bf5-99c4-4538ba6abd92", "code": "T3", "name": "Track 3", "description": "I have done IVF before"}, {"id": "103b544a-1e66-4d76-9960-c793517298cd", "code": "T1", "name": "Track 1", "description": "I have not gotten any testing done"}, {"id": "19064ee5-1de1-4d9d-96ff-e3c187a3c8b9", "code": "T2", "name": "Track 2", "description": "I have gotten fertility tests done"}]}, {"id": "7d97c410-caf8-42bb-a92e-c4c352a96a81", "question_text": "Height", "help_text": null, "is_mandatory": true, "order": 2, "field_type": {"value": "NUMBER_INPUT", "label": "Number Input", "description": "Numerical input with optional range", "capabilities": {"supportsOptions": false, "supportsRange": true, "supportsPlaceholder": true, "supportsUnit": true, "supportsScoring": true}}, "config": {"placeholder": "e.g 165", "min_value": 120, "max_value": 220, "step": null, "unit": "cm"}, "options": [], "tracks": [{"id": "19064ee5-1de1-4d9d-96ff-e3c187a3c8b9", "code": "T2", "name": "Track 2", "description": "I have gotten fertility tests done"}, {"id": "de70a14a-0edf-4bf5-99c4-4538ba6abd92", "code": "T3", "name": "Track 3", "description": "I have done IVF before"}, {"id": "103b544a-1e66-4d76-9960-c793517298cd", "code": "T1", "name": "Track 1", "description": "I have not gotten any testing done"}]}], "summary": {"total_questions": 9, "track_distribution": {"T3": 9, "T1": 7, "T2": 9}, "field_type_distribution": {"NUMBER_INPUT": 4, "RADIO_SELECT": 4, "DROPDOWN_SELECT": 1}}}]}, "filters": {"tracks": [], "category": null}}