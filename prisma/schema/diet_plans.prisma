model diet_plans {
  id                    String   @id @default(uuid())
  doctor_id             BigInt
  doctor                profiles @relation("DoctorDietPlans", fields: [doctor_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  user_id               BigInt
  user                  profiles @relation("UserDietPlans", fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  diet_plan_scores_id   String
  diet_plan_scores      diet_plan_scores @relation(fields: [diet_plan_scores_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  target_calories       Int?
  condition_note        String?
  meal_plan_doc         String?

  created_at            DateTime @default(now())
  updated_at            DateTime @updatedAt
}

// New model for condition-based meal plans
model condition_meal_plans {
  id                    String   @id @default(uuid())
  doctor_id             BigInt
  doctor                profiles @relation("DoctorConditionMealPlans", fields: [doctor_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  condition             diet_condition
  target_calories_min   Int
  target_calories_max   Int
  recommended_target_calories Int
  meal_plan_doc_url     String?

  nutritional_advice    String?
  is_active             Boolean  @default(true)

  created_at            DateTime @default(now())
  updated_at            DateTime @updatedAt
}

enum diet_condition {
  NO_CONDITION
  PCOS
  LOW_AMH
  PCOS_AND_LOW_AMH
}