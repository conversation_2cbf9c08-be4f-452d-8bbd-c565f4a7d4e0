model guest_sessions {
  id             String                @id @default(uuid())
  session_token  String                @unique
  ivf_data       Json
  current_step   Int                   @default(1)
  email          String?
  is_verified    Boolean               @default(false)
  expires_at     DateTime
  created_at     DateTime              @default(now())
  updated_at     DateTime              @updatedAt
  selected_track String?
  display_name   String?
  phone          String?
  status         ivf_assessment_status @default(pending)

  @@index([session_token])
  @@index([expires_at])
  @@index([email])
}
