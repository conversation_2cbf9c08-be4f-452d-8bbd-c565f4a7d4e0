model cities {
  id        BigInt   @id @default(autoincrement())
  city_name String
  state_name String?
  clinics   clinics[]
  lat        Float?
  lng        Float?
  iso2       String?
  created_at DateTime @default(now()) @db.Timestamptz(6)
  updated_at DateTime @default(now()) @db.Timestamptz(6)

  @@map("cities")
}

model clinics {
  id           BigInt   @id @default(autoincrement())
  clinic_name  String
  address      String?
  city_id      BigInt?
  contact_info String?
  latitude     Float?
  longitude    Float?
  clinic_start_time String? // Time in HH:mm format (e.g., "09:00")
  clinic_end_time   String? // Time in HH:mm format (e.g., "17:00")
  city         cities?  @relation(fields: [city_id], references: [id])
  appointments appointments[]
  doctor_clinics doctor_clinics[]
  availability_templates availability_templates[]
  unavailabilities unavailabilities[]
  created_at   DateTime @default(now()) @db.Timestamptz(6)
  updated_at   DateTime @default(now()) @db.Timestamptz(6)

  @@map("clinics")
}

model doctors {
  id                 BigInt   @id
  specialization_name String?
  years_of_experience Int?
  consultation_fees   Decimal? @db.Decimal(10, 2)
  consultation_currency String? @default("INR")
  consultation_mode   ConsultationMode? @default(both)
  profile            profiles @relation("DoctorProfile", fields: [id], references: [id])
  appointments       appointments[]
  availability_templates availability_templates[]
  unavailabilities   unavailabilities[]
  doctor_clinics     doctor_clinics[]
  created_at         DateTime @default(now()) @db.Timestamptz(6)
  updated_at         DateTime @default(now()) @db.Timestamptz(6)

  @@map("doctors")
}

model doctor_clinics {
  id         BigInt   @id @default(autoincrement())
  doctor_id  BigInt
  clinic_id  BigInt
  doctor     doctors  @relation(fields: [doctor_id], references: [id], onDelete: Cascade)
  clinic     clinics  @relation(fields: [clinic_id], references: [id], onDelete: Cascade)
  created_at DateTime @default(now()) @db.Timestamptz(6)
  updated_at DateTime @default(now()) @db.Timestamptz(6)

  @@unique([doctor_id, clinic_id])
  @@map("doctor_clinics")
}

model patients {
  id          BigInt   @id
  profile     profiles @relation("PatientProfile", fields: [id], references: [id])
  appointments appointments[]
  created_at  DateTime @default(now()) @db.Timestamptz(6)
  updated_at  DateTime @default(now()) @db.Timestamptz(6)

  @@map("patients")
}

model appointments {
  id                BigInt           @id @default(autoincrement())
  doctor_id         BigInt
  patient_id        BigInt?
  clinic_id         BigInt
  appointment_date  DateTime
  start_time        DateTime
  end_time          DateTime
  consultation_type ConsultationType
  status            AppointmentStatus
  duration          Int
  fees              Decimal          @db.Decimal(10, 2)
  currency          String
  payment_status    PaymentStatus
  booking_date      DateTime?
  doctor            doctors          @relation(fields: [doctor_id], references: [id])
  patient           patients?        @relation(fields: [patient_id], references: [id])
  clinic            clinics          @relation(fields: [clinic_id], references: [id])
  created_at        DateTime         @default(now()) @db.Timestamptz(6)
  updated_at        DateTime         @default(now()) @db.Timestamptz(6)

  @@map("appointments")
}



model availability_templates {
  id           BigInt   @id @default(autoincrement())
  doctor_id    BigInt
  clinic_id    BigInt?
  day_of_week  Int      // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
  start_time   String   // Time in HH:mm format (e.g., "09:00")
  end_time     String   // Time in HH:mm format (e.g., "09:15")
  duration     Int      @default(15)
  is_available Boolean  @default(true)
  fee          Decimal? @db.Decimal(10, 2)
  currency     String?
  doctor       doctors  @relation(fields: [doctor_id], references: [id])
  clinic       clinics? @relation(fields: [clinic_id], references: [id])
  created_at   DateTime @default(now()) @db.Timestamptz(6)
  updated_at   DateTime @default(now()) @db.Timestamptz(6)

  @@unique([doctor_id, clinic_id, day_of_week, start_time])
  @@map("availability_templates")
}

enum ConsultationType {
  in_person
  online
}

enum AppointmentStatus {
  completed
  upcoming
  cancelled
}

enum PaymentStatus {
  paid
  unpaid
}

enum ConsultationMode {
  in_person
  video
  both
}
