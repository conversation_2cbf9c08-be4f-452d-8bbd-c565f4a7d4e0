model permissions {
  id         BigInt   @id(map: "user_permissions_pkey") @default(autoincrement())
  created_at DateTime @default(now()) @db.Timestamptz(6)
  role_id    BigInt
  resource   String?
  condition  String?
  can_create <PERSON><PERSON>an  @default(false)
  can_read   <PERSON><PERSON>an  @default(false)
  can_update <PERSON><PERSON>an  @default(false)
  can_delete <PERSON>olean  @default(false)
  roles      roles    @relation(fields: [role_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}
