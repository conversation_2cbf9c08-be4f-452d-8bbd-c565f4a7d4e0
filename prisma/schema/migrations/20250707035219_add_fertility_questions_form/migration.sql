-- CreateEnum
CREATE TYPE "FormFieldType" AS ENUM ('INPUT', 'NUMBER_INPUT', 'RADIO_SELECT', 'DROPDOWN_SELECT', 'RANGE_SLIDER');

-- CreateTable
CREATE TABLE "forms" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "forms_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "fertility_questions" (
    "id" UUID NOT NULL,
    "form_id" UUID NOT NULL,
    "question_text" TEXT NOT NULL,
    "field_type" "FormFieldType" NOT NULL,
    "placeholder" TEXT,
    "min_value" INTEGER,
    "max_value" INTEGER,
    "step" INTEGER,
    "unit" TEXT,
    "order" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "fertility_questions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "form_options" (
    "id" UUID NOT NULL,
    "question_id" UUID NOT NULL,
    "option_text" TEXT NOT NULL,
    "value" TEXT,
    "order" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "form_options_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "forms_name_key" ON "forms"("name");

-- CreateIndex
CREATE INDEX "fertility_questions_form_id_idx" ON "fertility_questions"("form_id");

-- CreateIndex
CREATE INDEX "form_options_question_id_idx" ON "form_options"("question_id");

-- AddForeignKey
ALTER TABLE "fertility_questions" ADD CONSTRAINT "fertility_questions_form_id_fkey" FOREIGN KEY ("form_id") REFERENCES "forms"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "form_options" ADD CONSTRAINT "form_options_question_id_fkey" FOREIGN KEY ("question_id") REFERENCES "fertility_questions"("id") ON DELETE CASCADE ON UPDATE CASCADE;
