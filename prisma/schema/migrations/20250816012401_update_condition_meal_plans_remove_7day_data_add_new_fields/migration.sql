/*
  Warnings:

  - You are about to drop the column `day_1_breakfast` on the `condition_meal_plans` table. All the data in the column will be lost.
  - You are about to drop the column `day_1_dinner` on the `condition_meal_plans` table. All the data in the column will be lost.
  - You are about to drop the column `day_1_lunch` on the `condition_meal_plans` table. All the data in the column will be lost.
  - You are about to drop the column `day_1_snacks` on the `condition_meal_plans` table. All the data in the column will be lost.
  - You are about to drop the column `day_2_breakfast` on the `condition_meal_plans` table. All the data in the column will be lost.
  - You are about to drop the column `day_2_dinner` on the `condition_meal_plans` table. All the data in the column will be lost.
  - You are about to drop the column `day_2_lunch` on the `condition_meal_plans` table. All the data in the column will be lost.
  - You are about to drop the column `day_2_snacks` on the `condition_meal_plans` table. All the data in the column will be lost.
  - You are about to drop the column `day_3_breakfast` on the `condition_meal_plans` table. All the data in the column will be lost.
  - You are about to drop the column `day_3_dinner` on the `condition_meal_plans` table. All the data in the column will be lost.
  - You are about to drop the column `day_3_lunch` on the `condition_meal_plans` table. All the data in the column will be lost.
  - You are about to drop the column `day_3_snacks` on the `condition_meal_plans` table. All the data in the column will be lost.
  - You are about to drop the column `day_4_breakfast` on the `condition_meal_plans` table. All the data in the column will be lost.
  - You are about to drop the column `day_4_dinner` on the `condition_meal_plans` table. All the data in the column will be lost.
  - You are about to drop the column `day_4_lunch` on the `condition_meal_plans` table. All the data in the column will be lost.
  - You are about to drop the column `day_4_snacks` on the `condition_meal_plans` table. All the data in the column will be lost.
  - You are about to drop the column `day_5_breakfast` on the `condition_meal_plans` table. All the data in the column will be lost.
  - You are about to drop the column `day_5_dinner` on the `condition_meal_plans` table. All the data in the column will be lost.
  - You are about to drop the column `day_5_lunch` on the `condition_meal_plans` table. All the data in the column will be lost.
  - You are about to drop the column `day_5_snacks` on the `condition_meal_plans` table. All the data in the column will be lost.
  - You are about to drop the column `day_6_breakfast` on the `condition_meal_plans` table. All the data in the column will be lost.
  - You are about to drop the column `day_6_dinner` on the `condition_meal_plans` table. All the data in the column will be lost.
  - You are about to drop the column `day_6_lunch` on the `condition_meal_plans` table. All the data in the column will be lost.
  - You are about to drop the column `day_6_snacks` on the `condition_meal_plans` table. All the data in the column will be lost.
  - You are about to drop the column `day_7_breakfast` on the `condition_meal_plans` table. All the data in the column will be lost.
  - You are about to drop the column `day_7_dinner` on the `condition_meal_plans` table. All the data in the column will be lost.
  - You are about to drop the column `day_7_lunch` on the `condition_meal_plans` table. All the data in the column will be lost.
  - You are about to drop the column `day_7_snacks` on the `condition_meal_plans` table. All the data in the column will be lost.
  - Added the required column `recommended_target_calories` to the `condition_meal_plans` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "condition_meal_plans" DROP COLUMN "day_1_breakfast",
DROP COLUMN "day_1_dinner",
DROP COLUMN "day_1_lunch",
DROP COLUMN "day_1_snacks",
DROP COLUMN "day_2_breakfast",
DROP COLUMN "day_2_dinner",
DROP COLUMN "day_2_lunch",
DROP COLUMN "day_2_snacks",
DROP COLUMN "day_3_breakfast",
DROP COLUMN "day_3_dinner",
DROP COLUMN "day_3_lunch",
DROP COLUMN "day_3_snacks",
DROP COLUMN "day_4_breakfast",
DROP COLUMN "day_4_dinner",
DROP COLUMN "day_4_lunch",
DROP COLUMN "day_4_snacks",
DROP COLUMN "day_5_breakfast",
DROP COLUMN "day_5_dinner",
DROP COLUMN "day_5_lunch",
DROP COLUMN "day_5_snacks",
DROP COLUMN "day_6_breakfast",
DROP COLUMN "day_6_dinner",
DROP COLUMN "day_6_lunch",
DROP COLUMN "day_6_snacks",
DROP COLUMN "day_7_breakfast",
DROP COLUMN "day_7_dinner",
DROP COLUMN "day_7_lunch",
DROP COLUMN "day_7_snacks",
ADD COLUMN     "meal_plan_doc_url" TEXT,
ADD COLUMN     "recommended_target_calories" INTEGER NOT NULL;
