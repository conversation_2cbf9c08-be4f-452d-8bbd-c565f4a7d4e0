/*
  Warnings:

  - A unique constraint covering the columns `[doctor_id,clinic_id,day_of_week,start_time]` on the table `availability_templates` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX "availability_templates_doctor_id_day_of_week_start_time_key";

-- AlterTable
ALTER TABLE "availability_templates" ADD COLUMN     "clinic_id" BIGINT;

-- CreateIndex
CREATE UNIQUE INDEX "availability_templates_doctor_id_clinic_id_day_of_week_star_key" ON "availability_templates"("doctor_id", "clinic_id", "day_of_week", "start_time");

-- AddForeignKey
ALTER TABLE "availability_templates" ADD CONSTRAINT "availability_templates_clinic_id_fkey" FOREIGN KEY ("clinic_id") REFERENCES "clinics"("id") ON DELETE SET NULL ON UPDATE CASCADE;
