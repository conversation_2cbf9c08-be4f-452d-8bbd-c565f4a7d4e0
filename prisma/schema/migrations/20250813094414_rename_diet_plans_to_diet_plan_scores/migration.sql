/*
  Warnings:

  - You are about to drop the `diet_plans` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "diet_plans" DROP CONSTRAINT "diet_plans_user_id_fkey";

-- DropTable
DROP TABLE "diet_plans";

-- CreateTable
CREATE TABLE "diet_plan_scores" (
    "id" TEXT NOT NULL,
    "user_id" UUID NOT NULL,
    "form_data" JSON NOT NULL,
    "current_step" INTEGER NOT NULL DEFAULT 1,
    "status" "diet_plan_status" NOT NULL DEFAULT 'pending',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "diet_plan_scores_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "diet_plan_scores_user_id_key" ON "diet_plan_scores"("user_id");

-- Add<PERSON><PERSON>ignKey
ALTER TABLE "diet_plan_scores" ADD CONSTRAINT "diet_plan_scores_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("auth_id") ON DELETE CASCADE ON UPDATE NO ACTION;
