-- CreateEnum
CREATE TYPE "diet_plan_status" AS ENUM ('pending', 'completed', 'failed');

-- CreateTable
CREATE TABLE "diet_plans" (
    "id" TEXT NOT NULL,
    "user_id" UUID NOT NULL,
    "form_data" JSON NOT NULL,
    "current_step" INTEGER NOT NULL DEFAULT 1,
    "status" "diet_plan_status" NOT NULL DEFAULT 'pending',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "diet_plans_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "diet_plans_user_id_key" ON "diet_plans"("user_id");

-- Add<PERSON><PERSON>ign<PERSON><PERSON>
ALTER TABLE "diet_plans" ADD CONSTRAINT "diet_plans_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("auth_id") ON DELETE CASCADE ON UPDATE NO ACTION;
