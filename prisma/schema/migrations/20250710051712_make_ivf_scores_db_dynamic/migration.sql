/*
  Warnings:

  - You are about to drop the column `age` on the `ivf_scores` table. All the data in the column will be lost.
  - You are about to drop the column `diet_type` on the `ivf_scores` table. All the data in the column will be lost.
  - You are about to drop the column `emotional_support_at_home` on the `ivf_scores` table. All the data in the column will be lost.
  - You are about to drop the column `exercise_frequency` on the `ivf_scores` table. All the data in the column will be lost.
  - You are about to drop the column `height` on the `ivf_scores` table. All the data in the column will be lost.
  - You are about to drop the column `household_income_range` on the `ivf_scores` table. All the data in the column will be lost.
  - You are about to drop the column `infertility_duration` on the `ivf_scores` table. All the data in the column will be lost.
  - You are about to drop the column `ivf_attempts` on the `ivf_scores` table. All the data in the column will be lost.
  - You are about to drop the column `known_conditions` on the `ivf_scores` table. All the data in the column will be lost.
  - You are about to drop the column `living_area` on the `ivf_scores` table. All the data in the column will be lost.
  - You are about to drop the column `menstrual_regularity` on the `ivf_scores` table. All the data in the column will be lost.
  - You are about to drop the column `occupation_type` on the `ivf_scores` table. All the data in the column will be lost.
  - You are about to drop the column `pollution_exposure` on the `ivf_scores` table. All the data in the column will be lost.
  - You are about to drop the column `sleep_quality` on the `ivf_scores` table. All the data in the column will be lost.
  - You are about to drop the column `smoking_or_alcohol_habits` on the `ivf_scores` table. All the data in the column will be lost.
  - You are about to drop the column `stress_level` on the `ivf_scores` table. All the data in the column will be lost.
  - You are about to drop the column `weight` on the `ivf_scores` table. All the data in the column will be lost.
  - You are about to drop the column `work_stress_level` on the `ivf_scores` table. All the data in the column will be lost.
  - Added the required column `form_data` to the `ivf_scores` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "ivf_scores" DROP COLUMN "age",
DROP COLUMN "diet_type",
DROP COLUMN "emotional_support_at_home",
DROP COLUMN "exercise_frequency",
DROP COLUMN "height",
DROP COLUMN "household_income_range",
DROP COLUMN "infertility_duration",
DROP COLUMN "ivf_attempts",
DROP COLUMN "known_conditions",
DROP COLUMN "living_area",
DROP COLUMN "menstrual_regularity",
DROP COLUMN "occupation_type",
DROP COLUMN "pollution_exposure",
DROP COLUMN "sleep_quality",
DROP COLUMN "smoking_or_alcohol_habits",
DROP COLUMN "stress_level",
DROP COLUMN "weight",
DROP COLUMN "work_stress_level",
ADD COLUMN     "form_data" JSONB NOT NULL;

-- AlterTable
ALTER TABLE "question_tracks" ALTER COLUMN "id" DROP DEFAULT;

-- AlterTable
ALTER TABLE "tracks" ALTER COLUMN "id" DROP DEFAULT,
ALTER COLUMN "order" DROP DEFAULT;

-- RenameForeignKey
ALTER TABLE "options" RENAME CONSTRAINT "form_options_question_id_fkey" TO "options_question_id_fkey";

-- RenameForeignKey
ALTER TABLE "questions" RENAME CONSTRAINT "fertility_questions_form_id_fkey" TO "questions_form_id_fkey";
