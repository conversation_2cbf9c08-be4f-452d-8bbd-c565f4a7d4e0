-- Update existing forms with slugs based on their names
UPDATE "forms" SET "slug" = 'biological' WHERE "name" LIKE '%Biological%';
UPDATE "forms" SET "slug" = 'lifestyle' WHERE "name" LIKE '%Lifestyle%';
UPDATE "forms" SET "slug" = 'environmental' WHERE "name" LIKE '%Environmental%';

-- For any remaining forms without slugs, generate a slug from the name
UPDATE "forms" 
SET "slug" = LOWER(REGEXP_REPLACE("name", '[^a-zA-Z0-9]+', '-', 'g'))
WHERE "slug" IS NULL;

-- Remove leading and trailing dashes
UPDATE "forms" 
SET "slug" = TRIM(BOTH '-' FROM "slug")
WHERE "slug" LIKE '-%' OR "slug" LIKE '%-'; 