-- Create<PERSON><PERSON>
CREATE TYPE "diet_condition" AS ENUM ('PCOS', 'LOW_AMH', 'INCREASE_FERTILITY_NATURALLY', 'INCREASE_IRON');

-- CreateTable
CREATE TABLE "diet_plans" (
    "id" TEXT NOT NULL,
    "doctor_id" BIGINT NOT NULL,
    "user_id" BIGINT NOT NULL,
    "diet_plan_scores_id" TEXT NOT NULL,
    "target_calories" INTEGER,
    "condition_note" TEXT,
    "meal_plan_doc" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "diet_plans_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "condition_meal_plans" (
    "id" TEXT NOT NULL,
    "doctor_id" BIGINT NOT NULL,
    "condition" "diet_condition" NOT NULL,
    "target_calories_min" INTEGER NOT NULL,
    "target_calories_max" INTEGER NOT NULL,
    "day_1_breakfast" JSON NOT NULL,
    "day_1_lunch" JSON NOT NULL,
    "day_1_dinner" JSON NOT NULL,
    "day_1_snacks" JSON NOT NULL,
    "day_2_breakfast" JSON NOT NULL,
    "day_2_lunch" JSON NOT NULL,
    "day_2_dinner" JSON NOT NULL,
    "day_2_snacks" JSON NOT NULL,
    "day_3_breakfast" JSON NOT NULL,
    "day_3_lunch" JSON NOT NULL,
    "day_3_dinner" JSON NOT NULL,
    "day_3_snacks" JSON NOT NULL,
    "day_4_breakfast" JSON NOT NULL,
    "day_4_lunch" JSON NOT NULL,
    "day_4_dinner" JSON NOT NULL,
    "day_4_snacks" JSON NOT NULL,
    "day_5_breakfast" JSON NOT NULL,
    "day_5_lunch" JSON NOT NULL,
    "day_5_dinner" JSON NOT NULL,
    "day_5_snacks" JSON NOT NULL,
    "day_6_breakfast" JSON NOT NULL,
    "day_6_lunch" JSON NOT NULL,
    "day_6_dinner" JSON NOT NULL,
    "day_6_snacks" JSON NOT NULL,
    "day_7_breakfast" JSON NOT NULL,
    "day_7_lunch" JSON NOT NULL,
    "day_7_dinner" JSON NOT NULL,
    "day_7_snacks" JSON NOT NULL,
    "nutritional_advice" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "condition_meal_plans_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "diet_plans" ADD CONSTRAINT "diet_plans_doctor_id_fkey" FOREIGN KEY ("doctor_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "diet_plans" ADD CONSTRAINT "diet_plans_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "diet_plans" ADD CONSTRAINT "diet_plans_diet_plan_scores_id_fkey" FOREIGN KEY ("diet_plan_scores_id") REFERENCES "diet_plan_scores"("id") ON DELETE CASCADE ON UPDATE NO ACTION;

-- AddForeignKey
ALTER TABLE "condition_meal_plans" ADD CONSTRAINT "condition_meal_plans_doctor_id_fkey" FOREIGN KEY ("doctor_id") REFERENCES "profiles"("id") ON DELETE CASCADE ON UPDATE NO ACTION;
