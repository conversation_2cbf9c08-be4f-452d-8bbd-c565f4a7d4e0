-- CreateTable
CREATE TABLE "tracks" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "code" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "tracks_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "question_tracks" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "question_id" UUID NOT NULL,
    "track_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "question_tracks_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "tracks_code_key" ON "tracks"("code");

-- CreateIndex
CREATE UNIQUE INDEX "question_tracks_question_id_track_id_key" ON "question_tracks"("question_id", "track_id");

-- CreateIndex
CREATE INDEX "question_tracks_question_id_idx" ON "question_tracks"("question_id");

-- CreateIndex
CREATE INDEX "question_tracks_track_id_idx" ON "question_tracks"("track_id");

-- AddForeignKey
ALTER TABLE "question_tracks" ADD CONSTRAINT "question_tracks_question_id_fkey" FOREIGN KEY ("question_id") REFERENCES "fertility_questions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "question_tracks" ADD CONSTRAINT "question_tracks_track_id_fkey" FOREIGN KEY ("track_id") REFERENCES "tracks"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Insert initial track data
INSERT INTO "tracks" ("code", "name", "description", "updated_at") VALUES 
('T1', 'Track 1', 'I have not gotten any testing done', CURRENT_TIMESTAMP),
('T2', 'Track 2', 'I have gotten fertility tests done', CURRENT_TIMESTAMP),
('T3', 'Track 3', 'I have done IVF before', CURRENT_TIMESTAMP); 