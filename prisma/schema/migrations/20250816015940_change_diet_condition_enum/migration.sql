/*
  Warnings:

  - The values [INCREASE_FERTILITY_NATURALLY,INCREASE_IRON] on the enum `diet_condition` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "diet_condition_new" AS ENUM ('NO_CONDITION', 'PCOS', 'LOW_AMH', 'PCOS_AND_LOW_AMH');
ALTER TABLE "condition_meal_plans" ALTER COLUMN "condition" TYPE "diet_condition_new" USING ("condition"::text::"diet_condition_new");
ALTER TYPE "diet_condition" RENAME TO "diet_condition_old";
ALTER TYPE "diet_condition_new" RENAME TO "diet_condition";
DROP TYPE "diet_condition_old";
COMMIT;
