model unavailabilities {
  id           BigInt   @id @default(autoincrement())
  doctor_id    BigInt
  clinic_id    BigInt?
  date         DateTime // Specific date for unavailability
  start_time   String   // Time in HH:mm format (e.g., "09:00")
  end_time     String   // Time in HH:mm format (e.g., "17:00")
  duration     Int      @default(15) // Duration in minutes
  reason       String?  // Reason for unavailability (e.g., "Vacation", "Sick Leave", "Conference")
  notes        String?  // Optional notes for the unavailability
  doctor       doctors  @relation(fields: [doctor_id], references: [id], onDelete: Cascade)
  clinic       clinics? @relation(fields: [clinic_id], references: [id], onDelete: Cascade)
  created_at   DateTime @default(now()) @db.Timestamptz(6)
  updated_at   DateTime @default(now()) @db.Timestamptz(6)

  @@unique([doctor_id, clinic_id, date, start_time])
  @@map("unavailabilities")
}
