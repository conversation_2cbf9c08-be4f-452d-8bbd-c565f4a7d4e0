model otps {
  id           BigInt   @id @default(autoincrement())
  created_at   DateTime @default(now()) @db.Timestamptz(6)
  updated_at   DateTime @default(now()) @updatedAt @db.Timestamptz(6)
  phone_number String   @unique
  code         String
  expires_at   DateTime @db.Timestamptz(6)
  attempts     Int      @default(0)
  is_verified  Boolean  @default(false)

  @@index([phone_number, expires_at])
}
