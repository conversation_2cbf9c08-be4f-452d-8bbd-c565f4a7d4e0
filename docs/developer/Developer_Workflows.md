# Developer Workflows

This document outlines the common workflows and scripts for developers working on the GIVF project.

## Getting Started

1.  **Install dependencies:**
    ```bash
    npm install
    ```
2.  **Set up environment variables:**
    Copy `.env.example` to a new file named `.env.local` and fill in the required values for your local development environment.
3.  **Set up the database:**
    Run the Prisma migrations and seed the database with initial data.
    ```bash
    npm run prisma:dev
    npm run prisma:seed:dev
    ```
4.  **Run the development server:**
    ```bash
    npm run dev
    ```
    This will start the Next.js development server with Turbopack for faster performance.

## Scripts

The `package.json` file contains several scripts for common development tasks.

-   **`dev`**: Starts the development server.
-   **`build`**: Builds the application for production.
-   **`start`**: Starts the production server.
-   **`lint`**: Lints the codebase using ESLint to check for code quality and style issues.
-   **`format`**: Formats the code using Prettier.
-   **`test`**: Runs all tests using Jest.
    -   **`test:client`**: Runs only the client-side tests.
    -   **`test:server`**: Runs only the server-side tests.

## Testing

The project uses Jest for both client-side (React components, hooks) and server-side (API routes, utilities) testing.

-   **Client tests** are run in a `jsdom` environment.
-   **Server tests** are run in a `node` environment.

To run all tests:
```bash
npm test
```

## Committing Code

The project uses `commitlint` to enforce a consistent commit message format. This helps to keep the commit history clean and readable. Before you commit, your commit message will be checked to ensure it follows the conventional commit format.

A typical commit message looks like this:
```
feat: add user profile page
```
or
```
fix: correct calculation in scoring system
```

The allowed types are: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`.

Husky is used to automatically run checks before committing.

## Prisma Workflows

-   **Making schema changes:**
    1.  Modify the Prisma schema files in `prisma/schema/`.
    2.  Run `npm run prisma:migrate:dev` to create a new migration file and apply it to your local database.
-   **Seeding the database:**
    Run `npm run prisma:seed:dev` to populate your database with the data from `prisma/seed.ts`. This is useful after resetting the database or for setting up a new development environment.
-   **Generating the Prisma Client:**
    The Prisma Client is generated automatically when you run `npm install` or `npm run build`. If you make changes to the schema and need to update the client manually, you can run `npm run prisma:generate`.
