# GIVF Developer Documentation

Welcome to the developer documentation for the GIVF project. This collection of documents provides a comprehensive guide to the project's architecture, features, and development workflows.

## Table of Contents

*   [**1. Project Structure**](./Project_Structure.md): An overview of the directory structure and key files.
*   [**2. Developer Workflows**](./Developer_Workflows.md): How to get started, run the project, and use the available scripts.
*   [**3. UI Development**](./UI_Development.md): Information on the tools and conventions for building the user interface, including React, Tailwind CSS, and Storybook.
*   [**4. Data Layer**](./Data_Layer.md): Details on how the project uses PostgreSQL and Prisma for database management.
*   [**5. API**](./API.md): An explanation of the GraphQL and REST APIs.
*   [**6. Authentication**](./Authentication.md): How user authentication is handled with Supabase Auth.
*   [**7. Permissions**](./Permissions.md): A deep dive into the role-based access control (RBAC) system.
*   [**8. Admin Dashboard**](./Admin_Dashboard.md): A guide to the features and structure of the admin panel.
*   [**9. IVF Fertility Questions**](./IVF_Fertility_Questions.md): An explanation of the business logic behind the fertility questions.
*   [**10. Scoring System**](./Scoring_System.md): The technical implementation of the dynamic IVF scoring system.
