# Guest User APIs

This document describes the guest user APIs for updating user information and email verification.

## Overview

The guest user system allows users to complete the IVF assessment process without creating a full account, then optionally verify their email to access results.

## APIs

### 1. Update Guest User Info and Send Verification Email

**Endpoint:** `POST /api/v1/guest-auth/signup`

**Description:** Updates guest user information and sends a verification email with O<PERSON>.

**Request Body:**
```json
{
  "session_token": "string (required)",
  "email": "string (required, valid email format)",
  "display_name": "string (required)",
  "phone": "string (optional)",
  "selected_track": "1" | "2" | "3" (required)
}
```

**Response (Success - 200):**
```json
{
  "success": true,
  "message": "Guest user info updated and verification email sent",
  "data": {
    "message": "Please check your email for the verification code",
    "session_token": "string",
    "email": "string",
    "developmentCode": "string (only in development mode)",
    "expiresIn": "10 minutes (only in development mode)"
  }
}
```

**Response (Error - 400):**
```json
{
  "success": false,
  "message": "Validation error details"
}
```

**Response (Error - 404):**
```json
{
  "success": false,
  "message": "Guest session not found. Please complete the IVF scoring steps first."
}
```

**Response (Error - 500):**
```json
{
  "success": false,
  "message": "Failed to send verification email. Please try again."
}
```

### 2. Verify OTP

**Endpoint:** `POST /api/v1/guest-auth/verify-otp`

**Description:** Verifies the OTP sent to the guest user and marks the session as verified.

**Request Body:**
```json
{
  "session_token": "string (required)",
  "email": "string (required, valid email format)",
  "code": "string (required, 6 digits)"
}
```

**Response (Success - 200):**
```json
{
  "success": true,
  "message": "Email verified successfully",
  "data": {
    "message": "Email verification completed successfully",
    "verified": true,
    "session_token": "string",
    "email": "string"
  }
}
```

**Response (Error - 400):**
```json
{
  "success": false,
  "message": "Invalid OTP. Please check your code and try again.",
  "data": {
    "remainingAttempts": 4
  }
}
```

**Response (Error - 404):**
```json
{
  "success": false,
  "message": "No OTP found for this session. Please request a new one."
}
```

**Response (Error - 410):**
```json
{
  "success": false,
  "message": "OTP has expired. Please request a new one."
}
```

**Response (Error - 429):**
```json
{
  "success": false,
  "message": "Maximum attempts exceeded. Please request a new OTP."
}
```

## Email System

### Configuration

The email system uses nodemailer and supports both development and production environments:

**Development Environment:**
- Uses Ethereal Email for testing
- Environment variables: `ETHEREAL_USER`, `ETHEREAL_PASS`
- Default values provided for testing

**Production Environment:**
- Uses SMTP with configurable provider
- Environment variables: `SMTP_HOST`, `SMTP_PORT`, `SMTP_USER`, `SMTP_PASS`
- Default: Gmail SMTP

**Common Environment Variables:**
- `EMAIL_FROM`: Sender email address (default: <EMAIL>)

### Email Templates

Email templates are located in `src/lib/email-templates/` and include:

- **verify-email-with-otp.ts**: Template for verification emails
- Responsive HTML design
- Plain text fallback
- Security notices and expiry information

### OTP System

**Features:**
- 6-digit numeric codes
- 10-minute expiry
- Maximum 5 attempts per OTP
- Automatic cleanup of expired OTPs
- Database storage with Prisma

**Security:**
- OTPs are deleted after successful verification
- Failed attempts are tracked and limited
- Expired OTPs are automatically cleaned up

## Usage Examples

### Frontend Integration

```javascript
// Step 1: Update guest user info and send verification email
const signupResponse = await fetch('/api/v1/guest-auth/signup', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    session_token: 'guest-session-token',
    email: '<EMAIL>',
    display_name: 'John Doe',
    phone: '+1234567890',
    selected_track: '1'
  })
});

// Step 2: Verify OTP
const verifyResponse = await fetch('/api/v1/guest-auth/verify-otp', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    session_token: 'guest-session-token',
    email: '<EMAIL>',
    code: '123456'
  })
});
```

### Development Testing

In development mode, the OTP code is included in the API response for testing purposes:

```json
{
  "success": true,
  "data": {
    "developmentCode": "123456",
    "expiresIn": "10 minutes"
  }
}
```

## Error Handling

**Common Error Scenarios:**

1. **Invalid Session Token**: User must complete IVF assessment first
2. **Invalid Email Format**: Email validation fails
3. **OTP Expired**: Code expired after 10 minutes
4. **Too Many Attempts**: Exceeded 5 attempts per OTP
5. **Email Sending Failed**: SMTP configuration issues

**Best Practices:**

1. Always validate email format on frontend
2. Implement proper error handling for all scenarios
3. Show remaining attempts to users
4. Provide clear error messages
5. Implement retry mechanisms for email sending

## Security Considerations

1. **OTP Security:**
   - 6-digit codes provide sufficient entropy
   - 10-minute expiry prevents long-term attacks
   - Maximum attempts prevent brute force
   - OTPs are deleted after use

2. **Email Security:**
   - Use secure SMTP connections
   - Validate email addresses
   - Rate limiting on email sending
   - Monitor for abuse

3. **Session Security:**
   - Session tokens should be secure random strings
   - Sessions expire after 24 hours
   - Validate session ownership before operations

## Environment Setup

**Required Environment Variables:**

```bash
# Email Configuration
EMAIL_FROM=<EMAIL>

# Development (Ethereal Email)
ETHEREAL_USER=<EMAIL>
ETHEREAL_PASS=test123

# Production (SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

**Database Schema:**

The system uses the existing `otps` table with the following structure:
- `phone_number`: Used to store `session_token:email` combination
- `code`: 6-digit OTP
- `expires_at`: Expiry timestamp
- `attempts`: Number of verification attempts
- `is_verified`: Whether OTP was successfully used 