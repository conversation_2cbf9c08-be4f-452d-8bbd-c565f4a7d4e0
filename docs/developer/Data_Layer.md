# Data Layer

This document provides an overview of the data layer in the GIVF application, which is powered by PostgreSQL and Prisma.

## Overview

The data layer consists of a PostgreSQL database hosted by Supabase and the Prisma ORM for interacting with the database in a type-safe way.

## Key Files and Directories

-   **`prisma/`**: The main directory for all things Prisma.
    -   **`schema/`**: Contains the Prisma schema files. The main schema file is `schema.prisma`, which defines the database connection and generators. Other `.prisma` files define the data models.
    -   **`migrations/`**: Stores the SQL migration files generated by Prisma Migrate. These files track the evolution of the database schema over time.
    -   **`seed.ts`**: A script for populating the database with initial data. This is useful for development and testing.
-   **`src/generated/prisma/`**: This directory is automatically generated by Prisma and contains the Prisma Client, which is the type-safe query builder used to interact with the database.

## Prisma Schema

The Prisma schema (`prisma/schema/*.prisma`) is the blueprint for the database. It defines the data models, their fields, and the relationships between them.

Example of a model definition in the Prisma schema:
```prisma
model profiles {
  id           Int      @id @default(autoincrement())
  auth_id      Uuid     @unique
  username     String?
  email        String   @unique
  display_name String?
  phone        String?
  address      String?
  created_at   DateTime @default(now())
  updated_at   DateTime @updatedAt
}
```

## Prisma Client

The Prisma Client is a type-safe query builder that is generated from the Prisma schema. It is used to perform database operations (queries and mutations) from the application code.

To use the Prisma Client, you import it from `@/generated/prisma`:
```typescript
import { PrismaClient } from '@/generated/prisma';

const prisma = new PrismaClient();

async function getUsers() {
  const users = await prisma.profiles.findMany();
  return users;
}
```

## Migrations

Database schema changes are managed through Prisma Migrate. When you make changes to your Prisma schema, you run `prisma migrate dev` to generate a new SQL migration file and apply it to your development database. This ensures that schema changes are tracked and can be applied consistently across different environments.

## Seeding

The `prisma/seed.ts` script is used to populate the database with initial data. This is run using the `prisma:seed:dev` or `prisma:seed:prod` npm scripts. The seed script uses the Prisma Client to create records in the database. This is essential for setting up a development environment with realistic data.
