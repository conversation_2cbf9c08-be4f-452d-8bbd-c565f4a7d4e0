# Diet Plan Features Implementation

## Overview

This document outlines the implementation of the diet plan features for the GIVF platform, including condition-based meal plans and personalized PDF generation.

## Features Implemented

### 1. Admin Panel - Diet Plans Management

**Location**: `/admin/diet-plans`

**Features**:
- View all condition-based meal plans
- Create new meal plans for different fertility conditions
- Filter meal plans by condition (PCOS, Low AMH, etc.)
- Delete existing meal plans

**Database Schema**:
- `condition_meal_plans` table with 7-day meal plan data
- Support for multiple conditions and calorie ranges
- JSON storage for flexible meal data

### 2. Condition-Based Meal Plans

**Supported Conditions**:
- PCOS (Polycystic Ovary Syndrome)
- Low AMH (Anti-Müllerian Hormone)
- Increase Fertility Naturally
- Increase Iron

**Meal Plan Structure**:
- 7-day comprehensive meal plans
- 4 meals per day: Breakfast, Lunch, Dinner, Snacks
- Calorie counts for each meal
- Food items without specific quantities (as per requirements)

### 3. User Frontend - Meal Plan Generator

**Location**: `/user/fertility-diet-plan/bmr-calories-calculation`

**Features**:
- Generate personalized meal plans based on BMR calculation
- Download meal plans as HTML files (PDF-ready)
- Condition-specific nutritional advice
- "Book Nutrition Consultation" CTA

## Database Schema

### New Tables

#### `condition_meal_plans`
```sql
CREATE TABLE "condition_meal_plans" (
    "id" TEXT NOT NULL,
    "doctor_id" TEXT NOT NULL,
    "condition" "diet_condition" NOT NULL,
    "target_calories_min" INTEGER NOT NULL,
    "target_calories_max" INTEGER NOT NULL,
    -- 7-day meal plan data (28 JSON fields)
    "day_1_breakfast" JSONB NOT NULL,
    "day_1_lunch" JSONB NOT NULL,
    -- ... (all days and meals)
    "nutritional_advice" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL
);
```

#### `diet_condition` Enum
```sql
CREATE TYPE "diet_condition" AS ENUM (
    'PCOS', 
    'LOW_AMH', 
    'INCREASE_FERTILITY_NATURALLY', 
    'INCREASE_IRON'
);
```

## API Endpoints

### Admin Endpoints

#### GET `/api/v1/admin/condition-meal-plans`
- Fetch condition meal plans with pagination and filtering
- Query parameters: `page`, `per_page`, `condition`, `search`

#### POST `/api/v1/admin/condition-meal-plans`
- Create new condition meal plan
- Body: `doctorId`, `condition`, `targetCaloriesMin`, `targetCaloriesMax`, `nutritionalAdvice`, `weekPlan`

### User Endpoints

#### GET `/api/v1/fertility-diet-plan/meal-plan`
- Get personalized meal plan for user
- Query parameters: `userId`
- Returns: User data, BMR, target calories, condition, nutritional advice, and 7-day meal plan

## Services

### `condition-meal-plans.service.ts`
- Server-side service for managing condition meal plans
- Functions: `getConditionMealPlans`, `deleteConditionMealPlan`, `getMealPlanForUser`

### `meal-plan-pdf.service.ts`
- Service for generating meal plan PDFs
- Function: `generateMealPlanPDF`
- Currently generates HTML content (can be converted to PDF)

## Components

### Admin Components
- `src/app/admin/diet-plans/page.tsx` - Main admin page
- `src/app/admin/diet-plans/create/page.tsx` - Create meal plan form

### User Components
- `src/app/user/fertility-diet-plan/bmr-calories-calculation/meal-plan-generator.tsx` - Meal plan generator
- Updated `src/components/BMRCalculation/BMRCalculation.tsx` - Integrated meal plan generator

## Workflow

### For Doctors (Admin)
1. Navigate to `/admin/diet-plans`
2. Click "Add Meal Plan"
3. Select condition and set calorie range
4. Fill in 7-day meal plan data
5. Add nutritional advice
6. Save meal plan

### For Users
1. Complete diet assessment at `/user/fertility-diet-plan/diet-assessment`
2. View BMR calculation at `/user/fertility-diet-plan/bmr-calories-calculation`
3. Generate personalized meal plan based on their profile
4. Download meal plan as HTML file
5. Optionally book nutrition consultation

## Migration

To apply the database changes:

```bash
# Generate Prisma client (after running migration)
npx prisma generate

# Run the migration (user will run this manually)
npx prisma migrate dev --name add_condition_meal_plans
```

## Future Enhancements

1. **PDF Generation**: Integrate a proper PDF library (e.g., jsPDF, Puppeteer) for better PDF generation
2. **Condition Detection**: Automatically detect user's condition from assessment data
3. **Meal Plan Templates**: Pre-built templates for common conditions
4. **Nutritional Analysis**: Add nutritional breakdown (protein, carbs, fats)
5. **Shopping Lists**: Generate shopping lists based on meal plans
6. **Meal Plan History**: Track user's meal plan usage and preferences

## Notes

- Meal plans are stored as JSON for flexibility
- No specific quantities are provided (as per requirements)
- PDF generation currently creates HTML files that can be printed or converted to PDF
- The system supports multiple calorie ranges for each condition
- All meal plans are condition-specific and fertility-focused
