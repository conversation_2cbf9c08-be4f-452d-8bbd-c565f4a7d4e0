# Diet Plan Questions

## Overview

The Diet Plan Questions page allows administrators to configure and manage questions for the diet plan assessment form. This page mirrors the IVF Questions functionality but focuses on a single "Diet Plan" form rather than the three-category system used for IVF questions.

## Features

### Form Management
- **Single Form**: Unlike IVF questions which use three categories (biological, lifestyle, environmental), diet plan questions use a single "Diet Plan" form
- **Question Builder**: Full-featured question builder with support for all field types
- **Sub-Questions**: Support for conditional sub-questions that appear based on parent question answers
- **Scoring Configuration**: Comprehensive scoring system with range and single-choice scoring options

### Question Types Supported
- **Text Input**: Free-text fields for general input
- **Number Input**: Numerical input with optional min/max values and units
- **Radio Buttons**: Single selection from multiple options
- **Dropdown Select**: Single selection from dropdown menu
- **Range Slider**: Slider for selecting value within a range
- **Group Question**: Parent questions that contain sub-questions

### Scoring Options
- **Range Scoring**: Numeric input mapped to score buckets (e.g., Age, BMI, Income)
- **Single Choice Scoring**: Predefined options, each mapped to a score (e.g., Yes/No, Frequency)
- **Collective Scoring**: For group questions, supports formulas using sub-question answers

### Advanced Features
- **Conditional Logic**: Show questions only if user selects specific options from other questions
- **Track Assignment**: Associate questions with specific fertility tracks
- **Help Text**: Provide guidance and context for users
- **Mandatory Fields**: Mark questions as required
- **Question Reordering**: Drag and drop to reorder questions

## Database Structure

### Forms Table
- **Name**: "Diet Plan" (constant)
- **Description**: "Diet plan assessment form for fertility optimization"

### Questions Table
- All standard question fields (text, type, options, scoring, etc.)
- Support for parent-child relationships (sub-questions)
- Conditional logic via `depends_on_option_id`

## API Endpoints

### GET `/api/v1/diet-plan/form`
Returns the diet plan form with all questions and options.

### Client Services
- `src/lib/services/client/diet-plan.service.ts`: Client-side service functions
- `src/lib/services/diet-plan.service.ts`: Server-side service functions

## Components

### Main Components
- `DietPlanFormBuilder`: Main form builder component (single form, no tabs)
- `QuestionBuilder`: Reused from IVF questions for question creation/editing
- `SubQuestionBuilder`: Reused from IVF questions for sub-question management
- `QuestionList`: Reused from IVF questions for question display and management

### Supporting Components
- `ScoringTypeSelector`: Scoring type selection
- `RangeScoreConfigComponent`: Range scoring configuration
- `SingleChoiceScoreConfig`: Single choice scoring configuration
- `FormFieldRenderer`: Question preview and rendering

## Usage

### Accessing the Page
1. Navigate to `/admin/diet-plan-questions`
2. The page will automatically initialize the "Diet Plan" form if it doesn't exist
3. Use the "Add Question" button to create new questions

### Creating Questions
1. Click "Add Question"
2. Fill in the question details (text, type, options, etc.)
3. Configure scoring if needed
4. Add sub-questions if using group questions
5. Save the question

### Managing Questions
- **Edit**: Click the edit button on any question
- **Delete**: Click the delete button on any question
- **Reorder**: Drag and drop questions to reorder them
- **Refresh**: Use the refresh button to reload the form data

## Technical Implementation

### Hooks
- `useDietPlanForm`: Custom hook for fetching diet plan form data

### Services
- Client-side service wraps server-side functions for React Query integration
- Server-side service handles database operations and form initialization

### Form Initialization
The form is automatically created with the name "Diet Plan" when the page is first accessed. This ensures the form exists before any questions can be added.

## Differences from IVF Questions

1. **Single Form**: No tabs or categories - all questions belong to one "Diet Plan" form
2. **Simplified UI**: Removed tab navigation since there's only one form
3. **Same Functionality**: All question building, scoring, and management features are identical
4. **Reused Components**: Leverages existing question builder components for consistency

## Future Enhancements

- Diet-specific question templates
- Nutritional scoring algorithms
- Integration with diet plan recommendations
- Export/import functionality for question sets 