# Admin Dashboard

This document provides an overview of the admin dashboard, its structure, and key features.

## Overview

The admin dashboard is a protected area of the application where administrators can manage users, roles, permissions, and other system settings.

## Key Files

-   **`src/app/admin/`**: The root directory for all admin-related pages and components.
    -   **`layout.tsx`**: The main layout for the admin dashboard, including the sidebar navigation.
    -   **`page.tsx`**: The main dashboard page with overview stats and quick actions.
    -   **`roles-permissions/page.tsx`**: The page for managing roles and permissions.
    -   **`fertility-meter-questions/page.tsx`**: The page for managing the fertility meter questions.
-   **`src/components/admin/`**: Components that are specific to the admin dashboard.
-   **`src/styles/admin-theme.css`** and **`admin-overrides.css`**: Custom styling for the admin section.
-   **`src/components/AdminAuthWrapper.tsx`**: A component that protects the admin routes from unauthorized access.

## Access and Security

Access to the admin dashboard is restricted to users with the `admin.access` permission. The `AdminAuthWrapper` component enforces this by checking the user's permissions before rendering any admin content. If the user is not authorized, they are redirected to the login page.

## Navigation

The admin dashboard features a sidebar navigation defined in `src/app/admin/layout.tsx`. This navigation provides links to the different sections of the admin panel:
-   Dashboard
-   Roles & Permissions
-   Users
-   Fertility Questions
-   Settings

## Key Features

### Roles & Permissions Management

The "Roles & Permissions" page allows administrators to:
-   Create, edit, and delete roles.
-   Assign granular CRUD (Create, Read, Update, Delete) permissions to roles for various resources (e.g., profiles, users, ivf_scores).
-   View a matrix of all roles and their assigned permissions.

This is a critical feature for controlling access to different parts of the application.

### Fertility Meter Questions

The "Fertility Questions" page enables admins to manage the questions used in the IVF fertility meter. This includes:
-   Creating new questions with different field types (e.g., number input, radio select).
-   Defining the scoring logic for each question (either range-based or single-choice).
-   Editing and deleting existing questions.

This allows the scoring system to be dynamic and configurable by the administrators without requiring code changes.

### User Management

The "Users" section (when implemented) will allow admins to view, manage, and assign roles to users in the system.
