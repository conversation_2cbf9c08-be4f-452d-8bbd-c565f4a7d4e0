# Project Structure

This document provides an overview of the GIVF project's directory structure. Understanding this structure is key to navigating the codebase and contributing effectively.

## Root Directory

The root directory contains configuration files for the entire project, including Next.js, TypeScript, ESLint, Prettier, and Git.

-   `.env.example`: Example environment variables. Copy this to `.env.local` for development.
-   `next.config.ts`: Configuration for the Next.js framework.
-   `tsconfig.json`: TypeScript compiler options.
-   `package.json`: Project dependencies and scripts.
-   `prisma/`: Contains all database-related files, including the Prisma schema and migrations.

## `src/` Directory

The `src/` directory is the heart of the application, containing all the source code.

-   **`app/`**: The main application directory for Next.js, using the App Router.
    -   `app/api/`: API routes for the application, including GraphQL and REST endpoints.
    -   `app/admin/`: The admin dashboard section of the site.
    -   `app/auth/`: Authentication-related pages like password reset.
    -   `app/dashboard/`: The main user dashboard after login.
    -   `app/(auth-pages)/`: Route group for pages like login, signup, forgot-password.
    -   `layout.tsx`: The root layout for the application.
    -   `page.tsx`: The main landing page.

-   **`components/`**: Reusable React components used throughout the application.
    -   `components/ShadcnUI/`: UI components from the Shadcn UI library.
    -   `components/admin/`: Components specific to the admin dashboard.
    -   `components/shared/`: Components shared across different parts of the application.

-   **`hooks/`**: Custom React hooks for managing state and side effects.
    -   `useAuth.ts`: Hook for managing user authentication state.
    -   `useUserCan.ts`: Hook for checking user permissions.

-   **`lib/`**: Libraries and utility functions.
    -   `lib/services/`: Business logic and services, like the dynamic scoring service.
    -   `lib/utils/`: General utility functions.

-   **`prisma/`**: Prisma schema and database-related files.
    -   `schema/`: Contains the different prisma schema files.
    -   `seed.ts`: Script for seeding the database with initial data.
    -   `migrations/`: Database migration files generated by Prisma.

-   **`styles/`**: Global and shared CSS styles.

-   **`tests/`**: Test files for the application.

-   **`types/`**: TypeScript type definitions.

-   **`utils/`**: Utility functions, especially for Supabase and API interactions.

-   **`validations/`**: Zod validation schemas.

## `public/` Directory

The `public/` directory contains static assets like images, fonts, and icons.

## `.storybook/` Directory

Configuration for Storybook, a tool for developing and testing UI components in isolation.
