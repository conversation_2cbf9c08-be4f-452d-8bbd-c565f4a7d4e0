# Roles and Permissions

The GIVF application uses a robust role-based access control (RBAC) system built on top of Supabase. This document outlines how permissions are managed and checked.

## Overview

The system is centered around a custom PostgreSQL function in Supabase called `authorize`. This function checks if the currently authenticated user has the required permission to perform a specific action on a resource.

## Key Components

-   **`ROLES_PERMISSIONS_README.md`**: The main README for the roles and permissions feature.
-   **Database `authorize` function**: A SQL function that takes a `resource_action` (e.g., `'profiles.read'`) and returns `true` or `false`. It checks the user's role and the permissions associated with that role.
-   **Row-Level Security (RLS) Policies**: PostgreSQL policies on tables (e.g., `profiles`) that use the `authorize` function to restrict access to rows.
-   **`src/hooks/useUserCan.ts`**: A client-side hook that calls the `authorize` RPC function to check permissions from the frontend.
-   **`src/components/PermissionGate.tsx`**: A component that conditionally renders its children based on the `useUserCan` hook.
-   **`src/utils/permissions.ts`**: Utility functions for working with permissions (though `useUserCan` is the primary method for checking).

## `authorize` Function

The `authorize` function in the database is the single source of truth for permissions. It performs the following steps:
1.  Gets the current user's ID from `auth.uid()`.
2.  Finds the user's role from the `user_roles` table.
3.  If the user's role is 'admin', it immediately returns `true`.
4.  Parses the `resource_action` string (e.g., `'profiles.update'`) into a resource (`profiles`) and an action (`update`).
5.  Queries the `permissions` table to see if the user's role has the specified permission for the resource and action.

## `useUserCan` Hook

The `useUserCan` hook provides a simple way to check permissions in your React components. It uses `@tanstack/react-query` to cache the results and avoid unnecessary API calls.

```typescript
import { useUserCan } from '@/hooks/useUserCan';

function DeleteButton({ profileId }) {
  const { data: canDelete, isLoading } = useUserCan('profiles.delete');

  if (isLoading) {
    return <button disabled>...</button>;
  }

  if (!canDelete) {
    return null; // Or a disabled button
  }

  return <button onClick={() => deleteProfile(profileId)}>Delete</button>;
}
```

## `PermissionGate` Component

For declaratively showing or hiding UI elements based on permissions, you can use the `PermissionGate` component.

```typescript
import PermissionGate from '@/components/PermissionGate';

function UserActions() {
  return (
    <PermissionGate resource_action="profiles.create">
      <button>Create New Profile</button>
    </PermissionGate>
  );
}
```

## RLS Policies

Row-Level Security policies are defined on database tables to enforce permissions at the database level. This is a crucial security measure that prevents users from accessing or modifying data they shouldn't, even if they bypass the application's UI.

Example RLS policy for reading profiles:
```sql
create policy "Allow authorized read access"
on profiles for select
to authenticated
using (authorize('profiles.read'));
```
This policy ensures that a `SELECT` query on the `profiles` table will only return rows if the `authorize('profiles.read')` function returns `true` for the user making the query.
